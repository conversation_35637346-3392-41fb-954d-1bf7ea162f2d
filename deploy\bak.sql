-- MySQL dump 10.13  Distrib 8.0.42, for Linux (x86_64)
--
-- Host: localhost    Database: changan_5g
-- ------------------------------------------------------
-- Server version	8.0.42-0ubuntu0.22.04.1

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Current Database: `changan_5g`
--

CREATE DATABASE /*!32312 IF NOT EXISTS*/ `changan_5g` /*!40100 DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_bin */ /*!80016 DEFAULT ENCRYPTION='N' */;

USE `changan_5g`;

--
-- Table structure for table `casbin_rule`
--

DROP TABLE IF EXISTS `casbin_rule`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `casbin_rule` (
  `id` int NOT NULL AUTO_INCREMENT,
  `ptype` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `v0` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `v1` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `v2` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `v3` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `v4` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `v5` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2218 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `casbin_rule`
--

LOCK TABLES `casbin_rule` WRITE;
/*!40000 ALTER TABLE `casbin_rule` DISABLE KEYS */;
INSERT INTO `casbin_rule` VALUES (2120,'p','admin','/api/v1/admin/user-management/page','get',NULL,NULL,NULL),(2121,'p','user','/api/v1/account*','GET',NULL,NULL,NULL),(2122,'p','user','/api/v1/account*','POST',NULL,NULL,NULL),(2123,'p','user','/api/v1/account*','PUT',NULL,NULL,NULL),(2124,'p','user','/api/v1/account*','DELETE',NULL,NULL,NULL),(2125,'p','admin','/api/v1/account*','GET',NULL,NULL,NULL),(2126,'p','admin','/api/v1/account*','POST',NULL,NULL,NULL),(2127,'p','admin','/api/v1/account*','DELETE',NULL,NULL,NULL),(2128,'p','admin','/api/v1/account*','PUT',NULL,NULL,NULL),(2129,'p','admin','/api/v1/account*','DELETE',NULL,NULL,NULL),(2130,'p','admin','/api/v1/account*','PUT',NULL,NULL,NULL),(2131,'p','admin','/api/v1/admin/rbac*','GET',NULL,NULL,NULL),(2132,'p','admin','/api/v1/admin/rbac*','POST',NULL,NULL,NULL),(2133,'p','admin','/api/v1/admin/rbac*','PUT',NULL,NULL,NULL),(2134,'p','admin','/api/v1/admin/rbac*','DELETE',NULL,NULL,NULL),(2135,'p','auditor','/api/v1/admin/rbac*','GET',NULL,NULL,NULL),(2136,'p','admin','/api/v1/admin/samples*','GET',NULL,NULL,NULL),(2137,'p','admin','/api/v1/admin/samples*','POST',NULL,NULL,NULL),(2138,'p','admin','/api/v1/admin/samples*','PUT',NULL,NULL,NULL),(2139,'p','admin','/api/v1/admin/samples*','DELETE',NULL,NULL,NULL),(2140,'p','auditor','/api/v1/admin/samples*','GET',NULL,NULL,NULL),(2141,'p','auditor','/api/v1/admin/samples*','POST',NULL,NULL,NULL),(2142,'p','auditor','/api/v1/admin/samples*','PUT',NULL,NULL,NULL),(2143,'p','auditor','/api/v1/admin/samples*','DELETE',NULL,NULL,NULL),(2144,'p','admin','/api/v1/admin/users*','GET',NULL,NULL,NULL),(2145,'p','admin','/api/v1/admin/users*','POST',NULL,NULL,NULL),(2146,'p','admin','/api/v1/admin/users*','PUT',NULL,NULL,NULL),(2147,'p','admin','/api/v1/admin/users*','DELETE',NULL,NULL,NULL),(2148,'p','auditor','/api/v1/admin/users*','GET',NULL,NULL,NULL),(2149,'p','auditor','/api/v1/auth/refreshToken','GET',NULL,NULL,NULL),(2150,'p','admin','/api/v1/auth/refreshToken','GET',NULL,NULL,NULL),(2151,'p','user','/api/v1/auth/refreshToken','GET',NULL,NULL,NULL),(2152,'p','admin','/api/v1/admin/terminals*','GET',NULL,NULL,NULL),(2153,'p','admin','/api/v1/admin/terminals*','POST',NULL,NULL,NULL),(2154,'p','admin','/api/v1/admin/terminals*','PUT',NULL,NULL,NULL),(2155,'p','admin','/api/v1/admin/terminals*','DELETE',NULL,NULL,NULL),(2156,'p','admin','/api/v1/admin/terminal/metrics*','GET',NULL,NULL,NULL),(2157,'p','admin','/api/v1/admin/terminal/metrics*','POST',NULL,NULL,NULL),(2158,'p','admin','/api/v1/admin/changan5g*','GET',NULL,NULL,NULL),(2159,'p','admin','/api/v1/admin/users*','GET',NULL,NULL,NULL),(2160,'p','admin','/api/v1/admin/users*','POST',NULL,NULL,NULL),(2161,'p','admin','/api/v1/admin/users*','PUT',NULL,NULL,NULL),(2162,'p','admin','/api/v1/admin/users*','DELETE',NULL,NULL,NULL),(2163,'p','admin','/api/v1/admin/rbac*','GET',NULL,NULL,NULL),(2164,'p','admin','/api/v1/admin/rbac*','POST',NULL,NULL,NULL),(2165,'p','admin','/api/v1/admin/rbac*','PUT',NULL,NULL,NULL),(2166,'p','admin','/api/v1/admin/rbac*','DELETE',NULL,NULL,NULL),(2167,'p','admin','/api/v1/admin/samples*','GET',NULL,NULL,NULL),(2168,'p','admin','/api/v1/admin/samples*','POST',NULL,NULL,NULL),(2169,'p','admin','/api/v1/admin/samples*','PUT',NULL,NULL,NULL),(2170,'p','admin','/api/v1/admin/samples*','DELETE',NULL,NULL,NULL),(2171,'p','admin','/api/v1/account*','GET',NULL,NULL,NULL),(2172,'p','admin','/api/v1/account*','POST',NULL,NULL,NULL),(2173,'p','admin','/api/v1/account*','PUT',NULL,NULL,NULL),(2174,'p','admin','/api/v1/account*','DELETE',NULL,NULL,NULL),(2175,'p','admin','/api/v1/auth/refreshToken','GET',NULL,NULL,NULL),(2176,'p','user','/api/v1/admin/terminals*','GET',NULL,NULL,NULL),(2177,'p','user','/api/v1/admin/terminal/metrics*','GET',NULL,NULL,NULL),(2178,'p','user','/api/v1/admin/changan5g*','GET',NULL,NULL,NULL),(2179,'p','user','/api/v1/admin/samples*','GET',NULL,NULL,NULL),(2180,'p','user','/api/v1/admin/users*','GET',NULL,NULL,NULL),(2181,'p','user','/api/v1/admin/rbac*','GET',NULL,NULL,NULL),(2182,'p','user','/api/v1/account*','GET',NULL,NULL,NULL),(2183,'p','user','/api/v1/account*','PUT',NULL,NULL,NULL),(2184,'p','user','/api/v1/auth/refreshToken','GET',NULL,NULL,NULL),(2185,'p','admin','/api/v1/admin/terminals*','GET',NULL,NULL,NULL),(2186,'p','admin','/api/v1/admin/terminals*','POST',NULL,NULL,NULL),(2187,'p','admin','/api/v1/admin/terminals*','PUT',NULL,NULL,NULL),(2188,'p','admin','/api/v1/admin/terminals*','DELETE',NULL,NULL,NULL),(2189,'p','admin','/api/v1/admin/terminal/metrics*','GET',NULL,NULL,NULL),(2190,'p','admin','/api/v1/admin/terminal/metrics*','POST',NULL,NULL,NULL),(2191,'p','auditor','/api/v1/admin/terminals*','GET',NULL,NULL,NULL),(2192,'p','auditor','/api/v1/admin/terminal/metrics*','GET',NULL,NULL,NULL),(2193,'p','user','/api/v1/admin/terminals*','GET',NULL,NULL,NULL),(2194,'p','user','/api/v1/admin/terminal/metrics*','GET',NULL,NULL,NULL),(2195,'p','admin','/api/v1/admin/alerts*','GET',NULL,NULL,NULL),(2196,'p','admin','/api/v1/admin/alerts*','POST',NULL,NULL,NULL),(2197,'p','admin','/api/v1/admin/alerts*','PUT',NULL,NULL,NULL),(2198,'p','admin','/api/v1/admin/alerts*','DELETE',NULL,NULL,NULL),(2199,'p','admin','/api/v1/admin/user-management*','GET',NULL,NULL,NULL),(2200,'p','admin','/api/v1/admin/user-management*','POST',NULL,NULL,NULL),(2201,'p','admin','/api/v1/admin/user-management*','PUT',NULL,NULL,NULL),(2202,'p','admin','/api/v1/admin/user-management*','DELETE',NULL,NULL,NULL),(2203,'p','admin','/api/v1/admin/user-management/users','GET',NULL,NULL,NULL),(2204,'p','admin','/api/v1/admin/overview/*','GET',NULL,NULL,NULL),(2205,'p','user','/api/v1/admin/overview/*','GET',NULL,NULL,NULL),(2206,'p','user','/api/v1/admin/alerts*','GET',NULL,NULL,NULL),(2207,'p','admin','/api/v1/admin/metric-threshold-config*','GET',NULL,NULL,NULL),(2208,'p','admin','/api/v1/admin/metric-threshold-config*','POST',NULL,NULL,NULL),(2209,'p','admin','/api/v1/admin/metric-threshold-config*','PUT',NULL,NULL,NULL),(2210,'p','admin','/api/v1/admin/metric-threshold-config*','DELETE',NULL,NULL,NULL),(2211,'p','auditor','/api/v1/admin/metric-threshold-config*','GET',NULL,NULL,NULL),(2212,'p','user','/api/v1/admin/metric-threshold-config*','GET',NULL,NULL,NULL),(2213,'g','admin','admin',NULL,NULL,NULL,NULL),(2214,'g','user','user',NULL,NULL,NULL,NULL),(2215,'g','msysu99','admin',NULL,NULL,NULL,NULL),(2216,'g','ayou','admin',NULL,NULL,NULL,NULL),(2217,'g','test','user',NULL,NULL,NULL,NULL);
/*!40000 ALTER TABLE `casbin_rule` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `login_history`
--

DROP TABLE IF EXISTS `login_history`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `login_history` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `created_at` datetime(3) DEFAULT NULL,
  `updated_at` datetime(3) DEFAULT NULL,
  `deleted_at` bigint unsigned DEFAULT '0',
  `username` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `ip_addr` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci,
  `location` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `browser` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `login_method` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci,
  `success` tinyint(1) NOT NULL,
  `created_by` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `updated_by` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_login_histories_deleted_at` (`deleted_at`) USING BTREE,
  KEY `idx_login_histories_username` (`username`) USING BTREE,
  KEY `idx_login_history_username` (`username`) USING BTREE,
  KEY `idx_login_history_created_by` (`created_by`) USING BTREE,
  KEY `idx_login_history_updated_by` (`updated_by`) USING BTREE,
  KEY `idx_login_history_deleted_at` (`deleted_at`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=55 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `login_history`
--

LOCK TABLES `login_history` WRITE;
/*!40000 ALTER TABLE `login_history` DISABLE KEYS */;
INSERT INTO `login_history` VALUES (1,'2025-07-16 16:33:29.580','2025-07-16 16:33:29.580',0,'doctor','0:0:0:0:0:0:0:1','未知','Edge 138','password',0,'anonymousUser','anonymousUser'),(2,'2025-07-16 16:33:47.319','2025-07-16 16:33:47.319',0,'admin','0:0:0:0:0:0:0:1','未知','Edge 138','password',0,'anonymousUser','anonymousUser'),(3,'2025-07-16 16:34:13.423','2025-07-16 16:34:13.423',0,'admin','0:0:0:0:0:0:0:1','未知','Edge 138','password',0,'anonymousUser','anonymousUser'),(4,'2025-07-16 16:34:20.144','2025-07-16 16:34:20.144',0,'admin','0:0:0:0:0:0:0:1','未知','Edge 138','password',0,'anonymousUser','anonymousUser'),(5,'2025-07-16 16:35:55.043','2025-07-16 16:35:55.043',0,'admin','0:0:0:0:0:0:0:1','未知','Edge 138','password',0,'anonymousUser','anonymousUser'),(6,'2025-07-16 16:36:51.116','2025-07-16 16:36:51.116',0,'admin','0:0:0:0:0:0:0:1','未知','Edge 138','password',0,'anonymousUser','anonymousUser'),(7,'2025-07-16 16:48:49.304','2025-07-16 16:48:49.305',0,'doctor','0:0:0:0:0:0:0:1','未知','Edge 138','password',0,'anonymousUser','anonymousUser'),(8,'2025-07-16 16:54:27.122','2025-07-16 16:54:27.123',0,'doctor','0:0:0:0:0:0:0:1','未知','Edge 138','password',0,'anonymousUser','anonymousUser'),(9,'2025-07-16 17:07:33.616','2025-07-16 17:07:33.621',0,'doctor','0:0:0:0:0:0:0:1','未知','Edge 138','password',0,'anonymousUser','anonymousUser'),(10,'2025-07-16 17:08:02.341','2025-07-16 17:08:02.346',0,'doctor','0:0:0:0:0:0:0:1','未知','Edge 138','password',0,'anonymousUser','anonymousUser'),(11,'2025-07-16 17:09:25.814','2025-07-16 17:09:25.814',0,'doctor','0:0:0:0:0:0:0:1','未知','Edge 138','password',0,'anonymousUser','anonymousUser'),(12,'2025-07-16 17:10:57.752','2025-07-16 17:10:57.752',0,'admin','0:0:0:0:0:0:0:1','未知','Edge 138','password',0,'anonymousUser','anonymousUser'),(13,'2025-07-16 17:11:22.598','2025-07-16 17:11:22.599',0,'admin','0:0:0:0:0:0:0:1','未知','Edge 138','password',0,'anonymousUser','anonymousUser'),(14,'2025-07-16 17:20:20.876','2025-07-16 17:20:20.882',0,'admin','0:0:0:0:0:0:0:1','未知','Edge 138','password',0,'anonymousUser','anonymousUser'),(15,'2025-07-16 17:29:29.229','2025-07-16 17:29:29.230',0,'admin','0:0:0:0:0:0:0:1','未知','Edge 138','password',0,'anonymousUser','anonymousUser'),(16,'2025-07-16 17:29:42.656','2025-07-16 17:29:42.656',0,'admin','0:0:0:0:0:0:0:1','未知','Edge 138','password',0,'anonymousUser','anonymousUser'),(17,'2025-07-16 17:34:28.778','2025-07-16 17:34:28.783',0,'admin','0:0:0:0:0:0:0:1','未知','Edge 138','password',0,'anonymousUser','anonymousUser'),(18,'2025-07-16 17:35:30.067','2025-07-16 17:35:30.067',0,'admin','0:0:0:0:0:0:0:1','未知','Edge 138','password',1,'anonymousUser','anonymousUser'),(19,'2025-07-16 22:49:26.607','2025-07-16 22:49:26.613',0,'admIn','0:0:0:0:0:0:0:1','未知','Edge 138','password',1,'anonymousUser','anonymousUser'),(20,'2025-07-17 09:15:27.660','2025-07-17 09:15:27.661',0,'admin','0:0:0:0:0:0:0:1','未知','Edge 138','password',1,'anonymousUser','anonymousUser'),(21,'2025-07-17 10:16:58.070','2025-07-17 10:16:58.071',0,'ayou','0:0:0:0:0:0:0:1','未知','Edge 138','password',0,'anonymousUser','anonymousUser'),(22,'2025-07-17 10:18:18.370','2025-07-17 10:18:18.379',0,'ayou','0:0:0:0:0:0:0:1','未知','Edge 138','password',0,'anonymousUser','anonymousUser'),(23,'2025-07-17 10:18:36.163','2025-07-17 10:18:36.163',0,'ayou','0:0:0:0:0:0:0:1','未知','Edge 138','password',0,'anonymousUser','anonymousUser'),(24,'2025-07-17 10:19:04.909','2025-07-17 10:19:04.911',0,'admin','0:0:0:0:0:0:0:1','未知','Edge 138','password',1,'anonymousUser','anonymousUser'),(25,'2025-07-17 20:15:07.982','2025-07-17 20:15:07.983',0,'admin','0:0:0:0:0:0:0:1','未知','Edge 138','password',1,'anonymousUser','anonymousUser'),(26,'2025-07-18 09:47:31.760','2025-07-18 09:47:31.760',0,'ayou','0:0:0:0:0:0:0:1','未知','Edge 138','password',0,'anonymousUser','anonymousUser'),(27,'2025-07-18 09:49:08.268','2025-07-18 09:49:08.268',0,'ayou','0:0:0:0:0:0:0:1','未知','Edge 138','password',0,'anonymousUser','anonymousUser'),(28,'2025-07-18 09:49:42.929','2025-07-18 09:49:42.929',0,'ayou','0:0:0:0:0:0:0:1','未知','Edge 138','password',0,'anonymousUser','anonymousUser'),(29,'2025-07-18 09:50:30.432','2025-07-18 09:50:30.432',0,'user','0:0:0:0:0:0:0:1','未知','Edge 138','password',1,'anonymousUser','anonymousUser'),(30,'2025-07-18 09:51:50.674','2025-07-18 09:51:50.674',0,'ayou','0:0:0:0:0:0:0:1','未知','Edge 138','password',0,'anonymousUser','anonymousUser'),(31,'2025-07-18 09:53:04.614','2025-07-18 09:53:04.614',0,'admin','0:0:0:0:0:0:0:1','未知','Edge 138','password',1,'anonymousUser','anonymousUser'),(32,'2025-07-18 09:57:05.650','2025-07-18 09:57:05.650',0,'ayou','0:0:0:0:0:0:0:1','未知','Edge 138','password',0,'anonymousUser','anonymousUser'),(33,'2025-07-18 09:57:34.041','2025-07-18 09:57:34.047',0,'ayou','0:0:0:0:0:0:0:1','未知','Edge 138','password',0,'anonymousUser','anonymousUser'),(34,'2025-07-18 09:57:49.487','2025-07-18 09:57:49.487',0,'ayou','0:0:0:0:0:0:0:1','未知','Edge 138','password',0,'anonymousUser','anonymousUser'),(35,'2025-07-18 09:58:34.148','2025-07-18 09:58:34.148',0,'ayou','0:0:0:0:0:0:0:1','未知','Edge 138','password',0,'anonymousUser','anonymousUser'),(36,'2025-07-18 10:25:20.817','2025-07-18 10:25:20.825',0,'ayou','0:0:0:0:0:0:0:1','未知','Edge 138','password',0,'anonymousUser','anonymousUser'),(37,'2025-07-18 10:30:01.415','2025-07-18 10:30:01.419',0,'ayou','0:0:0:0:0:0:0:1','未知','Edge 138','password',0,'anonymousUser','anonymousUser'),(38,'2025-07-18 10:35:21.723','2025-07-18 10:35:21.735',0,'admin','0:0:0:0:0:0:0:1','未知','Edge 138','password',1,'anonymousUser','anonymousUser'),(39,'2025-07-18 11:08:49.702','2025-07-18 11:08:49.702',0,'ayou','0:0:0:0:0:0:0:1','未知','Edge 138','password',1,'anonymousUser','anonymousUser'),(40,'2025-07-18 11:13:00.004','2025-07-18 11:13:00.004',0,'user','0:0:0:0:0:0:0:1','未知','Edge 138','password',1,'anonymousUser','anonymousUser'),(41,'2025-07-18 11:17:09.131','2025-07-18 11:17:09.131',0,'admin','0:0:0:0:0:0:0:1','未知','Edge 138','password',0,'anonymousUser','anonymousUser'),(42,'2025-07-18 11:17:16.554','2025-07-18 11:17:16.554',0,'admin','0:0:0:0:0:0:0:1','未知','Edge 138','password',0,'anonymousUser','anonymousUser'),(43,'2025-07-18 11:17:28.352','2025-07-18 11:17:28.352',0,'admin','0:0:0:0:0:0:0:1','未知','Edge 138','password',1,'anonymousUser','anonymousUser'),(44,'2025-07-18 14:25:30.777','2025-07-18 14:25:30.783',0,'admin','0:0:0:0:0:0:0:1','未知','Edge 138','password',1,'anonymousUser','anonymousUser'),(45,'2025-07-18 14:35:31.385','2025-07-18 14:35:31.387',0,'admin','172.16.1.154','未知','Chrome 138','password',1,'anonymousUser','anonymousUser'),(46,'2025-07-18 14:37:49.894','2025-07-18 14:37:49.894',0,'admin','172.16.0.174','未知','Chrome 128','password',1,'anonymousUser','anonymousUser'),(47,'2025-07-18 15:31:38.830','2025-07-18 15:31:38.830',0,'admin','172.16.1.54','未知','Edge 138','password',1,'anonymousUser','anonymousUser'),(48,'2025-07-21 10:08:41.620','2025-07-21 10:08:41.620',0,'admin','172.16.1.158','未知','Chrome 138','password',1,'anonymousUser','anonymousUser'),(49,'2025-07-21 10:51:26.779','2025-07-21 10:51:26.779',0,'admin','172.16.1.54','未知','Edge 138','password',1,'anonymousUser','anonymousUser'),(50,'2025-07-21 11:58:06.269','2025-07-21 11:58:06.269',0,'admin','172.16.1.54','未知','Edge 138','password',1,'anonymousUser','anonymousUser'),(51,'2025-07-21 16:29:45.944','2025-07-21 16:29:45.944',0,'patient2','172.16.1.54','未知','Edge 138','password',0,'anonymousUser','anonymousUser'),(52,'2025-07-21 16:30:01.250','2025-07-21 16:30:01.250',0,'admin','172.16.1.54','未知','Edge 138','password',1,'anonymousUser','anonymousUser'),(53,'2025-07-23 09:23:43.031','2025-07-23 09:23:43.031',0,'admin','0:0:0:0:0:0:0:1','未知','Edge 138','password',1,'anonymousUser','anonymousUser'),(54,'2025-07-23 09:57:44.675','2025-07-23 09:57:44.675',0,'admin','172.16.0.132','未知','Chrome 126.0.0.0','password',1,'anonymousUser','anonymousUser');
/*!40000 ALTER TABLE `login_history` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `metric_threshold_config`
--

DROP TABLE IF EXISTS `metric_threshold_config`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `metric_threshold_config` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `metric_type` varchar(50) NOT NULL COMMENT '指标类型：CPU_TEMPERATURE, MEMORY_USAGE, DISK_USAGE, DISK_DATA_USAGE, DISK_SYSTEM_USAGE, LICENSE_EXPIRY',
  `metric_name` varchar(100) NOT NULL COMMENT '指标名称',
  `metric_description` varchar(200) DEFAULT NULL COMMENT '指标描述',
  `threshold_value` decimal(10,2) NOT NULL COMMENT '阈值',
  `threshold_unit` varchar(20) NOT NULL COMMENT '阈值单位：%, °C, days等',
  `comparison_operator` varchar(10) NOT NULL DEFAULT '>=' COMMENT '比较操作符：>=, >, <=, <, =',
  `alert_level` varchar(20) NOT NULL DEFAULT 'MEDIUM' COMMENT '告警级别：LOW, MEDIUM, HIGH, CRITICAL',
  `alert_message` varchar(500) DEFAULT NULL COMMENT '告警消息模板',
  `is_enabled` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用：0-禁用，1-启用',
  `sort_order` int DEFAULT '0' COMMENT '排序',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_metric_type` (`metric_type`),
  KEY `idx_is_enabled` (`is_enabled`),
  KEY `idx_alert_level` (`alert_level`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='指标阈值配置表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `metric_threshold_config`
--

LOCK TABLES `metric_threshold_config` WRITE;
/*!40000 ALTER TABLE `metric_threshold_config` DISABLE KEYS */;
INSERT INTO `metric_threshold_config` VALUES (1,'CPU_TEMPERATURE','CPU温度','CPU温度过高告警',85.00,'°C','>=','HIGH','CPU温度过高，当前温度：{current_value}，阈值：{threshold_value}',1,1,'2025-07-21 07:57:32','2025-07-21 07:57:32','system',NULL,'当CPU温度达到或超过85摄氏度时触发告警'),(2,'MEMORY_USAGE','内存使用率','内存使用率过高告警',90.00,'%','>=','HIGH','内存使用率过高，当前使用率：{current_value}，阈值：{threshold_value}',1,2,'2025-07-21 07:57:32','2025-07-21 07:57:32','system',NULL,'当内存使用率达到或超过90%时触发告警'),(3,'DISK_USAGE','磁盘使用率','磁盘使用率过高告警',80.00,'%','>=','MEDIUM','磁盘使用率过高，当前使用率：{current_value}，阈值：{threshold_value}',1,3,'2025-07-21 07:57:32','2025-07-21 07:57:32','system',NULL,'当磁盘使用率达到或超过80%时触发告警'),(4,'DISK_DATA_USAGE','数据磁盘使用率','数据磁盘使用率过高告警',80.00,'%','>=','MEDIUM','数据磁盘使用率过高，当前使用率：{current_value}，阈值：{threshold_value}',1,4,'2025-07-21 07:57:32','2025-07-21 07:57:32','system',NULL,'当数据磁盘使用率达到或超过80%时触发告警'),(5,'DISK_SYSTEM_USAGE','系统磁盘使用率','系统磁盘使用率过高告警',80.00,'%','>=','MEDIUM','系统磁盘使用率过高，当前使用率：{current_value}，阈值：{threshold_value}',1,5,'2025-07-21 07:57:32','2025-07-21 07:57:32','system',NULL,'当系统磁盘使用率达到或超过80%时触发告警'),(6,'LICENSE_EXPIRY','软件授权过期','软件授权即将过期告警',30.00,'days','<=','CRITICAL','软件授权即将过期，剩余天数：{current_value}，阈值：{threshold_value}',1,6,'2025-07-21 07:57:32','2025-07-21 07:57:32','system',NULL,'当软件授权剩余天数少于或等于30天时触发告警');
/*!40000 ALTER TABLE `metric_threshold_config` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `runtime_config`
--

DROP TABLE IF EXISTS `runtime_config`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `runtime_config` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '配置名称（app.auth.keypair.publicKey）',
  `value` longtext COLLATE utf8mb4_general_ci COMMENT '配置值（0xffffabcd）',
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '描述（认证密钥对公钥）',
  `created_at` datetime(3) DEFAULT NULL,
  `updated_at` datetime(3) DEFAULT NULL,
  `deleted_at` bigint unsigned DEFAULT '0',
  `created_by` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `updated_by` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `idx_runtime_config_name` (`name`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `runtime_config`
--

LOCK TABLES `runtime_config` WRITE;
/*!40000 ALTER TABLE `runtime_config` DISABLE KEYS */;
INSERT INTO `runtime_config` VALUES (1,'app.auth.keypair.publicKey','MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAhNqISciMaXMAF3iUxqUF9nSYZx1vofgIHFOjOMDAc6oC9ZbfzD9WsxIdwmjFu4xH1Hq1Tgb7+vSBqgivPwwAF91qv8wELjOloodXFyEe/Dlaql+6MPTyvglFpgtCgOTgFPd0a0lHHohf5mf23+5joWTR5BJB303YTZvM94DDYjSFT4nPCWDt80DFPkcnjTp8JWPP+QBP77T0Hpsu93vvPiTUyvabUc12leaVJGs2WZkQgwXpcdPM5zpwLwUBBujPcLaJn9KiPXzItxUL1YPgGfNa5eoA6qGJIIwJcQ4Myk1ZuyVdV1MGavuDzOwTfvlz2yhBoB/GaOy6NNDOoiLg7wIDAQAB','public key for auth','2025-07-16 16:31:36.606','2025-07-16 16:31:36.607',0,'anonymousUser','anonymousUser'),(2,'app.auth.keypair.privateKey','MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCE2ohJyIxpcwAXeJTGpQX2dJhnHW+h+AgcU6M4wMBzqgL1lt/MP1azEh3CaMW7jEfUerVOBvv69IGqCK8/DAAX3Wq/zAQuM6Wih1cXIR78OVqqX7ow9PK+CUWmC0KA5OAU93RrSUceiF/mZ/bf7mOhZNHkEkHfTdhNm8z3gMNiNIVPic8JYO3zQMU+RyeNOnwlY8/5AE/vtPQemy73e+8+JNTK9ptRzXaV5pUkazZZmRCDBelx08znOnAvBQEG6M9wtomf0qI9fMi3FQvVg+AZ81rl6gDqoYkgjAlxDgzKTVm7JV1XUwZq+4PM7BN++XPbKEGgH8Zo7Lo00M6iIuDvAgMBAAECggEAbcfTNP0mogBog/ydG+TDn5tLUHHvjCzULuwiBUSqAynqlhwdRL7WGtUOiFZhho/tzzOy3A5DbGVdPC8++N8muJG7o0+n6LG+3iXuHmu9Gfke5YiC5++RIt2wkvvc8Xuve0PcDMeZoC5a6J7iTrrD01rnYgqAKlE+OytSXPcm/N9rhT/ZtFAloThun79pn435q2KfDSR9I1mLxT8fpTJF25UETlIm7N2Q1PBHGzWbEy3KXlDPckb9m6aX9f5dbatCDzP8tlGktHdlDqDK+YtbeW+m07XJc42L6E6+NPJR4CXqKj30lxTXMsf3j19iKRQHObMu81iqlQNw2M35B1SGQQKBgQDVOI3LCPyCQxcuu6zBga9sY8Z5+7+Z+Ft8f/Gmb9BXy8rTEbKpxhau0PDfyEe+UpcHE8NLtIKCYFpygQr0mdT3gI74oHJU8zjfKu5Q62CJGbqbv3pqYy1rkoh/nVoY2uHVx8nvsMGYb2kOb8Iw43t5q4MLWOzkm6w9SavWqSKujwKBgQCfgig5b8xZCab6FlKFLHXdJT9xV4BbKt4hSws2HWw5PVPWmwYFafqDQFkRk0Xd/R/DcY++7u1Zi/mBXJUwBri24Pmy9MRID7MrJXV7YnFotIGGZN1gNSOrxbwSEMaIEE2dVbSUL7cqUds8g2qJRDHMJ3mRVJVMIHgMDSl4PjfXoQKBgC9ZwdNnCXx8HbfZIcJPZH+zoK4SRDiTOO3rE+UcS65TqZU6Bu1A+mSULgnkVlpjyPpfXy1+xsThVPS8x4wOZYcZW6bHKtzGrsiQph/+RY/s0M4ptavOnpWXEw0UCqAea6yoE6ZRII0ToMb99qgYvKEGf7GTa/hQwe1SYQDoyrTtAoGAQguinCWudy+H9AVKJdpC80549nVEsHsPfrmT6tRD+29pR3LpC1TWZYI/Xweb5k7o2AQtgOFtZaaPqQCsxTYDRbXhrjerDityBGt+OZiJXNFg0ETzZOKb4oP9W32WtsAJYxwZqUIYlW1PWb8QXcXe2Kl/rK1ZJCxNiK/qe99HQAECgYEAwqS7IaYp3HmtYgX2eL5jgaHfHLZDkxrBuVtMQqJ0aoQRSODsJD7OE5YXCH3fTo/X2eu/C4gJHj1Kx9Zooj2ebLHV/8n/HgUd346my6+hYNuCepzgpr/DQ5IN5AmEgne76bXKF98TXrBszTvUkWDsUCP9TeQ1rNCmdo4uDFEl594=','private key for auth','2025-07-16 16:31:36.619','2025-07-16 16:31:36.619',0,'anonymousUser','anonymousUser');
/*!40000 ALTER TABLE `runtime_config` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `terminal_alert_info`
--

DROP TABLE IF EXISTS `terminal_alert_info`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `terminal_alert_info` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `alert_id` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '告警唯一ID（UUID）',
  `metric_id` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '指标id',
  `device_id` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '告警终端设备ID',
  `alert_type` varchar(30) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '告警类型：CPU_TEMPERATURE,MEMORY_USAGE,DISK_USAGE,DISK_DATA_USAGE,DISK_SYSTEM_USAGE,LICENSE_EXPIRY',
  `alert_details` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '告警详情',
  `metric_name` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '告警指标名称',
  `threshold` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '告警阈值',
  `current_value` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '当前值',
  `alert_time` datetime DEFAULT NULL COMMENT '告警时间',
  `alert_status` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT 'ACTIVE' COMMENT '告警状态：ACTIVE,RESOLVED,ACKNOWLEDGED',
  `resolved_time` datetime DEFAULT NULL COMMENT '告警解决时间',
  `acknowledged_time` datetime DEFAULT NULL COMMENT '告警确认时间',
  `acknowledged_by` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '告警确认人',
  `resolve_comment` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '解决备注',
  `notification_sent` tinyint(1) DEFAULT '0' COMMENT '是否已发送通知：0-未发送，1-已发送',
  `notification_time` datetime DEFAULT NULL COMMENT '通知发送时间',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_alert_id` (`alert_id`),
  KEY `idx_device_id` (`device_id`)
) ENGINE=InnoDB AUTO_INCREMENT=26 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='终端告警信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `terminal_alert_info`
--

LOCK TABLES `terminal_alert_info` WRITE;
/*!40000 ALTER TABLE `terminal_alert_info` DISABLE KEYS */;
INSERT INTO `terminal_alert_info` VALUES (1,'3009f888-6b5c-4635-b452-7e84f5c6138b',NULL,'_0fd94938951f4a64bb11c6817a81f7e7','CPU_TEMPERATURE','CPU温度过高','cpu_temp','85°C','85.888°C','2025-07-15 11:38:24','RESOLVED','2025-07-15 11:38:25',NULL,NULL,'系统自动解决：指标已恢复正常',1,'2025-07-15 11:38:25','2025-07-15 11:38:25','2025-07-15 03:38:25'),(2,'d77fddd0-cf56-4a9a-ad07-9c140c9ea061',NULL,'_0fd94938951f4a64bb11c6817a81f7e7','CPU_TEMPERATURE','CPU温度过高','cpu_temp','85°C','85.888°C','2025-07-15 14:40:06','RESOLVED','2025-07-15 14:40:09',NULL,NULL,'系统自动解决：指标已恢复正常',1,'2025-07-15 14:40:09','2025-07-15 14:40:09','2025-07-15 06:40:09'),(3,'68a87514-b561-449d-8ee7-b2a3b44c5fe1',NULL,'_0fd94938951f4a64bb11c6817a81f7e7','MEMORY_USAGE','内存使用率过高','memory_percent','90%','92.0%','2025-07-15 14:40:06','RESOLVED','2025-07-15 14:40:09',NULL,NULL,'系统自动解决：指标已恢复正常',1,'2025-07-15 14:40:09','2025-07-15 14:40:09','2025-07-15 06:40:09'),(4,'03f5c4a6-9447-42bd-8e78-6fbd1a4363f9',NULL,'_0fd94938951f4a64bb11c6817a81f7e7','CPU_TEMPERATURE','CPU温度过高','cpu_temp','85°C','86.888°C','2025-07-17 11:05:19','RESOLVED','2025-07-17 11:05:21',NULL,NULL,'系统自动解决：指标已恢复正常',1,'2025-07-17 11:05:21','2025-07-17 11:05:21','2025-07-17 03:05:20'),(5,'b3580d30-2c9e-4c99-9a33-ebf188e0dcb3',NULL,'_0fd94938951f4a64bb11c6817a81f7e7','CPU_TEMPERATURE','CPU温度过高','cpu_temp','85°C','86.888°C','2025-07-17 11:08:48','RESOLVED','2025-07-17 10:20:09',NULL,NULL,'系统自动解决：指标已恢复正常',0,NULL,'2025-07-17 11:08:49','2025-07-17 10:20:08'),(6,'2463d5d9-db3f-4452-8f3f-c8b75d562d55',NULL,'_0fd94938951f4a64bb11c6817a81f7e7','CPU_TEMPERATURE','CPU温度过高','cpu_temp','85°C','86.888°C','2025-07-17 11:33:40','RESOLVED','2025-07-17 10:20:09',NULL,NULL,'系统自动解决：指标已恢复正常',0,NULL,'2025-07-17 11:33:42','2025-07-17 10:20:08'),(7,'467defc7-dd7e-42ba-8af7-c87eba865a32',NULL,'_0fd94938951f4a64bb11c6817a81f7e7','CPU_TEMPERATURE','CPU温度过高','cpu_temp','85°C','86.888°C','2025-07-17 11:35:07','RESOLVED','2025-07-17 10:20:09',NULL,NULL,'系统自动解决：指标已恢复正常',0,NULL,'2025-07-17 11:35:08','2025-07-17 10:20:08'),(8,'0af09063-5e2b-4b62-86cb-aa5624d7707e',NULL,'_0fd94938951f4a64bb11c6817a81f7e7','CPU_TEMPERATURE','CPU温度过高','cpu_temp','85°C','86.888°C','2025-07-17 11:52:59','RESOLVED','2025-07-17 10:20:09',NULL,NULL,'系统自动解决：指标已恢复正常',0,NULL,'2025-07-17 11:53:00','2025-07-17 10:20:08'),(9,'58a2f38b-07ab-4d20-8c61-c06e8cd33366',NULL,'_0fd94938951f4a64bb11c6817a81f7e7','CPU_TEMPERATURE','CPU温度过高','cpu_temp','85°C','89.888°C','2025-07-17 15:50:15','RESOLVED','2025-07-17 10:20:09',NULL,NULL,'系统自动解决：指标已恢复正常',0,NULL,'2025-07-17 15:50:16','2025-07-17 10:20:08'),(10,'6f3ce80f-2150-47d4-85bd-e35a83c9b9ce',NULL,'_0fd94938951f4a64bb11c6817a81f7e7','CPU_TEMPERATURE','CPU温度过高','cpu_temp','85°C','89.888°C','2025-07-17 16:35:13','RESOLVED','2025-07-17 10:20:09',NULL,NULL,'系统自动解决：指标已恢复正常',0,NULL,'2025-07-17 16:35:15','2025-07-17 10:20:08'),(11,'87ffbbbb-b597-483a-bcde-cee880c3c24c',NULL,'_0fd94938951f4a64bb11c6817a81f7e7','CPU_TEMPERATURE','CPU温度过高','cpu_temp','85°C','89.888°C','2025-07-17 16:43:21','RESOLVED','2025-07-17 10:20:09',NULL,NULL,'系统自动解决：指标已恢复正常',0,NULL,'2025-07-17 16:43:21','2025-07-17 10:20:08'),(12,'2bedbdfb-9825-405b-ad96-b844fcaabe84',NULL,'_0fd94938951f4a64bb11c6817a81f7e7','CPU_TEMPERATURE','CPU温度过高','cpu_temp','85°C','89.888°C','2025-07-17 16:55:35','RESOLVED','2025-07-17 10:20:09',NULL,NULL,'系统自动解决：指标已恢复正常',0,NULL,'2025-07-17 16:55:36','2025-07-17 10:20:08'),(13,'edb0489e-93ee-4c40-a2a9-59e9f3c87201',NULL,'_0fd94938951f4a64bb11c6817a81f7e7','CPU_TEMPERATURE','CPU温度过高','cpu_temp','85°C','89.888°C','2025-07-17 18:20:07','RESOLVED','2025-07-17 10:20:09',NULL,NULL,'系统自动解决：指标已恢复正常',1,'2025-07-17 10:20:09','2025-07-17 10:20:09','2025-07-17 10:20:08'),(14,'ce532f58-4078-4cb7-aab4-667298fb615d',NULL,'_0fd94938951f4a64bb11c6817a81f7e7','CPU_TEMPERATURE','CPU温度过高','cpu_temp','85°C','89.888°C','2025-07-17 18:22:47','RESOLVED','2025-07-17 10:22:48',NULL,NULL,'系统自动解决：指标已恢复正常',1,'2025-07-17 10:22:48','2025-07-17 10:22:48','2025-07-17 10:22:47'),(15,'ef27e572-1056-4fb4-b862-160470b568ac','_0fd94938951f4a64bb11c6817a81f7e7_20250717T204557','_0fd94938951f4a64bb11c6817a81f7e7','CPU_TEMPERATURE','CPU温度过高','cpu_temp','85°C','89.888°C','2025-07-17 20:45:57','RESOLVED','2025-07-17 13:45:02',NULL,NULL,'系统自动解决：指标已恢复正常',0,NULL,'2025-07-17 20:45:58','2025-07-17 13:45:01'),(16,'1a79b5de-b79a-4897-a21c-8cf4ac928276',NULL,'_0fd94938951f4a64bb11c6817a81f7e7','CPU_TEMPERATURE','CPU温度过高','cpu_temp','85°C','89.888°C','2025-07-17 21:45:00','RESOLVED','2025-07-17 13:45:02',NULL,NULL,'系统自动解决：指标已恢复正常',1,'2025-07-17 13:45:02','2025-07-17 13:45:02','2025-07-17 13:45:01'),(17,'7cfa4848-3a33-4d1b-8fef-046ca1a6fb09','_0fd94938951f4a64bb11c6817a81f7e7_20250717T214756','_0fd94938951f4a64bb11c6817a81f7e7','CPU_TEMPERATURE','CPU温度过高','cpu_temp','85°C','88.888°C','2025-07-17 21:47:56','ACTIVE',NULL,NULL,NULL,NULL,0,NULL,'2025-07-17 21:47:57','2025-07-17 21:47:57'),(18,'3b036617-2bd3-4609-a537-2d5c27adff6f','_0fd94938951f4a64bb11c6817a81f7e7_20250717T215004','_0fd94938951f4a64bb11c6817a81f7e7','CPU_TEMPERATURE','CPU温度过高','cpu_temp','85°C','88.888°C','2025-07-17 21:50:04','ACTIVE',NULL,NULL,NULL,NULL,0,NULL,'2025-07-17 21:50:05','2025-07-17 21:50:05'),(19,'304b3c13-386d-4735-8221-92f0d94744c2','_0fd94938951f4a64bb11c6817a81f7e7_20250718T091447','_0fd94938951f4a64bb11c6817a81f7e7','MEMORY_USAGE','内存使用率过高','memory_percent','90%','91.9%','2025-07-18 09:14:47','RESOLVED','2025-07-18 09:45:28','2025-07-18 09:45:17','哈哈','哈哈哈哈',0,NULL,'2025-07-18 09:14:48','2025-07-18 09:45:28'),(20,'bff077c5-f3f4-4a3a-afa3-9ff9a32d4c26','_0fd94938951f4a64bb11c6817a81f7e7_20250718T091539','_0fd94938951f4a64bb11c6817a81f7e7','MEMORY_USAGE','内存使用率过高','memory_percent','90%','91.9%','2025-07-18 09:15:39','ACTIVE',NULL,NULL,NULL,NULL,0,NULL,'2025-07-18 09:15:39','2025-07-18 09:15:39'),(21,'15639a42-e71a-4f55-85c8-223cdbb93f1e','_0fd94938951f4a64bb11c6817a81f7e7_20250718T091539','_0fd94938951f4a64bb11c6817a81f7e7','DISK_USAGE','磁盘使用率过高 - /dev/mmcblk0p3 (/)','disk_usage.percent','80%','81.3%','2025-07-18 09:15:39','RESOLVED','2025-07-18 09:41:44','2025-07-18 09:41:31','haha','无',1,'2025-07-18 09:31:52','2025-07-18 09:15:39','2025-07-18 09:41:44'),(22,'f339a5ff-e75b-450e-89b9-5cc3b046b748','_0fd94938951f4a64bb11c6817a81f7e7_20250718T111530','_0fd94938951f4a64bb11c6817a81f7e7','MEMORY_USAGE','内存使用率过高','memory_percent','90%','91.9%','2025-07-18 11:15:30','ACTIVE',NULL,NULL,NULL,NULL,0,NULL,'2025-07-18 11:15:31','2025-07-18 11:15:31'),(23,'5d735feb-6376-4ad3-ab7a-2700ac71bd35','_0fd94938951f4a64bb11c6817a81f7e7_20250718T111618','_0fd94938951f4a64bb11c6817a81f7e7','MEMORY_USAGE','内存使用率过高','memory_percent','90%','91.9%','2025-07-18 11:16:18','ACTIVE',NULL,NULL,NULL,NULL,0,NULL,'2025-07-18 11:16:19','2025-07-18 11:16:19'),(24,'1889affb-c3b6-4bd4-9cdc-f383ea0ee5d0','_0fd94938951f4a64bb11c6817a81f7e7_20250718T111631','_0fd94938951f4a64bb11c6817a81f7e7','CPU_TEMPERATURE','CPU温度过高','cpu_temp','85°C','88.888°C','2025-07-18 11:16:31','ACTIVE',NULL,NULL,NULL,NULL,0,NULL,'2025-07-18 11:16:32','2025-07-18 11:16:32'),(25,'3658aede-0585-429d-92fc-42ba04d8b124','_0fd94938951f4a64bb11c6817a81f7e7_20250718T111631','_0fd94938951f4a64bb11c6817a81f7e7','MEMORY_USAGE','内存使用率过高','memory_percent','90%','91.9%','2025-07-18 11:16:31','ACTIVE',NULL,NULL,NULL,NULL,0,NULL,'2025-07-18 11:16:32','2025-07-18 11:16:32');
/*!40000 ALTER TABLE `terminal_alert_info` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `terminal_basic_info`
--

DROP TABLE IF EXISTS `terminal_basic_info`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `terminal_basic_info` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `device_id` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '设备唯一标识符',
  `hostname` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '主机名',
  `identity_mac` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '对接长安车辆所在网口的MAC地址',
  `app_version` json DEFAULT NULL COMMENT '当前运行的组件版本号(JSON格式)',
  `expired_date` varchar(30) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '软件授权过期时间',
  `receive_time` datetime DEFAULT NULL COMMENT '数据接收时间',
  `data_source` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT 'kafka' COMMENT '数据来源',
  `status` tinyint(1) DEFAULT '1' COMMENT '设备状态：0-离线，1-在线',
  `first_register_time` datetime DEFAULT NULL COMMENT '首次注册时间',
  `last_update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  `tags` json DEFAULT NULL COMMENT '终端标签信息(JSON格式)，用于标签过滤',
  `custom_fields` json DEFAULT NULL COMMENT '自定义字段信息(JSON格式)，用于动态扩展字段',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除：0-未删除，1-已删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_device_id` (`device_id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='终端基本信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `terminal_basic_info`
--

LOCK TABLES `terminal_basic_info` WRITE;
/*!40000 ALTER TABLE `terminal_basic_info` DISABLE KEYS */;
INSERT INTO `terminal_basic_info` VALUES (1,'_0fd94938951f4a64bb11c6817a81f7e7','ec_3568_25030031','de0765523e60','{\"web\": \"1.0.55\", \"agent\": \"1.0.0\", \"front\": \"1.0.40\", \"detector\": \"1.0.48\", \"controller\": \"1.0.1\"}','2035-03-09 22:47:20','2025-07-18 07:49:46','kafka',0,'2025-07-15 11:37:37','2025-07-18 07:50:23','{\"四川\": \"02\"}','{\"else\": \"haha\", \"other_info\": \"\\\"\\\"\"}','2025-07-15 11:37:37','2025-07-18 07:50:23',0),(2,'sse_test_device_1752586446226','sse_test_terminal',NULL,'{\"web\": \"1.0.61\", \"detector\": \"1.0.54\", \"controller\": \"1.0.7\"}','2025-10-31 23:59:59','2025-07-15 21:34:06','kafka',0,'2025-07-15 21:34:08','2025-07-15 22:32:50',NULL,NULL,'2025-07-15 21:34:08','2025-07-15 14:32:51',0);
/*!40000 ALTER TABLE `terminal_basic_info` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `terminal_metric_info`
--

DROP TABLE IF EXISTS `terminal_metric_info`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `terminal_metric_info` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `device_id` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '设备唯一标识符',
  `uptime` decimal(10,2) DEFAULT NULL COMMENT '系统运行时长（单位：秒）',
  `cpu_temp` decimal(5,2) DEFAULT NULL COMMENT 'CPU温度（单位：摄氏度）',
  `temperatures` json DEFAULT NULL COMMENT '温度信息(JSON格式)',
  `cpu_usage` json DEFAULT NULL COMMENT 'CPU使用率信息(JSON格式)',
  `cpu_percent` decimal(5,2) DEFAULT NULL COMMENT 'CPU总使用率（%）',
  `memory_percent` decimal(5,2) DEFAULT NULL COMMENT '内存使用率（%）',
  `memory_usage` json DEFAULT NULL COMMENT '内存详细信息(JSON格式)',
  `disk_usage` json DEFAULT NULL COMMENT '磁盘使用情况数组(JSON格式)',
  `disk_data_percent` decimal(5,2) DEFAULT NULL COMMENT '采集数据磁盘分区使用率',
  `disk_system_percent` decimal(5,2) DEFAULT NULL COMMENT '系统盘使用率',
  `cdata` json DEFAULT NULL COMMENT '长安程序落盘文件信息(JSON格式)',
  `zdata` json DEFAULT NULL COMMENT '压缩文件信息(JSON格式)',
  `group_usage` json DEFAULT NULL COMMENT 'CPE集合信息(JSON格式)',
  `group_bps` bigint DEFAULT NULL COMMENT '所有CPE的带宽占用（单位：Byte/s）',
  `metric_time` datetime DEFAULT NULL COMMENT '指标采集时间',
  `receive_time` datetime DEFAULT NULL COMMENT '数据接收时间',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_device_id` (`device_id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='终端指标信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `terminal_metric_info`
--

LOCK TABLES `terminal_metric_info` WRITE;
/*!40000 ALTER TABLE `terminal_metric_info` DISABLE KEYS */;
INSERT INTO `terminal_metric_info` VALUES (1,'_0fd94938951f4a64bb11c6817a81f7e7',NULL,NULL,'{\"gpu-thermal\": 57.777, \"soc-thermal\": 58.888}','{\"num\": \"4\", \"sys\": \"9.7\", \"core\": \"4\", \"idle\": \"43.7\", \"user\": \"46.3\", \"thread\": \"4\"}',NULL,NULL,'{\"free\": 1373265920, \"slab\": 136617984, \"used\": 2024525824, \"total\": 4085002240, \"active\": 229298176, \"cached\": 630214656, \"shared\": 1032192, \"buffers\": 56995840, \"percent\": 50.9, \"inactive\": 2192863232, \"available\": 2006622208}','[{\"free\": 13409980416, \"used\": 15916904448, \"total\": 30602608640, \"device\": \"/dev/mmcblk0p3\", \"fstype\": \"ext4\", \"percent\": 54.3, \"mountpoint\": \"/\"}]',NULL,NULL,'{\"size\": 3627078580, \"count\": 1}','{\"size\": 0, \"count\": 0}','{}',0,'2025-07-15 11:37:36','2025-07-15 11:37:36','2025-07-15 11:37:37','2025-07-15 11:37:37'),(2,'_0fd94938951f4a64bb11c6817a81f7e7',NULL,NULL,'{\"gpu-thermal\": 57.777, \"soc-thermal\": 58.888}','{\"num\": \"4\", \"sys\": \"9.7\", \"core\": \"4\", \"idle\": \"43.7\", \"user\": \"46.3\", \"thread\": \"4\"}',NULL,NULL,'{\"free\": 1373265920, \"slab\": 136617984, \"used\": 2024525824, \"total\": 4085002240, \"active\": 229298176, \"cached\": 630214656, \"shared\": 1032192, \"buffers\": 56995840, \"percent\": 50.9, \"inactive\": 2192863232, \"available\": 2006622208}','[{\"free\": 13409980416, \"used\": 15916904448, \"total\": 30602608640, \"device\": \"/dev/mmcblk0p3\", \"fstype\": \"ext4\", \"percent\": 54.3, \"mountpoint\": \"/\"}]',NULL,NULL,'{\"size\": 3627078580, \"count\": 1}','{\"size\": 0, \"count\": 0}','{}',0,'2025-07-15 11:38:24','2025-07-15 11:38:24','2025-07-15 11:38:25','2025-07-15 11:38:25');
/*!40000 ALTER TABLE `terminal_metric_info` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `user`
--

DROP TABLE IF EXISTS `user`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `created_at` datetime(3) DEFAULT NULL,
  `updated_at` datetime(3) DEFAULT NULL,
  `deleted_at` bigint unsigned DEFAULT '0',
  `username` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `name` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci,
  `password` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci,
  `account_source` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `enable` tinyint(1) DEFAULT NULL,
  `locked` tinyint(1) DEFAULT NULL,
  `account_expire_date` datetime(3) DEFAULT NULL,
  `credential_expire_date` datetime(3) DEFAULT NULL,
  `created_by` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `updated_by` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `idx_username_account_source_del` (`deleted_at`,`username`,`account_source`) USING BTREE,
  UNIQUE KEY `idx_user_username` (`username`) USING BTREE,
  KEY `idx_users_created_by` (`created_by`) USING BTREE,
  KEY `idx_users_updated_by` (`updated_by`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `user`
--

LOCK TABLES `user` WRITE;
/*!40000 ALTER TABLE `user` DISABLE KEYS */;
INSERT INTO `user` VALUES (1,'2025-07-16 07:20:32.000','2025-07-16 07:20:32.000',0,'admin','系统管理员','$2a$10$4PV7umGBzurO1vdMIG4qM.l6MzdkkCLtoUVcxN9CQ7IkO3hP4wDlq','INTERNAL',1,0,'2099-12-31 23:59:59.000','2099-12-31 23:59:59.000','system',NULL),(2,'2025-07-16 07:20:32.000','2025-07-16 07:20:32.000',0,'user','普通用户','$2a$10$4PV7umGBzurO1vdMIG4qM.l6MzdkkCLtoUVcxN9CQ7IkO3hP4wDlq','INTERNAL',1,0,'2099-12-31 23:59:59.000','2099-12-31 23:59:59.000','system',NULL),(3,'2025-07-16 16:31:36.691','2025-07-16 16:31:36.691',0,'msysu99','msysu99','$2a$10$/nS25r6ttkfPOIg4xmqbtO8gCixUKu/gTXevlwFAh5eUHw9twnL12','internal',0,0,'2099-12-30 16:31:36.691','2099-12-30 16:31:36.691','anonymousUser','anonymousUser'),(5,'2025-07-18 11:08:24.784','2025-07-18 11:08:24.784',0,'ayou','ayou','$2a$10$a/VAHtEZNcY6uTVcIg6j5.3OINQBkXMsI8HvtDY4k9.LlrSI0Akia','internal',1,0,'2099-12-30 11:08:24.732','2099-12-30 11:08:24.732','admin','admin'),(6,'2025-07-18 14:45:08.378','2025-07-18 14:45:08.378',0,'test','','$2a$10$mv3XfUhhSgdUG8yDV4HDAOyKXQeRxYQxNeJ13vELztnFwMU3UisSi','internal',1,0,'2099-12-30 06:45:08.259','2099-12-30 06:45:08.259','admin','admin');
/*!40000 ALTER TABLE `user` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `user_role`
--

DROP TABLE IF EXISTS `user_role`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_role` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `role_name` varchar(50) NOT NULL COMMENT '角色名称',
  `created_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted_at` bigint DEFAULT '0' COMMENT '删除时间戳',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_role` (`user_id`,`role_name`,`deleted_at`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_role_name` (`role_name`),
  KEY `idx_deleted_at` (`deleted_at`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='用户角色关系表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `user_role`
--

LOCK TABLES `user_role` WRITE;
/*!40000 ALTER TABLE `user_role` DISABLE KEYS */;
INSERT INTO `user_role` VALUES (1,1,'admin','system','2025-07-16 07:20:33',NULL,'2025-07-16 07:20:33',0),(2,2,'user','system','2025-07-16 07:20:33',NULL,'2025-07-16 07:20:33',0),(3,4,'admin','admin','2025-07-17 09:57:25','admin','2025-07-17 09:57:25',0),(4,5,'admin','admin','2025-07-18 11:08:25','admin','2025-07-18 11:08:25',0),(5,6,'user','admin','2025-07-18 14:45:08','admin','2025-07-18 14:45:08',0);
/*!40000 ALTER TABLE `user_role` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-07-23  8:04:10
