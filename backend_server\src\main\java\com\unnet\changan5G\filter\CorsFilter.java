package com.unnet.changan5G.filter;

import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * CORS过滤器 - 确保在所有其他过滤器之前处理跨域请求
 * 
 * <AUTHOR>
 * @date 2024-07-14
 */
@Component
@Order(0) // 最高优先级，在所有其他过滤器之前执行
@Slf4j
public class CorsFilter implements Filter {

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {
        
        HttpServletRequest httpRequest = (HttpServletRequest) request;
        HttpServletResponse httpResponse = (HttpServletResponse) response;
        
        // 设置CORS响应头
        httpResponse.setHeader("Access-Control-Allow-Origin", "*");
        httpResponse.setHeader("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS, HEAD, PATCH");
        httpResponse.setHeader("Access-Control-Allow-Headers", "*");
        httpResponse.setHeader("Access-Control-Expose-Headers", "*");
        httpResponse.setHeader("Access-Control-Allow-Credentials", "true");
        httpResponse.setHeader("Access-Control-Max-Age", "86400");
        
        String method = httpRequest.getMethod();
        String origin = httpRequest.getHeader("Origin");
        
        log.debug("CORS处理 - 方法: {}, 来源: {}, URI: {}", method, origin, httpRequest.getRequestURI());
        
        // 对于OPTIONS预检请求，直接返回200状态码
        if ("OPTIONS".equalsIgnoreCase(method)) {
            httpResponse.setStatus(HttpServletResponse.SC_OK);
            log.debug("处理OPTIONS预检请求，直接返回200");
            return;
        }
        
        // 继续执行后续过滤器
        chain.doFilter(request, response);
    }
}
