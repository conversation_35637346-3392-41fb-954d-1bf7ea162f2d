package com.unnet.changan5G.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * JSON字段提取服务
 * 
 * <AUTHOR>
 * @since 2024-07-25
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class JsonFieldExtractorService {

    private final ObjectMapper objectMapper;

    /**
     * 从JSON字符串中提取指定路径的数值列表
     */
    public List<BigDecimal> extractValues(String jsonString, String fieldPath) {
        List<BigDecimal> results = new ArrayList<>();
        
        try {
            JsonNode rootNode = objectMapper.readTree(jsonString);
            extractValuesFromNode(rootNode, fieldPath, results);
        } catch (Exception e) {
            log.error("解析JSON失败 - 路径: {}, 错误: {}", fieldPath, e.getMessage());
        }
        
        return results;
    }

    /**
     * 从JSON字符串中提取指定路径的字符串值
     */
    public String extractStringValue(String jsonString, String fieldPath) {
        try {
            JsonNode rootNode = objectMapper.readTree(jsonString);
            JsonNode valueNode = getNodeByPath(rootNode, fieldPath);
            return valueNode != null ? valueNode.asText() : null;
        } catch (Exception e) {
            log.error("提取字符串值失败 - 路径: {}, 错误: {}", fieldPath, e.getMessage());
            return null;
        }
    }

    /**
     * 根据路径获取JSON节点
     */
    private JsonNode getNodeByPath(JsonNode node, String fieldPath) {
        if (node == null || fieldPath == null || fieldPath.trim().isEmpty()) {
            return null;
        }

        String[] pathParts = fieldPath.split("\\.", 2);
        String currentPart = pathParts[0];
        String remainingPath = pathParts.length > 1 ? pathParts[1] : null;

        // 处理数组索引
        if (currentPart.contains("[")) {
            return handleArrayPathForSingleValue(node, currentPart, remainingPath);
        } else {
            // 处理普通字段
            JsonNode childNode = node.get(currentPart);
            if (childNode != null && remainingPath != null) {
                return getNodeByPath(childNode, remainingPath);
            }
            return childNode;
        }
    }

    /**
     * 处理数组路径（单值）
     */
    private JsonNode handleArrayPathForSingleValue(JsonNode node, String arrayPart, String remainingPath) {
        int bracketStart = arrayPart.indexOf('[');
        int bracketEnd = arrayPart.indexOf(']');
        
        if (bracketStart == -1 || bracketEnd == -1) {
            return null;
        }

        String arrayFieldName = arrayPart.substring(0, bracketStart);
        String indexPart = arrayPart.substring(bracketStart + 1, bracketEnd);

        JsonNode arrayNode = node.get(arrayFieldName);
        if (arrayNode == null || !arrayNode.isArray()) {
            return null;
        }

        if ("*".equals(indexPart)) {
            // 返回第一个元素
            if (arrayNode.size() > 0) {
                JsonNode element = arrayNode.get(0);
                return remainingPath != null ? getNodeByPath(element, remainingPath) : element;
            }
        } else {
            try {
                int index = Integer.parseInt(indexPart);
                if (index >= 0 && index < arrayNode.size()) {
                    JsonNode element = arrayNode.get(index);
                    return remainingPath != null ? getNodeByPath(element, remainingPath) : element;
                }
            } catch (NumberFormatException e) {
                log.warn("无效的数组索引: {}", indexPart);
            }
        }
        
        return null;
    }

    /**
     * 递归提取JSON节点中的值
     */
    private void extractValuesFromNode(JsonNode node, String fieldPath, List<BigDecimal> results) {
        if (node == null || fieldPath == null || fieldPath.trim().isEmpty()) {
            return;
        }

        String[] pathParts = fieldPath.split("\\.", 2);
        String currentPart = pathParts[0];
        String remainingPath = pathParts.length > 1 ? pathParts[1] : null;

        // 处理数组索引
        if (currentPart.contains("[")) {
            handleArrayPath(node, currentPart, remainingPath, results);
        } else {
            // 处理普通字段
            JsonNode childNode = node.get(currentPart);
            if (childNode != null) {
                if (remainingPath != null) {
                    // 继续递归
                    extractValuesFromNode(childNode, remainingPath, results);
                } else {
                    // 到达叶子节点，提取值
                    BigDecimal value = extractNumericValue(childNode);
                    if (value != null) {
                        results.add(value);
                    }
                }
            }
        }
    }

    /**
     * 处理数组路径
     */
    private void handleArrayPath(JsonNode node, String arrayPart, String remainingPath, List<BigDecimal> results) {
        int bracketStart = arrayPart.indexOf('[');
        int bracketEnd = arrayPart.indexOf(']');
        
        if (bracketStart == -1 || bracketEnd == -1) {
            log.warn("无效的数组路径格式: {}", arrayPart);
            return;
        }

        String arrayFieldName = arrayPart.substring(0, bracketStart);
        String indexPart = arrayPart.substring(bracketStart + 1, bracketEnd);

        JsonNode arrayNode = node.get(arrayFieldName);
        if (arrayNode == null || !arrayNode.isArray()) {
            log.debug("字段不是数组或不存在: {}", arrayFieldName);
            return;
        }

        if ("*".equals(indexPart)) {
            // 处理所有数组元素
            for (JsonNode element : arrayNode) {
                if (remainingPath != null) {
                    extractValuesFromNode(element, remainingPath, results);
                } else {
                    BigDecimal value = extractNumericValue(element);
                    if (value != null) {
                        results.add(value);
                    }
                }
            }
        } else {
            // 处理特定索引
            try {
                int index = Integer.parseInt(indexPart);
                if (index >= 0 && index < arrayNode.size()) {
                    JsonNode element = arrayNode.get(index);
                    if (remainingPath != null) {
                        extractValuesFromNode(element, remainingPath, results);
                    } else {
                        BigDecimal value = extractNumericValue(element);
                        if (value != null) {
                            results.add(value);
                        }
                    }
                }
            } catch (NumberFormatException e) {
                log.warn("无效的数组索引: {}", indexPart);
            }
        }
    }

    /**
     * 从JSON节点提取数值
     */
    private BigDecimal extractNumericValue(JsonNode node) {
        if (node == null) {
            return null;
        }

        if (node.isNumber()) {
            return BigDecimal.valueOf(node.asDouble());
        }

        if (node.isTextual()) {
            String textValue = node.asText();
            try {
                return new BigDecimal(textValue);
            } catch (NumberFormatException e) {
                // 检查是否是日期时间格式
                if (textValue.matches("\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}")) {
                    log.debug("检测到日期时间格式，跳过数值转换: {}", textValue);
                } else {
                    log.debug("无法将文本转换为数值: {}", textValue);
                }
            }
        }

        return null;
    }
}
