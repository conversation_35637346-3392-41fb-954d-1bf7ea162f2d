package com.unnet.jmanul.system.controller;

import com.unnet.jmanul.common.utils.Dict;
import com.unnet.jmanul.common.utils.jwt.JwtComponent;
import com.unnet.jmanul.system.entity.User;
import com.unnet.jmanul.system.service.IUserService;
import com.unnet.jmanul.system.service.dto.account.UserNamePutReq;
import com.unnet.jmanul.system.service.dto.account.UserPasswordPutReq;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Map;
import java.util.Optional;

import static com.unnet.jmanul.common.rest.ApiDef.V1_ACCOUNT;

@Slf4j
@RequestMapping(V1_ACCOUNT)
@RestController
@RequiredArgsConstructor
@Api(tags = {"Z. 账号信息 AccountController"})
public class AccountController {

    private final IUserService userService;
    private final JwtComponent jwtComponent;

    @GetMapping("/me")
    public ResponseEntity<Optional<User>> me() {
        var user = jwtComponent.getUsernameFromSecurityContext();
        Optional<User> byUsername = userService.getUserByUsername(user.get());
        byUsername.get().setPassword("");
        return user.map(s -> ResponseEntity.ok(byUsername)).orElseGet(() -> ResponseEntity.status(401).body(null));
    }

    @PutMapping("/password")
    public ResponseEntity<Boolean> password(@RequestBody UserPasswordPutReq req) {
        var user = jwtComponent.getUsernameFromSecurityContext();
        if (user.isEmpty()) {
            return ResponseEntity.status(401).body(null);
        }

        boolean ok = userService.putUserPassword(user.get(), req.getCurrentPassword(), req.getNewPassword());
        return ResponseEntity.ok(ok);
    }

    @PutMapping("/name")
    public ResponseEntity<Boolean> name(@RequestBody UserNamePutReq req) {
        var user = jwtComponent.getUsernameFromSecurityContext();
        if (user.isEmpty()) {
            return ResponseEntity.status(401).body(null);
        }

        boolean ok = userService.putUserName(user.get(), req.getName());
        return ResponseEntity.ok(ok);
    }

    @GetMapping("/isAuthorized")
    public ResponseEntity<Map<String, Object>> isAuthorized() {
        return ResponseEntity.ok(Dict.build().put("isAuthorized", true).getMap());
    }

}
