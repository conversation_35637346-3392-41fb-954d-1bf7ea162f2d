package com.unnet.jmanul.common.init;

import com.unnet.jmanul.business.entity.UserRole;
import com.unnet.jmanul.business.service.IUserRoleService;
import com.unnet.jmanul.system.entity.User;
import com.unnet.jmanul.system.service.IUserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.casbin.jcasbin.main.Enforcer;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

/**
 * 认证权限初始化器
 * 
 * <AUTHOR>
 * @date 2024-07-16
 */
@Component
@Slf4j
@RequiredArgsConstructor
@Order(200) // 在其他初始化器之后执行
public class AuthPermissionInitializer implements ApplicationRunner {

    private final Enforcer enforcer;
    private final IUserService userService;
    private final IUserRoleService userRoleService;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        log.info("开始初始化认证权限系统...");

        try {
            // 初始化用户角色关系
            initUserRoles();
            
            // 初始化权限规则
            initPermissions();
            
            log.info("认证权限系统初始化完成");

        } catch (Exception e) {
            log.error("认证权限系统初始化失败: {}", e.getMessage(), e);
            // 不抛出异常，避免影响应用启动
        }
    }

    /**
     * 初始化用户角色关系
     */
    private void initUserRoles() {
        try {
            // 检查admin用户
            Optional<User> adminUser = userService.getUserByUsername("admin");
            if (adminUser.isPresent()) {
                Long adminUserId = adminUser.get().getId();
                if (!userRoleService.hasRole(adminUserId, "admin")) {
                    userRoleService.assignRole(adminUserId, "admin");
                    log.info("为admin用户分配admin角色");
                }

                // 确保Casbin中有用户角色关系
                if (!enforcer.hasGroupingPolicy("admin", "admin")) {
                    enforcer.addGroupingPolicy("admin", "admin");
                    log.info("添加admin用户的Casbin角色关系");
                }
            }

            // 检查user用户
            Optional<User> normalUser = userService.getUserByUsername("user");
            if (normalUser.isPresent()) {
                Long userId = normalUser.get().getId();
                if (!userRoleService.hasRole(userId, "user")) {
                    userRoleService.assignRole(userId, "user");
                    log.info("为user用户分配user角色");
                }

                // 确保Casbin中有用户角色关系
                if (!enforcer.hasGroupingPolicy("user", "user")) {
                    enforcer.addGroupingPolicy("user", "user");
                    log.info("添加user用户的Casbin角色关系");
                }
            }

            // 同步所有现有用户的角色关系到Casbin
            syncAllUserRolesToCasbin();

        } catch (Exception e) {
            log.error("初始化用户角色关系失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 同步所有用户角色关系到Casbin
     */
    private void syncAllUserRolesToCasbin() {
        try {
            log.info("开始同步所有用户角色关系到Casbin...");

            // 获取所有用户角色关系
            List<UserRole> allUserRoles = userRoleService.list();

            for (UserRole userRole : allUserRoles) {
                try {
                    // 获取用户信息
                    User user = userService.getById(userRole.getUserId());
                    if (user != null) {
                        String username = user.getUsername();
                        String roleName = userRole.getRoleName();

                        // 检查Casbin中是否已存在该用户角色关系
                        if (!enforcer.hasGroupingPolicy(username, roleName)) {
                            // 添加用户角色关系到Casbin
                            boolean added = enforcer.addGroupingPolicy(username, roleName);
                            if (added) {
                                log.info("同步用户角色关系到Casbin - 用户: {}, 角色: {}", username, roleName);
                            }
                        }
                    }
                } catch (Exception e) {
                    log.error("同步单个用户角色关系失败 - 用户ID: {}, 角色: {}, 错误: {}",
                            userRole.getUserId(), userRole.getRoleName(), e.getMessage());
                }
            }

            // 保存策略到数据库
            enforcer.savePolicy();
            log.info("所有用户角色关系同步到Casbin完成，共处理 {} 条记录", allUserRoles.size());

        } catch (Exception e) {
            log.error("同步所有用户角色关系到Casbin失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 初始化权限规则
     */
    private void initPermissions() {
        try {
            // 检查是否已经有长安5G相关权限
            boolean hasTerminalPermission = enforcer.hasPolicy("admin", "/api/v1/admin/terminals*", "GET");
            
            if (!hasTerminalPermission) {
                log.info("添加长安5G权限规则...");
                
                // 管理员权限：拥有所有权限
                addAdminPermissions();
                
                // 普通用户权限：只有查看权限
                addUserPermissions();
                
                // 保存权限规则到数据库
                enforcer.savePolicy();
                
                log.info("长安5G权限规则添加完成");
            } else {
                log.info("长安5G权限规则已存在，跳过初始化");
            }

        } catch (Exception e) {
            log.error("初始化权限规则失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 添加管理员权限
     */
    private void addAdminPermissions() {
        // 长安5G终端管理权限
        enforcer.addPolicy("admin", "/api/v1/admin/terminals*", "GET");
        enforcer.addPolicy("admin", "/api/v1/admin/terminals*", "POST");
        enforcer.addPolicy("admin", "/api/v1/admin/terminals*", "PUT");
        enforcer.addPolicy("admin", "/api/v1/admin/terminals*", "DELETE");
        
        // 长安5G终端指标查询权限
        enforcer.addPolicy("admin", "/api/v1/admin/terminal/metrics*", "GET");
        enforcer.addPolicy("admin", "/api/v1/admin/terminal/metrics*", "POST");

        // 长安5G告警管理权限
        enforcer.addPolicy("admin", "/api/v1/admin/alerts*", "GET");
        enforcer.addPolicy("admin", "/api/v1/admin/alerts*", "POST");
        enforcer.addPolicy("admin", "/api/v1/admin/alerts*", "PUT");
        enforcer.addPolicy("admin", "/api/v1/admin/alerts*", "DELETE");

        // 长安5G健康检查权限
        enforcer.addPolicy("admin", "/api/v1/admin/changan5g*", "GET");
        
        // 用户管理权限
        enforcer.addPolicy("admin", "/api/v1/admin/users*", "GET");
        enforcer.addPolicy("admin", "/api/v1/admin/users*", "POST");
        enforcer.addPolicy("admin", "/api/v1/admin/users*", "PUT");
        enforcer.addPolicy("admin", "/api/v1/admin/users*", "DELETE");
        
        // 权限管理
        enforcer.addPolicy("admin", "/api/v1/admin/rbac*", "GET");
        enforcer.addPolicy("admin", "/api/v1/admin/rbac*", "POST");
        enforcer.addPolicy("admin", "/api/v1/admin/rbac*", "PUT");
        enforcer.addPolicy("admin", "/api/v1/admin/rbac*", "DELETE");

        // 权限管理控制器
        enforcer.addPolicy("admin", "/api/v1/admin/permissions*", "GET");
        enforcer.addPolicy("admin", "/api/v1/admin/permissions*", "POST");

        // 指标阈值配置管理权限
        enforcer.addPolicy("admin", "/api/v1/admin/metric-threshold-config*", "GET");
        enforcer.addPolicy("admin", "/api/v1/admin/metric-threshold-config*", "POST");
        enforcer.addPolicy("admin", "/api/v1/admin/metric-threshold-config*", "PUT");
        enforcer.addPolicy("admin", "/api/v1/admin/metric-threshold-config*", "DELETE");

        // 示例模块权限
        enforcer.addPolicy("admin", "/api/v1/admin/samples*", "GET");
        enforcer.addPolicy("admin", "/api/v1/admin/samples*", "POST");
        enforcer.addPolicy("admin", "/api/v1/admin/samples*", "PUT");
        enforcer.addPolicy("admin", "/api/v1/admin/samples*", "DELETE");
        
        // 账号管理权限
        enforcer.addPolicy("admin", "/api/v1/account*", "GET");
        enforcer.addPolicy("admin", "/api/v1/account*", "POST");
        enforcer.addPolicy("admin", "/api/v1/account*", "PUT");
        enforcer.addPolicy("admin", "/api/v1/account*", "DELETE");
        
        // Token刷新权限
        enforcer.addPolicy("admin", "/api/v1/auth/refreshToken", "GET");
    }

    /**
     * 添加普通用户权限
     */
    private void addUserPermissions() {
        // 长安5G终端查看权限
        enforcer.addPolicy("user", "/api/v1/admin/terminals*", "GET");
        
        // 长安5G终端指标查看权限
        enforcer.addPolicy("user", "/api/v1/admin/terminal/metrics*", "GET");

        // 长安5G告警查看权限
        enforcer.addPolicy("user", "/api/v1/admin/alerts*", "GET");

        // 长安5G健康检查权限
        enforcer.addPolicy("user", "/api/v1/admin/changan5g*", "GET");

        // 指标阈值配置查看权限
        enforcer.addPolicy("user", "/api/v1/admin/metric-threshold-config*", "GET");

        // 示例模块查看权限
        enforcer.addPolicy("user", "/api/v1/admin/samples*", "GET");
        
        // 用户查看权限（只能查看用户列表，不能增删改）
        enforcer.addPolicy("user", "/api/v1/admin/users*", "GET");
        
        // 权限查看权限
        enforcer.addPolicy("user", "/api/v1/admin/rbac*", "GET");
        
        // 账号管理权限（可以修改自己的信息）
        enforcer.addPolicy("user", "/api/v1/account*", "GET");
        enforcer.addPolicy("user", "/api/v1/account*", "PUT");
        
        // Token刷新权限
        enforcer.addPolicy("user", "/api/v1/auth/refreshToken", "GET");
    }
}
