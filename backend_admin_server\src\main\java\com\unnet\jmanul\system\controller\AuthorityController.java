package com.unnet.jmanul.system.controller;

import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.casbin.jcasbin.main.Enforcer;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.unnet.jmanul.common.rest.ApiDef.V1_ADMIN_RBAC;

@Slf4j
@RequestMapping(V1_ADMIN_RBAC)
@RestController
@RequiredArgsConstructor
@Api(tags = {"Z. 权限管理 AuthorityController"})
public class AuthorityController {

    private final Enforcer enforcer;

    @GetMapping("/subjects")
    public ResponseEntity<List<String>> getAllSubjects() {
        return ResponseEntity.ok(enforcer.getAllSubjects());
    }

    //
    // user & role
    //

    @GetMapping("/{username}/roles")
    public ResponseEntity<List<String>> getRolesForUser(@PathVariable String username) {
        return ResponseEntity.ok(enforcer.getRolesForUser(username));
    }

    @PostMapping("/{username}/roles")
    public ResponseEntity<Boolean> addRoleForUser(@PathVariable String username, @RequestParam String role) {
        return ResponseEntity.ok(enforcer.addRoleForUser(username, role));
    }

    @DeleteMapping("/{username}/roles")
    public ResponseEntity<Boolean> deleteRoleForUser(@PathVariable String username, @RequestParam String role) {
        return ResponseEntity.ok(enforcer.deleteRoleForUser(username, role));
    }

    @GetMapping("/{username}/{permissions}")
    public ResponseEntity<?> getUserPermissions(@PathVariable String username, @PathVariable String permissions) {
        // TODO: 2021/5/30
        return ResponseEntity.ok().build();
    }

    //
    // role & permission
    //

    @GetMapping("/roles")
    public ResponseEntity<List<String>> getAllRoles() {
        return ResponseEntity.ok(enforcer.getAllRoles());
    }

    @GetMapping("/roles/{role}/permissions")
    public ResponseEntity<?> getRolePermissions(@PathVariable String role) {
        return ResponseEntity.ok(enforcer.getPermissionsForUser(role));
    }

    @GetMapping("/roles/{role}/users")
    public ResponseEntity<List<String>> getRoleUsers(@PathVariable String role) {
        return ResponseEntity.ok(enforcer.getUsersForRole(role));
    }

    @PostMapping("/roles/{role}/permissions")
    public ResponseEntity<Boolean> addPermissionForRole(@PathVariable String role, @RequestParam String resource, @RequestParam String action) {
        return ResponseEntity.ok(enforcer.addPolicy(role, resource, action));
    }

    @DeleteMapping("/roles/{role}/permissions")
    public ResponseEntity<Boolean> deletePermissionForRole(@PathVariable String role, @RequestParam String resource, @RequestParam String action) {
        return ResponseEntity.ok(enforcer.removePolicy(role, resource, action));
    }

    // list objects(resources) e.g. /api/v1/admin/samples*
    @GetMapping("/objects")
    public ResponseEntity<List<String>> getAllObjects() {
        return ResponseEntity.ok(enforcer.getAllObjects());
    }

    @GetMapping("/actions")
    public ResponseEntity<List<String>> getAllActions() {
        return ResponseEntity.ok(enforcer.getAllActions());
    }

    @GetMapping("/policies")
    public ResponseEntity<List<List<String>>> GetPolicy() {
        return ResponseEntity.ok(enforcer.getPolicy());
    }

}
