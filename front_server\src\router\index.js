import { createRouter, createWebHistory } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useAuthStore } from '@/stores/auth'

const routes = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/Login.vue'),
    meta: {
      title: '用户登录',
      requiresAuth: false
    }
  },
  {
    path: '/',
    name: 'Layout',
    component: () => import('@/views/Layout.vue'),
    redirect: '/overview',
    meta: { requiresAuth: true },
    children: [
      {
        path: '/overview',
        name: 'Overview',
        component: () => import('@/views/Overview.vue'),
        meta: {
          title: '概览',
          requiresAuth: true
        }
      },
      {
        path: '/terminals',
        name: 'Terminals',
        component: () => import('@/views/Terminals.vue'),
        meta: {
          title: '终端管理',
          requiresAuth: true
        }
      },
      {
        path: '/alerts',
        name: 'Alerts',
        component: () => import('@/views/Alerts.vue'),
        meta: {
          title: '站内告警',
          requiresAuth: true
        }
      },
      {
        path: '/real-time-metrics/:identityMac',
        name: 'RealTimeMetrics',
        component: () => import('@/views/RealTimeMetrics.vue'),
        meta: {
          title: '实时指标',
          requiresAuth: true
        }
      },
      {
        path: '/history-metrics/:identityMac',
        name: 'HistoryMetrics',
        component: () => import('@/views/HistoryMetrics.vue'),
        meta: {
          title: '历史指标',
          requiresAuth: true
        }
      },
      {
        path: '/system',
        name: 'System',
        meta: {
          title: '系统管理',
          requiresAuth: true,
          requiresAdmin: true
        },
        children: [
          {
            path: '/system/user-management',
            name: 'UserManagement',
            component: () => import('@/views/UserManagement.vue'),
            meta: {
              title: '用户管理',
              requiresAuth: true,
              requiresAdmin: true
            }
          },
          {
            path: '/system/metric-config',
            name: 'MetricConfig',
            component: () => import('@/views/MetricConfig.vue'),
            meta: {
              title: '指标配置管理',
              requiresAuth: true,
              requiresAdmin: true
            }
          }
        ]
      }
    ]
  },
  {
    path: '/:pathMatch(.*)*',
    redirect: '/login'
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  const authStore = useAuthStore()

  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - 长安5G管理平台`
  }

  // 检查是否需要认证
  if (to.meta.requiresAuth !== false) {
    // 需要认证的页面
    if (!authStore.isLoggedIn) {
      // 未登录，跳转到登录页
      next('/login')
      return
    }

    // 检查是否需要管理员权限
    if (to.meta.requiresAdmin && !authStore.isAdmin) {
      // 需要管理员权限但用户不是管理员
      ElMessage.error('您没有权限访问该页面')
      next('/overview') // 跳转到概览页
      return
    }

    // 已登录且权限检查通过，继续访问
    next()
  } else {
    // 不需要认证的页面（如登录页）
    if (to.path === '/login' && authStore.isLoggedIn) {
      // 已登录用户访问登录页，跳转到首页
      next('/')
      return
    }

    next()
  }
})

export default router