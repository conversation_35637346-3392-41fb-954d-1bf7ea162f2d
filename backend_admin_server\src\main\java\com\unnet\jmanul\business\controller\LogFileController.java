package com.unnet.jmanul.business.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.unnet.jmanul.business.entity.LogFile;
import com.unnet.jmanul.business.service.LogFileService;
import com.unnet.jmanul.common.dto.ApiResult;
import com.unnet.jmanul.service.MinioService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * 日志文件管理控制器
 * 
 * <AUTHOR>
 * @since 2025-07-28
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/admin/log-files")
@Api(tags = "日志文件管理", description = "日志文件的查询、下载、删除等操作")
public class LogFileController {

    @Autowired
    private LogFileService logFileService;

    @Autowired
    private MinioService minioService;

    /**
     * 根据终端MAC地址分页查询日志文件
     */
    @GetMapping("/terminal/{identityMac}")
    @ApiOperation(value = "查询终端日志文件", notes = "根据终端MAC地址分页查询日志文件列表")
    public ResponseEntity<IPage<LogFile>> getLogFilesByTerminal(
            @ApiParam(value = "终端MAC地址", required = true) @PathVariable("identityMac") String identityMac,
            @ApiParam(value = "页码，从1开始") @RequestParam(value = "page", defaultValue = "1") Integer page,
            @ApiParam(value = "每页大小") @RequestParam(value = "size", defaultValue = "10") Integer size) {

        try {
            log.info("查询终端日志文件 - 设备MAC: {}, 页码: {}, 每页大小: {}", identityMac, page, size);

            // 验证参数
            if (identityMac == null || identityMac.trim().isEmpty()) {
                return ResponseEntity.badRequest().build();
            }

            if (page < 1) {
                page = 1;
            }
            if (size < 1 || size > 100) {
                size = 10;
            }

            Page<LogFile> pageParam = new Page<>(page, size);
            IPage<LogFile> result = logFileService.getLogFilesByIdentityMac(pageParam, identityMac);

            log.info("查询终端日志文件成功 - 设备MAC: {}, 总数: {}", identityMac, result.getTotal());
            return ResponseEntity.ok(result);

        } catch (Exception e) {
            log.error("查询终端日志文件失败 - 设备MAC: {}, 错误: {}", identityMac, e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 根据终端MAC地址查询所有日志文件（不分页）
     */
    @GetMapping("/terminal/{identityMac}/all")
    @ApiOperation(value = "查询终端所有日志文件", notes = "根据终端MAC地址查询所有日志文件列表")
    public ResponseEntity<ApiResult<List<LogFile>>> getAllLogFilesByTerminal(
            @ApiParam(value = "终端MAC地址", required = true) @PathVariable("identityMac") String identityMac) {

        try {
            log.info("查询终端所有日志文件 - 设备MAC: {}", identityMac);

            if (identityMac == null || identityMac.trim().isEmpty()) {
                return ResponseEntity.ok(ApiResult.error(400, "终端MAC地址不能为空"));
            }

            List<LogFile> result = logFileService.getLogFilesByIdentityMac(identityMac);

            log.info("查询终端所有日志文件成功 - 设备MAC: {}, 总数: {}", identityMac, result.size());
            return ResponseEntity.ok(ApiResult.success("查询成功", result));

        } catch (Exception e) {
            log.error("查询终端所有日志文件失败 - 设备MAC: {}, 错误: {}", identityMac, e.getMessage(), e);
            return ResponseEntity.ok(ApiResult.error("查询失败: " + e.getMessage()));
        }
    }

    /**
     * 获取文件下载URL
     */
    @GetMapping("/{fileId}/download-url")
    @ApiOperation(value = "获取文件下载URL", notes = "获取指定文件的下载URL")
    public ResponseEntity<ApiResult<String>> getDownloadUrl(
            @ApiParam(value = "文件ID", required = true) @PathVariable("fileId") Long fileId) {

        try {
            log.info("获取文件下载URL - 文件ID: {}", fileId);

            if (fileId == null || fileId <= 0) {
                return ResponseEntity.ok(ApiResult.error(400, "文件ID无效"));
            }

            String downloadUrl = logFileService.getDownloadUrl(fileId);

            log.info("获取文件下载URL成功 - 文件ID: {}", fileId);
            return ResponseEntity.ok(ApiResult.success("获取下载链接成功", downloadUrl));

        } catch (Exception e) {
            log.error("获取文件下载URL失败 - 文件ID: {}, 错误: {}", fileId, e.getMessage(), e);
            return ResponseEntity.ok(ApiResult.error("获取下载链接失败: " + e.getMessage()));
        }
    }

    /**
     * 删除日志文件
     */
    @DeleteMapping("/{fileId}")
    @ApiOperation(value = "删除日志文件", notes = "删除指定的日志文件")
    public ResponseEntity<ApiResult<String>> deleteLogFile(
            @ApiParam(value = "文件ID", required = true) @PathVariable("fileId") Long fileId) {

        try {
            log.info("删除日志文件 - 文件ID: {}", fileId);

            if (fileId == null || fileId <= 0) {
                return ResponseEntity.ok(ApiResult.error(400, "文件ID无效"));
            }

            boolean success = logFileService.deleteLogFile(fileId);
            if (success) {
                log.info("删除日志文件成功 - 文件ID: {}", fileId);
                return ResponseEntity.ok(ApiResult.success("删除成功"));
            } else {
                log.warn("删除日志文件失败 - 文件ID: {}", fileId);
                return ResponseEntity.ok(ApiResult.error("删除失败"));
            }

        } catch (Exception e) {
            log.error("删除日志文件失败 - 文件ID: {}, 错误: {}", fileId, e.getMessage(), e);
            return ResponseEntity.ok(ApiResult.error("删除失败: " + e.getMessage()));
        }
    }

    /**
     * 直接下载日志文件
     */
    @GetMapping("/{fileId}/download")
    @ApiOperation(value = "直接下载日志文件", notes = "直接下载指定的日志文件")
    public void downloadLogFile(
            @ApiParam(value = "文件ID", required = true) @PathVariable("fileId") Long fileId,
            HttpServletResponse response) {

        try {
            log.info("直接下载日志文件 - 文件ID: {}", fileId);

            if (fileId == null || fileId <= 0) {
                response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
                return;
            }

            LogFile logFile = logFileService.getById(fileId);
            if (logFile == null) {
                response.setStatus(HttpServletResponse.SC_NOT_FOUND);
                return;
            }

            if (logFile.getStatus() != 0) {
                response.setStatus(HttpServletResponse.SC_NOT_FOUND);
                return;
            }

            // 设置响应头，强制下载
            String encodedFilename = URLEncoder.encode(logFile.getOriginalFilename(), StandardCharsets.UTF_8.toString());
            
            response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
            response.setHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + encodedFilename + "\"");
            response.setHeader(HttpHeaders.CACHE_CONTROL, "no-cache, no-store, must-revalidate");
            response.setHeader(HttpHeaders.PRAGMA, "no-cache");
            response.setHeader(HttpHeaders.EXPIRES, "0");
            response.setContentLengthLong(logFile.getFileSize());

            // 从MinIO获取文件流并直接写入响应
            try (InputStream fileStream = minioService.getFileStream(logFile.getStoragePath());
                 ServletOutputStream outputStream = response.getOutputStream()) {
                
                // 使用缓冲区分块传输，避免大文件内存溢出
                byte[] buffer = new byte[8192]; // 8KB缓冲区
                int bytesRead;
                
                while ((bytesRead = fileStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                    outputStream.flush(); // 确保数据及时发送
                }
                
                log.info("直接下载日志文件成功 - 文件ID: {}, 文件名: {}", fileId, logFile.getOriginalFilename());
                
            } catch (IOException e) {
                log.error("文件流传输失败 - 文件ID: {}, 错误: {}", fileId, e.getMessage(), e);
                if (!response.isCommitted()) {
                    response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
                }
            }

        } catch (Exception e) {
            log.error("直接下载日志文件失败 - 文件ID: {}, 错误: {}", fileId, e.getMessage(), e);
            if (!response.isCommitted()) {
                response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            }
        }
    }

    /**
     * 根据文件ID查询文件详情
     */
    @GetMapping("/{fileId}")
    @ApiOperation(value = "查询文件详情", notes = "根据文件ID查询文件详细信息")
    public ResponseEntity<LogFile> getLogFileById(
            @ApiParam(value = "文件ID", required = true) @PathVariable("fileId") Long fileId) {

        try {
            log.info("查询文件详情 - 文件ID: {}", fileId);

            if (fileId == null || fileId <= 0) {
                return ResponseEntity.badRequest().build();
            }

            LogFile logFile = logFileService.getById(fileId);
            if (logFile == null) {
                return ResponseEntity.notFound().build();
            }

            log.info("查询文件详情成功 - 文件ID: {}", fileId);
            return ResponseEntity.ok(logFile);

        } catch (Exception e) {
            log.error("查询文件详情失败 - 文件ID: {}, 错误: {}", fileId, e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }
}
