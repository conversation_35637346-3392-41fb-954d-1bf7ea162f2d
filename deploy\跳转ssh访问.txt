跳转ssh访问
	


// 访问********
ssh root@********


// 访问********
ssh root@********

sftp root@********
sftp> put ./backend_admin_server:1.0.tar /data/backend/backned_admin_server/
CA-CQUPT.20250717
# 使用 scp 上传文件
scp /data/tmp/opendjdk_8-jre-slim.tar root@********:/data/tmp/opendjdk_8-jre-slim.tar

CA-CQUPT.20250717


root@ecm-large-node1:~# curl -v -u elastic:es123 http://********:9200
*   Trying ********:9200...
* Connected to ******** (********) port 9200 (#0)
* Server auth using Basic with user 'elastic'
> GET / HTTP/1.1
> Host: ********:9200
> Authorization: Basic ZWxhc3RpYzpDaGFuZ2FuNWcuZXM=
> User-Agent: curl/7.81.0
> Accept: */*
> 
* Mark bundle as not supporting multiuse
< HTTP/1.1 200 OK
< X-elastic-product: Elasticsearch
< content-type: application/json; charset=UTF-8
< content-length: 538
< 
{
  "name" : "es-node-1",
  "cluster_name" : "es-cluster",
  "cluster_uuid" : "dPfiTrKSQYWcHs0-UOZTwg",


{"receiveTime":"2025-07-30 11:41:16","metricCollectTime":"2025-07-30 11:41:16","originalData":"{\n  \"hostname\": \"123_3568_25030031\",\n  \"device_id\": \"142215213fd7\",\n  \"identity_mac\": \"ccddee523e60\",\n  \"uptime\": 48538.47,\n  \"cpu_temp\": 46.23,\n  \"temperatures\": {\n    \"bigcore1-thermal\": 46.23,\n    \"soc-thermal\": 46.23,\n    \"gpu-thermal\": 45.307,\n    \"littlecore-thermal\": 46.23,\n    \"bigcore0-thermal\": 45.307,\n    \"npu-thermal\": 45.307,\n    \"center-thermal\": 46.23\n  },\n  \"cpu_usage\": {\n    \"num\": \"8\",\n    \"core\": \"8\",\n    \"thread\": \"8\",\n    \"user\": \"18.8\",\n    \"sys\": \"5.9\",\n    \"idle\": \"69.8\"\n  },\n  \"cpu_percent\": 28.2,\n  \"memory_percent\": 35.7,\n  \"memory_usage\": {\n    \"total\": 16719081472,\n    \"available\": 10753953792,\n    \"percent\": 35.7,\n    \"used\": 5615120384,\n    \"free\": 438583296,\n    \"active\": 5928108032,\n    \"inactive\": 9627013120,\n    \"buffers\": 33615872,\n    \"cached\": 10631761920,\n    \"shared\": 158978048,\n    \"slab\": 271261696\n  },\n  \"disk_usage\": [\n    {\n      \"device\": \"/dev/mmcblk0p6\",\n      \"mountpoint\": \"/\",\n      \"fstype\": \"ext4\",\n      \"total\": 245941342208,\n      \"used\": 11803267072,\n      \"free\": 224112996352,\n      \"percent\": 5.0,\n      \"smartmon\": {},\n      \"io_rate\": {\n        \"MBr/s\": 0.0,\n        \"MBw/s\": 0.0,\n        \"read/s\": 0,\n        \"write/s\": 0\n      }\n    },\n    {\n      \"device\": \"/dev/nvme0n1p1\",\n      \"mountpoint\": \"/mnt/nvme0n1p1\",\n      \"fstype\": \"ext4\",\n      \"total\": 983350091776,\n      \"used\": 15583633408,\n      \"free\": 917739491328,\n      \"percent\": 1.7,\n      \"smartmon\": {\n        \"critical_warning\": 0,\n        \"temperature\": 49,\n        \"available_spare\": 100,\n        \"available_spare_threshold\": 10,\n        \"percentage_used\": 9,\n        \"data_units_read\": 113016600,\n        \"data_units_written\": 143996158,\n        \"host_reads\": 388949179,\n        \"host_writes\": 175354762,\n        \"controller_busy_time\": 7964,\n        \"power_cycles\": 120,\n        \"power_on_hours\": 1080,\n        \"unsafe_shutdowns\": 80,\n        \"media_errors\": 0,\n        \"num_err_log_entries\": 0,\n        \"warning_temp_time\": 1754,\n        \"critical_comp_time\": 772,\n        \"temperature_sensors\": [57, 49],\n        \"power_on_time\": 1080\n      },\n      \"io_rate\": {\n        \"MBr/s\": 0.0,\n        \"MBw/s\": 0.0,\n        \"read/s\": 0,\n        \"write/s\": 0\n      }\n    }\n  ],\n  \"disk_data_percent\": 1.7,\n  \"disk_system_percent\": 5.0,\n  \"cdata\": {\n    \"count\": 1,\n    \"size\": 697724846\n  },\n  \"zdata\": {\n    \"count\": 9,\n    \"size\": 13811916993\n  },\n  \"group_usage\": {\n    \"group_id\": 105,\n    \"pps_total\": 1994.0,\n    \"bps_total\": 5385836.0,\n    \"buckets\": [\n      {\"bucket\": 0, \"conn\": 9, \"pps\": 520.0, \"bps\": 1386720.0,\"ip\": \"**********\",\"eth\": \"00:50:c6:7c:ad:74\"},\n      {\"bucket\": 1, \"conn\": 9, \"pps\": 8.0, \"bps\": 11312.0,\"ip\": \"**********\",\"eth\": \"00:50:c0:24:ed:ec\"},\n      {\"bucket\": 2, \"conn\": 9, \"pps\": 989.0, \"bps\": 2691806.0,\"ip\": \"**********\",\"eth\": \"00:50:c0:b8:ee:20\"},\n      {\"bucket\": 3, \"conn\": 9, \"pps\": 477.0, \"bps\": 1295998.0,\"ip\": \"**********\",\"eth\": \"00:50:c0:10:30:d0\"}\n    ]\n  },\n  \"group_bps\": 5385836.0,\n  \"app_version\": {\n    \"controller\": \"1.0.48\",\n    \"detector\": \"1.0.48\",\n    \"web\": \"1.0.55\",\n    \"dist\": \"1.0.40\",\n    \"extension\": \"1.0.383\",\n    \"site-packages\": \"1.0.2\",\n    \"edge-file-uploader\": \"2.3.1\"\n  },\n  \"expired_date\": \"2026-06-15 10:47:08\"\n}"}