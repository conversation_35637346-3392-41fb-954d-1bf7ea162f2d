package com.unnet.jmanul.business.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 告警解决请求DTO
 */
@Data
@ApiModel(description = "告警解决请求")
public class AlertResolveRequest {

    @ApiModelProperty(value = "解决人", required = true, example = "admin")
    @NotBlank(message = "解决人不能为空")
    private String resolvedBy;

    @ApiModelProperty(value = "解决备注", example = "问题已修复")
    private String resolveComment;
}
