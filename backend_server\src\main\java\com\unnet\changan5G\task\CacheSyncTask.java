package com.unnet.changan5G.task;

import com.unnet.changan5G.service.TerminalCacheService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 缓存同步定时任务
 * 
 * <AUTHOR>
 * @date 2024-07-15
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class CacheSyncTask {

    private final TerminalCacheService terminalCacheService;

    /**
     * 定期清理过期缓存
     * 每小时执行一次
     */
    @Scheduled(fixedRate = 3600000) // 1小时
    public void cleanExpiredCache() {
        try {
            log.debug("开始清理过期缓存...");
            terminalCacheService.cleanExpiredCache();
            log.debug("清理过期缓存完成");
        } catch (Exception e) {
            log.error("清理过期缓存失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 输出缓存统计信息
     * 每5分钟执行一次
     */
    @Scheduled(fixedRate = 300000) // 5分钟
    public void logCacheStatistics() {
        try {
            Object stats = terminalCacheService.getCacheStatistics();
            if (stats != null) {
                log.info("Redis缓存统计: {}", stats);
            }
        } catch (Exception e) {
            log.error("获取缓存统计信息失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 缓存预热任务
     * 每天凌晨2点执行一次
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void warmUpCache() {
        try {
            log.info("开始执行缓存预热任务...");
            terminalCacheService.warmUpCache();
            log.info("缓存预热任务完成");
        } catch (Exception e) {
            log.error("缓存预热任务失败: {}", e.getMessage(), e);
        }
    }
}
