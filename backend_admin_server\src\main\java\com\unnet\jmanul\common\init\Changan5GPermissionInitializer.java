package com.unnet.jmanul.common.init;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.casbin.jcasbin.main.Enforcer;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * 长安5G权限初始化器
 * 
 * <AUTHOR>
 * @date 2024-07-16
 */
@Component
@Slf4j
@RequiredArgsConstructor
@Order(100) // 确保在其他初始化器之后执行
public class Changan5GPermissionInitializer implements ApplicationRunner {

    private final Enforcer enforcer;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        log.info("开始初始化长安5G权限...");

        try {
            // 检查指标配置权限是否存在
            boolean hasMetricConfigPermission = enforcer.hasPolicy("admin", "/api/v1/admin/metric-threshold-config*", "GET");

            if (!hasMetricConfigPermission) {
                log.info("添加指标阈值配置权限规则...");

                // 为admin角色添加指标阈值配置管理权限
                enforcer.addPolicy("admin", "/api/v1/admin/metric-threshold-config*", "GET");
                enforcer.addPolicy("admin", "/api/v1/admin/metric-threshold-config*", "POST");
                enforcer.addPolicy("admin", "/api/v1/admin/metric-threshold-config*", "PUT");
                enforcer.addPolicy("admin", "/api/v1/admin/metric-threshold-config*", "DELETE");

                // 为auditor角色添加指标配置只读权限
                enforcer.addPolicy("auditor", "/api/v1/admin/metric-threshold-config*", "GET");

                // 为user角色添加指标配置查看权限
                enforcer.addPolicy("user", "/api/v1/admin/metric-threshold-config*", "GET");

                // 保存权限规则到数据库
                enforcer.savePolicy();

                log.info("指标阈值配置权限规则添加完成");
            } else {
                log.info("指标阈值配置权限规则已存在");
            }

            // 检查是否已经初始化过其他权限
            boolean hasTerminalPermission = enforcer.hasPolicy("admin", "/api/v1/admin/terminals*", "GET");

            if (!hasTerminalPermission) {
                log.info("添加长安5G权限规则...");

                // 为admin角色添加终端管理权限
                enforcer.addPolicy("admin", "/api/v1/admin/terminals*", "GET");
                enforcer.addPolicy("admin", "/api/v1/admin/terminals*", "POST");
                enforcer.addPolicy("admin", "/api/v1/admin/terminals*", "PUT");
                enforcer.addPolicy("admin", "/api/v1/admin/terminals*", "DELETE");

                // 为admin角色添加终端指标查询权限
                enforcer.addPolicy("admin", "/api/v1/admin/terminal/metrics*", "GET");
                enforcer.addPolicy("admin", "/api/v1/admin/terminal/metrics*", "POST");

                // 为admin角色添加告警管理权限
                enforcer.addPolicy("admin", "/api/v1/admin/alerts*", "GET");
                enforcer.addPolicy("admin", "/api/v1/admin/alerts*", "POST");
                enforcer.addPolicy("admin", "/api/v1/admin/alerts*", "PUT");
                enforcer.addPolicy("admin", "/api/v1/admin/alerts*", "DELETE");

                // 为auditor角色添加只读权限
                enforcer.addPolicy("auditor", "/api/v1/admin/terminals*", "GET");
                enforcer.addPolicy("auditor", "/api/v1/admin/terminal/metrics*", "GET");
                enforcer.addPolicy("auditor", "/api/v1/admin/alerts*", "GET");

                // 为user角色添加基本查看权限
                enforcer.addPolicy("user", "/api/v1/admin/terminals*", "GET");
                enforcer.addPolicy("user", "/api/v1/admin/terminal/metrics*", "GET");
                enforcer.addPolicy("user", "/api/v1/admin/alerts*", "GET");

                // 保存权限规则到数据库
                enforcer.savePolicy();

                log.info("长安5G权限规则添加完成");
            } else {
                log.info("长安5G权限规则已存在，跳过初始化");
            }

            log.info("长安5G权限初始化完成");

        } catch (Exception e) {
            log.error("长安5G权限初始化失败: {}", e.getMessage(), e);
            // 不抛出异常，避免影响应用启动
        }
    }
}
