package com.unnet.jmanul.business.service.dto.overview;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 概览统计信息响应
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "概览统计信息响应")
public class OverviewStatsResp {

    @ApiModelProperty(value = "终端总数", example = "100")
    private Integer totalTerminals;

    @ApiModelProperty(value = "在线终端数", example = "85")
    private Integer onlineTerminals;

    @ApiModelProperty(value = "离线终端数", example = "15")
    private Integer offlineTerminals;

    @ApiModelProperty(value = "告警总数", example = "5")
    private Integer totalAlerts;

    @ApiModelProperty(value = "严重告警数", example = "2")
    private Integer criticalAlerts;

    @ApiModelProperty(value = "警告告警数", example = "3")
    private Integer warningAlerts;

    @ApiModelProperty(value = "已解决告警数", example = "10")
    private Integer resolvedAlerts;

    @ApiModelProperty(value = "统计时间", example = "2025-07-17T10:30:00")
    private String timestamp;
}
