package com.unnet.changan5G.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.unnet.changan5G.dto.terminal.TerminalAlertInfo;
import com.unnet.changan5G.entity.TerminalAlertInfoEntity;
import com.unnet.changan5G.mapper.TerminalAlertInfoMapper;
import com.unnet.changan5G.service.TerminalAlertInfoService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 终端告警信息服务实现
 * 
 * <AUTHOR>
 * @date 2024-07-14
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class TerminalAlertInfoServiceImpl extends ServiceImpl<TerminalAlertInfoMapper, TerminalAlertInfoEntity> 
        implements TerminalAlertInfoService {

    private final TerminalAlertInfoMapper terminalAlertInfoMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveAlertInfo(TerminalAlertInfo terminalAlertInfo) {
        try {
            TerminalAlertInfoEntity entity = new TerminalAlertInfoEntity();
            BeanUtils.copyProperties(terminalAlertInfo, entity);
            entity.setAlertType(terminalAlertInfo.getAlertType());
            entity.setAlertLevel(terminalAlertInfo.getAlertLevel()); // 确保告警级别被设置
            entity.setAlertStatus(terminalAlertInfo.getAlertStatus().name());

            boolean result = save(entity);
            if (result) {
                log.debug("保存告警信息成功 - 设备: {}, 告警ID: {}, 告警级别: {}",
                        terminalAlertInfo.getIdentityMac(), terminalAlertInfo.getAlertId(),
                        terminalAlertInfo.getAlertLevel());
            }
            return result;
        } catch (Exception e) {
            log.error("保存告警信息失败 - 设备: {}, 错误: {}",
                    terminalAlertInfo.getIdentityMac(), e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchSaveAlertInfo(List<TerminalAlertInfo> alertInfoList) {
        try {
            List<TerminalAlertInfoEntity> entities = alertInfoList.stream().map(alertInfo -> {
                TerminalAlertInfoEntity entity = new TerminalAlertInfoEntity();
                BeanUtils.copyProperties(alertInfo, entity);
                entity.setAlertType(alertInfo.getAlertType());
                entity.setAlertLevel(alertInfo.getAlertLevel()); // 确保告警级别被设置
                entity.setAlertStatus(alertInfo.getAlertStatus().name());
                return entity;
            }).collect(Collectors.toList());

            boolean result = saveBatch(entities);
            if (result) {
                log.info("批量保存告警信息成功 - 数量: {}", alertInfoList.size());
            }
            return result;
        } catch (Exception e) {
            log.error("批量保存告警信息失败 - 数量: {}, 错误: {}", alertInfoList.size(), e.getMessage(), e);
            return false;
        }
    }

    @Override
    public List<TerminalAlertInfoEntity> getActiveAlertsByDeviceId(String deviceId) {
        return null;
    }

    @Override
    public TerminalAlertInfoEntity getActiveAlertByDeviceIdAndType(String deviceId, String alertType) {
        return null;
    }

    @Override
    public List<TerminalAlertInfoEntity> getAllActiveAlerts() {
        return terminalAlertInfoMapper.selectAllActiveAlerts();
    }

    @Override
    public List<TerminalAlertInfoEntity> getByTimeRange(LocalDateTime startTime, LocalDateTime endTime) {
        return terminalAlertInfoMapper.selectByTimeRange(startTime, endTime);
    }

    @Override
    public List<TerminalAlertInfoEntity> getByDeviceIdAndTimeRange(String deviceId, LocalDateTime startTime, LocalDateTime endTime) {
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateAlertStatus(String alertId, String status, String comment) {
        try {
            int result = terminalAlertInfoMapper.updateAlertStatus(alertId, status, LocalDateTime.now(), comment);
            log.debug("更新告警状态 - 告警ID: {}, 状态: {}, 影响行数: {}", alertId, status, result);
            return result > 0;
        } catch (Exception e) {
            log.error("更新告警状态失败 - 告警ID: {}, 状态: {}, 错误: {}", alertId, status, e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean acknowledgeAlert(String alertId, String acknowledgedBy) {
        try {
            int result = terminalAlertInfoMapper.acknowledgeAlert(alertId, LocalDateTime.now(), acknowledgedBy);
            log.debug("确认告警 - 告警ID: {}, 确认人: {}, 影响行数: {}", alertId, acknowledgedBy, result);
            return result > 0;
        } catch (Exception e) {
            log.error("确认告警失败 - 告警ID: {}, 确认人: {}, 错误: {}", alertId, acknowledgedBy, e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchResolveAlertsByType(String deviceId, String alertType, String comment) {
        try {
            int result = terminalAlertInfoMapper.batchResolveAlertsByType(deviceId, alertType, LocalDateTime.now(), comment);
            log.debug("批量解决告警 - 设备: {}, 类型: {}, 影响行数: {}", deviceId, alertType, result);
            return result > 0;
        } catch (Exception e) {
            log.error("批量解决告警失败 - 设备: {}, 类型: {}, 错误: {}", deviceId, alertType, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public List<TerminalAlertInfoEntity> getAlertStatistics(LocalDateTime startTime) {
        return terminalAlertInfoMapper.selectAlertStatistics(startTime);
    }

    @Override
    public List<TerminalAlertInfoEntity> getUnsentNotificationAlerts() {
        return terminalAlertInfoMapper.selectUnsentNotificationAlerts();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateNotificationSent(String alertId) {
        try {
            int result = terminalAlertInfoMapper.updateNotificationSent(alertId, LocalDateTime.now());
            log.debug("更新通知发送状态 - 告警ID: {}, 影响行数: {}", alertId, result);
            return result > 0;
        } catch (Exception e) {
            log.error("更新通知发送状态失败 - 告警ID: {}, 错误: {}", alertId, e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean autoResolveRecoveredAlerts(String deviceId, String alertType) {
        try {
            // 自动解决已恢复的告警（例如：CPU温度恢复正常后自动解决CPU温度告警）
            String comment = "系统自动解决：指标已恢复正常";
            int result = terminalAlertInfoMapper.batchResolveAlertsByType(deviceId, alertType, LocalDateTime.now(), comment);

            if (result > 0) {
                log.info("自动解决已恢复告警 - 设备: {}, 类型: {}, 解决数量: {}", deviceId, alertType, result);
            }
            return result >= 0;
        } catch (Exception e) {
            log.error("自动解决已恢复告警失败 - 设备: {}, 类型: {}, 错误: {}", deviceId, alertType, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public int countActiveAlerts() {
        try {
            int count = terminalAlertInfoMapper.countActiveAlerts();
            log.debug("统计活跃告警数量: {}", count);
            return count;
        } catch (Exception e) {
            log.error("统计活跃告警数量失败: {}", e.getMessage(), e);
            return 0;
        }
    }
}
