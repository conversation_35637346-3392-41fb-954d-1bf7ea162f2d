# 实时指标页面故障排除指南

## 问题描述

从终端管理页面点击"实时指标"按钮后，页面显示"无法获取指标数据 重新加载"的错误信息。

## 问题原因分析

### 1. Mock数据缺失
- 终端管理页面显示的设备MAC地址为 `de:07:65:52:3e:60`
- 但Mock数据中原本没有这个MAC地址对应的指标数据
- 当`VITE_USE_MOCK=false`时，会调用真实API

### 2. 后端服务状态
- 前端配置为调用真实API：`/api/v1/admin/terminal/metrics/device/{identityMac}/latest`
- 后端服务可能未启动或Elasticsearch中无数据
- 网络连接可能存在问题

### 3. 数据流程
```
终端管理页面 → 点击实时指标 → 跳转到实时指标页面 → 调用API获取数据 → 显示数据
                                                    ↓
                                              API调用失败 → 显示错误信息
```

## 解决方案

### 方案1：使用Mock数据（推荐用于开发测试）

1. **已添加Mock数据**：
   ```javascript
   'de:07:65:52:3e:60': {
     identityMac: 'de:07:65:52:3e:60',
     hostname: 'ec_3568_25030031',
     timestamp: '2024-07-28 14:30:25',
     cpu: { usage: 45.8, cores: 4, temperature: 58.3 },
     memory: { usage: 67.2, total: 8192, used: 5504, available: 2688 },
     disk: { usage: 52.1, total: 512000, used: 266752, available: 245248 },
     network: { uploadSpeed: 2048, downloadSpeed: 4096, totalUpload: 2048000, totalDownload: 4096000 }
   }
   ```

2. **启用Mock模式**：
   ```bash
   # 修改 .env.development
   VITE_USE_MOCK=true
   ```

3. **重启前端服务**：
   ```bash
   npm run dev
   ```

### 方案2：启动后端服务

1. **启动backend_admin_server**：
   ```bash
   cd backend_admin_server
   mvn spring-boot:run -Dspring-boot.run.profiles=dev
   ```

2. **确保Elasticsearch运行**：
   - 检查Elasticsearch服务状态
   - 确保有对应设备的指标数据

3. **设置环境变量**：
   ```bash
   # 使用真实API
   VITE_USE_MOCK=false
   ```

### 方案3：使用页面内置的故障排除功能

页面现在提供了增强的错误处理界面：

1. **重新加载**：点击"重新加载"按钮重试API调用
2. **测试连接**：点击"测试连接"按钮检查API连通性
3. **使用模拟数据**：点击"使用模拟数据"按钮切换到模拟数据模式

## 验证步骤

### 1. 检查环境配置
```bash
# 查看当前配置
cat front_server/.env.development | grep VITE_USE_MOCK
```

### 2. 检查Mock数据
```javascript
// 在浏览器控制台检查
console.log(import.meta.env.VITE_USE_MOCK)
```

### 3. 检查API调用
```bash
# 手动测试API
curl -X GET "http://localhost:8080/api/v1/admin/terminal/metrics/device/de:07:65:52:3e:60/latest" \
     -H "accept: application/json"
```

### 4. 检查网络请求
- 打开浏览器开发者工具
- 查看Network标签页
- 观察API请求的状态和响应

## 常见错误及解决方法

### 错误1：404 Not Found
- **原因**：后端服务未启动或API路径错误
- **解决**：启动backend_admin_server服务

### 错误2：500 Internal Server Error
- **原因**：Elasticsearch连接失败或数据查询异常
- **解决**：检查Elasticsearch服务状态

### 错误3：CORS错误
- **原因**：跨域请求被阻止
- **解决**：检查后端CORS配置

### 错误4：连接超时
- **原因**：网络连接问题或服务响应慢
- **解决**：检查网络连接，增加超时时间

## 开发建议

### 1. 开发阶段
- 使用Mock数据进行前端开发：`VITE_USE_MOCK=true`
- 确保Mock数据覆盖所有测试场景

### 2. 集成测试阶段
- 切换到真实API：`VITE_USE_MOCK=false`
- 确保后端服务正常运行

### 3. 生产部署
- 使用真实API
- 配置适当的错误处理和重试机制

## 监控和日志

### 前端日志
```javascript
// 在浏览器控制台查看
console.log('API Response:', response)
console.log('Processed Metrics Data:', metricsData)
```

### 后端日志
```bash
# 查看backend_admin_server日志
tail -f backend_admin_server/logs/application.log | grep "获取设备最新指标数据"
```

### Elasticsearch日志
```bash
# 检查Elasticsearch查询
curl -X GET "localhost:9200/terminal_metrics/_search?q=identityMac:de:07:65:52:3e:60&size=1&sort=metricTime:desc"
```

## 联系支持

如果问题仍然存在，请提供以下信息：
1. 浏览器控制台的错误信息
2. 网络请求的详细信息
3. 后端服务的日志
4. 当前的环境配置
