package com.unnet.jmanul.service;

import com.unnet.jmanul.config.MinioDevConfig;
import com.unnet.jmanul.config.MinioProdConfig;
import io.minio.BucketExistsArgs;
import io.minio.MinioClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * MinIO集群服务类
 * 提供集群健康检查、故障转移和负载均衡功能
 * 支持开发环境和生产环境的不同配置
 * 
 * <AUTHOR>
 * @since 2025-07-28
 */
@Slf4j
@Service
public class MinioClusterService {

    @Autowired(required = false)
    private MinioDevConfig minioDevConfig;

    @Autowired(required = false)
    private MinioProdConfig minioProdConfig;

    /**
     * 节点健康状态缓存
     * key: 节点地址, value: 是否健康
     */
    private final Map<String, Boolean> nodeHealthStatus = new ConcurrentHashMap<>();

    /**
     * 负载均衡计数器
     */
    private final AtomicInteger roundRobinCounter = new AtomicInteger(0);

    /**
     * 获取所有端点
     */
    private List<String> getAllEndpoints() {
        if (minioProdConfig != null) {
            return minioProdConfig.getAllEndpoints();
        } else if (minioDevConfig != null) {
            return List.of(minioDevConfig.getHost());
        }
        throw new RuntimeException("未找到MinIO配置");
    }

    /**
     * 是否启用集群模式
     */
    private boolean isClusterEnabled() {
        if (minioProdConfig != null) {
            return minioProdConfig.getCluster().getEnabled();
        }
        return false; // 开发环境默认不启用集群
    }

    /**
     * 获取访问密钥
     */
    private String getAccessKey() {
        if (minioProdConfig != null) {
            return minioProdConfig.getAccessKey();
        } else if (minioDevConfig != null) {
            return minioDevConfig.getAccessKey();
        }
        throw new RuntimeException("未找到MinIO配置");
    }

    /**
     * 获取秘密密钥
     */
    private String getSecretKey() {
        if (minioProdConfig != null) {
            return minioProdConfig.getSecretKey();
        } else if (minioDevConfig != null) {
            return minioDevConfig.getSecretKey();
        }
        throw new RuntimeException("未找到MinIO配置");
    }

    /**
     * 获取存储桶名称
     */
    private String getBucket() {
        if (minioProdConfig != null) {
            return minioProdConfig.getBucket();
        } else if (minioDevConfig != null) {
            return minioDevConfig.getBucket();
        }
        throw new RuntimeException("未找到MinIO配置");
    }

    /**
     * 初始化集群服务
     */
    @PostConstruct
    public void init() {
        if (isClusterEnabled()) {
            log.info("初始化MinIO集群服务，节点数量: {}", getAllEndpoints().size());
            // 初始化所有节点为健康状态
            for (String endpoint : getAllEndpoints()) {
                nodeHealthStatus.put(endpoint, true);
            }
            // 立即执行一次健康检查
            checkClusterHealth();
        } else {
            log.info("MinIO单节点模式，跳过集群服务初始化");
        }
    }

    /**
     * 定期健康检查
     * 每30秒检查一次集群节点健康状态
     */
    @Scheduled(fixedDelayString = "${spring.minio.cluster.health-check-interval-seconds:30}000")
    public void checkClusterHealth() {
        if (!isClusterEnabled()) {
            return;
        }

        log.debug("开始MinIO集群健康检查");
        List<String> endpoints = getAllEndpoints();
        
        for (String endpoint : endpoints) {
            boolean isHealthy = checkNodeHealth(endpoint);
            Boolean previousStatus = nodeHealthStatus.put(endpoint, isHealthy);
            
            if (previousStatus != null && previousStatus != isHealthy) {
                if (isHealthy) {
                    log.info("MinIO节点恢复健康: {}", endpoint);
                } else {
                    log.warn("MinIO节点不健康: {}", endpoint);
                }
            }
        }

        long healthyCount = nodeHealthStatus.values().stream().mapToLong(healthy -> healthy ? 1 : 0).sum();
        log.debug("MinIO集群健康检查完成，健康节点数: {}/{}", healthyCount, endpoints.size());
        
        if (healthyCount == 0) {
            log.error("所有MinIO节点都不健康！");
        }
    }

    /**
     * 检查单个节点健康状态
     */
    private boolean checkNodeHealth(String endpoint) {
        try {
            MinioClient client = MinioClient.builder()
                    .endpoint(endpoint)
                    .credentials(getAccessKey(), getSecretKey())
                    .build();

            // 尝试检查存储桶是否存在来验证连接
            client.bucketExists(BucketExistsArgs.builder()
                    .bucket(getBucket())
                    .build());
            
            return true;
        } catch (Exception e) {
            log.debug("MinIO节点健康检查失败: {}, 错误: {}", endpoint, e.getMessage());
            return false;
        }
    }

    /**
     * 获取健康的MinIO客户端
     * 使用轮询算法选择健康的节点
     */
    public MinioClient getHealthyClient() {
        if (!isClusterEnabled()) {
            // 单节点模式，直接返回默认客户端
            if (minioDevConfig != null) {
                return createClientForEndpoint(minioDevConfig.getHost());
            } else if (minioProdConfig != null) {
                return createClientForEndpoint(minioProdConfig.getHost());
            }
            throw new RuntimeException("未找到MinIO配置");
        }

        List<String> healthyEndpoints = nodeHealthStatus.entrySet().stream()
                .filter(Map.Entry::getValue)
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(healthyEndpoints)) {
            log.warn("没有健康的MinIO节点，尝试使用第一个节点");
            return createClientForEndpoint(getAllEndpoints().get(0));
        }

        // 使用轮询算法选择节点
        int index = roundRobinCounter.getAndIncrement() % healthyEndpoints.size();
        String selectedEndpoint = healthyEndpoints.get(index);
        
        log.debug("选择MinIO节点: {} (健康节点数: {})", selectedEndpoint, healthyEndpoints.size());
        return createClientForEndpoint(selectedEndpoint);
    }

    /**
     * 为指定端点创建MinIO客户端
     */
    private MinioClient createClientForEndpoint(String endpoint) {
        return MinioClient.builder()
                .endpoint(endpoint)
                .credentials(getAccessKey(), getSecretKey())
                .build();
    }

    /**
     * 执行带重试的操作
     */
    public <T> T executeWithRetry(MinioOperation<T> operation) throws Exception {
        int maxRetries = 3; // 默认重试次数
        long retryDelay = 1000; // 默认重试延迟
        
        // 获取重试配置
        if (minioProdConfig != null && minioProdConfig.getCluster() != null) {
            maxRetries = minioProdConfig.getCluster().getRetryAttempts();
            retryDelay = minioProdConfig.getCluster().getRetryDelayMs();
        }
        
        Exception lastException = null;
        
        for (int attempt = 0; attempt <= maxRetries; attempt++) {
            try {
                MinioClient client = getHealthyClient();
                return operation.execute(client);
            } catch (Exception e) {
                lastException = e;
                log.warn("MinIO操作失败，尝试次数: {}/{}, 错误: {}", attempt + 1, maxRetries + 1, e.getMessage());
                
                if (attempt < maxRetries) {
                    try {
                        Thread.sleep(retryDelay);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw new RuntimeException("操作被中断", ie);
                    }
                }
            }
        }
        
        throw new RuntimeException("MinIO操作失败，已重试" + (maxRetries + 1) + "次", lastException);
    }

    /**
     * MinIO操作接口
     */
    @FunctionalInterface
    public interface MinioOperation<T> {
        T execute(MinioClient client) throws Exception;
    }

    /**
     * 获取集群状态信息
     */
    public Map<String, Object> getClusterStatus() {
        Map<String, Object> status = new ConcurrentHashMap<>();
        status.put("clusterEnabled", isClusterEnabled());
        status.put("totalNodes", getAllEndpoints().size());
        
        if (isClusterEnabled()) {
            long healthyNodes = nodeHealthStatus.values().stream().mapToLong(healthy -> healthy ? 1 : 0).sum();
            status.put("healthyNodes", healthyNodes);
            status.put("nodeStatus", nodeHealthStatus);
        } else {
            status.put("healthyNodes", 1);
            String singleHost = minioDevConfig != null ? minioDevConfig.getHost() : 
                               (minioProdConfig != null ? minioProdConfig.getHost() : "unknown");
            status.put("nodeStatus", Map.of(singleHost, true));
        }
        
        // 添加环境信息
        if (minioProdConfig != null) {
            status.put("environment", "production");
        } else if (minioDevConfig != null) {
            status.put("environment", "development");
        }
        
        return status;
    }
}
