package com.unnet.jmanul.common.utils;

import java.util.HashMap;
import java.util.Map;

public class Dict {
    private final Map<String, Object> map = new HashMap<>();

    public static Dict build() {
        return new Dict();
    }

    public Dict put(String key, Object value) {
        map.put(key, value);
        return this;
    }

    public Object get(String key) {
        return map.get(key);
    }

    public Map<String, Object> getMap() {
        return map;
    }

}
