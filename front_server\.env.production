# 生产环境配置
NODE_ENV=production

# 管理服务API地址 - 通过nginx反向代理到backend_admin_server (8080端口)
VITE_ADMIN_API_BASE_URL=/admin-api

# 数据服务API地址 - 通过nginx反向代理到backend_server (8081端口)
VITE_DATA_API_BASE_URL=/data-api

# SSE服务地址 - 通过nginx反向代理到backend_server (8081端口)
VITE_SSE_BASE_URL=/data-api

# API密钥
VITE_API_KEY=api-SvardHNxjL87TMHbgS2JFDpUsyi1iq9e

# 是否启用Mock数据
VITE_USE_MOCK=false

# 应用标题
VITE_APP_TITLE=长安5G管理平台

# 调试模式
VITE_DEBUG=false
