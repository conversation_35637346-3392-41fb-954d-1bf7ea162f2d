package com.unnet.changan5G.controller;

import com.unnet.changan5G.dto.ApiResp;
import com.unnet.changan5G.service.MetricThresholdConfigService;
import com.unnet.changan5G.util.RedisCacheUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 缓存管理控制器
 * 
 * <AUTHOR>
 * @date 2024-07-24
 */
@RestController
@RequestMapping("/cache")
@Tag(name = "缓存管理", description = "Redis缓存管理相关接口")
@Slf4j
@RequiredArgsConstructor
public class CacheController {

    private final RedisCacheUtil redisCacheUtil;
    private final MetricThresholdConfigService metricThresholdConfigService;

    @GetMapping("/stats")
    @Operation(summary = "获取缓存统计信息", description = "获取Redis缓存的统计信息")
    public ApiResp<Map<String, Object>> getCacheStatistics() {
        try {
            Map<String, Object> stats = redisCacheUtil.getCacheStatistics();
            return ApiResp.success("获取缓存统计信息成功", stats);
        } catch (Exception e) {
            log.error("获取缓存统计信息失败: {}", e.getMessage(), e);
            return ApiResp.error(500, "获取缓存统计信息失败: " + e.getMessage());
        }
    }

    @PostMapping("/evict/{cacheName}")
    @Operation(summary = "清空指定缓存", description = "清空指定名称的缓存")
    public ApiResp<String> evictCache(
            @Parameter(description = "缓存名称", required = true) 
            @PathVariable String cacheName) {
        try {
            redisCacheUtil.evictCache(cacheName);
            return ApiResp.success("缓存清空成功: " + cacheName);
        } catch (Exception e) {
            log.error("清空缓存失败 - 缓存: {}, 错误: {}", cacheName, e.getMessage(), e);
            return ApiResp.error(500, "清空缓存失败: " + e.getMessage());
        }
    }

    @PostMapping("/evict-all")
    @Operation(summary = "清空所有缓存", description = "清空所有Redis缓存")
    public ApiResp<String> evictAllCaches() {
        try {
            redisCacheUtil.evictAllCaches();
            return ApiResp.success("所有缓存清空成功");
        } catch (Exception e) {
            log.error("清空所有缓存失败: {}", e.getMessage(), e);
            return ApiResp.error(500, "清空所有缓存失败: " + e.getMessage());
        }
    }

    @PostMapping("/refresh/threshold-config")
    @Operation(summary = "刷新指标阈值配置缓存", description = "清空并重新加载指标阈值配置缓存")
    public ApiResp<String> refreshThresholdConfigCache() {
        try {
            metricThresholdConfigService.refreshConfigCache();
            // 重新加载缓存
            metricThresholdConfigService.getAllEnabledConfigsMap();
            return ApiResp.success("指标阈值配置缓存刷新成功");
        } catch (Exception e) {
            log.error("刷新指标阈值配置缓存失败: {}", e.getMessage(), e);
            return ApiResp.error(500, "刷新指标阈值配置缓存失败: " + e.getMessage());
        }
    }

    @GetMapping("/value/{cacheName}/{key}")
    @Operation(summary = "获取缓存值", description = "获取指定缓存中指定key的值")
    public ApiResp<Object> getCacheValue(
            @Parameter(description = "缓存名称", required = true) 
            @PathVariable String cacheName,
            @Parameter(description = "缓存key", required = true) 
            @PathVariable String key) {
        try {
            Object value = redisCacheUtil.getCacheValue(cacheName, key);
            return ApiResp.success("获取缓存值成功", value);
        } catch (Exception e) {
            log.error("获取缓存值失败 - 缓存: {}, Key: {}, 错误: {}", cacheName, key, e.getMessage(), e);
            return ApiResp.error(500, "获取缓存值失败: " + e.getMessage());
        }
    }
}