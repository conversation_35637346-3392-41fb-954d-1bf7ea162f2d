package com.unnet.changan5G.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.unnet.changan5G.dto.metric.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 终端指标上送请求体
 * 
 * <AUTHOR>
 * @date 2024-07-14
 */
@Data
@Schema(description = "终端指标上送请求体")
public class MetricRequestBody {

    @Schema(description = "主机名", example = "ec_3568_25030031")
    @NotBlank(message = "主机名不能为空")
    private String hostname;

    @Schema(description = "设备唯一标识符", example = "_0fd94938951f4a64bb11c6817a81f7e7")
    @NotBlank(message = "设备ID不能为空")
    @JsonProperty("device_id")
    private String deviceId;

    @Schema(description = "对接长安车辆所在网口的MAC地址", example = "de0765523e60")
    @NotBlank(message = "MAC地址不能为空")
    @JsonProperty("identity_mac")
    private String identityMac;

    @Schema(description = "vin", example = "LS6C3G0Y2SK400208")
    @JsonProperty("vin")
    private String vin;

    @Schema(description = "系统运行时长（单位：秒）", example = "5916.29")
    @NotNull(message = "系统运行时长不能为空")
    private Double uptime;

    @Schema(description = "CPU温度（单位：摄氏度）", example = "58.888")
    @NotNull(message = "CPU温度不能为空")
    @JsonProperty("cpu_temp")
    private Double cpuTemp;

    @Schema(description = "温度信息")
    private Object temperatures;

    @Schema(description = "CPU使用率信息")
    @JsonProperty("cpu_usage")
    private Object cpuUsage;

    @Schema(description = "CPU总使用率（%）", example = "43.2")
    @NotNull(message = "CPU使用率不能为空")
    @JsonProperty("cpu_percent")
    private Double cpuPercent;

    @Schema(description = "内存使用率（%）", example = "50.9")
    @NotNull(message = "内存使用率不能为空")
    @JsonProperty("memory_percent")
    private Double memoryPercent;

    @Schema(description = "内存详细信息")
    @JsonProperty("memory_usage")
    private Object memoryUsage;

    @Schema(description = "磁盘使用情况数组")
    @JsonProperty("disk_usage")
    private Object diskUsage;

    @Schema(description = "采集数据磁盘分区使用率", example = "0")
    @JsonProperty("disk_data_percent")
    private Double diskDataPercent;

    @Schema(description = "系统盘使用率", example = "54.3")
    @JsonProperty("disk_system_percent")
    private Double diskSystemPercent;

    @Schema(description = "长安程序落盘文件信息")
    private Object cdata;

    @Schema(description = "压缩文件信息")
    private Object zdata;

    @Schema(description = "CPE集合信息")
    @JsonProperty("group_usage")
    private Object groupUsage;

    @Schema(description = "所有CPE的带宽占用（单位：Byte/s）", example = "0")
    @JsonProperty("group_bps")
    private Long groupBps;

    @Schema(description = "当前运行的组件版本号")
    @JsonProperty("app_version")
    private Object appVersion;

    @Schema(description = "软件授权过期时间", example = "2035-03-09 22:47:20")
    @NotBlank(message = "授权过期时间不能为空")
    @JsonProperty("expired_date")
    private String expiredDate;
}
