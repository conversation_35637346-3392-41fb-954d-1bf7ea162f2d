-- ========================================
-- 终端监控系统 - 常用查询示例
-- ========================================

-- 1. 查询所有在线终端基本信息
SELECT 
    device_id,
    hostname,
    identity_mac,
    JSON_EXTRACT(app_version, '$.controller') AS controller_version,
    expired_date,
    last_update_time
FROM terminal_basic_info 
WHERE status = 1 AND is_deleted = 0
ORDER BY last_update_time DESC;

-- 2. 查询指定设备的最新指标信息
SELECT 
    device_id,
    cpu_temp,
    cpu_percent,
    memory_percent,
    disk_data_percent,
    disk_system_percent,
    metric_time
FROM terminal_metric_info 
WHERE device_id = '_0fd94938951f4a64bb11c6817a81f7e7'
ORDER BY metric_time DESC 
LIMIT 1;

-- 3. 查询最近24小时的活跃告警
SELECT 
    alert_id,
    device_id,
    alert_type,
    alert_level,
    alert_details,
    current_value,
    alert_time
FROM terminal_alert_info 
WHERE alert_status = 'ACTIVE' 
    AND alert_time >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
ORDER BY alert_time DESC;

-- 4. 统计各类型告警数量
SELECT 
    alert_type,
    alert_level,
    COUNT(*) as alert_count
FROM terminal_alert_info 
WHERE alert_time >= DATE_SUB(NOW(), INTERVAL 7 DAY)
GROUP BY alert_type, alert_level
ORDER BY alert_count DESC;

-- 5. 查询CPU温度异常的设备
SELECT 
    tbi.device_id,
    tbi.hostname,
    tmi.cpu_temp,
    tmi.metric_time
FROM terminal_basic_info tbi
JOIN terminal_metric_info tmi ON tbi.device_id = tmi.device_id
WHERE tmi.cpu_temp >= 80.0
    AND tmi.metric_time >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
ORDER BY tmi.cpu_temp DESC;

-- 6. 查询内存使用率趋势（最近24小时）
SELECT 
    device_id,
    DATE_FORMAT(metric_time, '%Y-%m-%d %H:00:00') as hour_time,
    AVG(memory_percent) as avg_memory_percent,
    MAX(memory_percent) as max_memory_percent,
    MIN(memory_percent) as min_memory_percent
FROM terminal_metric_info 
WHERE device_id = '_0fd94938951f4a64bb11c6817a81f7e7'
    AND metric_time >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
GROUP BY device_id, hour_time
ORDER BY hour_time;

-- 7. 查询即将过期的软件授权
SELECT 
    device_id,
    hostname,
    expired_date,
    DATEDIFF(STR_TO_DATE(expired_date, '%Y-%m-%d %H:%i:%s'), NOW()) as days_remaining
FROM terminal_basic_info 
WHERE STR_TO_DATE(expired_date, '%Y-%m-%d %H:%i:%s') BETWEEN NOW() AND DATE_ADD(NOW(), INTERVAL 30 DAY)
    AND is_deleted = 0
ORDER BY days_remaining;

-- 8. 查询设备性能概览
SELECT 
    tbi.device_id,
    tbi.hostname,
    tbi.status,
    tmi.cpu_temp,
    tmi.cpu_percent,
    tmi.memory_percent,
    tmi.disk_system_percent,
    tmi.metric_time,
    (SELECT COUNT(*) FROM terminal_alert_info tai 
     WHERE tai.device_id = tbi.device_id 
       AND tai.alert_status = 'ACTIVE') as active_alerts
FROM terminal_basic_info tbi
LEFT JOIN (
    SELECT device_id, cpu_temp, cpu_percent, memory_percent, disk_system_percent, metric_time,
           ROW_NUMBER() OVER (PARTITION BY device_id ORDER BY metric_time DESC) as rn
    FROM terminal_metric_info
) tmi ON tbi.device_id = tmi.device_id AND tmi.rn = 1
WHERE tbi.is_deleted = 0
ORDER BY tbi.last_update_time DESC;

-- 9. 查询磁盘使用率告警详情
SELECT 
    tai.alert_id,
    tai.device_id,
    tbi.hostname,
    tai.alert_details,
    tai.current_value,
    tai.alert_time,
    tai.alert_status
FROM terminal_alert_info tai
JOIN terminal_basic_info tbi ON tai.device_id = tbi.device_id
WHERE tai.alert_type IN ('DISK_USAGE', 'DISK_DATA_USAGE', 'DISK_SYSTEM_USAGE')
    AND tai.alert_time >= DATE_SUB(NOW(), INTERVAL 7 DAY)
ORDER BY tai.alert_time DESC;

-- 10. 查询设备在线时长统计
SELECT 
    device_id,
    hostname,
    first_register_time,
    last_update_time,
    TIMESTAMPDIFF(HOUR, first_register_time, last_update_time) as online_hours,
    CASE 
        WHEN TIMESTAMPDIFF(MINUTE, last_update_time, NOW()) <= 5 THEN '在线'
        ELSE '离线'
    END as current_status
FROM terminal_basic_info 
WHERE is_deleted = 0
ORDER BY online_hours DESC;
