<template>
  <div class="terminals-page">
    <!-- 搜索过滤区域 -->
    <div class="card">
      <div class="card-header">
        <h3>终端列表</h3>
      </div>
      <div class="card-body">
        <div class="search-filters">
          <div class="form-group">
            <label>设备MAC地址</label>
            <input
              type="text"
              class="form-control"
              placeholder="输入设备MAC地址"
              v-model="searchForm.identityMac"
            >
          </div>
          <div class="form-group">
            <label>主机名</label>
            <input 
              type="text" 
              class="form-control" 
              placeholder="输入主机名"
              v-model="searchForm.hostname"
            >
          </div>
          <div class="form-group">
            <label>状态</label>
            <select class="form-control" v-model="searchForm.status">
              <option value="">全部</option>
              <option value="1">在线</option>
              <option value="0">离线</option>
            </select>
          </div>
          <div class="form-group">
            <label>标签</label>
            <input 
              type="text" 
              class="form-control" 
              placeholder="输入标签"
              v-model="searchForm.tag"
            >
          </div>
          <div class="form-group button-group">
            <label>&nbsp;</label> <!-- 占位标签，保持高度一致 -->
            <div class="button-container">
              <button class="btn btn-primary" @click="handleSearch">搜索</button>
              <button class="btn btn-secondary" @click="handleReset">重置</button>
            </div>
          </div>
        </div>

        <table class="table">
          <thead>
            <tr>
              <th>设备ID</th>
              <th>主机名</th>
              <th>MAC地址</th>
              <th>状态</th>
              <th>标签</th>
              <th>最后更新</th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="terminal in terminalStore.terminals" :key="terminal.identityMac">
              <td>{{ terminal.deviceId }}</td>
              <td>{{ terminal.hostname }}</td>
              <td>{{ terminal.identityMac }}</td>
              <td>
                <span :class="terminal.status === 1 ? 'status-online' : 'status-offline'">
                  {{ terminal.status === 1 ? '在线' : '离线' }}
                </span>
              </td>
              <td>
                <span 
                  v-for="(value, key) in terminal.tags" 
                  :key="key" 
                  class="badge badge-info"
                  style="margin-right: 5px;"
                >
                  {{ key }}: {{ value }}
                </span>
              </td>
              <td>{{ terminal.lastUpdateTime }}</td>
              <td>
                <div class="action-buttons">
                  <button
                    class="btn btn-success btn-sm"
                    @click="viewRealTimeMetrics(terminal.identityMac)"
                  >
                    实时指标
                  </button>
                  <button
                    class="btn btn-info btn-sm"
                    @click="viewHistoryMetrics(terminal.identityMac)"
                  >
                    历史指标
                  </button>
                  <button
                    class="btn btn-primary btn-sm"
                    @click="viewTerminalDetail(terminal)"
                  >
                    详情
                  </button>
                  <button
                    class="btn btn-success btn-sm"
                    @click="viewLogFiles(terminal)"
                  >
                    日志管理
                  </button>
                  <button
                    v-if="isAdmin"
                    class="btn btn-warning btn-sm"
                    @click="editTerminal(terminal)"
                  >
                    编辑
                  </button>
                  <button
                    v-if="isAdmin && terminal.status === 0"
                    class="btn btn-danger btn-sm"
                    @click="deleteTerminal(terminal)"
                  >
                    删除
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>

        <div class="pagination">
          <button @click="prevPage" :disabled="pagination.page <= 1">上一页</button>
          <button 
            v-for="page in visiblePages" 
            :key="page"
            :class="{ active: page === pagination.page }"
            @click="goToPage(page)"
          >
            {{ page }}
          </button>
          <button @click="nextPage" :disabled="pagination.page >= totalPages">下一页</button>
        </div>
      </div>
    </div>

    <!-- 终端详情模态框 -->
    <div v-if="detailDialogVisible" class="modal" @click="handleDetailClose">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>终端详情</h3>
          <span class="close" @click="handleDetailClose">&times;</span>
        </div>
        <div class="modal-body" v-if="currentTerminal">
          <!-- 基本信息 -->
          <div class="card">
            <div class="card-header">
              <h4>基本信息</h4>
            </div>
            <div class="card-body">
              <div class="detail-grid">
                <div>
                  <div class="detail-item">
                    <div class="detail-label">设备ID:</div>
                    <div class="detail-value">{{ currentTerminal.deviceId }}</div>
                  </div>
                  <div class="detail-item">
                    <div class="detail-label">主机名:</div>
                    <div class="detail-value">{{ currentTerminal.hostname }}</div>
                  </div>
                  <div class="detail-item">
                    <div class="detail-label">MAC地址:</div>
                    <div class="detail-value">{{ currentTerminal.identityMac }}</div>
                  </div>
                  <div class="detail-item">
                    <div class="detail-label">状态:</div>
                    <div class="detail-value">
                      <span :class="currentTerminal.status === 1 ? 'status-online' : 'status-offline'">
                        {{ currentTerminal.status === 1 ? '在线' : '离线' }}
                      </span>
                    </div>
                  </div>
                  <div class="detail-item">
                    <div class="detail-label">数据源:</div>
                    <div class="detail-value">{{ currentTerminal.dataSource }}</div>
                  </div>
                </div>
                <div>
                  <div class="detail-item">
                    <div class="detail-label">首次注册:</div>
                    <div class="detail-value">{{ currentTerminal.firstRegisterTime }}</div>
                  </div>
                  <div class="detail-item">
                    <div class="detail-label">最后更新:</div>
                    <div class="detail-value">{{ currentTerminal.lastUpdateTime }}</div>
                  </div>
                  <div class="detail-item">
                    <div class="detail-label">授权到期:</div>
                    <div class="detail-value">{{ currentTerminal.expiredDate }}</div>
                  </div>
                  <div class="detail-item">
                    <div class="detail-label">接收时间:</div>
                    <div class="detail-value">{{ currentTerminal.receiveTime }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 终端拓扑关系 -->
          <div class="card">
            <div class="card-header">
              <h4>终端拓扑关系</h4>
            </div>
            <div class="card-body">
              <div class="topology-container">
                <div class="topology-title">终端系统架构图</div>
                <div class="topology-graph">
                  <!-- 主终端 -->
                  <div class="main-terminal">
                    <div class="main-terminal-title">主终端服务</div>
                    <div class="main-terminal-id">{{ currentTerminal.hostname }}</div>
                  </div>
                  
                  <!-- 连接线 -->
                  <div class="connection-line"></div>
                  
                  <!-- CPE集合 -->
                  <div class="cpe-container" id="cpeContainer">
                    <div
                      v-if="mockGroupUsage.buckets && mockGroupUsage.buckets.length > 0"
                      v-for="(bucket, index) in mockGroupUsage.buckets"
                      :key="index"
                      class="cpe-item"
                      :class="bucket.conn > 0 || bucket.pps > 0 || bucket.bps > 0 ? 'active' : 'inactive'"
                    >
                      <div class="cpe-status" :class="bucket.conn > 0 || bucket.pps > 0 || bucket.bps > 0 ? 'online' : 'offline'"></div>
                      <div class="cpe-title">CPE</div>
                      <div class="cpe-bucket">Bucket {{ bucket.bucket }}</div>
                      <div class="cpe-stats">
                        连接: {{ bucket.conn || 0 }}<br>
                        PPS: {{ (bucket.pps || 0).toFixed(1) }}<br>
                        BPS: {{ formatBytes(bucket.bps || 0) }}
                      </div>
                    </div>

                    <!-- 空状态提示 -->
                    <div v-else class="no-cpe-message">
                      上送指标尚未提供CPE信息
                    </div>
                  </div>
                </div>
                
                <!-- 拓扑统计信息 -->
                <div class="topology-summary">
                  <div class="summary-grid">
                    <div class="summary-item">
                      <div class="summary-value">{{ mockGroupUsage.group_id }}</div>
                      <div class="summary-label">组ID</div>
                    </div>
                    <div class="summary-item">
                      <div class="summary-value">{{ mockGroupUsage.buckets ? mockGroupUsage.buckets.length : 0 }}</div>
                      <div class="summary-label">CPE数量</div>
                    </div>
                    <div class="summary-item">
                      <div class="summary-value">{{ mockGroupUsage.pps_total ? mockGroupUsage.pps_total.toFixed(1) : '0.0' }}</div>
                      <div class="summary-label">总PPS</div>
                    </div>
                    <div class="summary-item">
                      <div class="summary-value">{{ formatBytes(mockGroupUsage.bps_total || 0) }}/s</div>
                      <div class="summary-label">总带宽</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 版本信息 -->
          <div class="card">
            <div class="card-header">
              <h4>版本信息</h4>
            </div>
            <div class="card-body">
              <div class="detail-grid" v-if="currentTerminal.appVersion && Object.keys(currentTerminal.appVersion).length > 0">
                <div v-for="(version, key) in currentTerminal.appVersion" :key="key">
                  <div class="detail-item">
                    <div class="detail-label">{{ key }}:</div>
                    <div class="detail-value">{{ version }}</div>
                  </div>
                </div>
              </div>
              <div v-else class="no-data-message">
                暂无版本信息
              </div>
            </div>
          </div>

          <!-- 标签信息 -->
          <div class="card">
            <div class="card-header">
              <h4>标签信息</h4>
            </div>
            <div class="card-body">
              <div class="detail-grid" v-if="currentTerminal.tags && Object.keys(currentTerminal.tags).length > 0">
                <div v-for="(value, key) in currentTerminal.tags" :key="key">
                  <div class="detail-item">
                    <div class="detail-label">{{ key }}:</div>
                    <div class="detail-value">{{ value }}</div>
                  </div>
                </div>
              </div>
              <div v-else class="no-data-message">
                暂无标签信息
              </div>
            </div>
          </div>

          <!-- 存储信息 -->
          <div class="card">
            <div class="card-header">
              <div class="storage-header">
                <h4>存储信息</h4>
                <div class="storage-toggle">
                  <button
                    class="storage-type-btn"
                    :class="{ active: currentStorageType === 'external' }"
                    @click="switchStorageType('external')"
                  >
                    外置存储
                  </button>
                  <button
                    class="storage-type-btn"
                    :class="{ active: currentStorageType === 'internal' }"
                    @click="switchStorageType('internal')"
                  >
                    内置存储
                  </button>
                </div>
              </div>
            </div>
            <div class="card-body">
              <div v-if="currentStorageInfo">
                <!-- 基本存储信息 -->
                <div class="storage-basic-info">
                  <div class="detail-grid">
                    <div class="detail-item">
                      <div class="detail-label">设备路径:</div>
                      <div class="detail-value">{{ currentStorageInfo.filesystem || currentStorageInfo.device || 'N/A' }}</div>
                    </div>
                    <div class="detail-item">
                      <div class="detail-label">总容量:</div>
                      <div class="detail-value">{{ formatBytes(currentStorageInfo.total || 0) }}</div>
                    </div>
                    <div class="detail-item">
                      <div class="detail-label">已使用:</div>
                      <div class="detail-value">{{ formatBytes(currentStorageInfo.used || 0) }}</div>
                    </div>
                    <div class="detail-item">
                      <div class="detail-label">可用空间:</div>
                      <div class="detail-value">{{ formatBytes(currentStorageInfo.available || 0) }}</div>
                    </div>
                    <div class="detail-item">
                      <div class="detail-label">使用率:</div>
                      <div class="detail-value">
                        <span :class="getStorageUsageClass(currentStorageInfo.percent || currentStorageInfo.percentage || 0)">
                          {{ (currentStorageInfo.percent || currentStorageInfo.percentage || 0).toFixed(1) }}%
                        </span>
                      </div>
                    </div>
                  </div>

                  <!-- 使用率进度条 -->
                  <div class="storage-progress">
                    <div class="progress-bar">
                      <div
                        class="progress-fill"
                        :class="getStorageUsageClass(currentStorageInfo.percent || currentStorageInfo.percentage || 0)"
                        :style="{ width: (currentStorageInfo.percent || currentStorageInfo.percentage || 0) + '%' }"
                      ></div>
                    </div>
                  </div>
                </div>

                <!-- 硬盘健康信息 -->
                <div v-if="currentStorageSmartmon" class="storage-health-info">
                  <h5>硬盘健康状况</h5>
                  <div class="health-grid">
                    <div class="health-item">
                      <div class="health-label">硬盘温度</div>
                      <div class="health-value">
                        <span :class="getTempClass(currentStorageSmartmon.temperature)">
                          {{ currentStorageSmartmon.temperature }}°C
                        </span>
                      </div>
                    </div>
                    <div class="health-item">
                      <div class="health-label">已使用寿命</div>
                      <div class="health-value">
                        <span :class="getLifeUsageClass(currentStorageSmartmon.percentage_used)">
                          {{ currentStorageSmartmon.percentage_used }}%
                        </span>
                      </div>
                    </div>
                    <div class="health-item">
                      <div class="health-label">总写入量(TBW)</div>
                      <div class="health-value">
                        {{ formatTotalBytesWritten(currentStorageSmartmon.data_units_written) }}
                      </div>
                    </div>
                    <div class="health-item">
                      <div class="health-label">可用备用空间</div>
                      <div class="health-value">{{ currentStorageSmartmon.available_spare }}%</div>
                    </div>
                    <div class="health-item">
                      <div class="health-label">电源循环次数</div>
                      <div class="health-value">{{ currentStorageSmartmon.power_cycles }}</div>
                    </div>
                    <div class="health-item">
                      <div class="health-label">通电时间</div>
                      <div class="health-value">{{ currentStorageSmartmon.power_on_hours }} 小时</div>
                    </div>
                  </div>
                </div>

                <!-- IO性能信息 -->
                <div v-if="currentStorageInfo.io_rate" class="storage-io-info">
                  <h5>IO性能</h5>
                  <div class="io-grid">
                    <div class="io-item">
                      <div class="io-label">读取速率</div>
                      <div class="io-value">{{ formatDiskIOSpeed(currentStorageInfo.io_rate['MBr/s']) }}</div>
                    </div>
                    <div class="io-item">
                      <div class="io-label">写入速率</div>
                      <div class="io-value">{{ formatDiskIOSpeed(currentStorageInfo.io_rate['MBw/s']) }}</div>
                    </div>
                    <div class="io-item">
                      <div class="io-label">读取频率</div>
                      <div class="io-value">{{ currentStorageInfo.io_rate['read/s'] || 0 }}/s</div>
                    </div>
                    <div class="io-item">
                      <div class="io-label">写入频率</div>
                      <div class="io-value">{{ currentStorageInfo.io_rate['write/s'] || 0 }}/s</div>
                    </div>
                  </div>
                </div>
              </div>
              <div v-else class="no-data-message">
                {{ currentStorageType === 'external' ? '暂无外置存储信息' : '暂无内置存储信息' }}
              </div>
            </div>
          </div>

          <!-- 自定义字段 -->
          <div class="card">
            <div class="card-header">
              <h4>自定义字段</h4>
            </div>
            <div class="card-body">
              <div class="detail-grid" v-if="currentTerminal.customFields && Object.keys(currentTerminal.customFields).length > 0">
                <div v-for="(value, key) in currentTerminal.customFields" :key="key">
                  <div class="detail-item">
                    <div class="detail-label">{{ key }}:</div>
                    <div class="detail-value">{{ value }}</div>
                  </div>
                </div>
              </div>
              <div v-else class="no-data-message">
                暂无自定义字段
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 编辑终端模态框 -->
    <div v-if="editDialogVisible" class="modal" @click="handleEditClose">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>编辑终端</h3>
          <span class="close" @click="handleEditClose">&times;</span>
        </div>
        <div class="modal-body">
          <div class="card">
            <div class="card-header">
              <h4>基本信息</h4>
            </div>
            <div class="card-body">
              <div class="detail-grid">
                <div class="form-group">
                  <label>设备ID</label>
                  <input type="text" class="form-control" v-model="editForm.deviceId">
                </div>
                <div class="form-group">
                  <label>设备MAC地址</label>
                  <input type="text" class="form-control" v-model="editForm.identityMac" readonly>
                </div>
                <div class="form-group">
                  <label>主机名</label>
                  <input type="text" class="form-control" v-model="editForm.hostname">
                </div>
                <div class="form-group">
                  <label>过期时间</label>
                  <input type="text" class="form-control" v-model="editForm.expiredDate">
                </div>
              </div>
            </div>
          </div>

          <div class="card">
            <div class="card-header">
              <h4>标签管理</h4>
            </div>
            <div class="card-body">
              <div class="form-group">
                <label>添加标签</label>
                <div class="tag-input-container">
                  <input 
                    type="text" 
                    class="form-control" 
                    placeholder="标签键" 
                    v-model="newTagKey"
                    style="width: 120px; margin-right: 10px"
                  >
                  <input 
                    type="text" 
                    class="form-control" 
                    placeholder="标签值" 
                    v-model="newTagValue"
                    style="width: 120px; margin-right: 10px"
                  >
                  <button class="btn btn-primary btn-sm" @click="addTag">添加</button>
                </div>
              </div>
              <div class="form-group">
                <label>现有标签</label>
                <div class="tags-container">
                  <span 
                    v-for="(value, key) in editForm.tags" 
                    :key="key" 
                    class="tag"
                  >
                    {{ key }}: {{ value }} 
                    <span class="tag-remove" @click="removeTag(key)">×</span>
                  </span>
                </div>
              </div>
            </div>
          </div>

          <div class="card">
            <div class="card-header">
              <h4>自定义字段</h4>
            </div>
            <div class="card-body">
              <div class="form-group">
                <label>添加字段</label>
                <div class="tag-input-container">
                  <input 
                    type="text" 
                    class="form-control" 
                    placeholder="字段名" 
                    v-model="newFieldKey"
                    style="width: 120px; margin-right: 10px"
                  >
                  <input 
                    type="text" 
                    class="form-control" 
                    placeholder="字段值" 
                    v-model="newFieldValue"
                    style="width: 120px; margin-right: 10px"
                  >
                  <button class="btn btn-primary btn-sm" @click="addCustomField">添加</button>
                </div>
              </div>
              <div class="form-group">
                <label>现有字段</label>
                <div class="custom-fields">
                  <span 
                    v-for="(value, key) in editForm.customFields" 
                    :key="key" 
                    class="tag"
                  >
                    {{ key }}: {{ value }} 
                    <span class="tag-remove" @click="removeCustomField(key)">×</span>
                  </span>
                </div>
              </div>
            </div>
          </div>

          <div style="text-align: center; margin-top: 20px;">
            <button class="btn btn-primary" @click="saveTerminal">保存</button>
            <button class="btn btn-secondary" @click="handleEditClose">取消</button>
          </div>
        </div>
      </div>
    </div>

    <!-- 删除确认模态框 -->
    <div v-if="deleteConfirmVisible" class="modal" @click="deleteConfirmVisible = false">
      <div class="modal-content" style="max-width: 400px;" @click.stop>
        <div class="modal-header">
          <h3>确认删除</h3>
          <span class="close" @click="deleteConfirmVisible = false">&times;</span>
        </div>
        <div class="modal-body">
          <div class="alert alert-warning">
            <strong>警告!</strong> 您确定要删除这个离线终端吗？此操作不可恢复。
          </div>
          <div style="text-align: center; margin-top: 20px;">
            <button class="btn btn-danger" @click="confirmDelete">确认删除</button>
            <button class="btn btn-secondary" @click="deleteConfirmVisible = false">取消</button>
          </div>
        </div>
      </div>
    </div>

    <!-- 日志管理弹框 -->
    <div v-if="logFilesDialogVisible" class="modal" @click="handleLogFilesClose">
      <div class="modal-content large-modal" @click.stop>
        <div class="modal-header">
          <h3>日志文件管理 - {{ currentLogTerminal?.hostname || currentLogTerminal?.identityMac }}</h3>
          <span class="close" @click="handleLogFilesClose">&times;</span>
        </div>
        <div class="modal-body">
          <div class="log-files-container">
            <!-- 文件列表 -->
            <div class="card">
              <div class="card-header">
                <h4>日志文件列表</h4>
                <button class="btn btn-primary btn-sm" @click="refreshLogFiles">刷新</button>
              </div>
              <div class="card-body">
                <div v-if="loadingLogFiles" class="loading-message">
                  正在加载日志文件...
                </div>
                <div v-else-if="logFiles.length === 0" class="empty-message">
                  暂无日志文件
                </div>
                <div v-else class="log-files-table">
                  <table class="table">
                    <thead>
                      <tr>
                        <th>文件名</th>
                        <th>文件大小</th>
                        <th>上传时间</th>
                        <th>操作</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr v-for="file in logFiles" :key="file.id">
                        <td>{{ file.originalFilename }}</td>
                        <td>{{ formatBytes(file.fileSize) }}</td>
                        <td>{{ formatDateTime(file.uploadTime) }}</td>
                        <td>
                          <button
                            class="btn btn-info btn-sm"
                            @click="downloadLogFile(file)"
                          >
                            下载
                          </button>
                          <button
                            class="btn btn-danger btn-sm"
                            @click="deleteLogFile(file)"
                          >
                            删除
                          </button>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button class="btn btn-secondary" @click="handleLogFilesClose">关闭</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useTerminalStore } from '@/stores'
import { useAuthStore } from '@/stores/auth'
import { logFileApi } from '@/api/services'

const router = useRouter()
const route = useRoute()
const terminalStore = useTerminalStore()
const authStore = useAuthStore()

// 权限检查
const isAdmin = computed(() => authStore.isAdmin)

// 搜索表单
const searchForm = reactive({
  identityMac: '',
  hostname: '',
  status: '',
  tag: ''
})

// 分页
const pagination = reactive({
  page: 1,
  pageSize: 10
})

// 对话框状态
const detailDialogVisible = ref(false)
const editDialogVisible = ref(false)
const deleteConfirmVisible = ref(false)
const logFilesDialogVisible = ref(false)
const currentTerminal = ref(null)
const currentDeleteTerminal = ref(null)
const currentLogTerminal = ref(null)

// 日志文件管理
const logFiles = ref([])
const loadingLogFiles = ref(false)

// 编辑表单
const editForm = reactive({
  deviceId: '',
  identityMac: '',
  hostname: '',
  expiredDate: '',
  tags: {},
  customFields: {}
})

// 标签和自定义字段相关
const newTagKey = ref('')
const newTagValue = ref('')
const newFieldKey = ref('')
const newFieldValue = ref('')

// 存储信息相关
const currentStorageType = ref('external') // 默认显示外置存储

// 拓扑图数据（将被API返回的真实数据替换）
const mockGroupUsage = reactive({
  group_id: 'N/A',
  buckets: [],
  pps_total: 0,
  bps_total: 0,
})

// 计算属性
const totalPages = computed(() => {
  return Math.ceil(terminalStore.total / pagination.pageSize)
})

const visiblePages = computed(() => {
  const current = pagination.page
  const total = totalPages.value
  const pages = []
  
  let start = Math.max(1, current - 2)
  let end = Math.min(total, current + 2)
  
  for (let i = start; i <= end; i++) {
    pages.push(i)
  }
  
  return pages
})

// 存储信息计算属性
const currentStorageInfo = computed(() => {
  if (!currentTerminal.value || !currentTerminal.value.diskUsage) return null

  if (currentStorageType.value === 'external') {
    // 外置存储 - diskUsage[1]
    return currentTerminal.value.diskUsage[1] || null
  } else {
    // 内置存储 - diskUsage[0]
    return currentTerminal.value.diskUsage[0] || null
  }
})

const currentStorageSmartmon = computed(() => {
  if (!currentStorageInfo.value || !currentStorageInfo.value.smartmon) return null

  // 检查smartmon是否为空对象
  const smartmon = currentStorageInfo.value.smartmon
  return Object.keys(smartmon).length > 0 ? smartmon : null
})

// 初始化加载
onMounted(async () => {
  // 检查URL参数
  if (route.query.identityMac) {
    searchForm.identityMac = route.query.identityMac
    // 自动执行搜索
    await handleSearch()
  } else {
    await loadTerminals()
  }
})

// 监听分页变化
watch([() => pagination.page, () => pagination.pageSize], () => {
  loadTerminals()
})

// 加载终端列表
const loadTerminals = async () => {
  try {
    await terminalStore.fetchTerminals({
      page: pagination.page,
      pageSize: pagination.pageSize
    })
  } catch (error) {
    ElMessage.error('加载终端列表失败')
  }
}

// 搜索
const handleSearch = async () => {
  terminalStore.setFilters(searchForm)
  pagination.page = 1
  await loadTerminals()
}

// 重置搜索
const handleReset = async () => {
  Object.assign(searchForm, {
    identityMac: '',
    hostname: '',
    status: '',
    tag: ''
  })
  terminalStore.resetFilters()
  pagination.page = 1
  await loadTerminals()
}

// 分页处理
const prevPage = () => {
  if (pagination.page > 1) {
    pagination.page--
  }
}

const nextPage = () => {
  if (pagination.page < totalPages.value) {
    pagination.page++
  }
}

const goToPage = (page) => {
  pagination.page = page
}

// 查看实时指标
const viewRealTimeMetrics = (identityMac) => {
  router.push({
    name: 'RealTimeMetrics',
    params: { identityMac }
  })
}

// 查看历史指标
const viewHistoryMetrics = (identityMac) => {
  router.push({
    name: 'HistoryMetrics',
    params: { identityMac }
  })
}

// 清理groupUsage数据中的字段名空格
const cleanGroupUsageData = (groupUsage) => {
  if (!groupUsage) {
    console.log('groupUsage为空，返回默认值')
    return {
      group_id: 'N/A',
      buckets: [],
      pps_total: 0,
      bps_total: 0
    }
  }

  console.log('原始groupUsage数据:', groupUsage)

  const cleaned = {}

  // 清理顶级字段名的空格
  Object.keys(groupUsage).forEach(key => {
    const cleanKey = key.trim()
    cleaned[cleanKey] = groupUsage[key]
    if (key !== cleanKey) {
      console.log(`字段名清理: "${key}" -> "${cleanKey}"`)
    }
  })

  // 特别处理buckets数组
  if (cleaned.buckets && Array.isArray(cleaned.buckets)) {
    console.log(`处理buckets数组，共${cleaned.buckets.length}个bucket`)
    cleaned.buckets = cleaned.buckets.map((bucket, index) => {
      const cleanedBucket = {}
      Object.keys(bucket).forEach(key => {
        const cleanKey = key.trim()
        cleanedBucket[cleanKey] = bucket[key]
      })
      console.log(`Bucket ${index}:`, cleanedBucket)
      return cleanedBucket
    })
  } else {
    console.log('buckets字段不存在或不是数组')
    cleaned.buckets = []
  }

  // 确保数值字段存在
  cleaned.pps_total = cleaned.pps_total || 0
  cleaned.bps_total = cleaned.bps_total || 0
  cleaned.group_id = cleaned.group_id || 'N/A'

  console.log('清理后的groupUsage数据:', cleaned)
  return cleaned
}

// 查看终端详情
const viewTerminalDetail = async (terminal) => {
  try {
    // 获取终端详情（包含最新指标信息）
    const response = await terminalStore.fetchTerminalDetail(terminal.identityMac)

    // 处理JSON字符串字段
    const processedResponse = { ...response }

    // 解析appVersion字段
    if (typeof processedResponse.appVersion === 'string') {
      try {
        processedResponse.appVersion = JSON.parse(processedResponse.appVersion)
      } catch (e) {
        console.warn('解析appVersion失败:', e)
        processedResponse.appVersion = {}
      }
    }

    // 解析tags字段
    if (typeof processedResponse.tags === 'string') {
      try {
        processedResponse.tags = JSON.parse(processedResponse.tags)
      } catch (e) {
        console.warn('解析tags失败:', e)
        processedResponse.tags = {}
      }
    }

    // 解析customFields字段
    if (typeof processedResponse.customFields === 'string') {
      try {
        processedResponse.customFields = JSON.parse(processedResponse.customFields)
      } catch (e) {
        console.warn('解析customFields失败:', e)
        processedResponse.customFields = {}
      }
    }

    currentTerminal.value = processedResponse

    // 更新拓扑图数据
    if (response.groupUsage) {
      // 处理API返回的groupUsage数据，清理字段名中的空格
      const cleanGroupUsage = cleanGroupUsageData(response.groupUsage)
      Object.assign(mockGroupUsage, cleanGroupUsage)
      console.log('更新拓扑图数据:', cleanGroupUsage)
    } else {
      // 如果没有CPE信息，显示空状态
      mockGroupUsage.buckets = []
      mockGroupUsage.pps_total = 0
      mockGroupUsage.bps_total = 0
      mockGroupUsage.group_id = 'N/A'
    }

    detailDialogVisible.value = true
  } catch (error) {
    ElMessage.error('获取终端详情失败')
    console.error('获取终端详情失败:', error)
  }
}

// 编辑终端
const editTerminal = (terminal) => {
  // 处理可能的JSON字符串字段
  let tags = terminal.tags || {}
  let customFields = terminal.customFields || {}

  // 如果是字符串，尝试解析为JSON对象
  if (typeof tags === 'string') {
    try {
      tags = JSON.parse(tags)
    } catch (e) {
      console.warn('解析tags失败:', e)
      tags = {}
    }
  }

  if (typeof customFields === 'string') {
    try {
      customFields = JSON.parse(customFields)
    } catch (e) {
      console.warn('解析customFields失败:', e)
      customFields = {}
    }
  }

  Object.assign(editForm, {
    deviceId: terminal.deviceId,
    identityMac: terminal.identityMac,
    hostname: terminal.hostname,
    expiredDate: terminal.expiredDate,
    tags: { ...tags },
    customFields: { ...customFields }
  })
  editDialogVisible.value = true
}

// 删除终端
const deleteTerminal = (terminal) => {
  currentDeleteTerminal.value = terminal
  deleteConfirmVisible.value = true
}

const confirmDelete = async () => {
  try {
    await terminalStore.deleteTerminal(currentDeleteTerminal.value.identityMac)
    ElMessage.success('删除成功')
    deleteConfirmVisible.value = false
    await loadTerminals()
  } catch (error) {
    ElMessage.error('删除失败')
  }
}

// 添加标签
const addTag = () => {
  if (newTagKey.value && newTagValue.value) {
    editForm.tags[newTagKey.value] = newTagValue.value
    newTagKey.value = ''
    newTagValue.value = ''
  } else {
    ElMessage.warning('请输入标签键和值')
  }
}

// 移除标签
const removeTag = (key) => {
  delete editForm.tags[key]
}

// 添加自定义字段
const addCustomField = () => {
  if (newFieldKey.value && newFieldValue.value) {
    editForm.customFields[newFieldKey.value] = newFieldValue.value
    newFieldKey.value = ''
    newFieldValue.value = ''
  } else {
    ElMessage.warning('请输入字段名和值')
  }
}

// 移除自定义字段
const removeCustomField = (key) => {
  delete editForm.customFields[key]
}

// 保存终端
const saveTerminal = async () => {
  try {
    const updateData = {
      deviceId: editForm.deviceId,
      hostname: editForm.hostname,
      expiredDate: editForm.expiredDate,
      tags: editForm.tags,
      customFields: editForm.customFields
    }
    
    await terminalStore.updateTerminal(editForm.identityMac, updateData)
    ElMessage.success('保存成功')
    editDialogVisible.value = false
    await loadTerminals()
  } catch (error) {
    ElMessage.error('保存失败')
  }
}

// 对话框关闭处理
const handleDetailClose = () => {
  detailDialogVisible.value = false
  currentTerminal.value = null
}

const handleEditClose = () => {
  editDialogVisible.value = false
  Object.assign(editForm, {
    deviceId: '',
    hostname: '',
    expiredDate: '',
    tags: {},
    customFields: {}
  })
}

// Helper for formatting bytes
const formatBytes = (bytes, decimals = 2) => {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
}

// 日志文件管理相关方法
const viewLogFiles = async (terminal) => {
  try {
    currentLogTerminal.value = terminal
    logFilesDialogVisible.value = true
    await loadLogFiles()
  } catch (error) {
    console.error('打开日志管理失败:', error)
    ElMessage.error('打开日志管理失败')
  }
}

const loadLogFiles = async () => {
  if (!currentLogTerminal.value) return

  try {
    console.log('开始加载日志文件...')
    loadingLogFiles.value = true
    const response = await logFileApi.getAllLogFilesByTerminal(currentLogTerminal.value.identityMac)

    console.log('API响应:', response)
    console.log('响应code:', response.code)
    console.log('响应data:', response.data)

    if (response.code === 200) {
      logFiles.value = response.data || []
      console.log('设置logFiles:', logFiles.value)
    } else {
      throw new Error(response.message || '获取日志文件列表失败')
    }
  } catch (error) {
    console.error('加载日志文件失败:', error)
    ElMessage.error('加载日志文件失败: ' + error.message)
    logFiles.value = []
  } finally {
    console.log('设置loadingLogFiles为false')
    loadingLogFiles.value = false
  }
}

const refreshLogFiles = async () => {
  await loadLogFiles()
  ElMessage.success('刷新成功')
}

const downloadLogFile = async (file) => {
  let loadingMessage = null
  
  try {
    console.log('开始下载文件:', file.originalFilename)
    
    // 显示下载进度提示
    loadingMessage = ElMessage({
      message: `正在下载 ${file.originalFilename}...`,
      type: 'info',
      duration: 0, // 不自动关闭
      showClose: true
    })

    // 使用axios下载文件，会自动携带认证头
    const response = await logFileApi.downloadFile(file.id)

    // 关闭加载提示
    if (loadingMessage) {
      loadingMessage.close()
      loadingMessage = null
    }

    // 创建blob对象
    const blob = new Blob([response], { type: 'application/octet-stream' })

    // 创建下载链接
    const downloadUrl = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = downloadUrl
    link.download = file.originalFilename
    link.style.display = 'none'
    
    // 添加到DOM并触发下载
    document.body.appendChild(link)
    link.click()

    // 清理DOM和URL
    document.body.removeChild(link)
    window.URL.revokeObjectURL(downloadUrl)

    console.log('文件下载成功:', file.originalFilename)
    ElMessage.success(`文件 ${file.originalFilename} 下载成功`)
    
  } catch (error) {
    console.error('下载文件失败:', error)
    
    // 关闭加载提示
    if (loadingMessage) {
      loadingMessage.close()
    }
    
    // 根据错误类型显示不同的错误信息
    let errorMessage = '下载文件失败'
    
    if (error.code === 'ERR_NETWORK') {
      errorMessage = '网络连接失败，请检查网络或稍后重试'
    } else if (error.response?.status === 404) {
      errorMessage = '文件不存在或已被删除'
    } else if (error.response?.status === 403) {
      errorMessage = '没有权限访问该文件'
    } else if (error.response?.status === 401) {
      errorMessage = '认证失败，请重新登录'
    } else if (error.response?.status === 500) {
      errorMessage = '服务器内部错误，请稍后重试'
    } else if (error.message?.includes('timeout')) {
      errorMessage = '下载超时，请检查网络连接或稍后重试'
    } else if (error.message) {
      errorMessage = `下载失败: ${error.message}`
    }
    
    ElMessage.error(errorMessage)
  }
}

const deleteLogFile = async (file) => {
  if (!confirm(`确定要删除文件 "${file.originalFilename}" 吗？`)) {
    return
  }

  try {
    const response = await logFileApi.deleteLogFile(file.id)

    if (response.code === 200) {
      ElMessage.success('删除文件成功')
      await loadLogFiles() // 重新加载文件列表
    } else {
      throw new Error(response.message || '删除文件失败')
    }
  } catch (error) {
    console.error('删除文件失败:', error)
    ElMessage.error('删除文件失败: ' + error.message)
  }
}

const handleLogFilesClose = () => {
  logFilesDialogVisible.value = false
  currentLogTerminal.value = null
  logFiles.value = []
}

const formatDateTime = (dateTime) => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 存储相关函数
const switchStorageType = (type) => {
  currentStorageType.value = type
}

const getStorageUsageClass = (usage) => {
  if (usage >= 90) return 'usage-critical'
  if (usage >= 80) return 'usage-warning'
  if (usage >= 60) return 'usage-normal'
  return 'usage-good'
}

const getTempClass = (temp) => {
  if (temp >= 70) return 'temp-critical'
  if (temp >= 60) return 'temp-warning'
  if (temp >= 50) return 'temp-normal'
  return 'temp-good'
}

const getLifeUsageClass = (usage) => {
  if (usage >= 80) return 'life-critical'
  if (usage >= 60) return 'life-warning'
  if (usage >= 40) return 'life-normal'
  return 'life-good'
}

const formatTotalBytesWritten = (dataUnitsWritten) => {
  if (!dataUnitsWritten) return '0 GB'
  // data_units_written * 512 / 1000 / 1000 / 1000 单位为GB
  const gbWritten = (dataUnitsWritten * 512) / (1000 * 1000 * 1000)
  return `${gbWritten.toFixed(2)} GB`
}

const formatDiskIOSpeed = (mbPerSecond) => {
  if (!mbPerSecond || mbPerSecond === 0) return '0 MB/s'
  return mbPerSecond.toFixed(2) + ' MB/s'
}
</script>

<style scoped>
.terminals-page {
  /* 继承通用样式 */
}

/* 模态框样式 */
.modal {
  display: block;
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0,0,0,0.5);
}

.modal-content {
  background-color: white;
  margin: 5% auto;
  padding: 20px;
  border-radius: 8px;
  width: 80%;
  max-width: 800px;
  max-height: 80vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

.close {
  font-size: 24px;
  font-weight: bold;
  cursor: pointer;
  color: #999;
}

.close:hover {
  color: #333;
}

/* 详情布局 */
.detail-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 20px;
}

.detail-item {
  display: flex;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.detail-item:last-child {
  border-bottom: none;
}

.detail-label {
  font-weight: 600;
  color: #666;
  min-width: 120px;
  margin-right: 10px;
}

.detail-value {
  color: #333;
  flex: 1;
}

/* 标签样式 */
.tag-input-container {
  display: flex;
  align-items: center;
  gap: 10px;
  flex-wrap: wrap;
}

.tags-container,
.custom-fields {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
}

.tag {
  background: #667eea;
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 5px;
}

.tag-remove {
  cursor: pointer;
  font-weight: bold;
}

/* 告警样式 */
.alert {
  padding: 15px;
  margin-bottom: 20px;
  border-radius: 4px;
  border-left: 4px solid;
}

.alert-warning {
  background: #fff3cd;
  border-color: #ffc107;
  color: #856404;
}

/* 拓扑关系图样式 */
.topology-container {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 20px;
  margin: 20px 0;
  position: relative;
}

.topology-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 20px;
  color: #495057;
  text-align: center;
}

.topology-graph {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 30px;
}

.main-terminal {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 15px 25px;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
  text-align: center;
  min-width: 200px;
}

.main-terminal-title {
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 5px;
}

.main-terminal-id {
  font-size: 12px;
  opacity: 0.9;
}

.connection-line {
  width: 2px;
  height: 40px;
  background: linear-gradient(to bottom, #667eea, #ddd);
  position: relative;
}

.connection-line::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 8px;
  height: 8px;
  background: #667eea;
  border-radius: 50%;
}

.cpe-container {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  justify-content: center;
  max-width: 600px;
}

.cpe-item {
  background: white;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  padding: 12px;
  min-width: 120px;
  text-align: center;
  transition: all 0.3s;
  position: relative;
}

.cpe-item:hover {
  border-color: #667eea;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.cpe-item.active {
  border-color: #28a745;
  background: #f8fff9;
}

.cpe-item.inactive {
  border-color: #dc3545;
  background: #fff8f8;
}

.cpe-title {
  font-size: 12px;
  font-weight: 600;
  color: #666;
  margin-bottom: 5px;
}

.cpe-bucket {
  font-size: 14px;
  font-weight: bold;
  color: #333;
  margin-bottom: 3px;
}

.cpe-stats {
  font-size: 10px;
  color: #999;
  line-height: 1.2;
}

.cpe-status {
  position: absolute;
  top: -5px;
  right: -5px;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 2px solid white;
}

.cpe-status.online {
  background: #28a745;
}

.cpe-status.offline {
  background: #dc3545;
}

.topology-summary {
  background: #e3f2fd;
  border: 1px solid #bbdefb;
  border-radius: 6px;
  padding: 15px;
  margin-top: 20px;
}

.summary-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 15px;
}

.summary-item {
  text-align: center;
}

.summary-value {
  font-size: 18px;
  font-weight: bold;
  color: #1976d2;
  margin-bottom: 5px;
}

.summary-label {
  font-size: 12px;
  color: #666;
}

.no-cpe-message {
  text-align: center;
  color: #999;
  font-style: italic;
  padding: 20px;
}

.no-data-message {
  text-align: center;
  color: #999;
  font-style: italic;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 4px;
}

/* 搜索过滤器样式 */
.search-filters {
  display: flex;
  gap: 15px;
  margin-bottom: 20px;
  flex-wrap: wrap;
  align-items: end; /* 将所有项目底部对齐 */
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group label {
  margin-bottom: 5px;
  font-weight: 500;
  color: #555;
  min-height: 20px; /* 确保标签有统一高度 */
}

.form-control {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  min-width: 120px;
}

.form-control:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

/* 按钮组样式 */
.button-group {
  align-self: flex-end; /* 确保按钮组在底部 */
}

.button-container {
  display: flex;
  gap: 10px;
}

/* 按钮样式 */
.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s;
  text-decoration: none;
  display: inline-block;
}

.btn-primary {
  background: #667eea;
  color: white;
}

.btn-primary:hover {
  background: #5a6fd8;
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background: #5a6268;
}

.btn-warning {
  background: #ffc107;
  color: #212529;
}

.btn-warning:hover {
  background: #e0a800;
}

.btn-danger {
  background: #dc3545;
  color: white;
}

.btn-danger:hover {
  background: #c82333;
}

.btn-success {
  background: #28a745;
  color: white;
}

.btn-success:hover {
  background: #218838;
}

.btn-info {
  background: #17a2b8;
  color: white;
}

.btn-info:hover {
  background: #138496;
}

.btn-sm {
  padding: 5px 10px;
  font-size: 12px;
}

/* 表格样式 */
.table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 15px;
}

.table th,
.table td {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid #eee;
}

.table th {
  background: #f8f9fa;
  font-weight: 600;
  color: #495057;
}

.table tr:hover {
  background: #f8f9fa;
}

/* 分页样式 */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
  margin-top: 20px;
}

.pagination button {
  padding: 8px 12px;
  border: 1px solid #ddd;
  background: white;
  cursor: pointer;
  border-radius: 4px;
}

.pagination button:hover:not(:disabled) {
  background: #f8f9fa;
}

.pagination button.active {
  background: #667eea;
  color: white;
  border-color: #667eea;
}

.pagination button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  background: white !important;
  color: #999 !important;
  border-color: #ddd !important;
}

/* 操作按钮组 */
.action-buttons {
  display: flex;
  gap: 5px;
  flex-wrap: wrap;
}

/* 状态样式 */
.status-online {
  color: #28a745;
  font-weight: 600;
}

.status-offline {
  color: #dc3545;
  font-weight: 600;
}

/* 徽章样式 */
.badge {
  display: inline-block;
  padding: 4px 8px;
  font-size: 12px;
  font-weight: 500;
  border-radius: 12px;
  text-align: center;
  white-space: nowrap;
}

.badge-info {
  background: #17a2b8;
  color: white;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .search-filters {
    flex-direction: column;
    align-items: stretch;
  }
  
  .form-group {
    margin-bottom: 10px;
  }
  
  .action-buttons {
    flex-direction: column;
    gap: 5px;
  }
  
  .action-buttons .btn {
    width: 100%;
  }
  
  .modal-content {
    width: 95%;
    margin: 2vh auto;
  }
  
  .detail-grid {
    grid-template-columns: 1fr;
  }
  
  .tag-input-container {
    flex-direction: column;
    align-items: stretch;
  }
}

/* 日志管理弹框样式 */
.large-modal {
  width: 90%;
  max-width: 1000px;
}

.log-files-container {
  max-height: 600px;
  overflow-y: auto;
}

.log-files-table {
  overflow-x: auto;
}

.log-files-table table {
  width: 100%;
  min-width: 600px;
}

.log-files-table th,
.log-files-table td {
  padding: 8px 12px;
  text-align: left;
  border-bottom: 1px solid #eee;
}

.log-files-table th {
  background-color: #f8f9fa;
  font-weight: 600;
}

.log-files-table .btn {
  margin-right: 5px;
}

.loading-message,
.empty-message {
  text-align: center;
  padding: 40px 20px;
  color: #666;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 存储信息样式 */
.storage-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.storage-toggle {
  display: flex;
  gap: 8px;
}

.storage-type-btn {
  padding: 6px 12px;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  background: #f8f9fa;
  color: #495057;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.storage-type-btn:hover {
  border-color: #1a73e8;
  color: #1a73e8;
}

.storage-type-btn.active {
  background: #1a73e8;
  border-color: #1a73e8;
  color: white;
}

.storage-basic-info {
  margin-bottom: 20px;
}

.storage-progress {
  margin-top: 12px;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: #f1f3f4;
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  transition: width 0.3s ease;
}

.usage-good { background: #34a853; }
.usage-normal { background: #fbbc04; }
.usage-warning { background: #ea4335; }
.usage-critical { background: #d93025; }

.storage-health-info {
  margin-bottom: 20px;
}

.storage-health-info h5 {
  margin: 0 0 12px 0;
  color: #495057;
  font-size: 14px;
  font-weight: 600;
}

.health-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 12px;
}

.health-item {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 12px;
  text-align: center;
}

.health-label {
  font-size: 11px;
  color: #6c757d;
  margin-bottom: 4px;
  font-weight: 500;
}

.health-value {
  font-size: 14px;
  font-weight: 600;
}

.temp-good { color: #34a853; }
.temp-normal { color: #fbbc04; }
.temp-warning { color: #ff9800; }
.temp-critical { color: #ea4335; }

.life-good { color: #34a853; }
.life-normal { color: #fbbc04; }
.life-warning { color: #ff9800; }
.life-critical { color: #ea4335; }

.storage-io-info h5 {
  margin: 0 0 12px 0;
  color: #495057;
  font-size: 14px;
  font-weight: 600;
}

.io-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 12px;
}

.io-item {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 12px;
  text-align: center;
}

.io-label {
  font-size: 11px;
  color: #6c757d;
  margin-bottom: 4px;
  font-weight: 500;
}

.io-value {
  font-size: 13px;
  font-weight: 600;
  color: #495057;
}

.no-data-message {
  text-align: center;
  color: #999;
  font-style: italic;
  padding: 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .storage-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .storage-toggle {
    justify-content: center;
  }

  .health-grid {
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 8px;
  }

  .io-grid {
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: 8px;
  }
}
</style>