package com.unnet.jmanul.business.service.dto.overview;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 离线终端响应
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "离线终端响应")
public class OfflineTerminalResp {

    @ApiModelProperty(value = "设备ID", example = "_0fd94938951f4a64bb11c6817a81f7e7")
    private String deviceId;

    @ApiModelProperty(value = "主机名", example = "Terminal-001")
    private String hostname;

    @ApiModelProperty(value = "设备状态", example = "0")
    private Integer status;

    @ApiModelProperty(value = "最后更新时间", example = "2025-07-17T10:25:30")
    private String lastUpdateTime;

    @ApiModelProperty(value = "离线时长（分钟）", example = "15")
    private Long offlineDurationMinutes;

    @ApiModelProperty(value = "用户定义标签", example = "{\"location\":\"北京\",\"department\":\"IT\"}")
    private String userTags;

    @ApiModelProperty(value = "自定义字段", example = "{\"contact\":\"张三\",\"phone\":\"13800138000\"}")
    private String customFields;
}
