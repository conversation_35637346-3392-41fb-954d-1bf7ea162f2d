package com.unnet.jmanul.system.service.impl;

import com.unnet.jmanul.common.utils.DateTimeUtils;
import com.unnet.jmanul.common.utils.jwt.IssueRequest;
import com.unnet.jmanul.system.entity.User;
import com.unnet.jmanul.system.service.IUserService;
import com.unnet.jmanul.system.service.impl.LoginAttemptService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

@Service("userDetailsService")
@Transactional
@RequiredArgsConstructor
@Slf4j
public class MyUserDetailsService implements UserDetailsService {


    private final LoginAttemptService loginAttemptService;
    private final IUserService userService;

    //
    // API
    //

    @Override
    public UserDetails loadUserByUsername(final String username) throws UsernameNotFoundException {
        if (loginAttemptService.isBlocked()) {
            throw new RuntimeException("blocked");
        }

        try {
            var userOpt = userService.getUserByUsername(username);
            if (userOpt.isEmpty()) {
                throw new UsernameNotFoundException("No user found with username: " + username);
            }
            var user = userOpt.get();

            log.info("Loading user details for username: {}, stored password: {}", username, user.getPassword());

            IssueRequest request = new IssueRequest();
            BeanUtils.copyProperties(user, request);

            List<String> roles = Collections.emptyList();
            return new org.springframework.security.core.userdetails.User(
                    user.getUsername(),
                    user.getPassword(),
                    user.getEnable(),
                    isAccountNonExpired(user),
                    isCredentialsNonExpired(user),
                    isAccountNonLocked(user),
                    getAuthorities(roles));
        } catch (final Exception e) {
            throw new RuntimeException(e);
        }
    }

    //
    // UTIL
    //

    private Collection<? extends GrantedAuthority> getAuthorities(final List<String> roles) {
        final List<GrantedAuthority> authorities = new ArrayList<>();
        roles.forEach(role -> {
            authorities.add(new SimpleGrantedAuthority(role));
        });
        return authorities;
    }

    private boolean isAccountNonExpired(User user) {
        return DateTimeUtils.isNonExpired(user.getAccountExpireDate());
    }

    private boolean isAccountNonLocked(User user) {
        return !user.getLocked();
    }

    private boolean isCredentialsNonExpired(User user) {
        return DateTimeUtils.isNonExpired(user.getCredentialExpireDate());
    }
}
