# SSE连接问题排查指南

## 当前问题

前端请求 `http://localhost:8081/api/device/notifications/sse` 返回 404 Not Found，但后端日志显示路径跳过了API Key验证，说明请求到达了后端，但出现了媒体类型协商问题。

## 问题分析

### 1. 404 vs 媒体类型协商错误

- **404错误**: 表示路径不存在
- **HttpMediaTypeNotAcceptableException**: 表示路径存在但无法提供客户端期望的媒体类型

### 2. 前端请求分析

前端应该通过Vite代理访问：
```
前端请求: /api/device/notifications/sse (相对路径)
Vite代理: http://localhost:8081/api/device/notifications/sse
```

但实际请求显示为：`http://localhost:8081/api/device/notifications/sse`，说明没有通过代理。

## 解决方案

### 1. 后端修复

#### 已完成的修复：
- ✅ 添加了 `produces = MediaType.TEXT_EVENT_STREAM_VALUE`
- ✅ 手动设置响应头
- ✅ 添加了CORS配置
- ✅ 创建了简单的SSE测试端点
- ✅ 添加了OPTIONS预检请求处理

#### 当前后端配置：
```java
@CrossOrigin(origins = "*", allowedHeaders = "*")
@GetMapping(value = "/sse", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
public SseEmitter subscribeToDeviceNotifications(
        HttpServletRequest request, HttpServletResponse response) {
    
    // 手动设置SSE响应头
    response.setContentType("text/event-stream; charset=UTF-8");
    response.setHeader("Cache-Control", "no-cache");
    response.setHeader("Connection", "keep-alive");
    // ... 其他CORS头部
    
    return deviceNotificationService.subscribe();
}
```

### 2. 前端修复

#### 确保使用代理：
前端应该使用相对路径 `/api/device/notifications/sse`，而不是绝对路径。

#### 当前前端配置：
```javascript
// sseService.js
this.sseEndpoint = this.baseUrl ? 
  `${this.baseUrl}/api/device/notifications/sse` : 
  '/api/device/notifications/sse'

// .env.development
VITE_SSE_BASE_URL=  // 空值，使用相对路径
```

## 测试步骤

### 1. 重启服务

```bash
# 重启后端
cd backend_server
mvn spring-boot:run

# 重启前端（确保使用正确端口）
cd front_server
npm run dev
```

### 2. 验证代理配置

前端应该运行在 `http://localhost:5173`（Vite默认端口），而不是直接访问 `http://localhost:8081`。

### 3. 测试端点

#### 3.1 测试普通HTTP端点
```bash
curl http://localhost:8081/api/device/notifications/test
```

#### 3.2 测试SSE测试端点
```bash
curl -N -H "Accept: text/event-stream" \
  http://localhost:8081/api/device/notifications/sse-test
```

#### 3.3 测试主SSE端点
```bash
curl -N -H "Accept: text/event-stream" \
  http://localhost:8081/api/device/notifications/sse
```

### 4. 前端测试

#### 4.1 访问测试页面
```
http://localhost:5173/sse-test
```

#### 4.2 使用浏览器开发者工具
1. 打开 `http://localhost:5173`
2. 按F12打开开发者工具
3. 查看Network标签
4. 查找SSE连接请求
5. 检查请求URL是否为相对路径

### 5. 预期结果

#### 正确的请求流程：
```
前端: http://localhost:5173
请求: /api/device/notifications/sse (相对路径)
Vite代理: 转发到 http://localhost:8081/api/device/notifications/sse
后端: 返回 text/event-stream
状态: 200 OK
```

#### 错误的请求流程：
```
前端: http://localhost:5173
请求: http://localhost:8081/api/device/notifications/sse (绝对路径)
直接访问: 跳过Vite代理
后端: CORS或媒体类型问题
状态: 404 或其他错误
```

## 调试技巧

### 1. 检查前端请求

在浏览器开发者工具中：
- **Network标签**: 查看实际请求URL
- **Console标签**: 查看SSE连接日志
- **Application标签**: 查看EventSource状态

### 2. 检查后端日志

关键日志信息：
```
INFO - 新的客户端连接到设备通知SSE流
INFO - 请求Accept头: text/event-stream
INFO - 请求URI: /api/device/notifications/sse
INFO - SSE连接创建成功
```

### 3. 检查Vite代理

在前端控制台查看代理日志：
```
Sending Request to the Target: GET /api/device/notifications/sse
Received Response from the Target: 200 /api/device/notifications/sse
```

## 常见问题

### 1. 前端直接访问后端

**症状**: 请求URL显示为 `http://localhost:8081/...`
**原因**: 前端没有使用相对路径或代理配置错误
**解决**: 确保使用相对路径 `/api/...`

### 2. CORS错误

**症状**: `CORS policy` 错误
**原因**: 跨域配置问题
**解决**: 检查后端CORS配置和前端代理设置

### 3. 媒体类型协商错误

**症状**: `HttpMediaTypeNotAcceptableException`
**原因**: 客户端Accept头与服务端produces不匹配
**解决**: 确保produces包含 `text/event-stream`

### 4. 连接立即断开

**症状**: 连接建立后立即关闭
**原因**: 服务端异常或客户端不支持SSE
**解决**: 检查服务端日志和客户端EventSource支持

## 下一步行动

1. **确认前端端口**: 确保前端运行在5173端口
2. **检查请求路径**: 确保使用相对路径
3. **测试简单端点**: 先测试 `/sse-test` 端点
4. **逐步调试**: 从简单到复杂逐步测试
5. **查看详细日志**: 开启DEBUG级别日志

## 成功标志

当一切正常时，应该看到：
- ✅ 前端运行在 `http://localhost:5173`
- ✅ SSE请求使用相对路径 `/api/device/notifications/sse`
- ✅ Network标签显示状态200，类型text/event-stream
- ✅ 后端日志显示连接成功
- ✅ 前端收到连接确认消息
