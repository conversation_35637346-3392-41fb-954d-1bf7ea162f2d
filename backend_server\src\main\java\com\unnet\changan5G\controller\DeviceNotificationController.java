package com.unnet.changan5G.controller;

import com.unnet.changan5G.service.DeviceNotificationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

/**
 * 设备通知控制器 - 提供SSE实时通知
 *
 * <AUTHOR>
 * @date 2024-07-15
 */
@RestController
@RequestMapping("/device/notifications")
@Slf4j
@RequiredArgsConstructor
public class DeviceNotificationController {

    private final DeviceNotificationService deviceNotificationService;

    /**
     * 获取设备状态变化的SSE流
     *
     * @param request HTTP请求对象
     * @param response HTTP响应对象
     * @return SseEmitter 事件发射器
     */
    @CrossOrigin(origins = "*", allowedHeaders = "*", allowCredentials = "false")
    @GetMapping(value = "/sse", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter subscribeToDeviceNotifications(
            HttpServletRequest request, HttpServletResponse response) {

        // 获取客户端IP
        String clientIp = getClientIpAddress(request);

        log.info("新的客户端连接到设备通知SSE流");
        log.info("客户端IP: {}", clientIp);
        log.info("请求Accept头: {}", request.getHeader("Accept"));
        log.info("请求User-Agent: {}", request.getHeader("User-Agent"));
        log.info("请求Origin: {}", request.getHeader("Origin"));
        log.info("请求URI: {}", request.getRequestURI());
        log.info("请求URL: {}", request.getRequestURL());

        // 设置基本的SSE响应头
        response.setContentType("text/event-stream");
        response.setCharacterEncoding("UTF-8");
        response.setHeader("Cache-Control", "no-cache");
        response.setHeader("Connection", "keep-alive");

        try {
            SseEmitter emitter = deviceNotificationService.subscribe(clientIp);
            log.info("SSE连接创建成功，客户端IP: {}", clientIp);
            return emitter;
        } catch (Exception e) {
            log.error("创建SSE连接失败，客户端IP: {}", clientIp, e);
            throw new RuntimeException("创建SSE连接失败: " + e.getMessage());
        }
    }

    /**
     * 处理SSE端点的OPTIONS预检请求
     */
    @CrossOrigin(origins = "*", allowedHeaders = "*")
    @RequestMapping(value = "/sse", method = RequestMethod.OPTIONS)
    public ResponseEntity<Void> handleSseOptions(HttpServletResponse response) {
        log.info("处理SSE端点的OPTIONS预检请求");

        response.setHeader("Access-Control-Allow-Origin", "*");
        response.setHeader("Access-Control-Allow-Methods", "GET, OPTIONS");
        response.setHeader("Access-Control-Allow-Headers", "*");
        response.setHeader("Access-Control-Max-Age", "3600");

        return ResponseEntity.ok().build();
    }

    /**
     * 简单的SSE测试端点
     */
    @CrossOrigin(origins = "*", allowCredentials = "false")
    @GetMapping(value = "/sse-test", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter testSseEndpoint(HttpServletResponse response) {
        log.info("SSE测试端点被访问");

        // 设置响应头
        response.setContentType("text/event-stream; charset=UTF-8");
        response.setHeader("Cache-Control", "no-cache");
        response.setHeader("Connection", "keep-alive");
        response.setHeader("Access-Control-Allow-Origin", "*");

        SseEmitter emitter = new SseEmitter(30000L); // 30秒超时

        try {
            // 立即发送一条测试消息
            emitter.send(SseEmitter.event()
                    .name("test")
                    .data("SSE测试连接成功 - " + java.time.LocalDateTime.now()));

            // 设置完成回调
            emitter.onCompletion(() -> log.info("SSE测试连接完成"));
            emitter.onTimeout(() -> log.info("SSE测试连接超时"));
            emitter.onError((ex) -> log.error("SSE测试连接错误: {}", ex.getMessage()));

        } catch (Exception e) {
            log.error("发送SSE测试消息失败", e);
            emitter.completeWithError(e);
        }

        return emitter;
    }

    /**
     * 测试端点 - 用于验证服务是否正常
     *
     * @return 测试响应
     */
    @CrossOrigin(origins = "*", allowCredentials = "false")
    @GetMapping("/test")
    public String testEndpoint() {
        log.info("SSE服务测试端点被访问");
        return "SSE服务正常运行 - " + java.time.LocalDateTime.now();
    }

    /**
     * 测试SSE通知发送
     */
    @CrossOrigin(origins = "*", allowCredentials = "false")
    @GetMapping("/test-notifications")
    public ResponseEntity<String> testNotifications() {
        log.info("测试SSE通知发送");

        try {
            // 发送测试设备注册通知（使用模拟MAC地址）
            deviceNotificationService.sendDeviceRegisteredNotification("12:34:56:78:9a:bc", "test-hostname");

            // 延迟1秒后发送上线通知
            Thread.sleep(1000);
            deviceNotificationService.sendDeviceOnlineNotification("12:34:56:78:9a:bc", "test-hostname");

            // 延迟2秒后发送离线通知
            Thread.sleep(2000);
            deviceNotificationService.sendDeviceOfflineNotification("12:34:56:78:9a:bc", "test-hostname");

            // 发送统计信息更新
            deviceNotificationService.sendStatisticsUpdateNotification(1, 1, 0);

            return ResponseEntity.ok("测试通知发送成功");
        } catch (Exception e) {
            log.error("发送测试通知失败", e);
            return ResponseEntity.internalServerError().body("发送测试通知失败: " + e.getMessage());
        }
    }

    /**
     * 测试离线通知发送
     */
    @CrossOrigin(origins = "*", allowCredentials = "false")
    @GetMapping("/test-offline-notification")
    public ResponseEntity<String> testOfflineNotification() {
        log.info("测试离线通知发送");

        try {
            // 直接发送离线通知（使用模拟MAC地址）
            deviceNotificationService.sendDeviceOfflineNotification("aa:bb:cc:dd:ee:ff", "test-offline-hostname");

            return ResponseEntity.ok("离线通知发送成功");
        } catch (Exception e) {
            log.error("发送离线通知失败", e);
            return ResponseEntity.internalServerError().body("发送离线通知失败: " + e.getMessage());
        }
    }

    /**
     * 测试上线通知发送
     */
    @CrossOrigin(origins = "*", allowCredentials = "false")
    @GetMapping("/test-online-notification")
    public ResponseEntity<String> testOnlineNotification() {
        log.info("测试上线通知发送");

        try {
            // 直接发送上线通知（使用模拟MAC地址）
            deviceNotificationService.sendDeviceOnlineNotification("11:22:33:44:55:66", "test-online-hostname");

            return ResponseEntity.ok("上线通知发送成功");
        } catch (Exception e) {
            log.error("发送上线通知失败", e);
            return ResponseEntity.internalServerError().body("发送上线通知失败: " + e.getMessage());
        }
    }

    /**
     * 测试注册通知发送
     */
    @CrossOrigin(origins = "*", allowCredentials = "false")
    @GetMapping("/test-register-notification")
    public ResponseEntity<String> testRegisterNotification() {
        log.info("测试注册通知发送");

        try {
            // 直接发送注册通知（使用模拟MAC地址）
            deviceNotificationService.sendDeviceRegisteredNotification("99:88:77:66:55:44", "test-register-hostname");

            return ResponseEntity.ok("注册通知发送成功");
        } catch (Exception e) {
            log.error("发送注册通知失败", e);
            return ResponseEntity.internalServerError().body("发送注册通知失败: " + e.getMessage());
        }
    }

    /**
     * 获取客户端真实IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            // 多个IP时取第一个
            return xForwardedFor.split(",")[0].trim();
        }

        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty() && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }

        String proxyClientIp = request.getHeader("Proxy-Client-IP");
        if (proxyClientIp != null && !proxyClientIp.isEmpty() && !"unknown".equalsIgnoreCase(proxyClientIp)) {
            return proxyClientIp;
        }

        String wlProxyClientIp = request.getHeader("WL-Proxy-Client-IP");
        if (wlProxyClientIp != null && !wlProxyClientIp.isEmpty() && !"unknown".equalsIgnoreCase(wlProxyClientIp)) {
            return wlProxyClientIp;
        }

        return request.getRemoteAddr();
    }

    /**
     * 获取SSE连接统计信息
     */
    @CrossOrigin(origins = "*", allowCredentials = "false")
    @GetMapping("/connection-stats")
    public ResponseEntity<Map<String, Object>> getConnectionStats() {
        try {
            String stats = deviceNotificationService.getConnectionStats();

            // 返回详细的连接统计信息
            Map<String, Object> result = new HashMap<>();
            result.put("stats", stats);
            result.put("timestamp", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            result.put("serverInfo", "backend_server:8081");

            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取连接统计失败", e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("error", "获取连接统计失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(errorResult);
        }
    }

    /**
     * 模拟设备离线然后上线的场景
     */
    @CrossOrigin(origins = "*", allowCredentials = "false")
    @GetMapping("/simulate-device-cycle")
    public ResponseEntity<String> simulateDeviceCycle() {
        log.info("模拟设备离线-上线循环");

        try {
            String identityMac = "aa:bb:cc:dd:ee:01";
            String hostname = "simulate-hostname";

            // 1. 发送注册通知（使用模拟MAC地址）
            deviceNotificationService.sendDeviceRegisteredNotification(identityMac, hostname);
            log.info("已发送注册通知");

            // 2. 发送上线通知
            Thread.sleep(1000);
            deviceNotificationService.sendDeviceOnlineNotification(identityMac, hostname);
            log.info("已发送上线通知");

            // 3. 发送离线通知
            Thread.sleep(2000);
            deviceNotificationService.sendDeviceOfflineNotification(identityMac, hostname);
            log.info("已发送离线通知");

            // 4. 再次发送上线通知（模拟重新上线）
            Thread.sleep(2000);
            deviceNotificationService.sendDeviceOnlineNotification(identityMac, hostname);
            log.info("已发送重新上线通知");

            // 5. 发送统计更新
            deviceNotificationService.sendStatisticsUpdateNotification(1, 0, 0);

            return ResponseEntity.ok("设备生命周期模拟完成");
        } catch (Exception e) {
            log.error("模拟设备生命周期失败", e);
            return ResponseEntity.internalServerError().body("模拟失败: " + e.getMessage());
        }
    }
}
