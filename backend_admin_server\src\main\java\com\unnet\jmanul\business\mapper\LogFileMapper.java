package com.unnet.jmanul.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.unnet.jmanul.business.entity.LogFile;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 日志文件记录 Mapper 接口
 * 
 * <AUTHOR>
 * @since 2025-07-28
 */
@Mapper
public interface LogFileMapper extends BaseMapper<LogFile> {

    /**
     * 根据终端MAC地址分页查询日志文件
     * 
     * @param page 分页参数
     * @param identityMac 终端MAC地址
     * @return 分页结果
     */
    IPage<LogFile> selectByIdentityMacPage(Page<LogFile> page, @Param("identityMac") String identityMac);

    /**
     * 根据终端MAC地址查询日志文件列表
     * 
     * @param identityMac 终端MAC地址
     * @return 日志文件列表
     */
    List<LogFile> selectByIdentityMac(@Param("identityMac") String identityMac);

    /**
     * 根据文件MD5查询是否存在重复文件
     * 
     * @param identityMac 终端MAC地址
     * @param fileMd5 文件MD5值
     * @return 日志文件记录
     */
    LogFile selectByIdentityMacAndMd5(@Param("identityMac") String identityMac, @Param("fileMd5") String fileMd5);
}
