package com.unnet.changan5G.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 告警通知模型
 * 
 * <AUTHOR>
 * @date 2024-07-17
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AlertNotification {
    
    /**
     * 通知类型
     */
    private String type;
    
    /**
     * 设备ID
     */
    private String deviceId;
    
    /**
     * 主机名
     */
    private String hostname;
    
    /**
     * 告警类型
     */
    private String alertType;
    
    /**
     * 告警级别
     */
    private String alertLevel;
    
    /**
     * 告警消息
     */
    private String message;
    
    /**
     * 告警详情
     */
    private String details;
    
    /**
     * 当前值
     */
    private String currentValue;
    
    /**
     * 阈值
     */
    private String threshold;
    
    /**
     * 时间戳
     */
    private String timestamp;
}
