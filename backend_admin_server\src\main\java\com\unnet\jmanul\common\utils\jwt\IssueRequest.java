package com.unnet.jmanul.common.utils.jwt;

import com.unnet.jmanul.system.entity.User;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class IssueRequest {
    private Long id;
    private String name;
    private String username;
    private String accountSource;

    public static IssueRequest of(User user) {
        IssueRequest request = new IssueRequest();
        BeanUtils.copyProperties(user, request);
        return request;
    }
}
