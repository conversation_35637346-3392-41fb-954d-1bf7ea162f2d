package com.unnet.changan5G.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.unnet.changan5G.dto.terminal.TerminalAlertInfo;
import com.unnet.changan5G.entity.MetricThresholdConfigEntity;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * 动态指标告警检查服务
 * 基于配置的JSON字段路径动态检查告警
 * 
 * <AUTHOR>
 * @since 2024-07-25
 */
@Service
@Slf4j
@RequiredArgsConstructor
    public class DynamicMetricAlertService {

    private final ObjectMapper objectMapper;
    private final MetricThresholdConfigService metricThresholdConfigService;
    private final JsonFieldExtractorService jsonFieldExtractorService;

    /**
     * 动态检查并生成告警信息
     *
     * @param jsonData 原始JSON数据
     * @param identityMac 设备MAC地址
     * @param alertTime 告警时间
     * @param metricId 指标ID
     * @return 告警信息列表
     */
    public List<TerminalAlertInfo> checkAndGenerateAlerts(String jsonData, String identityMac,
                                                         LocalDateTime alertTime, String metricId) {
        List<TerminalAlertInfo> alerts = new ArrayList<>();
        
        try {
            // 获取所有启用的阈值配置
            Map<String, MetricThresholdConfigEntity> configMap = metricThresholdConfigService.getAllEnabledConfigsMap();
            
            log.info("开始动态告警检查 - 设备MAC: {}, 配置数量: {}", identityMac, configMap.size());

            // 遍历所有配置进行检查
            for (MetricThresholdConfigEntity config : configMap.values()) {
                if (!config.getIsEnabled() || config.getJsonFieldPath() == null || config.getJsonFieldPath().trim().isEmpty()) {
                    continue;
                }

                try {
                    checkSingleMetricAlert(jsonData, identityMac, alertTime, metricId, config, alerts);
                } catch (Exception e) {
                    log.error("检查指标告警失败 - 设备MAC: {}, 指标类型: {}, 错误: {}",
                            identityMac, config.getMetricType(), e.getMessage(), e);
                }
            }

            log.info("动态告警检查完成 - 设备MAC: {}, 生成告警数量: {}", identityMac, alerts.size());
            
        } catch (Exception e) {
            log.error("动态告警检查失败 - 设备: {}, 错误: {}", identityMac, e.getMessage(), e);
        }
        
        return alerts;
    }

    /**
     * 检查单个指标的告警
     */
    private void checkSingleMetricAlert(String jsonData, String identityMac, LocalDateTime alertTime,
                                      String metricId, MetricThresholdConfigEntity config,
                                      List<TerminalAlertInfo> alerts) {

        String fieldPath = config.getJsonFieldPath();
        String metricType = config.getMetricType();

        log.debug("检查指标告警 - 设备MAC: {}, 指标类型: {}, 字段路径: {}", identityMac, metricType, fieldPath);
        
        // 特殊处理授权过期时间
        if ("授权过期告警".equals(metricType) || "LICENSE_EXPIRY".equals(metricType) ||
            metricType.contains("授权") || metricType.contains("过期") ||
            "expired_date".equals(fieldPath)) {
            log.debug("检测到授权过期告警配置 - 设备MAC: {}, 指标类型: {}, 字段路径: {}", identityMac, metricType, fieldPath);
            checkLicenseExpiryAlert(jsonData, identityMac, alertTime, metricId, config, alerts);
            return;
        }
        
        // 提取字段值
        List<BigDecimal> values = jsonFieldExtractorService.extractValues(jsonData, fieldPath);
        
        if (values.isEmpty()) {
            log.debug("未找到字段值 - 设备mac: {}, 字段路径: {}", identityMac, fieldPath);
            return;
        }
        
        // 检查每个值是否超过阈值
        for (BigDecimal currentValue : values) {
            if (currentValue == null) {
                continue;
            }
            
            if (metricThresholdConfigService.isThresholdExceeded(currentValue, config)) {
                // 生成告警
                TerminalAlertInfo alert = createAlert(
                        identityMac,
                        getAlertType(metricType),
                        generateAlertMessage(config, currentValue),
                        fieldPath,
                        config.getThresholdValue() + config.getThresholdUnit(),
                        currentValue + config.getThresholdUnit(),
                        alertTime,
                        metricId
                );

                alerts.add(alert);

                log.warn("生成告警 - 设备MAC: {}, 指标类型: {}, 当前值: {}, 阈值: {}{}",
                        identityMac, metricType, currentValue, config.getThresholdValue(), config.getThresholdUnit());
                
                // 对于数组字段，只生成一个告警
                if (fieldPath.contains("[*]")) {
                    break;
                }
            }
        }
    }

    /**
     * 检查授权过期告警（特殊处理）
     */
    private void checkLicenseExpiryAlert(String jsonData, String identityMac, LocalDateTime alertTime,
                                       String metricId, MetricThresholdConfigEntity config,
                                       List<TerminalAlertInfo> alerts) {
        try {
            String expiredDateStr = jsonFieldExtractorService.extractStringValue(jsonData, config.getJsonFieldPath());
            
            if (expiredDateStr == null || expiredDateStr.trim().isEmpty()) {
                return;
            }
            
            // 解析过期时间
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            LocalDateTime expiredDate = LocalDateTime.parse(expiredDateStr, formatter);
            LocalDateTime now = LocalDateTime.now();
            
            // 计算剩余天数
            long daysUntilExpiry = ChronoUnit.DAYS.between(now, expiredDate);
            
            // 检查是否需要告警
            BigDecimal thresholdDays = config.getThresholdValue();
            BigDecimal currentDays = BigDecimal.valueOf(daysUntilExpiry);
            
            if (daysUntilExpiry >= 0 && metricThresholdConfigService.isThresholdExceeded(currentDays, config)) {
                // 即将过期告警
                TerminalAlertInfo alert = createAlert(
                        identityMac,
                        config.getMetricType(), // 直接使用数据库中的中文指标类型
                        generateAlertMessage(config, currentDays),
                        config.getJsonFieldPath(),
                        config.getThresholdValue() + config.getThresholdUnit(),
                        daysUntilExpiry + config.getThresholdUnit(),
                        alertTime,
                        metricId
                );
                alerts.add(alert);

                log.warn("软件授权过期告警 - 设备MAC: {}, 过期时间: {}, 剩余天数: {}, 阈值: {}{}",
                        identityMac, expiredDateStr, daysUntilExpiry, config.getThresholdValue(), config.getThresholdUnit());
                
            } else if (daysUntilExpiry < 0) {
                // 已过期告警
                TerminalAlertInfo alert = createAlert(
                        identityMac,
                        config.getMetricType(), // 直接使用数据库中的中文指标类型
                        "软件授权已过期",
                        config.getJsonFieldPath(),
                        "有效期内",
                        "已过期" + Math.abs(daysUntilExpiry) + "天",
                        alertTime,
                        metricId
                );
                alerts.add(alert);

                log.error("软件授权已过期告警 - 设备MAC: {}, 过期时间: {}, 已过期: {}天",
                        identityMac, expiredDateStr, Math.abs(daysUntilExpiry));
            }
            
        } catch (Exception e) {
            log.warn("解析授权过期时间失败 - 设备MAC: {}, 指标类型: {}, 字段路径: {}, 过期时间值: {}, 错误: {}",
                    identityMac, config.getMetricType(), config.getJsonFieldPath(),
                    jsonFieldExtractorService.extractStringValue(jsonData, config.getJsonFieldPath()),
                    e.getMessage());
        }
    }

    /**
     * 生成告警消息
     */
    private String generateAlertMessage(MetricThresholdConfigEntity config, BigDecimal currentValue) {
        String alertMessage = config.getAlertMessage();
        if (alertMessage == null || alertMessage.trim().isEmpty()) {
            alertMessage = config.getMetricName() + "超过阈值";
        }
        
        return alertMessage
                .replace("{current_value}", currentValue + config.getThresholdUnit())
                .replace("{threshold_value}", config.getThresholdValue() + config.getThresholdUnit());
    }

    /**
     * 根据指标类型获取告警类型（现在直接返回中文描述）
     */
    private String getAlertType(String metricType) {
        // 直接返回数据库中的中文指标类型
        return metricType;
    }

    /**
     * 创建告警信息
     */
    private TerminalAlertInfo createAlert(String identityMac, String alertType,
                                        String alertDetails, String metricName, String threshold,
                                        String currentValue, LocalDateTime alertTime, String metricId) {
        TerminalAlertInfo alert = new TerminalAlertInfo();
        alert.setAlertId(UUID.randomUUID().toString());  // 生成随机告警ID
        alert.setIdentityMac(identityMac); // 现在存储的是格式化的MAC地址
        alert.setAlertType(alertType);
        alert.setAlertLevel(getAlertLevelFromConfig(alertType)); // 设置告警级别
        alert.setAlertDetails(alertDetails);
        alert.setMetricName(metricName);
        alert.setThreshold(threshold);
        alert.setCurrentValue(currentValue);
        alert.setMetricId(metricId);  // 设置关联的指标ID
        alert.setAlertTime(alertTime);
        alert.setAlertStatus(TerminalAlertInfo.AlertStatus.ACTIVE);  // 设置告警状态为活跃

        return alert;
    }

    /**
     * 从配置中获取告警级别
     */
    private String getAlertLevelFromConfig(String alertType) {
        try {
            // 从数据库配置中获取告警级别
            MetricThresholdConfigEntity config = metricThresholdConfigService.getConfigByMetricType(alertType);
            if (config != null && config.getAlertLevel() != null) {
                return config.getAlertLevel();
            }
        } catch (Exception e) {
            log.warn("获取告警级别配置失败 - 告警类型: {}, 错误: {}", alertType, e.getMessage());
        }

        // 如果无法从配置获取，使用默认级别
        return getDefaultAlertLevel(alertType);
    }

    /**
     * 获取默认告警级别（兼容性方法）
     */
    private String getDefaultAlertLevel(String alertType) {
        if (alertType == null) {
            return "MEDIUM";
        }

        // 根据中文告警类型确定默认级别
        if (alertType.contains("CPU") || alertType.contains("内存")) {
            return "HIGH";
        } else if (alertType.contains("授权") || alertType.contains("过期")) {
            return "CRITICAL";
        } else if (alertType.contains("磁盘") || alertType.contains("存储")) {
            return "MEDIUM";
        } else {
            return "MEDIUM";
        }
    }
}
