package com.unnet.jmanul.business.util;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * JSON字段提取工具类
 * 支持复杂的JSON路径解析，包括嵌套对象和数组
 * 
 * <AUTHOR>
 * @since 2024-07-25
 */
@Slf4j
@Component
public class JsonFieldExtractor {

    private final ObjectMapper objectMapper;

    public JsonFieldExtractor(ObjectMapper objectMapper) {
        this.objectMapper = objectMapper;
    }

    /**
     * 从JSON字符串中提取指定路径的值
     * 
     * @param jsonString JSON字符串
     * @param fieldPath 字段路径，支持以下格式：
     *                  - 简单字段：cpu_temp
     *                  - 嵌套字段：memory_usage.percent
     *                  - 数组字段：disk_usage[0].percent
     *                  - 数组所有元素：disk_usage[*].percent
     * @return 提取的值列表（对于数组路径可能返回多个值）
     */
    public List<BigDecimal> extractValues(String jsonString, String fieldPath) {
        List<BigDecimal> results = new ArrayList<>();
        
        try {
            JsonNode rootNode = objectMapper.readTree(jsonString);
            extractValuesFromNode(rootNode, fieldPath, results);
        } catch (Exception e) {
            log.error("解析JSON失败 - 路径: {}, 错误: {}", fieldPath, e.getMessage());
        }
        
        return results;
    }

    /**
     * 从JSON字符串中提取单个值
     * 
     * @param jsonString JSON字符串
     * @param fieldPath 字段路径
     * @return 提取的值，如果路径不存在或解析失败返回null
     */
    public BigDecimal extractSingleValue(String jsonString, String fieldPath) {
        List<BigDecimal> values = extractValues(jsonString, fieldPath);
        return values.isEmpty() ? null : values.get(0);
    }

    /**
     * 递归提取JSON节点中的值
     */
    private void extractValuesFromNode(JsonNode node, String fieldPath, List<BigDecimal> results) {
        if (node == null || fieldPath == null || fieldPath.trim().isEmpty()) {
            return;
        }

        String[] pathParts = fieldPath.split("\\.", 2);
        String currentPart = pathParts[0];
        String remainingPath = pathParts.length > 1 ? pathParts[1] : null;

        // 处理数组索引
        if (currentPart.contains("[")) {
            handleArrayPath(node, currentPart, remainingPath, results);
        } else {
            // 处理普通字段
            JsonNode childNode = node.get(currentPart);
            if (childNode != null) {
                if (remainingPath != null) {
                    // 继续递归
                    extractValuesFromNode(childNode, remainingPath, results);
                } else {
                    // 到达叶子节点，提取值
                    BigDecimal value = extractNumericValue(childNode);
                    if (value != null) {
                        results.add(value);
                    }
                }
            }
        }
    }

    /**
     * 处理数组路径
     */
    private void handleArrayPath(JsonNode node, String arrayPart, String remainingPath, List<BigDecimal> results) {
        int bracketStart = arrayPart.indexOf('[');
        int bracketEnd = arrayPart.indexOf(']');
        
        if (bracketStart == -1 || bracketEnd == -1) {
            log.warn("无效的数组路径格式: {}", arrayPart);
            return;
        }

        String arrayFieldName = arrayPart.substring(0, bracketStart);
        String indexPart = arrayPart.substring(bracketStart + 1, bracketEnd);

        JsonNode arrayNode = node.get(arrayFieldName);
        if (arrayNode == null || !arrayNode.isArray()) {
            log.debug("字段不是数组或不存在: {}", arrayFieldName);
            return;
        }

        if ("*".equals(indexPart)) {
            // 处理所有数组元素
            for (JsonNode element : arrayNode) {
                if (remainingPath != null) {
                    extractValuesFromNode(element, remainingPath, results);
                } else {
                    BigDecimal value = extractNumericValue(element);
                    if (value != null) {
                        results.add(value);
                    }
                }
            }
        } else {
            // 处理特定索引
            try {
                int index = Integer.parseInt(indexPart);
                if (index >= 0 && index < arrayNode.size()) {
                    JsonNode element = arrayNode.get(index);
                    if (remainingPath != null) {
                        extractValuesFromNode(element, remainingPath, results);
                    } else {
                        BigDecimal value = extractNumericValue(element);
                        if (value != null) {
                            results.add(value);
                        }
                    }
                }
            } catch (NumberFormatException e) {
                log.warn("无效的数组索引: {}", indexPart);
            }
        }
    }

    /**
     * 从JSON节点提取数值
     */
    private BigDecimal extractNumericValue(JsonNode node) {
        if (node == null) {
            return null;
        }

        if (node.isNumber()) {
            return BigDecimal.valueOf(node.asDouble());
        }

        if (node.isTextual()) {
            try {
                return new BigDecimal(node.asText());
            } catch (NumberFormatException e) {
                log.debug("无法将文本转换为数值: {}", node.asText());
            }
        }

        return null;
    }

    /**
     * 从JSON字符串中提取所有可能的字段路径
     * 用于前端显示可选的字段路径
     * 
     * @param jsonString JSON字符串
     * @return 字段路径列表
     */
    public List<String> extractAllFieldPaths(String jsonString) {
        List<String> paths = new ArrayList<>();
        
        try {
            JsonNode rootNode = objectMapper.readTree(jsonString);
            extractPathsFromNode(rootNode, "", paths);
        } catch (Exception e) {
            log.error("提取字段路径失败: {}", e.getMessage());
        }
        
        return paths;
    }

    /**
     * 递归提取所有字段路径
     */
    private void extractPathsFromNode(JsonNode node, String currentPath, List<String> paths) {
        if (node == null) {
            return;
        }

        if (node.isObject()) {
            node.fieldNames().forEachRemaining(fieldName -> {
                String newPath = currentPath.isEmpty() ? fieldName : currentPath + "." + fieldName;
                JsonNode childNode = node.get(fieldName);
                
                if (childNode.isValueNode()) {
                    // 叶子节点，添加路径
                    paths.add(newPath);
                } else {
                    // 继续递归
                    extractPathsFromNode(childNode, newPath, paths);
                }
            });
        } else if (node.isArray()) {
            // 数组节点
            if (node.size() > 0) {
                JsonNode firstElement = node.get(0);
                if (firstElement.isValueNode()) {
                    // 数组元素是值节点
                    paths.add(currentPath + "[*]");
                } else {
                    // 数组元素是对象，递归处理
                    extractPathsFromNode(firstElement, currentPath + "[*]", paths);
                }
            }
        }
    }
}
