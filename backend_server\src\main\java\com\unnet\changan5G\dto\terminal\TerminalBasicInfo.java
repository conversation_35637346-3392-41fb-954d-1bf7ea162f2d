package com.unnet.changan5G.dto.terminal;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 终端基本信息
 * 
 * <AUTHOR>
 * @date 2024-07-14
 */
@Data
@Schema(description = "终端基本信息")
public class TerminalBasicInfo {

    @Schema(description = "主机名", example = "ec_3568_25030031")
    private String hostname;

    @Schema(description = "设备唯一标识符", example = "_0fd94938951f4a64bb11c6817a81f7e7")
    private String deviceId;

    @Schema(description = "对接长安车辆所在网口的MAC地址", example = "de0765523e60")
    private String identityMac;

    @Schema(description = "当前运行的组件版本号")
    private Object appVersion;

    @Schema(description = "软件授权过期时间", example = "2035-03-09 22:47:20")
    private String expiredDate;

    @Schema(description = "数据接收时间")
    private LocalDateTime receiveTime;

    @Schema(description = "数据来源")
    private String dataSource = "kafka";

    @Schema(description = "设备状态：0-离线，1-在线")
    private Integer status = 1;

    @Schema(description = "首次注册时间")
    private LocalDateTime firstRegisterTime;

    @Schema(description = "最后更新时间")
    private LocalDateTime lastUpdateTime;

    @Schema(description = "终端标签信息（JSON格式）", example = "{\"location\": \"北京\", \"department\": \"研发部\", \"environment\": \"生产\", \"priority\": \"高\"}")
    private Object tags;

    @Schema(description = "自定义字段信息（JSON格式）", example = "{\"vehicle_model\": \"长安CS75\", \"installation_date\": \"2024-01-15\", \"maintenance_contact\": \"张工\"}")
    private Object customFields;
}
