package com.unnet.changan5G.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.unnet.changan5G.entity.TerminalAlertInfoEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 终端告警信息Mapper
 * 
 * <AUTHOR>
 * @date 2024-07-14
 */
@Mapper
public interface TerminalAlertInfoMapper extends BaseMapper<TerminalAlertInfoEntity> {

    /**
     * 查询设备活跃告警
     */
    @Select("SELECT * FROM terminal_alert_info WHERE identity_mac = #{identityMac} " +
            "AND alert_status = 'ACTIVE' ORDER BY alert_time DESC")
    List<TerminalAlertInfoEntity> selectActiveAlertsByIdentityMac(@Param("identityMac") String identityMac);

    /**
     * 查询指定类型的活跃告警
     */
    @Select("SELECT * FROM terminal_alert_info WHERE identity_mac = #{identityMac} " +
            "AND alert_type = #{alertType} AND alert_status = 'ACTIVE' " +
            "ORDER BY alert_time DESC LIMIT 1")
    TerminalAlertInfoEntity selectActiveAlertByIdentityMacAndType(@Param("identityMac") String identityMac,
                                                                 @Param("alertType") String alertType);

    /**
     * 查询所有活跃告警
     */
    @Select("SELECT * FROM terminal_alert_info WHERE alert_status = 'ACTIVE' " +
            "ORDER BY alert_time DESC")
    List<TerminalAlertInfoEntity> selectAllActiveAlerts();

    /**
     * 查询指定时间范围内的告警
     */
    @Select("SELECT * FROM terminal_alert_info WHERE alert_time BETWEEN #{startTime} AND #{endTime} " +
            "ORDER BY alert_time DESC")
    List<TerminalAlertInfoEntity> selectByTimeRange(@Param("startTime") LocalDateTime startTime,
                                                   @Param("endTime") LocalDateTime endTime);

    /**
     * 查询指定设备和时间范围内的告警
     */
    @Select("SELECT * FROM terminal_alert_info WHERE identity_mac = #{identityMac} " +
            "AND alert_time BETWEEN #{startTime} AND #{endTime} " +
            "ORDER BY alert_time DESC")
    List<TerminalAlertInfoEntity> selectByIdentityMacAndTimeRange(@Param("identityMac") String identityMac,
                                                                 @Param("startTime") LocalDateTime startTime,
                                                                 @Param("endTime") LocalDateTime endTime);

    /**
     * 更新告警状态
     */
    @Update("UPDATE terminal_alert_info SET alert_status = #{status}, " +
            "resolved_time = #{resolvedTime}, resolve_comment = #{comment} " +
            "WHERE alert_id = #{alertId}")
    int updateAlertStatus(@Param("alertId") String alertId,
                         @Param("status") String status,
                         @Param("resolvedTime") LocalDateTime resolvedTime,
                         @Param("comment") String comment);

    /**
     * 确认告警
     */
    @Update("UPDATE terminal_alert_info SET alert_status = 'ACKNOWLEDGED', " +
            "acknowledged_time = #{acknowledgedTime}, acknowledged_by = #{acknowledgedBy} " +
            "WHERE alert_id = #{alertId}")
    int acknowledgeAlert(@Param("alertId") String alertId,
                        @Param("acknowledgedTime") LocalDateTime acknowledgedTime,
                        @Param("acknowledgedBy") String acknowledgedBy);

    /**
     * 批量解决同类型告警
     */
    @Update("UPDATE terminal_alert_info SET alert_status = 'RESOLVED', " +
            "resolved_time = #{resolvedTime}, resolve_comment = #{comment} " +
            "WHERE identity_mac = #{identityMac} AND alert_type = #{alertType} AND alert_status = 'ACTIVE'")
    int batchResolveAlertsByType(@Param("identityMac") String identityMac,
                                @Param("alertType") String alertType,
                                @Param("resolvedTime") LocalDateTime resolvedTime,
                                @Param("comment") String comment);

    /**
     * 统计告警数量
     */
    @Select("SELECT alert_type, alert_status, COUNT(*) as count " +
            "FROM terminal_alert_info WHERE alert_time >= #{startTime} " +
            "GROUP BY alert_type, alert_status")
    List<TerminalAlertInfoEntity> selectAlertStatistics(@Param("startTime") LocalDateTime startTime);

    /**
     * 查询未发送通知的告警
     */
    @Select("SELECT * FROM terminal_alert_info WHERE notification_sent = 0 " +
            "AND alert_status = 'ACTIVE' ORDER BY alert_time ASC")
    List<TerminalAlertInfoEntity> selectUnsentNotificationAlerts();

    /**
     * 更新通知发送状态
     */
    @Update("UPDATE terminal_alert_info SET notification_sent = 1, notification_time = #{notificationTime} " +
            "WHERE alert_id = #{alertId}")
    int updateNotificationSent(@Param("alertId") String alertId,
                              @Param("notificationTime") LocalDateTime notificationTime);

    /**
     * 统计活跃告警数量
     */
    @Select("SELECT COUNT(*) FROM terminal_alert_info WHERE alert_status = 'ACTIVE'")
    int countActiveAlerts();
}
