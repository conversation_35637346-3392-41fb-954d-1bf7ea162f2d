package com.unnet.jmanul.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.unnet.jmanul.business.entity.MetricThresholdConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 指标阈值配置Mapper接口
 * 
 * <AUTHOR>
 * @since 2024-07-21
 */
@Mapper
public interface MetricThresholdConfigMapper extends BaseMapper<MetricThresholdConfig> {

    /**
     * 分页查询指标阈值配置
     * 
     * @param page 分页参数
     * @param metricType 指标类型（可选）
     * @param metricName 指标名称（可选）
     * @param isEnabled 是否启用（可选）
     * @param alertLevel 告警级别（可选）
     * @return 分页结果
     */
    @Select("<script>" +
            "SELECT * FROM metric_threshold_config " +
            "WHERE 1=1 " +
            "<if test='metricType != null and metricType != \"\"'>" +
            "  AND metric_type = #{metricType} " +
            "</if>" +
            "<if test='metricName != null and metricName != \"\"'>" +
            "  AND metric_name LIKE CONCAT('%', #{metricName}, '%') " +
            "</if>" +
            "<if test='isEnabled != null'>" +
            "  AND is_enabled = #{isEnabled} " +
            "</if>" +
            "<if test='alertLevel != null and alertLevel != \"\"'>" +
            "  AND alert_level = #{alertLevel} " +
            "</if>" +
            "ORDER BY sort_order ASC, create_time DESC" +
            "</script>")
    IPage<MetricThresholdConfig> selectPageWithConditions(
            Page<MetricThresholdConfig> page,
            @Param("metricType") String metricType,
            @Param("metricName") String metricName,
            @Param("isEnabled") Boolean isEnabled,
            @Param("alertLevel") String alertLevel
    );

    /**
     * 根据指标类型查询配置
     * 
     * @param metricType 指标类型
     * @return 配置信息
     */
    @Select("SELECT * FROM metric_threshold_config WHERE metric_type = #{metricType} AND is_enabled = 1")
    MetricThresholdConfig selectByMetricType(@Param("metricType") String metricType);

    /**
     * 查询所有启用的配置
     * 
     * @return 配置列表
     */
    @Select("SELECT * FROM metric_threshold_config WHERE is_enabled = 1 ORDER BY sort_order ASC")
    List<MetricThresholdConfig> selectAllEnabled();

    /**
     * 检查指标类型是否已存在（排除指定ID）
     *
     * @param metricType 指标类型
     * @param excludeId 排除的ID
     * @return 数量
     */
    @Select("<script>" +
            "SELECT COUNT(*) FROM metric_threshold_config " +
            "WHERE metric_type = #{metricType} " +
            "<if test='excludeId != null'>" +
            "  AND id != #{excludeId} " +
            "</if>" +
            "</script>")
    int countByMetricType(@Param("metricType") String metricType, @Param("excludeId") Long excludeId);

    /**
     * 查询所有不重复的指标类型
     *
     * @return 指标类型列表
     */
    @Select("SELECT DISTINCT metric_type FROM metric_threshold_config ORDER BY metric_type")
    List<String> selectDistinctMetricTypes();
}
