package com.unnet.changan5G.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.unnet.changan5G.entity.TerminalMetricInfoEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 终端指标信息Mapper
 * 
 * <AUTHOR>
 * @date 2024-07-14
 */
@Mapper
public interface TerminalMetricInfoMapper extends BaseMapper<TerminalMetricInfoEntity> {

    /**
     * 查询设备最新指标信息
     */
    @Select("SELECT * FROM terminal_metric_info WHERE device_id = #{deviceId} " +
            "ORDER BY metric_time DESC LIMIT 1")
    TerminalMetricInfoEntity selectLatestByDeviceId(@Param("deviceId") String deviceId);

    /**
     * 查询设备指定时间范围内的指标信息
     */
    @Select("SELECT * FROM terminal_metric_info WHERE device_id = #{deviceId} " +
            "AND metric_time BETWEEN #{startTime} AND #{endTime} " +
            "ORDER BY metric_time DESC")
    List<TerminalMetricInfoEntity> selectByDeviceIdAndTimeRange(@Param("deviceId") String deviceId,
                                                               @Param("startTime") LocalDateTime startTime,
                                                               @Param("endTime") LocalDateTime endTime);

    /**
     * 查询CPU温度异常的设备
     */
    @Select("SELECT * FROM terminal_metric_info WHERE cpu_temp >= #{threshold} " +
            "AND metric_time >= #{startTime} ORDER BY cpu_temp DESC")
    List<TerminalMetricInfoEntity> selectByCpuTempThreshold(@Param("threshold") Double threshold,
                                                           @Param("startTime") LocalDateTime startTime);

    /**
     * 查询内存使用率异常的设备
     */
    @Select("SELECT * FROM terminal_metric_info WHERE memory_percent >= #{threshold} " +
            "AND metric_time >= #{startTime} ORDER BY memory_percent DESC")
    List<TerminalMetricInfoEntity> selectByMemoryPercentThreshold(@Param("threshold") Double threshold,
                                                                 @Param("startTime") LocalDateTime startTime);

    /**
     * 查询磁盘使用率异常的设备
     */
    @Select("SELECT * FROM terminal_metric_info WHERE " +
            "(disk_data_percent >= #{threshold} OR disk_system_percent >= #{threshold}) " +
            "AND metric_time >= #{startTime} ORDER BY metric_time DESC")
    List<TerminalMetricInfoEntity> selectByDiskPercentThreshold(@Param("threshold") Double threshold,
                                                               @Param("startTime") LocalDateTime startTime);

    /**
     * 删除指定时间之前的历史数据
     */
    @Select("DELETE FROM terminal_metric_info WHERE metric_time < #{beforeTime}")
    int deleteHistoryData(@Param("beforeTime") LocalDateTime beforeTime);

    /**
     * 查询设备指标统计信息
     */
    @Select("SELECT device_id, " +
            "AVG(cpu_temp) as avg_cpu_temp, MAX(cpu_temp) as max_cpu_temp, " +
            "AVG(cpu_percent) as avg_cpu_percent, MAX(cpu_percent) as max_cpu_percent, " +
            "AVG(memory_percent) as avg_memory_percent, MAX(memory_percent) as max_memory_percent, " +
            "COUNT(*) as record_count " +
            "FROM terminal_metric_info WHERE device_id = #{deviceId} " +
            "AND metric_time BETWEEN #{startTime} AND #{endTime} " +
            "GROUP BY device_id")
    TerminalMetricInfoEntity selectStatsByDeviceIdAndTimeRange(@Param("deviceId") String deviceId,
                                                              @Param("startTime") LocalDateTime startTime,
                                                              @Param("endTime") LocalDateTime endTime);
}
