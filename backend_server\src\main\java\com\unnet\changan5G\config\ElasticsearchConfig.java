package com.unnet.changan5G.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.convert.converter.Converter;
import org.springframework.data.elasticsearch.client.ClientConfiguration;
import org.springframework.data.elasticsearch.client.RestClients;
import org.springframework.data.elasticsearch.core.ElasticsearchOperations;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.convert.ElasticsearchCustomConversions;
import org.springframework.data.elasticsearch.repository.config.EnableElasticsearchRepositories;
import org.elasticsearch.client.RestHighLevelClient;

import java.util.ArrayList;
import java.util.List;

/**
 * Elasticsearch配置类 - 适用于Spring Boot 2.7
 *
 * <AUTHOR>
 * @date 2024-07-14
 */
@Configuration
@EnableElasticsearchRepositories(basePackages = "com.unnet.changan5G.repository")
public class ElasticsearchConfig {

    @Value("${spring.elasticsearch.uris}")
    private String[] elasticsearchUrl;

    @Value("${spring.elasticsearch.username}")
    private String username;

    @Value("${spring.elasticsearch.password}")
    private String password;

    @Bean
    public RestHighLevelClient client() {
        ClientConfiguration clientConfiguration = ClientConfiguration.builder()
                .connectedTo(elasticsearchUrl)
                .withBasicAuth(username, password)
                .build();

        return RestClients.create(clientConfiguration).rest();
    }

    @Bean
    public ElasticsearchOperations elasticsearchTemplate() {
        return new ElasticsearchRestTemplate(client());
    }

    @Bean
    public ElasticsearchCustomConversions elasticsearchCustomConversions() {
        List<Converter<?, ?>> converters = new ArrayList<>();
        converters.add(new ElasticsearchDateTimeConverter.StringToLocalDateTimeConverter());
        converters.add(new ElasticsearchDateTimeConverter.LocalDateTimeToStringConverter());
        converters.add(new ElasticsearchDateTimeConverter.DateToLocalDateTimeConverter());
        converters.add(new ElasticsearchDateTimeConverter.LocalDateTimeToDateConverter());
        converters.add(new ElasticsearchDateTimeConverter.LongToLocalDateTimeConverter());
        return new ElasticsearchCustomConversions(converters);
    }
}
