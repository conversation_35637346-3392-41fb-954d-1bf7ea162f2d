<template>
  <div class="device-alert-notifications">
    <!-- 实时告警通知列表 -->
    <div class="alert-notifications-container">
      <div class="alert-notifications-list" v-if="alertNotifications.length > 0">
        <transition-group name="alert-notification" tag="div">
          <div 
            v-for="notification in alertNotifications" 
            :key="notification.id"
            class="alert-notification-item"
            :class="'level-' + (notification.alertLevel || 'medium').toLowerCase()"
          >
            <div class="alert-notification-icon">
              <i class="el-icon-warning-outline"></i>
            </div>
            <div class="alert-notification-content">
              <div class="alert-notification-header">
                <div class="alert-level-badge" :class="'level-' + (notification.alertLevel || 'medium').toLowerCase()">
                  {{ getAlertLevelText(notification.alertLevel) }}
                </div>
                <div class="alert-type-text">
                  {{ getAlertTypeText(notification.alertType) }}
                </div>
              </div>
              
              <div class="alert-notification-message">{{ notification.message }}</div>
              
              <div class="alert-notification-details">
                <div class="alert-values" v-if="notification.currentValue && notification.threshold">
                  <span class="current-value">当前值: {{ notification.currentValue }}</span>
                  <span class="threshold">阈值: {{ notification.threshold }}</span>
                </div>
                <div class="device-info">
                  <span class="device-id" :title="'设备MAC地址: ' + notification.deviceId">{{ notification.deviceId }}</span>
                  <span class="hostname" v-if="notification.hostname">{{ notification.hostname }}</span>
                  <span class="timestamp">{{ notification.timestamp }}</span>
                </div>
              </div>
            </div>
            <div class="alert-notification-close" @click="removeAlertNotification(notification.id)">
              <i class="el-icon-close"></i>
            </div>
          </div>
        </transition-group>
      </div>

      <div class="no-alert-notifications" v-else>
        <i class="el-icon-info"></i>
        <span>暂无告警通知</span>
      </div>
    </div>
  </div>
</template>

<script>
import sseService from '@/services/sseService'

export default {
  name: 'DeviceAlertNotifications',
  data() {
    return {
      alertNotifications: [],
      alertNotificationIdCounter: 0,
      processedAlertEvents: new Set() // 用于去重的告警事件ID集合
    }
  },
  mounted() {
    this.initAlertSSE()
  },
  beforeUnmount() {
    this.cleanupAlertSSE()
  },
  methods: {
    /**
     * 初始化告警SSE监听
     */
    initAlertSSE() {
      console.log('=== 初始化告警SSE监听 ===')
      
      // 监听设备告警事件
      sseService.on('device-alert', this.handleDeviceAlert)
    },

    /**
     * 清理告警SSE监听
     */
    cleanupAlertSSE() {
      console.log('=== 清理告警SSE事件监听器 ===')
      
      // 移除告警事件监听器
      sseService.off('device-alert', this.handleDeviceAlert)
    },

    /**
     * 处理设备告警通知
     */
    handleDeviceAlert(data) {
      console.log('DeviceAlertNotifications: 处理设备告警通知', data)

      // 生成事件唯一标识
      const eventId = `${data.type}_${data.deviceId}_${data.timestamp}`

      // 检查是否已处理过此事件
      if (this.processedAlertEvents.has(eventId)) {
        console.log('DeviceAlertNotifications: 重复告警事件已忽略', eventId)
        return
      }

      // 记录已处理的事件
      this.processedAlertEvents.add(eventId)

      // 清理过期的事件ID（保留最近100个）
      if (this.processedAlertEvents.size > 100) {
        const eventsArray = Array.from(this.processedAlertEvents)
        this.processedAlertEvents = new Set(eventsArray.slice(-50))
      }

      this.addAlertNotification({
        type: 'DEVICE_ALERT',
        deviceId: data.deviceId,
        hostname: data.hostname,
        alertType: data.alertType,
        alertLevel: data.alertLevel,
        message: data.message,
        details: data.details,
        currentValue: data.currentValue,
        threshold: data.threshold,
        timestamp: data.timestamp
      })

      // 触发父组件事件
      console.log('DeviceAlertNotifications: 触发device-alert事件', data)
      this.$emit('device-alert', data)
    },

    /**
     * 添加告警通知
     */
    addAlertNotification(notification) {
      const id = ++this.alertNotificationIdCounter
      const notificationWithId = {
        ...notification,
        id
      }
      
      // 添加到列表开头
      this.alertNotifications.unshift(notificationWithId)
      
      // 限制通知数量（最多显示8条）
      if (this.alertNotifications.length > 8) {
        this.alertNotifications = this.alertNotifications.slice(0, 8)
      }
      
      // 15秒后自动移除（告警通知保留时间更长）
      setTimeout(() => {
        this.removeAlertNotification(id)
      }, 15000)
    },

    /**
     * 移除告警通知
     */
    removeAlertNotification(id) {
      const index = this.alertNotifications.findIndex(n => n.id === id)
      if (index > -1) {
        this.alertNotifications.splice(index, 1)
      }
    },

    /**
     * 获取告警级别文本
     */
    getAlertLevelText(level) {
      switch (level) {
        case 'CRITICAL':
          return '严重'
        case 'HIGH':
          return '高'
        case 'MEDIUM':
          return '中'
        case 'LOW':
          return '低'
        default:
          return '中'
      }
    },

    /**
     * 获取告警类型文本
     */
    getAlertTypeText(type) {
      // 如果已经是中文，直接返回
      if (type && (type.includes('告警') || type.includes('过期'))) {
        return type
      }

      // 兼容旧的英文枚举值
      switch (type) {
        case 'CPU_TEMPERATURE':
          return 'CPU温度告警'
        case 'MEMORY_USAGE':
          return '内存使用率告警'
        case 'DISK_USAGE':
          return '磁盘使用率告警'
        case 'DISK_DATA_USAGE':
          return '数据磁盘使用率告警'
        case 'DISK_SYSTEM_USAGE':
          return '系统磁盘使用率告警'
        case 'LICENSE_EXPIRY':
          return '软件授权过期告警'
        default:
          return type || '系统告警'
      }
    }
  }
}
</script>

<style scoped>
.device-alert-notifications {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.alert-notifications-list {
  max-height: 400px;
  overflow-y: auto;
}

.alert-notification-item {
  display: flex;
  align-items: flex-start;
  padding: 16px;
  margin-bottom: 12px;
  border-radius: 8px;
  border-left: 4px solid;
  background: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.alert-notification-item:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.alert-notification-item.level-critical {
  border-left-color: #ff4d4f;
  background: linear-gradient(135deg, #fff2f0 0%, #fff 100%);
}

.alert-notification-item.level-high {
  border-left-color: #fa8c16;
  background: linear-gradient(135deg, #fff7e6 0%, #fff 100%);
}

.alert-notification-item.level-medium {
  border-left-color: #faad14;
  background: linear-gradient(135deg, #fffbe6 0%, #fff 100%);
}

.alert-notification-item.level-low {
  border-left-color: #52c41a;
  background: linear-gradient(135deg, #f6ffed 0%, #fff 100%);
}

.alert-notification-icon {
  margin-right: 16px;
  font-size: 20px;
  color: #fa8c16;
  margin-top: 2px;
}

.alert-notification-content {
  flex: 1;
}

.alert-notification-header {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  gap: 12px;
}

.alert-level-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: bold;
  color: white;
}

.alert-level-badge.level-critical {
  background: #ff4d4f;
}

.alert-level-badge.level-high {
  background: #fa8c16;
}

.alert-level-badge.level-medium {
  background: #faad14;
}

.alert-level-badge.level-low {
  background: #52c41a;
}

.alert-type-text {
  font-size: 13px;
  color: #666;
  font-weight: 500;
}

.alert-notification-message {
  font-size: 14px;
  font-weight: 600;
  color: #262626;
  margin-bottom: 8px;
  line-height: 1.4;
}

.alert-notification-details {
  font-size: 12px;
  color: #8c8c8c;
}

.alert-values {
  margin-bottom: 6px;
  display: flex;
  gap: 16px;
}

.current-value, .threshold {
  font-size: 12px;
  color: #666;
  background: #f5f5f5;
  padding: 2px 6px;
  border-radius: 3px;
}

.device-info span {
  margin-right: 12px;
}

.device-id {
  font-family: monospace;
  background: #f0f0f0;
  padding: 2px 4px;
  border-radius: 2px;
}

.alert-notification-close {
  cursor: pointer;
  color: #bfbfbf;
  font-size: 16px;
  padding: 4px;
  margin-top: 2px;
}

.alert-notification-close:hover {
  color: #8c8c8c;
}

.no-alert-notifications {
  text-align: center;
  padding: 40px 20px;
  color: #8c8c8c;
}

.no-alert-notifications i {
  font-size: 24px;
  margin-bottom: 8px;
  display: block;
}

/* 动画效果 */
.alert-notification-enter-active,
.alert-notification-leave-active {
  transition: all 0.4s ease;
}

.alert-notification-enter-from {
  opacity: 0;
  transform: translateY(-20px) scale(0.95);
}

.alert-notification-leave-to {
  opacity: 0;
  transform: translateX(30px) scale(0.95);
}
</style>
