package com.unnet.jmanul.system.init;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.unnet.jmanul.common.utils.RSAUtil;
import com.unnet.jmanul.system.constants.AuthKeyPairNames;
import com.unnet.jmanul.system.entity.RuntimeConfig;
import com.unnet.jmanul.system.service.IRuntimeConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.security.KeyPair;
import java.util.Optional;

@Component
@Order(20)
@Slf4j
public class AuthKeyPairInitializer implements ApplicationRunner {

    @Resource
    private IRuntimeConfigService runtimeConfigService;

    @Override
    public void run(ApplicationArguments args) throws Exception {

        Optional<RuntimeConfig> publicKeyOpt = runtimeConfigService.getOneOpt(
                new QueryWrapper<RuntimeConfig>()
                        .eq("name", AuthKeyPairNames.PUBLIC_KEY_NAME)
        );

        if (publicKeyOpt.isEmpty()) {
            log.info("public key not found, generate new one");
            KeyPair keyPair = RSAUtil.generateRSAKeyPair();
            String publicKey = RSAUtil.getPublicKey(keyPair);
            String privateKey = RSAUtil.getPrivateKey(keyPair);

            RuntimeConfig publicKeyItem = new RuntimeConfig();
            publicKeyItem.setName(AuthKeyPairNames.PUBLIC_KEY_NAME);
            publicKeyItem.setValue(publicKey);
            publicKeyItem.setDescription("public key for auth");
            boolean ok = runtimeConfigService.saveOrUpdate(publicKeyItem);

            RuntimeConfig privateKeyItem = new RuntimeConfig();
            privateKeyItem.setName(AuthKeyPairNames.PRIVATE_KEY_NAME);
            privateKeyItem.setValue(privateKey);
            privateKeyItem.setDescription("private key for auth");
            ok &= runtimeConfigService.saveOrUpdate(privateKeyItem);

            if (!ok) {
                throw new RuntimeException("generate key pair failed");
            } else {
                log.info("generate key pair and save success");
            }
        }

    }
}
