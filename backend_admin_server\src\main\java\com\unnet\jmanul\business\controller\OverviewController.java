package com.unnet.jmanul.business.controller;

import com.unnet.jmanul.business.service.IOverviewService;
import com.unnet.jmanul.business.service.dto.overview.OverviewStatsResp;
import com.unnet.jmanul.business.service.dto.overview.OfflineTerminalResp;
import com.unnet.jmanul.business.service.dto.overview.AlertTerminalResp;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.unnet.jmanul.common.rest.ApiDef.V1_ADMIN;

@Slf4j
@RestController
@RequestMapping(V1_ADMIN + "/overview")
@RequiredArgsConstructor
@Api(tags = {"概览管理 OverviewController"})
public class OverviewController {

    private final IOverviewService overviewService;

    @GetMapping("/stats")
    @ApiOperation(value = "获取概览统计信息", notes = "获取终端总数、在线数、离线数、告警数等统计信息")
    public ResponseEntity<OverviewStatsResp> getOverviewStats() {
        try {
            log.info("=== 开始获取概览统计信息 ===");
            OverviewStatsResp stats = overviewService.getOverviewStats();
            log.info("=== 获取概览统计信息成功 ===");
            log.info("终端总数: {}", stats.getTotalTerminals());
            log.info("在线终端: {}", stats.getOnlineTerminals());
            log.info("离线终端: {}", stats.getOfflineTerminals());
            log.info("告警总数: {}", stats.getTotalAlerts());
            log.info("严重告警: {}", stats.getCriticalAlerts());
            log.info("警告告警: {}", stats.getWarningAlerts());
            log.info("已解决告警: {}", stats.getResolvedAlerts());
            return ResponseEntity.ok(stats);
        } catch (Exception e) {
            log.error("获取概览统计信息失败: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }

    @GetMapping("/offline-terminals")
    @ApiOperation(value = "获取离线终端列表", notes = "获取当前离线的终端列表，用于概览页面显示")
    public ResponseEntity<List<OfflineTerminalResp>> getOfflineTerminals(
            @RequestParam(defaultValue = "10") int limit) {
        try {
            log.info("获取离线终端列表 - 限制数量: {}", limit);
            List<OfflineTerminalResp> offlineTerminals = overviewService.getOfflineTerminals(limit);
            log.info("获取离线终端列表成功 - 数量: {}", offlineTerminals.size());
            return ResponseEntity.ok(offlineTerminals);
        } catch (Exception e) {
            log.error("获取离线终端列表失败: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }

    @GetMapping("/alert-terminals")
    @ApiOperation(value = "获取告警终端列表", notes = "获取当前有告警的终端列表，用于概览页面显示")
    public ResponseEntity<List<AlertTerminalResp>> getAlertTerminals(
            @RequestParam(defaultValue = "10") int limit) {
        try {
            log.info("获取告警终端列表 - 限制数量: {}", limit);
            List<AlertTerminalResp> alertTerminals = overviewService.getAlertTerminals(limit);
            log.info("获取告警终端列表成功 - 数量: {}", alertTerminals.size());
            return ResponseEntity.ok(alertTerminals);
        } catch (Exception e) {
            log.error("获取告警终端列表失败: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }

    @GetMapping("/init")
    @ApiOperation(value = "初始化概览数据", notes = "一次性获取概览页面所需的所有初始化数据")
    public ResponseEntity<Object> initOverviewData(
            @RequestParam(defaultValue = "10") int terminalLimit,
            @RequestParam(defaultValue = "10") int alertLimit) {
        try {
            log.info("初始化概览数据 - 终端限制: {}, 告警限制: {}", terminalLimit, alertLimit);
            
            // 获取统计信息
            OverviewStatsResp stats1 = overviewService.getOverviewStats();
            
            // 获取离线终端列表
            List<OfflineTerminalResp> offlineTerminals1 = overviewService.getOfflineTerminals(terminalLimit);
            
            // 获取告警终端列表
            List<AlertTerminalResp> alertTerminals1 = overviewService.getAlertTerminals(alertLimit);
            
            // 构建响应对象
            Object response = new Object() {
                public final OverviewStatsResp stats = stats1;
                public final List<OfflineTerminalResp> offlineTerminals = offlineTerminals1;
                public final List<AlertTerminalResp> alertTerminals = alertTerminals1;
                public final String timestamp = java.time.LocalDateTime.now().toString();
            };
            
            log.info("初始化概览数据成功 - 统计信息: {}, 离线终端: {}, 告警终端: {}",
                    stats1, offlineTerminals1.size(), alertTerminals1.size());
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("初始化概览数据失败: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }

//    @GetMapping("/test")
//    @ApiOperation(value = "测试数据库连接", notes = "测试数据库连接和数据查询")
//    public ResponseEntity<Object> testDatabase() {
//        try {
//            log.info("=== 开始测试数据库连接 ===");
//
//            // 测试终端基本信息表
//            OverviewStatsResp stats = overviewService.getOverviewStats();
//            log.info("统计信息: {}", stats);
//
//            // 测试离线终端列表
//            List<OfflineTerminalResp> offlineTerminals = overviewService.getOfflineTerminals(5);
//            log.info("离线终端数量: {}", offlineTerminals.size());
//
//            // 测试告警终端列表
//            List<AlertTerminalResp> alertTerminals = overviewService.getAlertTerminals(5);
//            log.info("告警终端数量: {}", alertTerminals.size());
//
//            Object response = new Object() {
//                public final OverviewStatsResp stats = stats;
//                public final int offlineTerminalCount = offlineTerminals.size();
//                public final int alertTerminalCount = alertTerminals.size();
//                public final String message = "数据库连接正常";
//                public final String timestamp = java.time.LocalDateTime.now().toString();
//            };
//
//            return ResponseEntity.ok(response);
//        } catch (Exception e) {
//            log.error("测试数据库连接失败: {}", e.getMessage(), e);
//            return ResponseEntity.internalServerError().build();
//        }
//    }
}
