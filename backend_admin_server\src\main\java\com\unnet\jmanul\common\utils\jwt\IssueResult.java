package com.unnet.jmanul.common.utils.jwt;

import com.unnet.jmanul.system.service.dto.auth.LoginResp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class IssueResult {
    private Integer code;
    private String token;
    private Instant expire;

    public LoginResp buildLoginResp() {
        return LoginResp.builder()
                .code(this.getCode())
                .token(this.getToken())
                .expire(this.getExpire())
                .build();
    }
}
