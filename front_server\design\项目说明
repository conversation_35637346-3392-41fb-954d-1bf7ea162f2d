我现在想做一个用于终端运维的管理平台，方便运维人员对于终端的控制和状态信息查看，注意目前先给出对应的原型即可包含三个页面，页面包含概览、终端管理、站内告警页面功能，你给我对应的html样式的原型吧。
 主要功能：
   概览：
     终端统计信息(终端数量，在线数量，离线数量)、终端上下线的滚动提醒
   终端管理：
     终端列表信息，包含终端基本字段，添加过滤查询功能，添加按钮：查看对应终端历史指标信息、查看终端详情(一个主终端，多个cpe服务端，需要显示对应关系图)、编辑终端(包含本身的信息，以及支持自定义字段)、支持标签(用户设置键值，然后后续可以根据用户的标签信息进行过滤查询)
   对于自定义字段、以及标签功能，实现方案就是在终端表添加两个字段，一个字段专门用于存储自定义字段，一个字段专门用于存储标签信息，然后后续可以根据标签信息进行过滤查询，自定义字段相当于实际的数据(动态的添加)
   站内告警：
     告警列表信息，包含告警基本字段，添加过滤查询功能，添加按钮：查看告警详情(显示告警所有信息)，实时指标查看(点击查看后，跳转到一个新的页面，显示这条告警终端的实时指标，是否还是持续告警状态)，对应终端信息钉钉提醒(目前先只做简单提醒，提醒用户已发送提醒了哦)

以下是涉及到的mysql表

-- ========================================
--  终端监控相关表初始化脚本
-- 创建时间: 2024-07-14
-- 说明: 包含终端基本信息、指标信息、告警信息三张核心表
-- ========================================

-- 设置字符集
SET NAMES utf8mb4;

-- ========================================
-- 1. 终端基本信息表
-- ========================================
DROP TABLE IF EXISTS `terminal_basic_info`;
CREATE TABLE `terminal_basic_info` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `device_id` varchar(100) NOT NULL COMMENT '设备唯一标识符',
  `hostname` varchar(100) DEFAULT NULL COMMENT '主机名',
  `identity_mac` varchar(20) DEFAULT NULL COMMENT '对接长安车辆所在网口的MAC地址',
  `app_version` json DEFAULT NULL COMMENT '当前运行的组件版本号(JSON格式)',
  `expired_date` varchar(30) DEFAULT NULL COMMENT '软件授权过期时间',
  `receive_time` datetime DEFAULT NULL COMMENT '数据接收时间',
  `data_source` varchar(20) DEFAULT 'kafka' COMMENT '数据来源',
  `status` tinyint(1) DEFAULT 1 COMMENT '设备状态：0-离线，1-在线',
  `first_register_time` datetime DEFAULT NULL COMMENT '首次注册时间',
  `last_update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  `tags` json DEFAULT NULL COMMENT '终端标签信息(JSON格式)，用于标签过滤',
  `custom_fields` json DEFAULT NULL COMMENT '自定义字段信息(JSON格式)，用于动态扩展字段',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_deleted` tinyint(1) DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_device_id` (`device_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='终端基本信息表';

-- ========================================
-- 2. 终端指标信息表
-- ========================================
DROP TABLE IF EXISTS `terminal_metric_info`;
CREATE TABLE `terminal_metric_info` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `device_id` varchar(100) NOT NULL COMMENT '设备唯一标识符',
  `uptime` decimal(10,2) DEFAULT NULL COMMENT '系统运行时长（单位：秒）',
  `cpu_temp` decimal(5,2) DEFAULT NULL COMMENT 'CPU温度（单位：摄氏度）',
  `temperatures` json DEFAULT NULL COMMENT '温度信息(JSON格式)',
  `cpu_usage` json DEFAULT NULL COMMENT 'CPU使用率信息(JSON格式)',
  `cpu_percent` decimal(5,2) DEFAULT NULL COMMENT 'CPU总使用率（%）',
  `memory_percent` decimal(5,2) DEFAULT NULL COMMENT '内存使用率（%）',
  `memory_usage` json DEFAULT NULL COMMENT '内存详细信息(JSON格式)',
  `disk_usage` json DEFAULT NULL COMMENT '磁盘使用情况数组(JSON格式)',
  `disk_data_percent` decimal(5,2) DEFAULT NULL COMMENT '采集数据磁盘分区使用率',
  `disk_system_percent` decimal(5,2) DEFAULT NULL COMMENT '系统盘使用率',
  `cdata` json DEFAULT NULL COMMENT '长安程序落盘文件信息(JSON格式)',
  `zdata` json DEFAULT NULL COMMENT '压缩文件信息(JSON格式)',
  `group_usage` json DEFAULT NULL COMMENT 'CPE集合信息(JSON格式)',
  `group_bps` bigint(20) DEFAULT NULL COMMENT '所有CPE的带宽占用（单位：Byte/s）',
  `metric_time` datetime DEFAULT NULL COMMENT '指标采集时间',
  `receive_time` datetime DEFAULT NULL COMMENT '数据接收时间',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_device_id` (`device_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='终端指标信息表';

-- ========================================
-- 3. 终端告警信息表
-- ========================================
DROP TABLE IF EXISTS `terminal_alert_info`;
CREATE TABLE `terminal_alert_info` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `alert_id` varchar(50) NOT NULL COMMENT '告警唯一ID（UUID）',
  `device_id` varchar(100) NOT NULL COMMENT '告警终端设备ID',
  `alert_type` varchar(30) NOT NULL COMMENT '告警类型：CPU_TEMPERATURE,MEMORY_USAGE,DISK_USAGE,DISK_DATA_USAGE,DISK_SYSTEM_USAGE,LICENSE_EXPIRY',
  `alert_details` varchar(500) DEFAULT NULL COMMENT '告警详情',
  `metric_name` varchar(50) DEFAULT NULL COMMENT '告警指标名称',
  `threshold` varchar(20) DEFAULT NULL COMMENT '告警阈值',
  `current_value` varchar(20) DEFAULT NULL COMMENT '当前值',
  `alert_time` datetime DEFAULT NULL COMMENT '告警时间',
  `alert_status` varchar(20) DEFAULT 'ACTIVE' COMMENT '告警状态：ACTIVE,RESOLVED,ACKNOWLEDGED',
  `resolved_time` datetime DEFAULT NULL COMMENT '告警解决时间',
  `acknowledged_time` datetime DEFAULT NULL COMMENT '告警确认时间',
  `acknowledged_by` varchar(50) DEFAULT NULL COMMENT '告警确认人',
  `resolve_comment` varchar(500) DEFAULT NULL COMMENT '解决备注',
  `notification_sent` tinyint(1) DEFAULT 0 COMMENT '是否已发送通知：0-未发送，1-已发送',
  `notification_time` datetime DEFAULT NULL COMMENT '通知发送时间',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_alert_id` (`alert_id`),
  KEY `idx_device_id` (`device_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='终端告警信息表';
