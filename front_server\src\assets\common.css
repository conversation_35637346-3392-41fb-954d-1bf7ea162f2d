/* 原型设计通用样式 */

/* 通用卡片样式 */
.card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  margin-bottom: 20px;
  overflow: hidden;
}

.card-header {
  padding: 20px;
  border-bottom: 1px solid #eee;
  background: #fafafa;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.card-body {
  padding: 20px;
}

/* 统计卡片样式 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.stat-card {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  text-align: center;
  border-left: 4px solid #667eea;
}

.stat-number {
  font-size: 32px;
  font-weight: bold;
  color: #667eea;
  margin-bottom: 5px;
}

.stat-label {
  color: #666;
  font-size: 14px;
}

/* 按钮样式 */
.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s;
  text-decoration: none;
  display: inline-block;
}

.btn-primary {
  background: #667eea;
  color: white;
}

.btn-primary:hover {
  background: #5a6fd8;
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-info {
  background: #17a2b8;
  color: white;
}

.btn-warning {
  background: #ffc107;
  color: #212529;
}

.btn-danger {
  background: #dc3545;
  color: white;
}

.btn-success {
  background: #28a745;
  color: white;
}

.btn-sm {
  padding: 5px 10px;
  font-size: 12px;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  background-color: #6c757d !important;
}

/* 表格样式 */
.table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 15px;
}

.table th,
.table td {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid #eee;
}

.table th {
  background: #f8f9fa;
  font-weight: 600;
  color: #495057;
}

.table tr:hover {
  background: #f8f9fa;
}

/* 表单样式 */
.search-filters {
  display: flex;
  gap: 15px;
  margin-bottom: 20px;
  flex-wrap: wrap;
  align-items: center;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group label {
  margin-bottom: 5px;
  font-weight: 500;
  color: #555;
}

.form-control {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.form-control:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

/* 状态样式 */
.status-online {
  color: #28a745;
  font-weight: bold;
}

.status-offline {
  color: #dc3545;
  font-weight: bold;
}

/* 徽章样式 */
.badge {
  display: inline-block;
  padding: 4px 8px;
  font-size: 12px;
  font-weight: 500;
  border-radius: 12px;
  text-align: center;
  white-space: nowrap;
}

.badge-success {
  background: #28a745;
  color: white;
}

.badge-danger {
  background: #dc3545;
  color: white;
}

.badge-warning {
  background: #ffc107;
  color: #212529;
}

.badge-info {
  background: #17a2b8;
  color: white;
}

/* 状态徽章 */
.status-badge {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
  margin-right: 8px;
}

.status-badge.status-offline {
  background: #f8d7da;
  color: #721c24;
}

.status-badge.status-alert {
  background: #fff3cd;
  color: #856404;
}

/* 告警级别 */
.alert-level {
  display: inline-block;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 10px;
  font-weight: 500;
  margin-left: 8px;
}

.alert-level.high {
  background: #dc3545;
  color: white;
}

.alert-level.medium {
  background: #ffc107;
  color: #212529;
}

.alert-level.low {
  background: #6c757d;
  color: white;
}

/* 内容布局 */
.content-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 20px;
}

/* 滚动容器 */
.scroll-container {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #eee;
  border-radius: 4px;
  padding: 10px;
}

/* 日志项目 */
.log-item {
  padding: 12px;
  margin-bottom: 8px;
  border-radius: 4px;
  font-size: 14px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: opacity 0.3s ease;
}

.log-item.online {
  background: #d4edda;
  color: #155724;
  border-left: 4px solid #28a745;
}

.log-item.offline {
  background: #f8d7da;
  color: #721c24;
  border-left: 4px solid #dc3545;
}

.log-item.fade-out {
  opacity: 0.5;
}

.log-content {
  flex: 1;
}

.log-actions {
  margin-left: 10px;
}

/* 列表项目 */
.list-item {
  padding: 12px;
  margin-bottom: 8px;
  border-radius: 4px;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.list-item:hover {
  background: #e9ecef;
}

.item-info {
  flex: 1;
}

.item-title {
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.item-subtitle {
  font-size: 12px;
  color: #666;
}

.item-actions {
  margin-left: 10px;
}

/* 空消息 */
.empty-message {
  text-align: center;
  color: #999;
  font-style: italic;
  padding: 20px;
}

/* 分页 */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
  margin-top: 20px;
}

.pagination button {
  padding: 8px 12px;
  border: 1px solid #ddd;
  background: white;
  cursor: pointer;
  border-radius: 4px;
}

.pagination button:hover {
  background: #f8f9fa;
}

.pagination button.active {
  background: #667eea;
  color: white;
  border-color: #667eea;
}

/* 操作按钮组 */
.action-buttons {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

/* 响应式调整 */
@media (max-width: 1024px) {
  .content-grid {
    grid-template-columns: 1fr;
  }
  
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .card-header,
  .card-body {
    padding: 15px;
  }
  
  .stat-card {
    padding: 15px;
  }
  
  .stat-number {
    font-size: 24px;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }
  
  .search-filters {
    flex-direction: column;
    align-items: stretch;
  }
  
  .form-group {
    margin-bottom: 10px;
  }
  
  .action-buttons {
    flex-direction: column;
    gap: 5px;
  }
  
  .action-buttons .btn {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .card-header,
  .card-body {
    padding: 12px;
  }
  
  .stat-card {
    padding: 12px;
  }
  
  .stat-number {
    font-size: 20px;
  }
  
  .btn {
    font-size: 12px;
    padding: 6px 12px;
  }
  
  .btn-sm {
    font-size: 11px;
    padding: 4px 8px;
  }
} 