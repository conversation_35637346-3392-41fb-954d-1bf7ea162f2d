package com.unnet.jmanul.system.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.unnet.jmanul.common.constants.Empty;
import com.unnet.jmanul.common.utils.DateTimeUtils;
import com.unnet.jmanul.system.constants.AccountSource;
import com.unnet.jmanul.system.service.dto.user.UserGetReq;
import com.unnet.jmanul.system.service.dto.user.UserGetResp;
import com.unnet.jmanul.system.service.dto.user.UserPostReq;
import com.unnet.jmanul.system.service.dto.user.UserPutReq;
import com.unnet.jmanul.system.entity.User;
import com.unnet.jmanul.system.service.IUserService;
import io.swagger.annotations.Api;
import org.springframework.beans.BeanUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

import java.util.List;
import java.util.stream.Collectors;

import static com.unnet.jmanul.common.rest.ApiDef.V1_ADMIN_USER;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-11
 */
@RestController
@RequestMapping(V1_ADMIN_USER)
@Api(tags = {"Z. 系统用户管理 UserController"})
public class UserController {
    @Resource
    private IUserService svc;
    @Resource
    private PasswordEncoder passwordEncoder;

    @GetMapping("")
    public ResponseEntity<Page<UserGetResp>> gets(UserGetReq req) {
        Page<User> page = new Page<>(req.getPage(), req.getSize(), true);
        QueryWrapper<User> queryWrapper = new QueryWrapper<>();

        if (req.getName() != null) {
            queryWrapper.like("name", req.getName());
        }
        if (req.getUsername() != null) {
            queryWrapper.like("username", req.getUsername());
        }

        if (req.getSortBy() != null) {
            queryWrapper.orderBy(true, req.isAsc(), req.getSortBy());
        }

        Page<User> items = svc.page(page, queryWrapper);

        List<UserGetResp> records = items.getRecords()
                .stream()
                .map(UserGetResp::fromUser)
                .collect(Collectors.toList());

        Page<UserGetResp> resp = new Page<>();
        resp.setCurrent(items.getCurrent());
        resp.setSize(items.getSize());
        resp.setTotal(items.getTotal());
        resp.setRecords(records);

        return ResponseEntity.ok(resp);
    }

    @GetMapping("/{id}")
    public ResponseEntity<UserGetResp> get(@PathVariable String id) {
        User item = svc.getById(id);
        if (item == null) {
            return ResponseEntity.notFound().build();
        }
        return ResponseEntity.ok(UserGetResp.fromUser(item));
    }

    @PostMapping("")
    public ResponseEntity<Boolean> post(@RequestBody UserPostReq req) {
        User item = new User();
        BeanUtils.copyProperties(req, item);

        item.setAccountSource(AccountSource.INTERNAL);
        item.setEnable(req.getEnable() != null ? req.getEnable() : true);
        item.setLocked(req.getLocked() != null ? req.getLocked() : false);

        // 如果没有设置到期时间，则使用默认的最大时间
        if (item.getAccountExpireDate() == null) {
            item.setAccountExpireDate(DateTimeUtils.getMaxLocalDate());
        }
        if (item.getCredentialExpireDate() == null) {
            item.setCredentialExpireDate(DateTimeUtils.getMaxLocalDate());
        }

        item.setPassword(passwordEncoder.encode(item.getPassword()));

        boolean ok = svc.save(item);
        if (!ok) {
            return ResponseEntity.badRequest().build();
        }

        return ResponseEntity.ok(true);
    }

    @PutMapping("/{id}")
    public ResponseEntity<Boolean> put(@RequestBody UserPutReq req, @PathVariable Long id) {
        User item = svc.getById(id);
        if (item == null) {
            return ResponseEntity.notFound().build();
        }

        BeanUtils.copyProperties(req, item);
        item.setId(id);

        boolean ok = svc.updateById(item);
        if (!ok) {
            return ResponseEntity.badRequest().build();
        }

        return ResponseEntity.ok(true);
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Empty> delete(@PathVariable Long id) {
        boolean ok = svc.removeById(id);
        if (!ok) {
            return ResponseEntity.badRequest().body(Empty.instant());
        }

        return ResponseEntity.ok(Empty.instant());
    }
}
