//package com.unnet.changan5G.controller;
//
//import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
//import com.baomidou.mybatisplus.core.metadata.IPage;
//import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
//import com.unnet.changan5G.document.TerminalMetricDocument;
//import com.unnet.changan5G.dto.ApiResp;
//import com.unnet.changan5G.dto.terminal.TerminalDetailResponse;
//import com.unnet.changan5G.dto.terminal.TerminalListRequest;
//import com.unnet.changan5G.dto.terminal.TerminalListResponse;
//import com.unnet.changan5G.dto.terminal.TerminalUpdateRequest;
//import com.unnet.changan5G.entity.TerminalBasicInfoEntity;
//import com.unnet.changan5G.mapper.TerminalBasicInfoMapper;
//import com.unnet.changan5G.service.TerminalBasicInfoService;
//import com.unnet.changan5G.service.TerminalMetricElasticsearchService;
//import io.swagger.v3.oas.annotations.Operation;
//import io.swagger.v3.oas.annotations.Parameter;
//import io.swagger.v3.oas.annotations.responses.ApiResponse;
//import io.swagger.v3.oas.annotations.responses.ApiResponses;
//import io.swagger.v3.oas.annotations.tags.Tag;
//import lombok.RequiredArgsConstructor;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.BeanUtils;
//import org.springframework.util.StringUtils;
//import org.springframework.web.bind.annotation.*;
//
//import javax.validation.Valid;
//import java.time.LocalDateTime;
//import java.util.List;
//import java.util.Optional;
//import java.util.stream.Collectors;
//
///**
// * 终端管理Controller
// *
// * <AUTHOR>
// * @date 2024-07-16
// */
//@RestController
//@RequestMapping("/terminals")
//@Slf4j
//@RequiredArgsConstructor
//@Tag(name = "终端管理", description = "终端列表查询、详情查询、信息修改等功能")
//public class TerminalController {
//
//    private final TerminalBasicInfoService terminalBasicInfoService;
//    private final TerminalMetricElasticsearchService terminalMetricElasticsearchService;
//    private final TerminalBasicInfoMapper terminalBasicInfoMapper;
//
//    /**
//     * 分页查询终端列表
//     */
//    @GetMapping
//    @Operation(summary = "分页查询终端列表", description = "支持按设备ID、主机名、状态、标签等条件过滤查询")
//    @ApiResponses({
//        @ApiResponse(responseCode = "200", description = "查询成功"),
//        @ApiResponse(responseCode = "400", description = "请求参数错误"),
//        @ApiResponse(responseCode = "500", description = "服务器内部错误")
//    })
//    public ApiResp<TerminalListResponse> getTerminalList(
//            @Parameter(description = "页码，从1开始") @RequestParam(defaultValue = "1") int page,
//            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") int pageSize,
//            @Parameter(description = "设备ID") @RequestParam(required = false) String deviceId,
//            @Parameter(description = "主机名") @RequestParam(required = false) String hostname,
//            @Parameter(description = "状态：0-离线，1-在线") @RequestParam(required = false) Integer status,
//            @Parameter(description = "标签搜索") @RequestParam(required = false) String tag) {
//
//        try {
//            log.info("查询终端列表 - 页码: {}, 每页大小: {}, 设备ID: {}, 主机名: {}, 状态: {}, 标签: {}",
//                    page, pageSize, deviceId, hostname, status, tag);
//
//            // 参数验证
//            if (page < 1) page = 1;
//            if (pageSize < 1 || pageSize > 100) pageSize = 10;
//
//            // 如果有标签搜索，使用自定义方法
//            if (StringUtils.hasText(tag)) {
//                // 使用自定义的标签搜索方法，避免MyBatis-Plus的参数绑定问题
//                return handleTagSearch(page, pageSize, deviceId, hostname, status, tag);
//            }
//
//            // 构建查询条件
//            QueryWrapper<TerminalBasicInfoEntity> queryWrapper = new QueryWrapper<>();
//            queryWrapper.eq("is_deleted", 0);
//
//            if (StringUtils.hasText(deviceId)) {
//                queryWrapper.like("device_id", deviceId);
//            }
//            if (StringUtils.hasText(hostname)) {
//                queryWrapper.like("hostname", hostname);
//            }
//            if (status != null) {
//                queryWrapper.eq("status", status);
//            }
//
//            // 按最后更新时间倒序排列
//            queryWrapper.orderByDesc("last_update_time");
//
//            // 分页查询
//            Page<TerminalBasicInfoEntity> pageRequest = new Page<>(page, pageSize);
//            IPage<TerminalBasicInfoEntity> pageResult = terminalBasicInfoService.page(pageRequest, queryWrapper);
//
//            // 转换为响应对象
//            List<TerminalListResponse.TerminalItem> terminalItems = pageResult.getRecords().stream()
//                    .map(this::convertToTerminalItem)
//                    .collect(Collectors.toList());
//
//            TerminalListResponse response = new TerminalListResponse();
//            response.setList(terminalItems);
//            response.setTotal(pageResult.getTotal());
//            response.setPage(page);
//            response.setPageSize(pageSize);
//
//            log.info("查询终端列表成功 - 总数: {}, 当前页数据量: {}", pageResult.getTotal(), terminalItems.size());
//            return ApiResp.success(response);
//
//        } catch (Exception e) {
//            log.error("查询终端列表失败 - 错误: {}", e.getMessage(), e);
//            return ApiResp.error(500, "查询终端列表失败: " + e.getMessage());
//        }
//    }
//
//    /**
//     * 查询终端详情
//     */
//    @GetMapping("/{deviceId}")
//    @Operation(summary = "查询终端详情", description = "获取终端基本信息和最新实时指标信息")
//    @ApiResponses({
//        @ApiResponse(responseCode = "200", description = "查询成功"),
//        @ApiResponse(responseCode = "404", description = "终端不存在"),
//        @ApiResponse(responseCode = "500", description = "服务器内部错误")
//    })
//    public ApiResp<TerminalDetailResponse> getTerminalDetail(
//            @Parameter(description = "设备ID", required = true) @PathVariable String deviceId) {
//
//        try {
//            log.info("查询终端详情 - 设备: {}", deviceId);
//
//            // 查询基本信息
//            TerminalBasicInfoEntity basicInfo = terminalBasicInfoService.getByDeviceId(deviceId);
//            if (basicInfo == null) {
//                log.warn("终端不存在 - 设备: {}", deviceId);
//                return ApiResp.error(404, "终端不存在");
//            }
//
//            // 查询最新指标信息
//            Optional<TerminalMetricDocument> latestMetric = terminalMetricElasticsearchService
//                    .getLatestMetricByDeviceId(deviceId);
//
//            // 构建响应对象
//            TerminalDetailResponse response = new TerminalDetailResponse();
//            BeanUtils.copyProperties(basicInfo, response);
//
//            if (latestMetric.isPresent()) {
//                TerminalMetricDocument metric = latestMetric.get();
//                response.setLatestMetric(metric);
//                response.setGroupUsage(metric.getGroupUsage());
//                response.setGroupBps(metric.getGroupBps());
//                log.info("获取到最新指标数据 - 设备: {}, 采集时间: {}", deviceId, metric.getMetricTime());
//            } else {
//                log.warn("未找到最新指标数据 - 设备: {}", deviceId);
//                response.setLatestMetric(null);
//                response.setGroupUsage(null);
//                response.setGroupBps(null);
//            }
//
//            log.info("查询终端详情成功 - 设备: {}", deviceId);
//            return ApiResp.success(response);
//
//        } catch (Exception e) {
//            log.error("查询终端详情失败 - 设备: {}, 错误: {}", deviceId, e.getMessage(), e);
//            return ApiResp.error(500, "查询终端详情失败: " + e.getMessage());
//        }
//    }
//
//    /**
//     * 更新终端信息
//     */
//    @PutMapping("/{deviceId}")
//    @Operation(summary = "更新终端信息", description = "更新终端的主机名、过期时间、标签、自定义字段等信息")
//    @ApiResponses({
//        @ApiResponse(responseCode = "200", description = "更新成功"),
//        @ApiResponse(responseCode = "404", description = "终端不存在"),
//        @ApiResponse(responseCode = "400", description = "请求参数错误"),
//        @ApiResponse(responseCode = "500", description = "服务器内部错误")
//    })
//    public ApiResp<TerminalBasicInfoEntity> updateTerminal(
//            @Parameter(description = "设备ID", required = true) @PathVariable String deviceId,
//            @Parameter(description = "更新请求", required = true) @Valid @RequestBody TerminalUpdateRequest request) {
//
//        try {
//            log.info("更新终端信息 - 设备: {}, 请求: {}", deviceId, request);
//
//            // 查询终端是否存在
//            TerminalBasicInfoEntity existingTerminal = terminalBasicInfoService.getByDeviceId(deviceId);
//            if (existingTerminal == null) {
//                log.warn("终端不存在 - 设备: {}", deviceId);
//                return ApiResp.error(404, "终端不存在");
//            }
//
//            // 更新字段
//            if (StringUtils.hasText(request.getHostname())) {
//                existingTerminal.setHostname(request.getHostname());
//            }
//            if (StringUtils.hasText(request.getExpiredDate())) {
//                existingTerminal.setExpiredDate(request.getExpiredDate());
//            }
//            if (request.getTags() != null) {
//                existingTerminal.setTags(request.getTags());
//            }
//            if (request.getCustomFields() != null) {
//                existingTerminal.setCustomFields(request.getCustomFields());
//            }
//
//            // 更新最后修改时间
//            existingTerminal.setLastUpdateTime(LocalDateTime.now());
//
//            // 保存更新
//            boolean updateResult = terminalBasicInfoService.updateById(existingTerminal);
//            if (!updateResult) {
//                log.error("更新终端信息失败 - 设备: {}", deviceId);
//                return ApiResp.error(500, "更新终端信息失败");
//            }
//
//            log.info("更新终端信息成功 - 设备: {}", deviceId);
//            return ApiResp.success(existingTerminal);
//
//        } catch (Exception e) {
//            log.error("更新终端信息失败 - 设备: {}, 错误: {}", deviceId, e.getMessage(), e);
//            return ApiResp.error(500, "更新终端信息失败: " + e.getMessage());
//        }
//    }
//
//    /**
//     * 删除终端
//     */
//    @DeleteMapping("/{deviceId}")
//    @Operation(summary = "删除终端", description = "删除离线状态的终端（逻辑删除）")
//    @ApiResponses({
//        @ApiResponse(responseCode = "200", description = "删除成功"),
//        @ApiResponse(responseCode = "404", description = "终端不存在"),
//        @ApiResponse(responseCode = "400", description = "只能删除离线终端"),
//        @ApiResponse(responseCode = "500", description = "服务器内部错误")
//    })
//    public ApiResp<String> deleteTerminal(
//            @Parameter(description = "设备ID", required = true) @PathVariable String deviceId) {
//
//        try {
//            log.info("删除终端 - 设备: {}", deviceId);
//
//            // 查询终端是否存在
//            TerminalBasicInfoEntity terminal = terminalBasicInfoService.getByDeviceId(deviceId);
//            if (terminal == null) {
//                log.warn("终端不存在 - 设备: {}", deviceId);
//                return ApiResp.error(404, "终端不存在");
//            }
//
//            // 检查终端状态，只能删除离线终端
//            if (terminal.getStatus() != 0) {
//                log.warn("只能删除离线终端 - 设备: {}, 当前状态: {}", deviceId, terminal.getStatus());
//                return ApiResp.error(400, "只能删除离线状态的终端");
//            }
//
//            // 逻辑删除
//            terminal.setIsDeleted(1);
//            terminal.setUpdateTime(LocalDateTime.now());
//
//            boolean deleteResult = terminalBasicInfoService.updateById(terminal);
//            if (!deleteResult) {
//                log.error("删除终端失败 - 设备: {}", deviceId);
//                return ApiResp.error(500, "删除终端失败");
//            }
//
//            log.info("删除终端成功 - 设备: {}", deviceId);
//            return ApiResp.success("删除终端成功");
//
//        } catch (Exception e) {
//            log.error("删除终端失败 - 设备: {}, 错误: {}", deviceId, e.getMessage(), e);
//            return ApiResp.error(500, "删除终端失败: " + e.getMessage());
//        }
//    }
//
//    /**
//     * 处理标签搜索的特殊方法
//     */
//    private ApiResp<TerminalListResponse> handleTagSearch(int page, int pageSize, String deviceId,
//                                                         String hostname, Integer status, String tag) {
//        try {
//            // 使用自定义Mapper方法进行标签搜索
//            List<TerminalBasicInfoEntity> allResults = terminalBasicInfoMapper
//                    .selectByTagSearch("%" + tag + "%");
//
//            // 在内存中进行其他条件过滤
//            List<TerminalBasicInfoEntity> filteredResults = allResults.stream()
//                    .filter(entity -> {
//                        if (StringUtils.hasText(deviceId) && !entity.getDeviceId().contains(deviceId)) {
//                            return false;
//                        }
//                        if (StringUtils.hasText(hostname) && !entity.getHostname().contains(hostname)) {
//                            return false;
//                        }
//                        if (status != null && !status.equals(entity.getStatus())) {
//                            return false;
//                        }
//                        return true;
//                    })
//                    .sorted((a, b) -> b.getLastUpdateTime().compareTo(a.getLastUpdateTime()))
//                    .collect(Collectors.toList());
//
//            // 手动分页
//            int total = filteredResults.size();
//            int startIndex = (page - 1) * pageSize;
//            int endIndex = Math.min(startIndex + pageSize, total);
//
//            List<TerminalBasicInfoEntity> pageResults = filteredResults.subList(startIndex, endIndex);
//
//            // 转换为响应对象
//            List<TerminalListResponse.TerminalItem> terminalItems = pageResults.stream()
//                    .map(this::convertToTerminalItem)
//                    .collect(Collectors.toList());
//
//            TerminalListResponse response = new TerminalListResponse();
//            response.setList(terminalItems);
//            response.setTotal((long) total);
//            response.setPage(page);
//            response.setPageSize(pageSize);
//
//            log.info("标签搜索完成 - 总数: {}, 当前页数据量: {}", total, terminalItems.size());
//            return ApiResp.success(response);
//
//        } catch (Exception e) {
//            log.error("标签搜索失败 - 错误: {}", e.getMessage(), e);
//            return ApiResp.error(500, "标签搜索失败: " + e.getMessage());
//        }
//    }
//
//    /**
//     * 转换为终端列表项
//     */
//    private TerminalListResponse.TerminalItem convertToTerminalItem(TerminalBasicInfoEntity entity) {
//        TerminalListResponse.TerminalItem item = new TerminalListResponse.TerminalItem();
//        BeanUtils.copyProperties(entity, item);
//        return item;
//    }
//}
