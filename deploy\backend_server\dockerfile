# 长安5G管理平台 - 数据收集服务多阶段构建

# 构建阶段
FROM maven:3.8.6-openjdk-8-slim AS builder

WORKDIR /app

# 设置Maven阿里云镜像源
RUN mkdir -p /root/.m2 && \
    printf '%s\n' \
    '<?xml version="1.0" encoding="UTF-8"?>' \
    '<settings>' \
    '  <mirrors>' \
    '    <mirror>' \
    '      <id>aliyun</id>' \
    '      <mirrorOf>*</mirrorOf>' \
    '      <name>Aliyun Public</name>' \
    '      <url>https://maven.aliyun.com/repository/public</url>' \
    '    </mirror>' \
    '  </mirrors>' \
    '</settings>' \
    > /root/.m2/settings.xml

# 复制pom.xml并下载依赖
COPY pom.xml ./
RUN mvn dependency:go-offline -B

# 复制源代码并构建
COPY src ./src
RUN mvn clean package -DskipTests -B

# 运行阶段
FROM openjdk:8-jre-slim

WORKDIR /app

# 复制应用JAR
COPY --from=builder /app/target/backend_server-0.0.1.jar ./app.jar

# 创建日志目录
RUN mkdir -p /app/logs

# 配置
EXPOSE 8081
ENV JAVA_OPTS="-Xms512m -Xmx1024m -XX:+UseG1GC"

# 启动应用
CMD java $JAVA_OPTS -jar app.jar
