package com.unnet.changan5G.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 终端基本信息实体类
 * 
 * <AUTHOR>
 * @date 2024-07-14
 */
@Data
@TableName(value = "terminal_basic_info", autoResultMap = true)
public class TerminalBasicInfoEntity {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("device_id")
    private String deviceId;

    @TableField("hostname")
    private String hostname;

    @TableField("identity_mac")
    private String identityMac;

    @TableField(value = "app_version", typeHandler = JacksonTypeHandler.class)
    private Object appVersion;

    @TableField("expired_date")
    private String expiredDate;

    @TableField("receive_time")
    private LocalDateTime receiveTime;

    @TableField("data_source")
    private String dataSource;

    @TableField("status")
    private Integer status;

    @TableField("first_register_time")
    private LocalDateTime firstRegisterTime;

    @TableField("last_update_time")
    private LocalDateTime lastUpdateTime;

    @TableField(value = "tags", typeHandler = JacksonTypeHandler.class)
    private Object tags;

    @TableField(value = "custom_fields", typeHandler = JacksonTypeHandler.class)
    private Object customFields;

    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @TableField("is_deleted")
    @TableLogic
    private Integer isDeleted;
}
