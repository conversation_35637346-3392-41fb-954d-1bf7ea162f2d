package com.unnet.jmanul.system.service;

import com.unnet.jmanul.system.entity.User;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Optional;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-11
 */
public interface IUserService extends IService<User> {

    Optional<String> getAuthPublicKey();

    Optional<String> decodeAuthCredential(String encrypted);

    List<User> getUserWithUsernamePrefix(String prefix);

    Optional<User> getUserByUsername(String username);

    Optional<String> isAuthenticated(String token);

    boolean putUserPassword(String username, String currentPassword, String newPassword);

    boolean putUserName(String username, String name);
}
