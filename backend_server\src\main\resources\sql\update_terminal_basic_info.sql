-- ========================================
-- 终端基本信息表字段扩展脚本
-- 添加标签和自定义字段支持
-- 创建时间: 2024-07-14
-- ========================================

-- 为现有的终端基本信息表添加新字段
ALTER TABLE `terminal_basic_info` 
ADD COLUMN `tags` json DEFAULT NULL COMMENT '终端标签信息(JSON格式)，用于标签过滤' AFTER `is_deleted`,
ADD COLUMN `custom_fields` json DEFAULT NULL COMMENT '自定义字段信息(JSON格式)，用于动态扩展字段' AFTER `tags`;

-- 更新现有数据，添加示例标签和自定义字段
UPDATE `terminal_basic_info` 
SET 
  `tags` = JSON_OBJECT(
    'location', '北京',
    'department', '研发部', 
    'environment', '生产',
    'priority', '高'
  ),
  `custom_fields` = JSON_OBJECT(
    'vehicle_model', '长安CS75',
    'installation_date', '2024-01-15',
    'maintenance_contact', '张工',
    'notes', '重要测试设备'
  )
WHERE `device_id` = '_0fd94938951f4a64bb11c6817a81f7e7';

UPDATE `terminal_basic_info` 
SET 
  `tags` = JSON_OBJECT(
    'location', '上海',
    'department', '测试部',
    'environment', '测试', 
    'priority', '中'
  ),
  `custom_fields` = JSON_OBJECT(
    'vehicle_model', '长安UNI-T',
    'installation_date', '2024-02-20',
    'maintenance_contact', '李工',
    'notes', '测试环境设备'
  )
WHERE `device_id` = '_test_device_001';

-- 验证字段添加成功
SELECT 'Terminal basic info table updated successfully!' AS message;

-- 查看表结构
DESC `terminal_basic_info`;

-- 查看示例数据
SELECT 
  `device_id`,
  `hostname`,
  `tags`,
  `custom_fields`
FROM `terminal_basic_info` 
LIMIT 5;
