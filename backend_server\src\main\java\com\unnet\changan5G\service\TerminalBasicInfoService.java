package com.unnet.changan5G.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.unnet.changan5G.dto.terminal.TerminalBasicInfo;
import com.unnet.changan5G.entity.TerminalBasicInfoEntity;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 终端基本信息服务接口
 *
 * <AUTHOR>
 * @date 2024-07-14
 */
public interface TerminalBasicInfoService extends IService<TerminalBasicInfoEntity> {

    /**
     * 根据设备ID查询终端基本信息
     */
    TerminalBasicInfoEntity getByDeviceId(String deviceId);

    /**
     * 保存或更新终端基本信息
     */
    boolean saveOrUpdateTerminalInfo(TerminalBasicInfo terminalBasicInfo);

    /**
     * 更新设备状态
     */
    boolean updateDeviceStatus(String deviceId, Integer status);

    /**
     * 批量更新设备状态
     */
    boolean batchUpdateDeviceStatus(List<String> deviceIds, Integer status);

    /**
     * 检查设备是否存在
     */
    boolean existsByDeviceId(String deviceId);

    /**
     * 根据MAC地址查询终端基本信息
     */
    TerminalBasicInfoEntity getByIdentityMac(String identityMac);

    /**
     * 检查设备是否存在（根据MAC地址）
     */
    boolean existsByIdentityMac(String identityMac);

    /**
     * 获取在线设备数量
     */
    long getOnlineDeviceCount();

    /**
     * 获取离线设备数量
     */
    long getOfflineDeviceCount();

    /**
     * 获取所有设备的最后更新时间
     */
    List<TerminalBasicInfoEntity> getDeviceLastUpdateTimes();

    /**
     * 根据标签查询设备
     */
    List<TerminalBasicInfoEntity> getDevicesByTag(String tagKey, String tagValue);

    /**
     * 查询即将过期的设备
     */
    List<TerminalBasicInfoEntity> getExpiringDevices(int days);

    /**
     * 注册新设备
     */
    boolean registerNewDevice(TerminalBasicInfo terminalBasicInfo);

    /**
     * 更新设备最后上报时间
     */
    boolean updateLastReportTime(String deviceId, LocalDateTime reportTime);

    /**
     * 根据MAC地址更新设备最后上报时间
     */
    boolean updateLastReportTimeByMac(String identityMac, LocalDateTime reportTime);

    /**
     * 统计在线设备数量
     */
    int countOnlineDevices();

    /**
     * 统计离线设备数量
     */
    int countOfflineDevices();

    /**
     * 根据MAC地址更新设备状态
     * @param mac 设备MAC地址
     * @param status 设备状态（0-离线，1-在线）
     * @return 更新是否成功
     */
    boolean updateDeviceStatusByMac(String mac, int status);
}
