<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unnet.jmanul.business.mapper.LogFileMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.unnet.jmanul.business.entity.LogFile">
        <id column="id" property="id" />
        <result column="identity_mac" property="identityMac" />
        <result column="original_filename" property="originalFilename" />
        <result column="stored_filename" property="storedFilename" />
        <result column="file_size" property="fileSize" />
        <result column="file_type" property="fileType" />
        <result column="storage_path" property="storagePath" />
        <result column="file_md5" property="fileMd5" />
        <result column="upload_time" property="uploadTime" />
        <result column="description" property="description" />
        <result column="status" property="status" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="deleted" property="deleted" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, identity_mac, original_filename, stored_filename, file_size, file_type, 
        storage_path, file_md5, upload_time, description, status, create_time, update_time, deleted
    </sql>

    <!-- 根据终端MAC地址分页查询日志文件 -->
    <select id="selectByIdentityMacPage" resultMap="BaseResultMap">
        SELECT 
            <include refid="Base_Column_List" />
        FROM log_file 
        WHERE identity_mac = #{identityMac} 
          AND deleted = 0 
          AND status = 0
        ORDER BY upload_time DESC, create_time DESC
    </select>

    <!-- 根据终端MAC地址查询日志文件列表 -->
    <select id="selectByIdentityMac" resultMap="BaseResultMap">
        SELECT 
            <include refid="Base_Column_List" />
        FROM log_file 
        WHERE identity_mac = #{identityMac} 
          AND deleted = 0 
          AND status = 0
        ORDER BY upload_time DESC, create_time DESC
    </select>

    <!-- 根据文件MD5查询是否存在重复文件 -->
    <select id="selectByIdentityMacAndMd5" resultMap="BaseResultMap">
        SELECT 
            <include refid="Base_Column_List" />
        FROM log_file 
        WHERE identity_mac = #{identityMac} 
          AND file_md5 = #{fileMd5}
          AND deleted = 0 
          AND status = 0
        LIMIT 1
    </select>

</mapper>
