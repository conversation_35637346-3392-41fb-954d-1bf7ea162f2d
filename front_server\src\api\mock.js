// 模拟终端数据
export const mockTerminals = [
  {
    id: 1,
    deviceId: 'TERM-001',
    hostname: 'terminal-beijing-01',
    identityMac: '00:1B:44:11:3A:B7',
    appVersion: {
      Controller: '1.0.1',
      Detector: '1.0.48',
      Web: '1.0.55',
      Agent: '1.0.0',
      Front: '1.0.40'
    },
    expiredDate: '2025-12-31',
    receiveTime: '2024-01-15 14:30:25',
    dataSource: 'kafka',
    status: 1, // 1-在线, 0-离线
    firstRegisterTime: '2024-01-01 08:00:00',
    lastUpdateTime: '2024-01-15 14:30:25',
    tags: {
      位置: '北京',
      部门: '研发部',
      环境: '生产',
      优先级: '高'
    },
    customFields: {
      车辆型号: '长安车型',
      安装日期: '2024-01-01',
      维护联系人: '张三',
      备注: '重要设备'
    }
  },
  {
    id: 2,
    deviceId: 'TERM-002',
    hostname: 'terminal-shanghai-01',
    identityMac: '00:1B:44:11:3A:B8',
    appVersion: {
      Controller: '1.0.1',
      Detector: '1.0.47',
      Web: '1.0.54',
      Agent: '1.0.0',
      Front: '1.0.39'
    },
    expiredDate: '2025-11-30',
    receiveTime: '2024-01-15 14:28:15',
    dataSource: 'kafka',
    status: 0,
    firstRegisterTime: '2024-01-02 09:15:00',
    lastUpdateTime: '2024-01-15 14:28:15',
    tags: {
      位置: '上海',
      部门: '测试部',
      环境: '测试',
      优先级: '中'
    },
    customFields: {
      车辆型号: '测试车型',
      安装日期: '2024-01-02',
      维护联系人: '李四',
      备注: '测试设备'
    }
  },
  {
    id: 3,
    deviceId: 'TERM-003',
    hostname: 'terminal-guangzhou-01',
    identityMac: '00:1B:44:11:3A:B9',
    appVersion: {
      Controller: '1.0.1',
      Detector: '1.0.48',
      Web: '1.0.55',
      Agent: '1.0.0',
      Front: '1.0.40'
    },
    expiredDate: '2025-12-15',
    receiveTime: '2024-01-15 14:32:10',
    dataSource: 'kafka',
    status: 1,
    firstRegisterTime: '2024-01-03 10:30:00',
    lastUpdateTime: '2024-01-15 14:32:10',
    tags: {
      位置: '广州',
      部门: '运维部',
      环境: '生产',
      优先级: '高'
    },
    customFields: {
      车辆型号: '长安车型Pro',
      安装日期: '2024-01-03',
      维护联系人: '王五',
      备注: '关键节点'
    }
  },
  {
    id: 4,
    deviceId: '_0fd94938951f4a64bb11c6817a81f7e7',
    hostname: 'ec_3568_25030031',
    identityMac: 'de:07:65:52:3e:60',
    appVersion: {
      Controller: '1.0.1',
      Detector: '1.0.48',
      Web: '1.0.55',
      Agent: '1.0.0',
      Front: '1.0.40'
    },
    expiredDate: '2035-03-09 22:47:20',
    receiveTime: '2024-07-14 18:30:45',
    dataSource: 'kafka',
    status: 1,
    firstRegisterTime: '2024-07-14 18:30:45',
    lastUpdateTime: '2024-07-14 18:30:45',
    tags: {
      位置: '未知',
      部门: '运维部',
      环境: '生产',
      优先级: '高'
    },
    customFields: {
      车辆型号: '长安车型',
      安装日期: '2024-07-14',
      维护联系人: '技术支持',
      备注: '生产环境设备'
    }
  }
]

// 模拟告警数据
export const mockAlerts = [
  {
    id: 1,
    alertId: 'alert-001',
    deviceId: 'TERM-001',
    alertType: 'CPU_TEMPERATURE',
    alertDetails: 'CPU温度超过阈值',
    metricName: 'cpu_temperature',
    threshold: '80',
    currentValue: '85.9',
    alertTime: '2024-01-15 14:25:00',
    alertStatus: 'ACTIVE',
    resolvedTime: null,
    acknowledgedTime: null,
    acknowledgedBy: null,
    resolveComment: null,
    notificationSent: false,
    notificationTime: null,
    createTime: '2024-01-15 14:25:00',
    updateTime: '2024-01-15 14:25:00'
  },
  {
    id: 2,
    alertId: 'alert-002',
    deviceId: 'TERM-002',
    alertType: 'MEMORY_USAGE',
    alertDetails: '内存使用率超过阈值',
    metricName: 'memory_usage_percent',
    threshold: '85',
    currentValue: '92.5',
    alertTime: '2024-01-15 14:28:15',
    alertStatus: 'ACTIVE',
    resolvedTime: null,
    acknowledgedTime: null,
    acknowledgedBy: null,
    resolveComment: null,
    notificationSent: true,
    notificationTime: '2024-01-15 14:28:20',
    createTime: '2024-01-15 14:28:15',
    updateTime: '2024-01-15 14:28:20'
  },
  {
    id: 3,
    alertId: 'alert-003',
    deviceId: 'TERM-003',
    alertType: 'DISK_USAGE',
    alertDetails: '磁盘使用率超过阈值',
    metricName: 'disk_usage_percent',
    threshold: '90',
    currentValue: '95.2',
    alertTime: '2024-01-15 13:45:00',
    alertStatus: 'RESOLVED',
    resolvedTime: '2024-01-15 14:00:00',
    acknowledgedTime: '2024-01-15 13:55:00',
    acknowledgedBy: '王五',
    resolveComment: '磁盘清理完成，使用率已降至正常水平',
    notificationSent: true,
    notificationTime: '2024-01-15 13:45:05',
    createTime: '2024-01-15 13:45:00',
    updateTime: '2024-01-15 14:00:00'
  },
  {
    id: 4,
    alertId: 'alert-004',
    deviceId: '_0fd94938951f4a64bb11c6817a81f7e7',
    alertType: 'LICENSE_EXPIRY',
    alertDetails: '许可证即将过期',
    metricName: 'license_expiry_days',
    threshold: '30',
    currentValue: '15',
    alertTime: '2024-07-14 18:25:30',
    alertStatus: 'ACKNOWLEDGED',
    resolvedTime: null,
    acknowledgedTime: '2024-07-14 18:30:00',
    acknowledgedBy: '技术支持',
    resolveComment: null,
    notificationSent: true,
    notificationTime: '2024-07-14 18:25:35',
    createTime: '2024-07-14 18:25:30',
    updateTime: '2024-07-14 18:30:00'
  }
]

// 模拟实时指标数据
export const mockMetrics = {
  '00:1B:44:11:3A:B7': {
    identityMac: '00:1B:44:11:3A:B7',
    hostname: 'terminal-beijing-01',
    timestamp: '2024-01-15 14:30:25',
    cpu: {
      usage: 92.5,
      cores: 8,
      temperature: 65.2
    },
    memory: {
      usage: 78.3,
      total: 16384,
      used: 12820,
      available: 3564
    },
    disk: {
      usage: 45.2,
      total: 512000,
      used: 231424,
      available: 280576
    },
    network: {
      uploadSpeed: 1024,
      downloadSpeed: 2048,
      totalUpload: 1024000,
      totalDownload: 2048000
    }
  },
  '00:1B:44:11:3A:B8': {
    identityMac: '00:1B:44:11:3A:B8',
    hostname: 'terminal-shanghai-01',
    timestamp: '2024-01-15 14:28:15',
    cpu: {
      usage: 0,
      cores: 4,
      temperature: 0
    },
    memory: {
      usage: 0,
      total: 8192,
      used: 0,
      available: 0
    },
    disk: {
      usage: 0,
      total: 256000,
      used: 0,
      available: 0
    },
    network: {
      uploadSpeed: 0,
      downloadSpeed: 0,
      totalUpload: 0,
      totalDownload: 0
    }
  },
  'TERM-003': {
    deviceId: 'TERM-003',
    hostname: 'terminal-guangzhou-01',
    timestamp: '2024-01-15 14:32:10',
    cpu: {
      usage: 35.6,
      cores: 16,
      temperature: 52.8
    },
    memory: {
      usage: 62.4,
      total: 32768,
      used: 20447,
      available: 12321
    },
    disk: {
      usage: 68.9,
      total: 1024000,
      used: 705536,
      available: 318464
    },
    network: {
      uploadSpeed: 512,
      downloadSpeed: 1024,
      totalUpload: 512000,
      totalDownload: 1024000
    }
  },
  'de:07:65:52:3e:60': {
    identityMac: 'de:07:65:52:3e:60',
    hostname: 'ec_3568_25030031',
    timestamp: '2024-07-28 14:30:25',
    cpu: {
      usage: 45.8,
      cores: 4,
      temperature: 58.3
    },
    memory: {
      usage: 67.2,
      total: 8192,
      used: 5504,
      available: 2688
    },
    disk: {
      usage: 52.1,
      total: 512000,
      used: 266752,
      available: 245248
    },
    network: {
      uploadSpeed: 2048,
      downloadSpeed: 4096,
      totalUpload: 2048000,
      totalDownload: 4096000
    }
  }
}

// 模拟统计数据
export const mockStats = {
  totalTerminals: 3,
  onlineTerminals: 2,
  offlineTerminals: 1,
  totalAlerts: 2,
  criticalAlerts: 1,
  warningAlerts: 0,
  resolvedAlerts: 1
} 