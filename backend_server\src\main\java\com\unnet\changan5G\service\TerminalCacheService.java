package com.unnet.changan5G.service;

import com.unnet.changan5G.dto.terminal.TerminalBasicInfo;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

/**
 * 终端缓存服务接口
 * 
 * <AUTHOR>
 * @date 2024-07-15
 */
public interface TerminalCacheService {

    /**
     * 缓存终端基本信息
     */
    void cacheTerminalBasicInfo(String deviceId, TerminalBasicInfo basicInfo);

    /**
     * 获取缓存的终端基本信息
     */
    TerminalBasicInfo getCachedTerminalBasicInfo(String deviceId);

    /**
     * 删除终端基本信息缓存
     */
    void removeCachedTerminalBasicInfo(String deviceId);

    /**
     * 缓存终端最后上报时间
     */
    void cacheLastReportTime(String deviceId, LocalDateTime reportTime);

    /**
     * 获取缓存的最后上报时间
     */
    LocalDateTime getCachedLastReportTime(String deviceId);

    /**
     * 批量获取最后上报时间
     */
    List<LocalDateTime> batchGetLastReportTime(List<String> deviceIds);

    /**
     * 缓存终端在线状态
     */
    void cacheDeviceStatus(String deviceId, Integer status);

    /**
     * 获取缓存的设备状态
     */
    Integer getCachedDeviceStatus(String deviceId);

    /**
     * 批量更新设备状态
     */
    void batchUpdateDeviceStatus(List<String> deviceIds, Integer status);

    /**
     * 获取所有在线设备ID
     */
    Set<String> getOnlineDeviceIds();

    /**
     * 获取所有离线设备ID
     */
    Set<String> getOfflineDeviceIds();

    /**
     * 检查设备是否存在于缓存中
     */
    boolean isDeviceExistsInCache(String deviceId);

    /**
     * 设置设备注册标记
     */
    void markDeviceAsRegistered(String identityMac);

    /**
     * 检查设备是否已注册
     */
    boolean isDeviceRegistered(String identityMac);

    /**
     * 清理过期的缓存数据
     */
    void cleanExpiredCache();

    /**
     * 获取缓存统计信息
     */
    Object getCacheStatistics();

    /**
     * 预热缓存（从数据库加载数据到缓存）
     */
    void warmUpCache();

    /**
     * 设置设备在线状态，使用TTL自动过期
     * @param mac 设备MAC地址
     * @param ttlSeconds TTL时间（秒）
     */
    void setDeviceOnlineWithTTL(String mac, long ttlSeconds);

    /**
     * 缓存设备基本信息（使用MAC地址作为键）
     * @param mac 设备MAC地址
     * @param basicInfo 设备基本信息
     */
    void cacheTerminalBasicInfoByMac(String mac, TerminalBasicInfo basicInfo);

    /**
     * 获取缓存的设备基本信息（使用MAC地址）
     * @param mac 设备MAC地址
     * @return 设备基本信息
     */
    TerminalBasicInfo getCachedTerminalBasicInfoByMac(String mac);

    /**
     * 缓存设备最后上报时间（使用MAC地址）
     * @param mac 设备MAC地址
     * @param reportTime 上报时间
     */
    void cacheLastReportTimeByMac(String mac, LocalDateTime reportTime);

    /**
     * 获取设备最后上报时间（使用MAC地址）
     * @param mac 设备MAC地址
     * @return 最后上报时间
     */
    LocalDateTime getCachedLastReportTimeByMac(String mac);

    /**
     * 移除设备在线状态标记
     * @param mac 设备MAC地址
     */
    void removeDeviceOnlineStatus(String mac);

    /**
     * 缓存设备状态（使用MAC地址）
     * @param mac 设备MAC地址
     * @param status 设备状态（0-离线，1-在线）
     */
    void cacheDeviceStatusByMac(String mac, int status);
}
