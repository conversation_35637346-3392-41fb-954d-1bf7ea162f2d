package com.unnet.jmanul.business.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.unnet.jmanul.business.entity.MetricThresholdConfig;
import com.unnet.jmanul.business.entity.dto.MetricThresholdConfigRequest;
import com.unnet.jmanul.business.entity.dto.MetricThresholdConfigResponse;
import com.unnet.jmanul.business.entity.dto.OptionDto;
import com.unnet.jmanul.business.util.JsonFieldExtractor;
import com.unnet.jmanul.business.service.IMetricThresholdConfigService;
import com.unnet.jmanul.common.rest.ApiDef;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 指标阈值配置控制器
 *
 * <AUTHOR>
 * @since 2024-07-21
 */
@Slf4j
@RestController
@RequestMapping(ApiDef.V1_ADMIN + "/metric-threshold-config")
@RequiredArgsConstructor
@Validated
@Api(tags = {"长安5G - 指标阈值配置管理"})
public class MetricThresholdConfigController {

    private final IMetricThresholdConfigService metricThresholdConfigService;
    private final JsonFieldExtractor jsonFieldExtractor;

    @GetMapping("/page")
    @ApiOperation(value = "分页查询指标阈值配置", notes = "支持按指标类型、名称、状态等条件筛选")
    public ResponseEntity<Map<String, Object>> getConfigPage(
            @ApiParam(value = "当前页", defaultValue = "1") @RequestParam(defaultValue = "1") Long current,
            @ApiParam(value = "每页大小", defaultValue = "10") @RequestParam(defaultValue = "10") Long size,
            @ApiParam(value = "指标类型") @RequestParam(required = false) String metricType,
            @ApiParam(value = "指标名称") @RequestParam(required = false) String metricName,
            @ApiParam(value = "是否启用") @RequestParam(required = false) Boolean isEnabled,
            @ApiParam(value = "告警级别") @RequestParam(required = false) String alertLevel) {

        IPage<MetricThresholdConfigResponse> page = metricThresholdConfigService.getConfigPage(
                current, size, metricType, metricName, isEnabled, alertLevel);

        Map<String, Object> result = new HashMap<>();
        result.put("records", page.getRecords());
        result.put("total", page.getTotal());
        result.put("size", page.getSize());
        result.put("current", page.getCurrent());
        result.put("pages", page.getPages());

        return ResponseEntity.ok(result);
    }

    @GetMapping("/{id}")
    @ApiOperation(value = "根据ID获取指标阈值配置详情")
    public ResponseEntity<MetricThresholdConfigResponse> getConfigById(
            @ApiParam(value = "配置ID") @PathVariable @NotNull Long id) {

        MetricThresholdConfigResponse config = metricThresholdConfigService.getConfigById(id);
        return ResponseEntity.ok(config);
    }

    @PostMapping
    @ApiOperation(value = "创建指标阈值配置")
    public ResponseEntity<Map<String, Object>> createConfig(
            @Valid @RequestBody MetricThresholdConfigRequest request,
            Authentication authentication) {

        String operator = authentication.getName();
        boolean result = metricThresholdConfigService.createConfig(request, operator);

        Map<String, Object> response = new HashMap<>();
        response.put("success", result);
        response.put("message", result ? "创建成功" : "创建失败");

        return ResponseEntity.ok(response);
    }

    @PutMapping("/{id}")
    @ApiOperation(value = "更新指标阈值配置")
    public ResponseEntity<Map<String, Object>> updateConfig(
            @ApiParam(value = "配置ID") @PathVariable @NotNull Long id,
            @Valid @RequestBody MetricThresholdConfigRequest request,
            Authentication authentication) {

        String operator = authentication.getName();
        boolean result = metricThresholdConfigService.updateConfig(id, request, operator);

        Map<String, Object> response = new HashMap<>();
        response.put("success", result);
        response.put("message", result ? "更新成功" : "更新失败");

        return ResponseEntity.ok(response);
    }

    @DeleteMapping("/{id}")
    @ApiOperation(value = "删除指标阈值配置")
    public ResponseEntity<Map<String, Object>> deleteConfig(
            @ApiParam(value = "配置ID") @PathVariable @NotNull Long id) {

        boolean result = metricThresholdConfigService.deleteConfig(id);

        Map<String, Object> response = new HashMap<>();
        response.put("success", result);
        response.put("message", result ? "删除成功" : "删除失败");

        return ResponseEntity.ok(response);
    }

    @DeleteMapping("/batch")
    @ApiOperation(value = "批量删除指标阈值配置")
    public ResponseEntity<Map<String, Object>> batchDeleteConfig(
            @ApiParam(value = "配置ID列表") @RequestBody @NotEmpty List<Long> ids) {

        boolean result = metricThresholdConfigService.batchDeleteConfig(ids);

        Map<String, Object> response = new HashMap<>();
        response.put("success", result);
        response.put("message", result ? "批量删除成功" : "批量删除失败");

        return ResponseEntity.ok(response);
    }

    @PutMapping("/{id}/toggle")
    @ApiOperation(value = "启用/禁用指标阈值配置")
    public ResponseEntity<Map<String, Object>> toggleConfig(
            @ApiParam(value = "配置ID") @PathVariable @NotNull Long id,
            @ApiParam(value = "是否启用") @RequestParam @NotNull Boolean enabled,
            Authentication authentication) {

        String operator = authentication.getName();
        boolean result = metricThresholdConfigService.toggleConfig(id, enabled, operator);

        Map<String, Object> response = new HashMap<>();
        response.put("success", result);
        response.put("message", result ? (enabled ? "启用成功" : "禁用成功") : "操作失败");

        return ResponseEntity.ok(response);
    }

    @GetMapping("/metric-type/{metricType}")
    @ApiOperation(value = "根据指标类型获取配置")
    public ResponseEntity<MetricThresholdConfig> getConfigByMetricType(
            @ApiParam(value = "指标类型") @PathVariable String metricType) {

        MetricThresholdConfig config = metricThresholdConfigService.getConfigByMetricType(metricType);
        return ResponseEntity.ok(config);
    }

    @GetMapping("/enabled")
    @ApiOperation(value = "获取所有启用的配置")
    public ResponseEntity<List<MetricThresholdConfig>> getAllEnabledConfigs() {
        List<MetricThresholdConfig> configs = metricThresholdConfigService.getAllEnabledConfigs();
        return ResponseEntity.ok(configs);
    }

    @GetMapping("/options/metric-types")
    @ApiOperation(value = "获取指标类型选项")
    public ResponseEntity<List<OptionDto>> getMetricTypeOptions() {
        List<OptionDto> options = metricThresholdConfigService.getMetricTypeOptions();
        return ResponseEntity.ok(options);
    }

    @GetMapping("/options/alert-levels")
    @ApiOperation(value = "获取告警级别选项")
    public ResponseEntity<List<OptionDto>> getAlertLevelOptions() {
        List<OptionDto> options = metricThresholdConfigService.getAlertLevelOptions();
        return ResponseEntity.ok(options);
    }

    @PostMapping("/extract-field-paths")
    @ApiOperation(value = "从JSON数据中提取字段路径")
    public ResponseEntity<List<String>> extractFieldPaths(
            @ApiParam(value = "JSON数据") @RequestBody String jsonData) {

        List<String> fieldPaths = jsonFieldExtractor.extractAllFieldPaths(jsonData);
        return ResponseEntity.ok(fieldPaths);
    }
}
