-- 终端基本信息表
CREATE TABLE `terminal_basic_info` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `device_id` varchar(100) NOT NULL COMMENT '设备唯一标识符',
  `hostname` varchar(100) DEFAULT NULL COMMENT '主机名',
  `identity_mac` varchar(20) DEFAULT NULL COMMENT '对接长安车辆所在网口的MAC地址',
  `app_version` json DEFAULT NULL COMMENT '当前运行的组件版本号(JSON格式)',
  `expired_date` varchar(30) DEFAULT NULL COMMENT '软件授权过期时间',
  `receive_time` datetime DEFAULT NULL COMMENT '数据接收时间',
  `data_source` varchar(20) DEFAULT 'kafka' COMMENT '数据来源',
  `status` tinyint(1) DEFAULT 1 COMMENT '设备状态：0-离线，1-在线',
  `first_register_time` datetime DEFAULT NULL COMMENT '首次注册时间',
  `last_update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_deleted` tinyint(1) DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_device_id` (`device_id`),
  KEY `idx_hostname` (`hostname`),
  KEY `idx_identity_mac` (`identity_mac`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='终端基本信息表';

-- 添加索引优化查询性能
CREATE INDEX `idx_expired_date` ON `terminal_basic_info` (`expired_date`);
CREATE INDEX `idx_last_update_time` ON `terminal_basic_info` (`last_update_time`);
