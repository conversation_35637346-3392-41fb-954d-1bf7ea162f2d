package com.unnet.changan5G.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;
import org.springframework.stereotype.Service;
import org.springframework.util.concurrent.ListenableFuture;
import org.springframework.util.concurrent.ListenableFutureCallback;

/**
 * 指标数据Kafka生产者服务
 * 
 * <AUTHOR>
 * @date 2024-07-14
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class MetricKafkaProducer {

    private static final String TOPIC_NAME = "5g_metric";
    
    private final KafkaTemplate<String, String> kafkaTemplate;
    private final ObjectMapper objectMapper;

    /**
     * 发送指标数据到Kafka
     * 
     * @param metricData 指标数据对象
     * @param deviceId 设备ID作为消息key
     */
    public void sendMetricData(Object metricData, String deviceId) {
        try {
            // 将对象转换为JSON字符串
            String jsonData = objectMapper.writeValueAsString(metricData);
            
            log.info("准备发送指标数据到Kafka - Topic: {}, DeviceId: {}", TOPIC_NAME, deviceId);
            log.debug("发送的JSON数据: {}", jsonData);
            
            // 发送消息到Kafka，使用设备ID作为key确保同一设备的消息发送到同一分区
            ListenableFuture<SendResult<String, String>> future = 
                kafkaTemplate.send(TOPIC_NAME, deviceId, jsonData);
            
            // 添加回调处理发送结果
            future.addCallback(new ListenableFutureCallback<SendResult<String, String>>() {
                @Override
                public void onSuccess(SendResult<String, String> result) {
                    log.info("指标数据发送成功 - Topic: {}, Partition: {}, Offset: {}, DeviceId: {}", 
                            result.getRecordMetadata().topic(),
                            result.getRecordMetadata().partition(),
                            result.getRecordMetadata().offset(),
                            deviceId);
                }

                @Override
                public void onFailure(Throwable ex) {
                    log.error("指标数据发送失败 - DeviceId: {}, 错误: {}", deviceId, ex.getMessage(), ex);
                }
            });
            
        } catch (Exception e) {
            log.error("序列化指标数据失败 - DeviceId: {}, 错误: {}", deviceId, e.getMessage(), e);
            throw new RuntimeException("发送指标数据到Kafka失败", e);
        }
    }

    /**
     * 发送原始JSON字符串到Kafka
     * 
     * @param jsonData JSON字符串
     * @param identityMac 设备ID作为消息key
     */
    public void sendRawJsonData(String jsonData, String identityMac) {
        try {
            log.info("准备发送原始JSON数据到Kafka - Topic: {}, identityMac: {}", TOPIC_NAME, identityMac);
            log.debug("发送的原始JSON数据: {}", jsonData);
            
            ListenableFuture<SendResult<String, String>> future = 
                kafkaTemplate.send(TOPIC_NAME, identityMac, jsonData);
            
            future.addCallback(new ListenableFutureCallback<SendResult<String, String>>() {
                @Override
                public void onSuccess(SendResult<String, String> result) {
                    log.info("原始JSON数据发送成功 - Topic: {}, Partition: {}, Offset: {}, identityMac: {}",
                            result.getRecordMetadata().topic(),
                            result.getRecordMetadata().partition(),
                            result.getRecordMetadata().offset(),
                            identityMac);
                }

                @Override
                public void onFailure(Throwable ex) {
                    log.error("原始JSON数据发送失败 - identityMac: {}, 错误: {}", identityMac, ex.getMessage(), ex);
                }
            });
            
        } catch (Exception e) {
            log.error("发送原始JSON数据到Kafka失败 - identityMac: {}, 错误: {}", identityMac, e.getMessage(), e);
            throw new RuntimeException("发送原始JSON数据到Kafka失败", e);
        }
    }
}
