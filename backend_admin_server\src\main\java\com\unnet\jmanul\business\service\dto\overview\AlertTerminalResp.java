package com.unnet.jmanul.business.service.dto.overview;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 告警终端响应
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "告警终端响应")
public class AlertTerminalResp {

    @ApiModelProperty(value = "数据库主键ID", example = "1")
    private Long id;

    @ApiModelProperty(value = "告警业务ID", example = "ALERT_20250717_001")
    private String alertId;

    @ApiModelProperty(value = "设备mac地址", example = "_0fd94938951f4a64bb11c6817a81f7e7")
    private String identityMac;

    @ApiModelProperty(value = "主机名", example = "Terminal-001")
    private String hostname;

    @ApiModelProperty(value = "告警类型", example = "CPU_TEMPERATURE")
    private String alertType;

    @ApiModelProperty(value = "告警级别", example = "CRITICAL")
    private String alertLevel;

    @ApiModelProperty(value = "告警消息", example = "CPU温度过高")
    private String alertMessage;

    @ApiModelProperty(value = "告警详情", example = "当前CPU温度85°C，超过阈值80°C")
    private String alertDetails;

    @ApiModelProperty(value = "当前值", example = "85.5°C")
    private String currentValue;

    @ApiModelProperty(value = "阈值", example = "80°C")
    private String threshold;

    @ApiModelProperty(value = "关联的指标记录ID", example = "_0fd94938951f4a64bb11c6817a81f7e7_20250717164321")
    private String metricId;

    @ApiModelProperty(value = "告警状态", example = "ACTIVE")
    private String alertStatus;

    @ApiModelProperty(value = "首次发生时间", example = "2025-07-17T10:20:00")
    private String firstOccurrence;

    @ApiModelProperty(value = "最后发生时间", example = "2025-07-17T10:30:00")
    private String lastOccurrence;

    @ApiModelProperty(value = "告警次数", example = "5")
    private Integer occurrenceCount;
}
