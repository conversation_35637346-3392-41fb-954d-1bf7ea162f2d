package com.unnet.jmanul.system.service.impl;

import com.auth0.jwt.interfaces.DecodedJWT;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.unnet.jmanul.common.utils.RSAUtil;
import com.unnet.jmanul.common.utils.jwt.JwtComponent;
import com.unnet.jmanul.system.constants.AuthKeyPairNames;
import com.unnet.jmanul.system.entity.RuntimeConfig;
import com.unnet.jmanul.system.entity.User;
import com.unnet.jmanul.system.mapper.UserMapper;
import com.unnet.jmanul.system.service.IRuntimeConfigService;
import com.unnet.jmanul.system.service.IUserService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-11
 */
@Service
@RequiredArgsConstructor
public class UserServiceImpl extends ServiceImpl<UserMapper, User> implements IUserService {

    private final PasswordEncoder passwordEncoder;
    private final JwtComponent jwtComponent;
    private final IRuntimeConfigService runtimeConfigService;

    @Override
    public Optional<String> getAuthPublicKey() {
        Optional<RuntimeConfig> publicKey = runtimeConfigService.getOneOpt(
                new QueryWrapper<RuntimeConfig>()
                        .eq("name", AuthKeyPairNames.PUBLIC_KEY_NAME)
        );
        return publicKey.map(RuntimeConfig::getValue);
    }

    @Override
    public Optional<String> decodeAuthCredential(String encrypted) {

        Optional<RuntimeConfig> privateKey = runtimeConfigService.getOneOpt(
                new QueryWrapper<RuntimeConfig>()
                        .eq("name", AuthKeyPairNames.PRIVATE_KEY_NAME)
        );

        try {
            if (privateKey.isPresent()) {
                String raw = RSAUtil.decryptFromBase64StringToString(RSAUtil.stringToPrivateKey(privateKey.get().getValue()), encrypted);
                return Optional.of(raw);
            }
        } catch (Exception e) {
            log.error("decodeAuthCredential error", e);
        }
        return Optional.empty();
    }

    @Override
    public List<User> getUserWithUsernamePrefix(String prefix) {
        return this.baseMapper.selectList(new QueryWrapper<User>().likeRight("username", prefix).orderByAsc("id"));
    }

    @Override
    public Optional<User> getUserByUsername(String username) {
        User user = this.baseMapper.selectOne(new QueryWrapper<User>().eq("username", username));
        return Optional.ofNullable(user);
    }

    @Override
    public Optional<String> isAuthenticated(String token) {
        Optional<DecodedJWT> jwt = jwtComponent.getValidatedToken(token);
        return jwt.map(decodedJWT -> decodedJWT.getClaim("username").asString());
    }

    @Override
    public boolean putUserPassword(String username, String currentPassword, String newPassword) {
        Optional<User> user = this.getUserByUsername(username);
        if (user.isPresent()) {
            if (passwordEncoder.matches(currentPassword, user.get().getPassword())) {
                user.get().setPassword(passwordEncoder.encode(newPassword));
                return this.updateById(user.get());
            }
        }
        return false;
    }

    @Override
    public boolean putUserName(String username, String name) {
        Optional<User> user = this.getUserByUsername(username);
        if (user.isPresent()) {
            user.get().setName(name);
            return this.updateById(user.get());
        }
        return false;
    }
}
