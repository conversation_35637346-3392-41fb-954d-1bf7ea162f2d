package com.unnet.changan5G.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.unnet.changan5G.dto.MetricRequestBody;
import com.unnet.changan5G.dto.metric.DiskUsageInfo;
import com.unnet.changan5G.dto.terminal.TerminalAlertInfo;
import com.unnet.changan5G.dto.terminal.TerminalBasicInfo;
import com.unnet.changan5G.dto.terminal.TerminalMetricInfo;
import com.unnet.changan5G.entity.MetricThresholdConfigEntity;
import com.unnet.changan5G.util.MetricJsonUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * 指标数据分类处理服务
 * 
 * <AUTHOR>
 * @date 2024-07-14
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class MetricDataClassificationService {

    private final MetricJsonUtil metricJsonUtil;
    private final ObjectMapper objectMapper;
    private final MetricThresholdConfigService metricThresholdConfigService;

    /**
     * 提取终端基本信息
     */
    public TerminalBasicInfo extractTerminalBasicInfo(MetricRequestBody metricData, LocalDateTime receiveTime) {
        TerminalBasicInfo basicInfo = new TerminalBasicInfo();

        basicInfo.setHostname(metricData.getHostname());
        basicInfo.setDeviceId(metricData.getDeviceId());
        basicInfo.setIdentityMac(metricData.getIdentityMac());
        basicInfo.setAppVersion(metricData.getAppVersion());
        basicInfo.setExpiredDate(metricData.getExpiredDate());
        basicInfo.setReceiveTime(receiveTime);

        log.info("提取终端基本信息完成 - 设备ID: {}, 主机名: {}, 接收时间: {}",
                basicInfo.getDeviceId(), basicInfo.getHostname(), receiveTime);

        return basicInfo;
    }

    /**
     * 提取终端指标信息
     */
    public TerminalMetricInfo extractTerminalMetricInfo(MetricRequestBody metricData, String formattedMac, LocalDateTime metricCollectTime, LocalDateTime receiveTime) {
        TerminalMetricInfo metricInfo = new TerminalMetricInfo();
        metricInfo.setVin(metricData.getVin());
        metricInfo.setIdentityMac(formattedMac); // 使用格式化的MAC地址作为设备标识
        metricInfo.setUptime(metricData.getUptime());
        metricInfo.setCpuTemp(metricData.getCpuTemp());
        metricInfo.setTemperatures(metricData.getTemperatures());
        metricInfo.setCpuUsage(metricData.getCpuUsage());
        metricInfo.setCpuPercent(metricData.getCpuPercent());
        metricInfo.setMemoryPercent(metricData.getMemoryPercent());
        metricInfo.setMemoryUsage(metricData.getMemoryUsage());
        metricInfo.setDiskUsage(metricData.getDiskUsage());
        metricInfo.setDiskDataPercent(metricData.getDiskDataPercent());
        metricInfo.setDiskSystemPercent(metricData.getDiskSystemPercent());
        metricInfo.setCdata(metricData.getCdata());
        metricInfo.setZdata(metricData.getZdata());
        metricInfo.setGroupUsage(metricData.getGroupUsage());
        metricInfo.setGroupBps(metricData.getGroupBps());
        metricInfo.setMetricTime(metricCollectTime);  // 使用传入的指标采集时间
        metricInfo.setReceiveTime(receiveTime);       // 使用传入的接收时间

        log.info("提取终端指标信息完成 - 设备MAC: {}, CPU温度: {}°C, CPU使用率: {}%, 内存使用率: {}%, 采集时间: {}",
                metricInfo.getIdentityMac(), metricInfo.getCpuTemp(),
                metricInfo.getCpuPercent(), metricInfo.getMemoryPercent(), metricCollectTime);

        return metricInfo;
    }

    /**
     * 检查并生成告警信息
     */
    public List<TerminalAlertInfo> checkAndGenerateAlerts(MetricRequestBody metricData, String identityMac, LocalDateTime alertTime, String metricId) {
        List<TerminalAlertInfo> alerts = new ArrayList<>();

        // 获取所有启用的阈值配置
        Map<String, MetricThresholdConfigEntity> configMap = metricThresholdConfigService.getAllEnabledConfigsMap();

        // 1. CPU温度告警检查
        checkCpuTemperatureAlert(metricData, alerts, identityMac, alertTime, metricId, configMap);

        // 2. 内存使用率告警检查
        checkMemoryUsageAlert(metricData, alerts, identityMac, alertTime, metricId, configMap);

        // 3. 磁盘使用率告警检查
        checkDiskUsageAlerts(metricData, alerts, identityMac, alertTime, metricId, configMap);

        // 4. 数据磁盘使用率告警检查
        checkDiskDataUsageAlert(metricData, alerts, identityMac, alertTime, metricId, configMap);

        // 5. 系统磁盘使用率告警检查
        checkDiskSystemUsageAlert(metricData, alerts, identityMac, alertTime, metricId, configMap);

        // 6. 软件授权过期告警检查
        checkLicenseExpiryAlert(metricData, alerts, identityMac, alertTime, metricId, configMap);

        log.info("告警检查完成 - 设备MAC: {}, 生成告警数量: {}", identityMac, alerts.size());
        return alerts;
    }

    /**
     * 检查CPU温度告警
     */
    private void checkCpuTemperatureAlert(MetricRequestBody metricData, List<TerminalAlertInfo> alerts,
                                        String identityMac, LocalDateTime alertTime, String metricId,
                                        Map<String, MetricThresholdConfigEntity> configMap) {
        if (metricData.getCpuTemp() == null) {
            return;
        }

        MetricThresholdConfigEntity config = configMap.get("CPU告警");
        if (config == null || !config.getIsEnabled()) {
            // 如果没有配置或配置被禁用，使用默认值
            if (metricData.getCpuTemp() >= 85.0) {
                TerminalAlertInfo alert = createAlert(
                        identityMac,
                        "CPU告警", // 使用中文告警类型
                        "CPU温度过高",
                        "cpu_temp",
                        "85°C",
                        metricData.getCpuTemp() + "°C",
                        alertTime,
                        metricId
                );
                alerts.add(alert);
                log.warn("CPU温度告警(默认阈值) - 设备MAC: {}, 当前温度: {}°C", identityMac, metricData.getCpuTemp());
            }
            return;
        }

        // 使用配置的阈值
        if (metricThresholdConfigService.isThresholdExceeded(metricData.getCpuTemp(), config)) {
            String alertMessage = config.getAlertMessage()
                    .replace("{current_value}", metricData.getCpuTemp() + config.getThresholdUnit())
                    .replace("{threshold_value}", config.getThresholdValue() + config.getThresholdUnit());

            TerminalAlertInfo alert = createAlert(
                    identityMac,
                    config.getMetricType(), // 直接使用数据库中的中文告警类型
                    alertMessage,
                    "cpu_temp",
                    config.getThresholdValue() + config.getThresholdUnit(),
                    metricData.getCpuTemp() + config.getThresholdUnit(),
                    alertTime,
                    metricId
            );
            alerts.add(alert);
            log.warn("CPU温度告警 - 设备MAC: {}, 当前温度: {}°C, 阈值: {}{}",
                    identityMac, metricData.getCpuTemp(), config.getThresholdValue(), config.getThresholdUnit());
        }
    }

    /**
     * 检查内存使用率告警
     */
    private void checkMemoryUsageAlert(MetricRequestBody metricData, List<TerminalAlertInfo> alerts,
                                     String identityMac, LocalDateTime alertTime, String metricId,
                                     Map<String, MetricThresholdConfigEntity> configMap) {
        if (metricData.getMemoryPercent() == null) {
            return;
        }

        MetricThresholdConfigEntity config = configMap.get("内存告警");
        if (config == null || !config.getIsEnabled()) {
            // 如果没有配置或配置被禁用，使用默认值
            if (metricData.getMemoryPercent() >= 90.0) {
                TerminalAlertInfo alert = createAlert(
                        identityMac,
                        "内存告警", // 使用中文告警类型
                        "内存使用率过高",
                        "memory_percent",
                        "90%",
                        metricData.getMemoryPercent() + "%",
                        alertTime,
                        metricId
                );
                alerts.add(alert);
                log.warn("内存使用率告警(默认阈值) - 设备MAC: {}, 当前使用率: {}%", identityMac, metricData.getMemoryPercent());
            }
            return;
        }

        // 使用配置的阈值
        if (metricThresholdConfigService.isThresholdExceeded(metricData.getMemoryPercent(), config)) {
            String alertMessage = config.getAlertMessage()
                    .replace("{current_value}", metricData.getMemoryPercent() + config.getThresholdUnit())
                    .replace("{threshold_value}", config.getThresholdValue() + config.getThresholdUnit());

            TerminalAlertInfo alert = createAlert(
                    identityMac,
                    config.getMetricType(), // 直接使用数据库中的中文告警类型
                    alertMessage,
                    "memory_percent",
                    config.getThresholdValue() + config.getThresholdUnit(),
                    metricData.getMemoryPercent() + config.getThresholdUnit(),
                    alertTime,
                    metricId
            );
            alerts.add(alert);
            log.warn("内存使用率告警 - 设备MAC: {}, 当前使用率: {}%, 阈值: {}{}",
                    identityMac, metricData.getMemoryPercent(), config.getThresholdValue(), config.getThresholdUnit());
        }
    }

    /**
     * 检查磁盘使用率告警
     */
    private void checkDiskUsageAlerts(MetricRequestBody metricData, List<TerminalAlertInfo> alerts,
                                    String identityMac, LocalDateTime alertTime, String metricId,
                                    Map<String, MetricThresholdConfigEntity> configMap) {
        if (metricData.getDiskUsage() == null) {
            return;
        }

        MetricThresholdConfigEntity config = configMap.get("磁盘告警");
        List<DiskUsageInfo> diskUsageInfos = metricJsonUtil.parseDiskUsageInfo(metricData.getDiskUsage());

        if (diskUsageInfos == null || diskUsageInfos.isEmpty()) {
            return;
        }

        for (DiskUsageInfo diskInfo : diskUsageInfos) {
            if (diskInfo.getPercent() == null) {
                continue;
            }

            if (config == null || !config.getIsEnabled()) {
                // 如果没有配置或配置被禁用，使用默认值
                if (diskInfo.getPercent() >= 80.0) {
                    TerminalAlertInfo alert = createAlert(
                            identityMac,
                            "磁盘告警", // 使用中文告警类型
                            "磁盘使用率过高 - " + diskInfo.getDevice() + " (" + diskInfo.getMountpoint() + ")",
                            "disk_usage.percent",
                            "80%",
                            diskInfo.getPercent() + "%",
                            alertTime,
                            metricId
                    );
                    alerts.add(alert);
                    log.warn("磁盘使用率告警(默认阈值) - 设备MAC: {}, 磁盘: {}, 挂载点: {}, 当前使用率: {}%",
                            identityMac, diskInfo.getDevice(), diskInfo.getMountpoint(), diskInfo.getPercent());
                }
                continue;
            }

            // 使用配置的阈值
            if (metricThresholdConfigService.isThresholdExceeded(diskInfo.getPercent(), config)) {
                String alertMessage = config.getAlertMessage()
                        .replace("{current_value}", diskInfo.getPercent() + config.getThresholdUnit())
                        .replace("{threshold_value}", config.getThresholdValue() + config.getThresholdUnit());

                alertMessage = "磁盘使用率过高 - " + diskInfo.getDevice() + " (" + diskInfo.getMountpoint() + "): " + alertMessage;

                TerminalAlertInfo alert = createAlert(
                        identityMac,
                        config.getMetricType(), // 直接使用数据库中的中文告警类型
                        alertMessage,
                        "disk_usage.percent",
                        config.getThresholdValue() + config.getThresholdUnit(),
                        diskInfo.getPercent() + config.getThresholdUnit(),
                        alertTime,
                        metricId
                );
                alerts.add(alert);
                log.warn("磁盘使用率告警 - 设备MAC: {}, 磁盘: {}, 挂载点: {}, 当前使用率: {}%, 阈值: {}{}",
                        identityMac, diskInfo.getDevice(), diskInfo.getMountpoint(),
                        diskInfo.getPercent(), config.getThresholdValue(), config.getThresholdUnit());
            }
        }
    }

    /**
     * 检查数据磁盘使用率告警
     */
    private void checkDiskDataUsageAlert(MetricRequestBody metricData, List<TerminalAlertInfo> alerts,
                                       String identityMac, LocalDateTime alertTime, String metricId,
                                       Map<String, MetricThresholdConfigEntity> configMap) {
        if (metricData.getDiskDataPercent() == null) {
            return;
        }

        MetricThresholdConfigEntity config = configMap.get("数据磁盘告警");
        if (config == null || !config.getIsEnabled()) {
            // 如果没有配置或配置被禁用，使用默认值
            if (metricData.getDiskDataPercent() >= 80.0) {
                TerminalAlertInfo alert = createAlert(
                        identityMac,
                        "数据磁盘告警", // 使用中文告警类型
                        "数据磁盘使用率过高",
                        "disk_data_percent",
                        "80%",
                        metricData.getDiskDataPercent() + "%",
                        alertTime,
                        metricId
                );
                alerts.add(alert);
                log.warn("数据磁盘使用率告警(默认阈值) - 设备MAC: {}, 当前使用率: {}%", identityMac, metricData.getDiskDataPercent());
            }
            return;
        }

        // 使用配置的阈值
        if (metricThresholdConfigService.isThresholdExceeded(metricData.getDiskDataPercent(), config)) {
            String alertMessage = config.getAlertMessage()
                    .replace("{current_value}", metricData.getDiskDataPercent() + config.getThresholdUnit())
                    .replace("{threshold_value}", config.getThresholdValue() + config.getThresholdUnit());

            TerminalAlertInfo alert = createAlert(
                    identityMac,
                    config.getMetricType(), // 直接使用数据库中的中文告警类型
                    alertMessage,
                    "disk_data_percent",
                    config.getThresholdValue() + config.getThresholdUnit(),
                    metricData.getDiskDataPercent() + config.getThresholdUnit(),
                    alertTime,
                    metricId
            );
            alerts.add(alert);
            log.warn("数据磁盘使用率告警 - 设备MAC: {}, 当前使用率: {}%, 阈值: {}{}",
                    identityMac, metricData.getDiskDataPercent(), config.getThresholdValue(), config.getThresholdUnit());
        }
    }

    /**
     * 检查系统磁盘使用率告警
     */
    private void checkDiskSystemUsageAlert(MetricRequestBody metricData, List<TerminalAlertInfo> alerts,
                                         String identityMac, LocalDateTime alertTime, String metricId,
                                         Map<String, MetricThresholdConfigEntity> configMap) {
        if (metricData.getDiskSystemPercent() == null) {
            return;
        }

        MetricThresholdConfigEntity config = configMap.get("系统磁盘告警");
        if (config == null || !config.getIsEnabled()) {
            // 如果没有配置或配置被禁用，使用默认值
            if (metricData.getDiskSystemPercent() >= 80.0) {
                TerminalAlertInfo alert = createAlert(
                        identityMac,
                        "系统磁盘告警", // 使用中文告警类型
                        "系统磁盘使用率过高",
                        "disk_system_percent",
                        "80%",
                        metricData.getDiskSystemPercent() + "%",
                        alertTime,
                        metricId
                );
                alerts.add(alert);
                log.warn("系统磁盘使用率告警(默认阈值) - 设备MAC: {}, 当前使用率: {}%", identityMac, metricData.getDiskSystemPercent());
            }
            return;
        }

        // 使用配置的阈值
        if (metricThresholdConfigService.isThresholdExceeded(metricData.getDiskSystemPercent(), config)) {
            String alertMessage = config.getAlertMessage()
                    .replace("{current_value}", metricData.getDiskSystemPercent() + config.getThresholdUnit())
                    .replace("{threshold_value}", config.getThresholdValue() + config.getThresholdUnit());

            TerminalAlertInfo alert = createAlert(
                    identityMac,
                    config.getMetricType(), // 直接使用数据库中的中文告警类型
                    alertMessage,
                    "disk_system_percent",
                    config.getThresholdValue() + config.getThresholdUnit(),
                    metricData.getDiskSystemPercent() + config.getThresholdUnit(),
                    alertTime,
                    metricId
            );
            alerts.add(alert);
            log.warn("系统磁盘使用率告警 - 设备MAC: {}, 当前使用率: {}%, 阈值: {}{}",
                    identityMac, metricData.getDiskSystemPercent(), config.getThresholdValue(), config.getThresholdUnit());
        }
    }

    /**
     * 检查软件授权过期告警
     */
    private void checkLicenseExpiryAlert(MetricRequestBody metricData, List<TerminalAlertInfo> alerts,
                                       String identityMac, LocalDateTime alertTime, String metricId,
                                       Map<String, MetricThresholdConfigEntity> configMap) {
        if (metricData.getExpiredDate() == null || metricData.getExpiredDate().trim().isEmpty()) {
            return;
        }

        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            LocalDateTime expiredDate = LocalDateTime.parse(metricData.getExpiredDate(), formatter);
            LocalDateTime now = LocalDateTime.now();

            long daysUntilExpiry = ChronoUnit.DAYS.between(now, expiredDate);

            MetricThresholdConfigEntity config = configMap.get("授权过期告警");
            if (config == null || !config.getIsEnabled()) {
                // 如果没有配置或配置被禁用，使用默认值
                if (daysUntilExpiry <= 30 && daysUntilExpiry >= 0) {
                    String alertDetails = daysUntilExpiry <= 7 ?
                            "软件授权即将过期，剩余" + daysUntilExpiry + "天（紧急）" :
                            "软件授权即将过期，剩余" + daysUntilExpiry + "天";

                    TerminalAlertInfo alert = createAlert(
                            identityMac,
                            "授权过期告警", // 使用中文告警类型
                            alertDetails,
                            "expired_date",
                            "30天内",
                            daysUntilExpiry + "天",
                            alertTime,
                            metricId
                    );
                    alerts.add(alert);
                    log.warn("软件授权过期告警(默认阈值) - 设备MAC: {}, 过期时间: {}, 剩余天数: {}",
                            identityMac, metricData.getExpiredDate(), daysUntilExpiry);
                } else if (daysUntilExpiry < 0) {
                    TerminalAlertInfo alert = createAlert(
                            identityMac,
                            "授权过期告警", // 使用中文告警类型
                            "软件授权已过期",
                            "expired_date",
                            "有效期内",
                            "已过期" + Math.abs(daysUntilExpiry) + "天",
                            alertTime,
                            metricId
                    );
                    alerts.add(alert);
                    log.error("软件授权已过期告警 - 设备MAC: {}, 过期时间: {}, 已过期: {}天",
                            identityMac, metricData.getExpiredDate(), Math.abs(daysUntilExpiry));
                }
                return;
            }

            // 使用配置的阈值
            BigDecimal thresholdDays = config.getThresholdValue();
            BigDecimal currentDays = BigDecimal.valueOf(daysUntilExpiry);

            // 对于过期告警，我们需要检查剩余天数是否小于等于阈值
            if (daysUntilExpiry >= 0 && metricThresholdConfigService.isThresholdExceeded(currentDays, config)) {
                String alertMessage = config.getAlertMessage()
                        .replace("{current_value}", daysUntilExpiry + config.getThresholdUnit())
                        .replace("{threshold_value}", config.getThresholdValue() + config.getThresholdUnit());

                TerminalAlertInfo alert = createAlert(
                        identityMac,
                        config.getMetricType(), // 直接使用数据库中的中文告警类型
                        alertMessage,
                        "expired_date",
                        config.getThresholdValue() + config.getThresholdUnit(),
                        daysUntilExpiry + config.getThresholdUnit(),
                        alertTime,
                        metricId
                );
                alerts.add(alert);
                log.warn("软件授权过期告警 - 设备MAC: {}, 过期时间: {}, 剩余天数: {}, 阈值: {}{}",
                        identityMac, metricData.getExpiredDate(), daysUntilExpiry,
                        config.getThresholdValue(), config.getThresholdUnit());
            } else if (daysUntilExpiry < 0) {
                // 已过期的情况
                TerminalAlertInfo alert = createAlert(
                        identityMac,
                        config.getMetricType(), // 直接使用数据库中的中文告警类型
                        "软件授权已过期",
                        "expired_date",
                        "有效期内",
                        "已过期" + Math.abs(daysUntilExpiry) + "天",
                        alertTime,
                        metricId
                );
                alerts.add(alert);
                log.error("软件授权已过期告警 - 设备MAC: {}, 过期时间: {}, 已过期: {}天",
                        identityMac, metricData.getExpiredDate(), Math.abs(daysUntilExpiry));
            }
        } catch (Exception e) {
            log.warn("解析授权过期时间失败 - 设备MAC: {}, 过期时间: {}, 错误: {}",
                    identityMac, metricData.getExpiredDate(), e.getMessage());
        }
    }

    /**
     * 创建告警信息
     */
    private TerminalAlertInfo createAlert(String identityMac, String alertType,
                                        String alertDetails, String metricName, String threshold,
                                        String currentValue, LocalDateTime alertTime, String metricId) {
        TerminalAlertInfo alert = new TerminalAlertInfo();
        alert.setAlertId(UUID.randomUUID().toString());
        alert.setIdentityMac(identityMac);
        alert.setAlertType(alertType);
        alert.setAlertLevel(getAlertLevelFromConfig(alertType)); // 设置告警级别
        alert.setAlertDetails(alertDetails);
        alert.setMetricName(metricName);
        alert.setThreshold(threshold);
        alert.setCurrentValue(currentValue);
        alert.setMetricId(metricId);  // 设置关联的指标ID
        alert.setAlertTime(alertTime);
        alert.setAlertStatus(TerminalAlertInfo.AlertStatus.ACTIVE);

        return alert;
    }

    /**
     * 从配置中获取告警级别
     */
    private String getAlertLevelFromConfig(String alertType) {
        try {
            // 从数据库配置中获取告警级别
            MetricThresholdConfigEntity config = metricThresholdConfigService.getConfigByMetricType(alertType);
            if (config != null && config.getAlertLevel() != null) {
                return config.getAlertLevel();
            }
        } catch (Exception e) {
            log.warn("获取告警级别配置失败 - 告警类型: {}, 错误: {}", alertType, e.getMessage());
        }

        // 如果无法从配置获取，使用默认级别
        return getDefaultAlertLevel(alertType);
    }

    /**
     * 获取默认告警级别（兼容性方法）
     */
    private String getDefaultAlertLevel(String alertType) {
        if (alertType == null) {
            return "MEDIUM";
        }

        // 根据中文告警类型确定默认级别
        if (alertType.contains("CPU") || alertType.contains("内存")) {
            return "HIGH";
        } else if (alertType.contains("授权") || alertType.contains("过期")) {
            return "CRITICAL";
        } else if (alertType.contains("磁盘") || alertType.contains("存储")) {
            return "MEDIUM";
        } else {
            return "MEDIUM";
        }
    }
}
