-- ----------------------------
-- Table structure for sample
-- ----------------------------
DROP TABLE IF EXISTS `sample`;
CREATE TABLE `sample`  (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `created_at` datetime(3) NULL DEFAULT NULL,
    `updated_at` datetime(3) NULL DEFAULT NULL,
    `deleted_at` bigint(20) UNSIGNED NULL DEFAULT 0,
    `title` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL,
    `description` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL,
    `created_by` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
    `updated_by` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
    PRIMARY KEY (`id`) USING BTREE,
    INDEX `idx_samples_deleted_at`(`deleted_at`) USING BTREE,
    INDEX `idx_users_created_by`(`created_by`) USING BTREE,
    INDEX `idx_users_updated_by`(`updated_by`) USING BTREE,
    INDEX `idx_sample_deleted_at`(`deleted_at`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;
