// ----------------前端部署脚本------------------
//第一步： 用户上传dist文件到服务器10.0.0.4中这个目录 /tmp/front_server

//第二步： 通过sftp上传dist文件到10.0.0.3
sftp root@10.0.0.3
sftp> put -r /tmp/front_server/dist /data/front_server/
输入服务器密码： XXXXXX


//第三步：通过ssh 登录到10.0.0.3服务器，


// 构建前端镜像
docker build -f ./dockerfile -t front_server:1.0 .

// 启动容器
docker run -d \
  --name front_server \
  -p 3030:80 \
  front_server:1.0

// 查看容器日志
docker logs front_server


CA-CQUPT.20250717

cqcadevice
cadxylt@2025!@#


curl https://youzhi.un-net.com:8888/postoperative_patient/api/auth/check-phone?phone=18481854722

接收到指标数据 - 上报时间: 2025-08-03 19:18:00

grep -C 10 "接收到指标数据 - 上报时间: 2025-08-03 19:18:43" changan5g-data.log