package com.unnet.jmanul.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.unnet.jmanul.business.entity.UserRole;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 用户角色关系Mapper
 * 
 * <AUTHOR>
 * @date 2024-07-16
 */
@Mapper
public interface UserRoleMapper extends BaseMapper<UserRole> {

    /**
     * 根据用户ID查询角色列表
     */
    @Select("SELECT * FROM user_role WHERE user_id = #{userId} AND deleted_at = 0")
    List<UserRole> selectByUserId(@Param("userId") Long userId);

    /**
     * 根据角色名称查询用户列表
     */
    @Select("SELECT * FROM user_role WHERE role_name = #{roleName} AND deleted_at = 0")
    List<UserRole> selectByRoleName(@Param("roleName") String roleName);

    /**
     * 检查用户是否拥有指定角色
     */
    @Select("SELECT COUNT(*) FROM user_role WHERE user_id = #{userId} AND role_name = #{roleName} AND deleted_at = 0")
    int countByUserIdAndRoleName(@Param("userId") Long userId, @Param("roleName") String roleName);
}
