<template>
  <div class="overview-page">
    <!-- 统计卡片 -->
    <div class="stats-grid">
      <div class="stat-card">
        <div class="stat-number">{{ statsStore.stats.totalTerminals }}</div>
        <div class="stat-label">终端总数</div>
      </div>
      <div class="stat-card">
        <div class="stat-number">{{ statsStore.stats.onlineTerminals }}</div>
        <div class="stat-label">在线终端</div>
      </div>
      <div class="stat-card">
        <div class="stat-number">{{ statsStore.stats.offlineTerminals }}</div>
        <div class="stat-label">离线终端</div>
      </div>
      <div class="stat-card">
        <div class="stat-number">{{ statsStore.stats.totalAlerts }}</div>
        <div class="stat-label">告警数量</div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="content-grid">
      <!-- 上下线实时动态 -->
      <div class="card">
        <div class="card-header">
          <h3>终端上下线实时动态</h3>
        </div>
        <div class="card-body">
          <!-- 使用SSE实时通知组件，只显示上下线通知 -->
          <DeviceNotifications
            :show-alerts="false"
            @statistics-updated="updateStatistics"
            @device-registered="handleDeviceRegistered"
            @device-online="handleDeviceOnline"
            @device-offline="handleDeviceOffline"
          />
        </div>
      </div>

      <!-- 告警实时动态 -->
      <div class="card">
        <div class="card-header">
          <h3>终端告警实时动态</h3>
        </div>
        <div class="card-body">
          <!-- 告警通知组件 -->
          <DeviceAlertNotifications
            @device-alert="handleDeviceAlert"
          />
        </div>
      </div>
    </div>

    <!-- 第二行内容 -->
    <div class="content-grid">
      <!-- 离线终端列表 -->
      <div class="card">
        <div class="card-header">
          <h3>离线终端列表</h3>
        </div>
        <div class="card-body">
          <div class="scroll-container">
            <div
              v-for="terminal in offlineTerminals"
              :key="terminal.deviceId"
              class="list-item"
            >
              <div class="item-info">
                <div class="item-title">
                  <span class="status-badge status-offline">离线</span>
                  {{ terminal.deviceId }}
                </div>
                <div class="item-subtitle">
                  {{ terminal.hostname }} - 离线时长: {{ formatOfflineDuration(terminal.offlineDurationMinutes) }}
                </div>
                <div class="item-time">最后更新: {{ terminal.lastUpdateTime }}</div>
              </div>
              <div class="item-actions">
                <button class="btn btn-info btn-sm" @click="viewTerminalDetail(terminal.deviceId)">查看详情</button>
              </div>
            </div>
            <div v-if="offlineTerminals.length === 0" class="empty-message">
              暂无离线终端
            </div>
          </div>
        </div>
      </div>

      <!-- 告警终端列表 -->
      <div class="card">
        <div class="card-header">
          <h3>告警终端列表</h3>
        </div>
        <div class="card-body">
          <div class="scroll-container">
            <div
              v-for="alert in alertTerminals"
              :key="alert.id"
              class="list-item"
            >
              <div class="item-info">
                <div class="item-title">
                  <span class="status-badge status-alert">告警</span>
                  {{ alert.deviceId }}
                  <span class="alert-level" :class="getAlertLevelClass(alert.alertLevel)">{{ getAlertLevelText(alert.alertLevel) }}</span>
                </div>
                <div class="item-subtitle">{{ alert.alertMessage }} - 告警时间: {{ alert.firstOccurrence }}</div>
              </div>
              <div class="item-actions">
                <button class="btn btn-danger btn-sm" @click="viewAlertDetail(alert)">查看告警</button>
                <button class="btn btn-info btn-sm" @click="viewMetricDetail(alert.identityMac, alert.metricId)">指标详情</button>
              </div>
            </div>
            <div v-if="alertTerminals.length === 0" class="empty-message">
              暂无告警信息
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 指标详情弹框 -->
    <div v-if="metricDetailDialogVisible" class="modal" @click="handleMetricDetailClose">
      <div class="modal-content detail-modal" @click.stop>
        <div class="modal-header">
          <h3>终端指标详情</h3>
          <span class="close" @click="handleMetricDetailClose">&times;</span>
        </div>
        <div class="modal-body" v-if="currentMetricDetail">
          <!-- 时间信息 -->
          <div class="time-info">
            <div>
              <span class="time-label">信息采集时间：</span>
              <span class="time-value">{{ formatDateTime(currentMetricDetail.metricTime) }}</span>
            </div>
            <div>
              <span class="time-label">数据接收时间：</span>
              <span class="time-value">{{ formatDateTime(currentMetricDetail.receiveTime) }}</span>
            </div>
            <div v-if="currentMetricDetail.dataSource === 'ALERT_METRIC'">
              <span class="time-label">数据类型：</span>
              <span class="time-value alert-badge">告警时指标</span>
            </div>
            <div v-else>
              <span class="time-label">数据类型：</span>
              <span class="time-value latest-badge">最新指标</span>
            </div>
          </div>

          <!-- 网络流量信息 -->
          <div class="network-info">
            <div class="network-card">
              <div class="network-title">上行实时速率</div>
              <div class="network-value">{{ formatNetworkSpeed(getDetailGroupBps(currentMetricDetail)) }}</div>
            </div>
          </div>

          <!-- 概览指标 -->
          <div class="stats-grid">
            <div class="stat-card">
              <div class="stat-number" :style="{ color: getTempColor(getDetailCpuTemp(currentMetricDetail)) }">
                {{ formatTemperature(getDetailCpuTemp(currentMetricDetail)) }}
              </div>
              <div class="stat-label">CPU温度</div>
            </div>
            <div class="stat-card">
              <div class="stat-number" :style="{ color: getCpuColor(getDetailCpuUsage(currentMetricDetail)) }">
                {{ formatPercentage(getDetailCpuUsage(currentMetricDetail)) }}
              </div>
              <div class="stat-label">CPU使用率</div>
            </div>
            <div class="stat-card">
              <div class="stat-number" :style="{ color: getMemoryColor(getDetailMemoryUsage(currentMetricDetail)) }">
                {{ formatPercentage(getDetailMemoryUsage(currentMetricDetail)) }}
              </div>
              <div class="stat-label">内存使用率</div>
            </div>
            <div class="stat-card">
              <div class="stat-number" :style="{ color: getDiskColor(getDetailDiskUsage(currentMetricDetail)) }">
                {{ formatPercentage(getDetailDiskUsage(currentMetricDetail)) }}
              </div>
              <div class="stat-label">磁盘使用率</div>
            </div>
          </div>

          <!-- 详细指标信息 -->
          <div class="card">
            <div class="card-header">
              <h4>详细指标信息</h4>
            </div>
            <div class="card-body">
              <div class="metrics-grid">
                <div class="metric-section system-section">
                  <h5><i class="icon-system"></i>系统信息</h5>
                  <div class="metric-row">
                    <div class="metric-label">系统运行时长</div>
                    <div class="metric-value">{{ formatUptime(currentMetricDetail.uptime) }}</div>
                  </div>
                  <div class="metric-row">
                    <div class="metric-label">设备温度</div>
                    <div class="metric-value">
                      SOC温度: {{ formatTemperature(getDetailSocTemp(currentMetricDetail)) }}<br>
                      GPU温度: {{ formatTemperature(getDetailGpuTemp(currentMetricDetail)) }}
                    </div>
                  </div>
                </div>

                <div class="metric-section cpu-section">
                  <h5><i class="icon-cpu"></i>CPU信息</h5>
                  <div class="metric-row">
                    <div class="metric-label">CPU核心</div>
                    <div class="metric-value">{{ getDetailCpuCores(currentMetricDetail) }}核{{ getDetailCpuCores(currentMetricDetail) }}线程</div>
                  </div>
                  <div class="metric-row">
                    <div class="metric-label">CPU使用率</div>
                    <div class="metric-value">
                      用户态: {{ formatPercentage(getDetailCpuUser(currentMetricDetail)) }} |
                      系统态: {{ formatPercentage(getDetailCpuSys(currentMetricDetail)) }} |
                      空闲: {{ formatPercentage(getDetailCpuIdle(currentMetricDetail)) }}
                      <div class="progress-bar">
                        <div class="progress-fill" :style="{
                          width: getDetailCpuUsage(currentMetricDetail) + '%',
                          backgroundColor: getCpuColor(getDetailCpuUsage(currentMetricDetail))
                        }"></div>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="metric-section memory-section">
                  <h5><i class="icon-memory"></i>内存信息</h5>

                  <!-- 内存总览 -->
                  <div class="memory-overview">
                    <div class="memory-summary">
                      <div class="memory-item">
                        <span class="memory-label">总内存</span>
                        <span class="memory-value">{{ formatBytes(getDetailMemoryTotal(currentMetricDetail)) }}</span>
                      </div>
                      <div class="memory-item">
                        <span class="memory-label">已用内存</span>
                        <span class="memory-value">{{ formatBytes(getDetailMemoryUsed(currentMetricDetail)) }}</span>
                      </div>
                      <div class="memory-item">
                        <span class="memory-label">可用内存</span>
                        <span class="memory-value">{{ formatBytes(getDetailMemoryAvailable(currentMetricDetail)) }}</span>
                      </div>
                      <div class="memory-item">
                        <span class="memory-label">使用率</span>
                        <span class="memory-value">{{ formatPercentage(getDetailMemoryUsage(currentMetricDetail)) }}</span>
                      </div>
                    </div>
                    <div class="progress-bar">
                      <div class="progress-fill" :style="{ width: getDetailMemoryUsage(currentMetricDetail) + '%', backgroundColor: getMemoryColor(getDetailMemoryUsage(currentMetricDetail)) }"></div>
                    </div>
                  </div>
                </div>

                <div class="metric-section storage-section">
                  <h5><i class="icon-storage"></i>存储信息</h5>

                  <!-- 内置存储 -->
                  <div class="storage-device">
                    <div class="storage-header">内置存储 ({{ getDetailInternalStorageDevice(currentMetricDetail) }})</div>
                    <div class="metric-row">
                      <div class="metric-label">存储使用</div>
                      <div class="metric-value">
                        总空间: {{ formatBytes(getDetailInternalStorageTotal(currentMetricDetail)) }} | 已用: {{ formatBytes(getDetailInternalStorageUsed(currentMetricDetail)) }} | 可用: {{ formatBytes(getDetailInternalStorageFree(currentMetricDetail)) }}
                        <div class="progress-bar">
                          <div class="progress-fill" :class="getDiskClass(getDetailInternalStoragePercent(currentMetricDetail))" :style="{ width: getDetailInternalStoragePercent(currentMetricDetail) + '%' }"></div>
                        </div>
                      </div>
                    </div>
                    <div class="metric-row">
                      <div class="metric-label">IO性能</div>
                      <div class="metric-value">
                        读取: {{ formatDiskIOSpeed(getDetailInternalStorageReadSpeed(currentMetricDetail)) }} | 写入: {{ formatDiskIOSpeed(getDetailInternalStorageWriteSpeed(currentMetricDetail)) }}<br>
                        读取频率: {{ getDetailInternalStorageReadRate(currentMetricDetail) }}/s | 写入频率: {{ getDetailInternalStorageWriteRate(currentMetricDetail) }}/s
                      </div>
                    </div>
                  </div>

                  <!-- 外置存储 -->
                  <div v-if="hasDetailExternalStorage(currentMetricDetail)" class="storage-device">
                    <div class="storage-header">外置存储 ({{ getDetailExternalStorageDevice(currentMetricDetail) }})</div>
                    <div class="metric-row">
                      <div class="metric-label">存储使用</div>
                      <div class="metric-value">
                        总空间: {{ formatBytes(getDetailExternalStorageTotal(currentMetricDetail)) }} | 已用: {{ formatBytes(getDetailExternalStorageUsed(currentMetricDetail)) }} | 可用: {{ formatBytes(getDetailExternalStorageFree(currentMetricDetail)) }}
                        <div class="progress-bar">
                          <div class="progress-fill" :class="getDiskClass(getDetailExternalStoragePercent(currentMetricDetail))" :style="{ width: getDetailExternalStoragePercent(currentMetricDetail) + '%' }"></div>
                        </div>
                      </div>
                    </div>
                    <div class="metric-row">
                      <div class="metric-label">IO性能</div>
                      <div class="metric-value">
                        读取: {{ formatDiskIOSpeed(getDetailExternalStorageReadSpeed(currentMetricDetail)) }} | 写入: {{ formatDiskIOSpeed(getDetailExternalStorageWriteSpeed(currentMetricDetail)) }}<br>
                        读取频率: {{ getDetailExternalStorageReadRate(currentMetricDetail) }}/s | 写入频率: {{ getDetailExternalStorageWriteRate(currentMetricDetail) }}/s
                      </div>
                    </div>
                  </div>
                </div>

                <div class="metric-section data-section">
                  <h5><i class="icon-file"></i>数据文件</h5>
                  <div class="metric-row">
                    <div class="metric-label">程序生成文件</div>
                    <div class="metric-value">{{ getDetailCdataCount(currentMetricDetail) }}个文件，总大小: {{ formatBytes(getDetailCdataSize(currentMetricDetail)) }}</div>
                  </div>
                  <div class="metric-row">
                    <div class="metric-label">压缩数据</div>
                    <div class="metric-value">{{ getDetailZdataCount(currentMetricDetail) }}个文件，总大小: {{ formatBytes(getDetailZdataSize(currentMetricDetail)) }}</div>
                  </div>
                </div>

                <div class="metric-section terminal-cpe-section">
                  <h5><i class="icon-network"></i>终端与CPE信息</h5>

                  <!-- 终端总览信息 -->
                  <div class="terminal-overview">
                    <div class="overview-grid">
                      <div class="overview-item">
                        <div class="overview-label">终端组ID</div>
                        <div class="overview-value">{{ getDetailTerminalGroupId(currentMetricDetail) }}</div>
                      </div>
                      <div class="overview-item">
                        <div class="overview-label">总连接数</div>
                        <div class="overview-value">{{ getDetailTotalConnections(currentMetricDetail) }}</div>
                      </div>
                      <div class="overview-item">
                        <div class="overview-label">总包速率</div>
                        <div class="overview-value">{{ formatPacketRate(getDetailTotalPps(currentMetricDetail)) }}</div>
                      </div>
                      <div class="overview-item">
                        <div class="overview-label">总流量速率</div>
                        <div class="overview-value">{{ formatNetworkSpeed(getDetailTotalBps(currentMetricDetail)) }}</div>
                      </div>
                    </div>
                  </div>

                  <!-- CPE分组详情 -->
                  <div class="cpe-groups">
                    <div class="cpe-groups-header">CPE分组详情</div>
                    <div class="cpe-groups-grid">
                      <div v-for="(bucket, index) in getDetailCpeBuckets(currentMetricDetail)" :key="index" class="cpe-group-card">
                        <div class="cpe-group-title">分组 {{ bucket.bucket }}</div>
                        <div class="cpe-group-stats">
                          <div class="cpe-stat">
                            <span class="cpe-stat-label">连接</span>
                            <span class="cpe-stat-value">{{ bucket.conn }}</span>
                          </div>
                          <div class="cpe-stat">
                            <span class="cpe-stat-label">包速率</span>
                            <span class="cpe-stat-value">{{ formatPacketRate(bucket.pps) }}</span>
                          </div>
                          <div class="cpe-stat">
                            <span class="cpe-stat-label">流量</span>
                            <span class="cpe-stat-value">{{ formatNetworkSpeed(bucket.bps) }}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="modal-body" v-else-if="loadingMetricDetail">
          <div class="loading-container">
            <div class="loading-spinner"></div>
            <div class="loading-text">正在加载指标数据...</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, onActivated, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useStatsStore, useAlertStore, useTerminalStore } from '@/stores'
import DeviceNotifications from '@/components/DeviceNotifications.vue'
import DeviceAlertNotifications from '@/components/DeviceAlertNotifications.vue'
import sseService from '@/services/sseService'
import { terminalApi } from '@/api/services'

const router = useRouter()
const statsStore = useStatsStore()
const alertStore = useAlertStore()
const terminalStore = useTerminalStore()

// 响应式数据
const recentAlerts = ref([])
const autoRefreshTimer = ref(null)

// 响应式数据
// 离线终端列表
const offlineTerminals = ref([])
// 告警终端列表
const alertTerminals = ref([])

// 是否已经初始化过
const isInitialized = ref(false)

// 初始化
onMounted(async () => {
  console.log('概览页面挂载，检查初始化状态...')

  // 只在首次挂载或者数据为空时才初始化
  if (!isInitialized.value || statsStore.stats.totalTerminals === 0) {
    console.log('开始初始化概览数据...')
    await loadData()
    isInitialized.value = true
  } else {
    console.log('概览数据已存在，跳过初始化')
  }

  // SSE连接由DeviceNotifications组件管理，这里不需要重复连接
  console.log('SSE连接由DeviceNotifications组件管理')
})

// 页面激活时检查是否需要重新加载
onActivated(async () => {
  console.log('概览页面激活，检查状态...')

  // 如果从其他页面返回，且数据为空或未初始化，则重新加载
  if (!isInitialized.value || statsStore.stats.totalTerminals === 0) {
    console.log('重新加载概览数据...')
    await loadData()
    isInitialized.value = true
  }

  // SSE连接由DeviceNotifications组件管理
  console.log('SSE连接由DeviceNotifications组件管理')
})

// 清理定时器
onUnmounted(() => {
  console.log('概览页面卸载，清理资源...')

  if (autoRefreshTimer.value) {
    clearInterval(autoRefreshTimer.value)
  }

  // SSE连接由DeviceNotifications组件管理，这里不需要断开
  console.log('SSE连接由DeviceNotifications组件管理')
})



// 加载数据
const loadData = async () => {
  try {
    console.log('开始加载概览数据...')

    // 使用新的初始化接口一次性获取所有数据
    const response = await statsStore.fetchInitData()

    if (response) {
      console.log('获取到初始化数据:', response)

      // 更新统计信息
      statsStore.stats = response.stats

      // 更新离线终端列表
      offlineTerminals.value = response.offlineTerminals || []
      console.log('离线终端列表:', offlineTerminals.value)

      // 更新告警终端列表
      alertTerminals.value = response.alertTerminals || []
      recentAlerts.value = alertTerminals.value.slice(0, 3)
      console.log('告警终端列表:', alertTerminals.value)

      // 检查告警数据结构，特别是metricId字段
      if (alertTerminals.value.length > 0) {
        console.log('第一个告警数据结构:', alertTerminals.value[0])
        console.log('告警是否包含metricId:', 'metricId' in alertTerminals.value[0])
        console.log('metricId值:', alertTerminals.value[0].metricId)
      }

      ElMessage.success('数据加载成功')
    }
  } catch (error) {
    console.error('加载概览数据失败:', error)
    ElMessage.error('加载数据失败: ' + (error.message || '未知错误'))
  }
}

// 自动刷新
const startAutoRefresh = () => {
  autoRefreshTimer.value = setInterval(async () => {
    try {
      await loadData()
    } catch (error) {
      console.error('自动刷新失败:', error)
    }
  }, 30000) // 30秒刷新一次
}

// 更新统计信息（从SSE通知）
const updateStatistics = (data) => {
  console.log('收到统计信息更新:', data)

  // 更新statsStore中的统计数据
  statsStore.stats.onlineTerminals = data.onlineCount
  statsStore.stats.offlineTerminals = data.offlineCount
  statsStore.stats.totalAlerts = data.alertCount
  statsStore.stats.totalTerminals = data.totalCount

  console.log('统计信息已更新:', statsStore.stats)
}

// 处理设备上线事件
const handleDeviceOnline = (data) => {
  console.log('=== 处理设备上线事件 ===')
  console.log('设备上线数据:', data)
  console.log('当前离线终端列表:', offlineTerminals.value)

  // 从离线终端列表中移除该设备
  const index = offlineTerminals.value.findIndex(terminal => terminal.deviceId === data.deviceId)
  if (index > -1) {
    const removedTerminal = offlineTerminals.value.splice(index, 1)[0]
    console.log('从离线列表移除设备:', removedTerminal)
    console.log('更新后的离线终端列表:', offlineTerminals.value)
  } else {
    console.log('设备不在离线列表中:', data.deviceId)
  }
}

// 处理设备离线事件
const handleDeviceOffline = (data) => {
  console.log('=== 处理设备离线事件 ===')
  console.log('设备离线数据:', data)
  console.log('当前离线终端列表:', offlineTerminals.value)

  // 检查设备是否已在离线列表中
  const exists = offlineTerminals.value.some(terminal => terminal.deviceId === data.deviceId)
  if (!exists) {
    // 添加到离线终端列表
    const offlineTerminal = {
      deviceId: data.deviceId,
      hostname: data.hostname,
      status: 0,
      lastUpdateTime: data.timestamp,
      offlineDurationMinutes: 0,
      userTags: null,
      customFields: null
    }
    offlineTerminals.value.unshift(offlineTerminal)
    console.log('添加到离线列表:', offlineTerminal)
    console.log('更新后的离线终端列表:', offlineTerminals.value)
  } else {
    console.log('设备已在离线列表中:', data.deviceId)
  }
}

// 处理设备注册事件
const handleDeviceRegistered = (data) => {
  console.log('收到设备注册通知:', data)
  // 设备注册后通常是在线状态，可以根据需要处理
}

// 处理设备告警事件
const handleDeviceAlert = (data) => {
  console.log('=== 处理设备告警事件 ===')
  console.log('告警数据:', data)

  // 显示告警消息
  ElMessage({
    message: `${data.hostname || data.deviceId} 发生告警: ${data.message}`,
    type: 'warning',
    duration: 5000,
    showClose: true
  })

  // 可以在这里添加其他告警处理逻辑，比如：
  // 1. 更新告警列表
  // 2. 发送通知
  // 3. 记录告警日志
  console.log('告警详情:', {
    设备ID: data.deviceId,
    主机名: data.hostname,
    告警类型: data.alertType,
    告警级别: data.alertLevel,
    当前值: data.currentValue,
    阈值: data.threshold,
    时间: data.timestamp
  })
}

// 页面跳转和弹框相关
const viewTerminalDetail = async (identityMac) => {
  // 跳转到终端管理页面并自动执行搜索
  await router.push({
    name: 'Terminals',
    query: { identityMac: identityMac }
  })

  // 等待页面加载后触发搜索
  await nextTick()

  // 通过事件总线或其他方式通知终端页面执行搜索
  // 这里我们依赖终端页面的onMounted中的URL参数检查和自动搜索逻辑
}

const viewAlertDetail = async (alert) => {
  console.log('查看告警详情 - 告警数据:', alert)
  console.log('告警ID字段检查 - alertId:', alert.alertId, 'id:', alert.id)

  // 跳转到告警页面并根据告警ID进行过滤查询
  await router.push({
    name: 'Alerts',
    query: {
      alertId: alert.alertId || alert.id,  // 优先使用alertId（业务ID），回退到数据库主键ID
      identityMac: alert.identityMac,  // 同时传递设备MAC地址，便于过滤
      autoSearch: 'true'  // 标记需要自动搜索
    }
  })
}

// 指标详情弹框相关
const metricDetailDialogVisible = ref(false)
const currentMetricDetail = ref(null)
const loadingMetricDetail = ref(false)

const viewMetricDetail = async (identityMac, metricId = null) => {
  try {
    console.log('=== 开始获取指标详情 ===')
    console.log('设备MAC:', identityMac)
    console.log('指标ID:', metricId)
    console.log('指标ID类型:', typeof metricId)
    console.log('指标ID是否为空:', !metricId)

    loadingMetricDetail.value = true

    let response = null

    // 优先使用指标ID获取告警时的具体指标数据
    if (metricId && metricId !== null && metricId !== undefined && metricId !== '') {
      try {
        console.log('使用指标ID获取指标详情:', metricId)
        response = await terminalApi.getMetricById(metricId)
        console.log('指标ID查询结果:', response)
      } catch (error) {
        console.warn('根据指标ID获取数据失败，回退到最新数据:', error.message)
        console.error('指标ID查询错误详情:', error)
        // 如果指标ID查询失败，回退到获取最新数据
        response = null
      }
    } else {
      console.log('没有指标ID，直接获取最新数据')
    }

    // 如果没有指标ID或指标ID查询失败，获取设备的最新指标数据
    if (!response) {
      console.log('获取设备最新指标数据:', identityMac)
      response = await terminalApi.getTerminalMetrics(identityMac)
      console.log('最新指标查询结果:', response)
    }

    if (response && response.data) {
      console.log('指标数据获取成功:', response.data)

      currentMetricDetail.value = {
        identityMac: identityMac,
        dataSource: metricId ? 'ALERT_METRIC' : 'LATEST_METRIC', // 标记数据来源
        metricId: metricId,
        ...response.data
      }
      metricDetailDialogVisible.value = true
    } else {
      console.error('指标数据响应格式错误:', response)
      ElMessage.error('获取指标数据失败')
    }
  } catch (error) {
    console.error('获取指标详情失败:', error)
    ElMessage.error('获取指标详情失败: ' + (error.message || '未知错误'))
  } finally {
    loadingMetricDetail.value = false
  }
}

const handleMetricDetailClose = () => {
  metricDetailDialogVisible.value = false
  currentMetricDetail.value = null
}

// 格式化离线时长
const formatOfflineDuration = (minutes) => {
  if (!minutes || minutes < 0) return '未知'

  const days = Math.floor(minutes / (24 * 60))
  const hours = Math.floor((minutes % (24 * 60)) / 60)
  const mins = minutes % 60

  if (days > 0) {
    return `${days}天${hours}小时${mins}分钟`
  } else if (hours > 0) {
    return `${hours}小时${mins}分钟`
  } else {
    return `${mins}分钟`
  }
}

// 获取告警级别样式类
const getAlertLevelClass = (level) => {
  const classMap = {
    'CRITICAL': 'critical',
    'HIGH': 'high',
    'MEDIUM': 'medium',
    'LOW': 'low'
  }
  return classMap[level] || 'medium'
}

// 获取告警级别文本
const getAlertLevelText = (level) => {
  const levelMap = {
    'CRITICAL': '严重',
    'HIGH': '高',
    'MEDIUM': '中',
    'LOW': '低'
  }
  return levelMap[level] || level
}

// 格式化函数
const formatUptime = (seconds) => {
  if (!seconds || seconds < 0) return '未知'

  const days = Math.floor(seconds / (24 * 3600))
  const hours = Math.floor((seconds % (24 * 3600)) / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = seconds % 60

  if (days > 0) {
    return `${days}天${hours}小时${minutes}分钟`
  } else if (hours > 0) {
    return `${hours}小时${minutes}分钟${secs}秒`
  } else if (minutes > 0) {
    return `${minutes}分钟${secs}秒`
  } else {
    return `${secs}秒`
  }
}

const formatBytes = (bytes, unit = 'auto') => {
  if (!bytes || bytes === 0) return '0 B'

  if (unit === 'MB') {
    return (bytes / (1024 * 1024)).toFixed(2) + ' MB'
  }

  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 数据提取函数（与历史指标页面保持一致）
const getDetailCpuTemp = (data) => {
  const value = data?.cpuTemp || data?.cpu_temp || 0
  return parseFloat(value) || 0
}

const getDetailCpuUsage = (data) => {
  // 优先使用cpuPercent字段，如果没有则尝试其他字段
  const value = data?.cpuPercent || data?.cpu_percent || data?.cpuUsage?.total || data?.cpu_usage?.total || 0
  return parseFloat(value) || 0
}

const getDetailCpuUser = (data) => {
  const value = data?.cpuUsage?.user || data?.cpu_usage?.user || data?.cpuUsage?.user || 0
  return parseFloat(value) || 0
}

const getDetailCpuSys = (data) => {
  const value = data?.cpuUsage?.sys || data?.cpuUsage?.system || data?.cpu_usage?.system || 0
  return parseFloat(value) || 0
}

const getDetailCpuIdle = (data) => {
  const value = data?.cpuUsage?.idle || data?.cpu_usage?.idle || 0
  return parseFloat(value) || 0
}

const getDetailSocTemp = (data) => {
  const value = data?.temperatures?.['soc-thermal'] || data?.temperatures?.soc || data?.soc_temp || 0
  return parseFloat(value) || 0
}

const getDetailGpuTemp = (data) => {
  const value = data?.temperatures?.['gpu-thermal'] || data?.temperatures?.gpu || data?.gpu_temp || 0
  return parseFloat(value) || 0
}

const getDetailMemoryUsage = (data) => {
  const value = data?.memoryPercent || data?.memory_percent || data?.memoryUsage?.percentage || data?.memory_usage?.percentage || 0
  return parseFloat(value) || 0
}

const getDetailMemoryTotal = (data) => {
  return data?.memoryUsage?.total || data?.memory_usage?.total || 0
}

const getDetailMemoryUsed = (data) => {
  return data?.memoryUsage?.used || data?.memory_usage?.used || 0
}

const getDetailMemoryAvailable = (data) => {
  return data?.memoryUsage?.available || data?.memory_usage?.available || 0
}

const getDetailMemoryCached = (data) => {
  return data?.memoryUsage?.cached || data?.memory_usage?.cached || 0
}

const getDetailMemoryBuffers = (data) => {
  return data?.memoryUsage?.buffers || data?.memory_usage?.buffers || 0
}

const getDetailDiskUsage = (data) => {
  if (data?.diskUsage && data.diskUsage.length > 0) {
    const value = data.diskUsage[0].percent || data.diskUsage[0].percentage || 0
    return parseFloat(value) || 0
  }
  const value = data?.disk_usage?.percent || data?.disk_usage?.percentage || 0
  return parseFloat(value) || 0
}

const getDetailDiskTotal = (data) => {
  if (data?.diskUsage && data.diskUsage.length > 0) {
    return data.diskUsage[0].total || 0
  }
  return data?.disk_usage?.total || 0
}

const getDetailDiskUsed = (data) => {
  if (data?.diskUsage && data.diskUsage.length > 0) {
    return data.diskUsage[0].used || 0
  }
  return data?.disk_usage?.used || 0
}

const getDetailDiskFree = (data) => {
  if (data?.diskUsage && data.diskUsage.length > 0) {
    return data.diskUsage[0].available || 0
  }
  return data?.disk_usage?.available || 0
}

const getDetailDiskDevice = (data) => {
  if (data?.diskUsage && data.diskUsage.length > 0) {
    return data.diskUsage[0].filesystem || '未知设备'
  }
  return data?.disk_usage?.device || '未知设备'
}

const getDetailCdataCount = (data) => {
  return data?.cdataFileInfo?.count || data?.cdata_file_info?.count || 0
}

const getDetailCdataSize = (data) => {
  return data?.cdataFileInfo?.totalSize || data?.cdata_file_info?.total_size || 0
}

const getDetailZdataCount = (data) => {
  return data?.zdataFileInfo?.count || data?.zdata_file_info?.count || 0
}

const getDetailZdataSize = (data) => {
  return data?.zdataFileInfo?.totalSize || data?.zdata_file_info?.total_size || 0
}

// 颜色函数（与历史指标页面保持一致）
const getTempColor = (temp) => {
  if (temp >= 85) return '#F56C6C'
  if (temp >= 75) return '#E6A23C'
  return '#67C23A'
}

const getCpuColor = (usage) => {
  if (usage >= 90) return '#F56C6C'
  if (usage >= 70) return '#E6A23C'
  return '#67C23A'
}

const getMemoryColor = (usage) => {
  if (usage >= 85) return '#F56C6C'
  if (usage >= 70) return '#E6A23C'
  return '#67C23A'
}

const getDiskColor = (usage) => {
  if (usage >= 90) return '#F56C6C'
  if (usage >= 80) return '#E6A23C'
  return '#67C23A'
}

// 数据来源相关函数
const getDataSourceText = (dataSource) => {
  switch (dataSource) {
    case 'ALERT_METRIC':
      return '告警时指标'
    case 'LATEST_METRIC':
      return '最新指标'
    default:
      return '实时数据'
  }
}

const getDataSourceBadgeClass = (dataSource) => {
  switch (dataSource) {
    case 'ALERT_METRIC':
      return 'alert-metric-badge'
    case 'LATEST_METRIC':
      return 'latest-metric-badge'
    default:
      return 'real-time-badge'
  }
}

// 添加缺失的函数
const formatTemperature = (temp) => {
  return parseFloat(temp).toFixed(1) + '°C'
}

const formatPercentage = (percent) => {
  return parseFloat(percent).toFixed(1) + '%'
}

const formatDiskIOSpeed = (mbPerSecond) => {
  if (!mbPerSecond || mbPerSecond === 0) return '0 MB/s'
  return mbPerSecond.toFixed(2) + ' MB/s'
}

const getDetailGroupBps = (data) => {
  return data?.groupBps || data?.group_bps || 0
}

const getDetailCpuCores = (data) => {
  return data?.cpuCores || data?.cpu_cores || data?.cpu?.cores || 4
}

// 内置存储相关函数
const getDetailInternalStorageDevice = (data) => {
  if (data?.diskUsage && data.diskUsage.length > 0) {
    return data.diskUsage[0].filesystem || '/dev/mmcblk0p6'
  }
  return data?.disk_usage?.device || '/dev/mmcblk0p6'
}

const getDetailInternalStorageTotal = (data) => {
  if (data?.diskUsage && data.diskUsage.length > 0) {
    return data.diskUsage[0].total || 0
  }
  return data?.disk_usage?.total || 0
}

const getDetailInternalStorageUsed = (data) => {
  if (data?.diskUsage && data.diskUsage.length > 0) {
    return data.diskUsage[0].used || 0
  }
  return data?.disk_usage?.used || 0
}

const getDetailInternalStorageFree = (data) => {
  if (data?.diskUsage && data.diskUsage.length > 0) {
    return data.diskUsage[0].available || 0
  }
  return data?.disk_usage?.available || 0
}

const getDetailInternalStoragePercent = (data) => {
  if (data?.diskUsage && data.diskUsage.length > 0) {
    return parseFloat(data.diskUsage[0].percent || data.diskUsage[0].percentage || 0)
  }
  return parseFloat(data?.disk_usage?.percent || data?.disk_usage?.percentage || 0)
}

const getDetailInternalStorageReadSpeed = (data) => {
  if (data?.diskUsage && data.diskUsage.length > 0 && data.diskUsage[0].io_rate) {
    return parseFloat(data.diskUsage[0].io_rate['MBr/s'] || 0)
  }
  return 0
}

const getDetailInternalStorageWriteSpeed = (data) => {
  if (data?.diskUsage && data.diskUsage.length > 0 && data.diskUsage[0].io_rate) {
    return parseFloat(data.diskUsage[0].io_rate['MBw/s'] || 0)
  }
  return 0
}

const getDetailInternalStorageReadRate = (data) => {
  if (data?.diskUsage && data.diskUsage.length > 0 && data.diskUsage[0].io_rate) {
    return data.diskUsage[0].io_rate['read/s'] || 0
  }
  return 0
}

const getDetailInternalStorageWriteRate = (data) => {
  if (data?.diskUsage && data.diskUsage.length > 0 && data.diskUsage[0].io_rate) {
    return data.diskUsage[0].io_rate['write/s'] || 0
  }
  return 0
}

// 外置存储相关函数
const hasDetailExternalStorage = (data) => {
  return data?.diskUsage && data.diskUsage.length > 1
}

const getDetailExternalStorageDevice = (data) => {
  if (data?.diskUsage && data.diskUsage.length > 1) {
    return data.diskUsage[1].filesystem || '/dev/sda1'
  }
  return '/dev/sda1'
}

const getDetailExternalStorageTotal = (data) => {
  if (data?.diskUsage && data.diskUsage.length > 1) {
    return data.diskUsage[1].total || 0
  }
  return 0
}

const getDetailExternalStorageUsed = (data) => {
  if (data?.diskUsage && data.diskUsage.length > 1) {
    return data.diskUsage[1].used || 0
  }
  return 0
}

const getDetailExternalStorageFree = (data) => {
  if (data?.diskUsage && data.diskUsage.length > 1) {
    return data.diskUsage[1].available || 0
  }
  return 0
}

const getDetailExternalStoragePercent = (data) => {
  if (data?.diskUsage && data.diskUsage.length > 1) {
    return parseFloat(data.diskUsage[1].percent || data.diskUsage[1].percentage || 0)
  }
  return 0
}

const getDetailExternalStorageReadSpeed = (data) => {
  if (data?.diskUsage && data.diskUsage.length > 1 && data.diskUsage[1].io_rate) {
    return parseFloat(data.diskUsage[1].io_rate['MBr/s'] || 0)
  }
  return 0
}

const getDetailExternalStorageWriteSpeed = (data) => {
  if (data?.diskUsage && data.diskUsage.length > 1 && data.diskUsage[1].io_rate) {
    return parseFloat(data.diskUsage[1].io_rate['MBw/s'] || 0)
  }
  return 0
}

const getDetailExternalStorageReadRate = (data) => {
  if (data?.diskUsage && data.diskUsage.length > 1 && data.diskUsage[1].io_rate) {
    return data.diskUsage[1].io_rate['read/s'] || 0
  }
  return 0
}

const getDetailExternalStorageWriteRate = (data) => {
  if (data?.diskUsage && data.diskUsage.length > 1 && data.diskUsage[1].io_rate) {
    return data.diskUsage[1].io_rate['write/s'] || 0
  }
  return 0
}

// 终端与CPE信息相关函数
const getDetailTerminalGroupId = (data) => {
  return data?.groupUsage?.group_id || data?.group_usage?.group_id || 'N/A'
}

const getDetailTotalConnections = (data) => {
  if (data?.groupUsage?.buckets) {
    return data.groupUsage.buckets.reduce((total, bucket) => total + (bucket.conn || 0), 0)
  }
  return 0
}

const getDetailTotalPps = (data) => {
  return data?.groupUsage?.pps_total || data?.group_usage?.pps_total || 0
}

const getDetailTotalBps = (data) => {
  return data?.groupUsage?.bps_total || data?.group_usage?.bps_total || 0
}

const getDetailCpeBuckets = (data) => {
  return data?.groupUsage?.buckets || data?.group_usage?.buckets || []
}

const getDiskClass = (usage) => {
  if (usage >= 90) return 'danger'
  if (usage >= 80) return 'warning'
  return ''
}

// 格式化日期时间函数
const formatDateTime = (dateTime) => {
  if (!dateTime) return ''

  const date = new Date(dateTime)
  return date.getFullYear() + '-' +
         String(date.getMonth() + 1).padStart(2, '0') + '-' +
         String(date.getDate()).padStart(2, '0') + ' ' +
         String(date.getHours()).padStart(2, '0') + ':' +
         String(date.getMinutes()).padStart(2, '0') + ':' +
         String(date.getSeconds()).padStart(2, '0')
}

// 格式化网络速度 (bps转换为合适的单位)
const formatNetworkSpeed = (bps) => {
  if (!bps || bps === 0) return '0 bps'

  // 8 bit = 1 byte，所以 bps / 8 = Bps (字节每秒)
  const bytesPerSecond = bps

  if (bytesPerSecond >= 1024 * 1024 * 1024) {
    return (bytesPerSecond / (1024 * 1024 * 1024)).toFixed(2) + ' GB/s'
  } else if (bytesPerSecond >= 1024 * 1024) {
    return (bytesPerSecond / (1024 * 1024)).toFixed(2) + ' MB/s'
  } else if (bytesPerSecond >= 1024) {
    return (bytesPerSecond / 1024).toFixed(2) + ' KB/s'
  } else {
    return bytesPerSecond.toFixed(2) + ' B/s'
  }
}

// 格式化包速率
const formatPacketRate = (pps) => {
  if (!pps || pps === 0) return '0 pps'

  if (pps >= 1000000) {
    return (pps / 1000000).toFixed(2) + ' Mpps'
  } else if (pps >= 1000) {
    return (pps / 1000).toFixed(2) + ' Kpps'
  } else {
    return pps.toFixed(0) + ' pps'
  }
}
</script>

<style scoped>
.overview-page {
  /* 继承通用样式，不需要额外定义 */
}

/* 指标详情弹框样式 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 8px;
  max-width: 90%;
  max-height: 90%;
  overflow-y: auto;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.detail-modal {
  width: 1200px;
  max-width: 95%;
}

.modal-header {
  padding: 20px;
  border-bottom: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #f8f9fa;
  border-radius: 8px 8px 0 0;
}

.modal-header h3 {
  margin: 0;
  color: #333;
  font-size: 18px;
  font-weight: 600;
}

.close {
  font-size: 24px;
  cursor: pointer;
  color: #999;
  line-height: 1;
}

.close:hover {
  color: #333;
}

.modal-body {
  padding: 20px;
}

/* 设备信息样式 */
.device-info-section {
  display: flex;
  gap: 30px;
  margin-bottom: 20px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 6px;
  flex-wrap: wrap;
}

.info-item {
  display: flex;
  align-items: center;
}

.info-label {
  font-weight: 600;
  color: #666;
  margin-right: 8px;
}

.info-value {
  color: #333;
  font-family: monospace;
}

.real-time-badge {
  background: #52c41a;
  color: white;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: bold;
}

.alert-metric-badge {
  background: #fa8c16;
  color: white;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: bold;
}

.latest-metric-badge {
  background: #1890ff;
  color: white;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: bold;
}

.metric-id {
  font-family: monospace;
  background: #f0f0f0;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 11px;
  color: #666;
}

/* 概览指标卡片样式 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.stat-card {
  background: white;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.stat-number {
  font-size: 32px;
  font-weight: bold;
  margin-bottom: 8px;
}

.stat-label {
  color: #666;
  font-size: 14px;
}

/* 详细指标网格样式 */
.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.metric-section {
  background: #f9f9f9;
  border-radius: 6px;
  padding: 16px;
}

.metric-section h5 {
  color: #333;
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 12px;
  padding-bottom: 6px;
  border-bottom: 2px solid #409eff;
}

.metric-row {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 8px 0;
  border-bottom: 1px solid #e8e8e8;
}

.metric-row:last-child {
  border-bottom: none;
}

.metric-label {
  font-weight: 600;
  color: #666;
  min-width: 120px;
  margin-right: 12px;
}

.metric-value {
  color: #333;
  flex: 1;
  text-align: right;
}

/* 进度条样式 */
.progress-bar {
  width: 100%;
  height: 8px;
  background: #f0f0f0;
  border-radius: 4px;
  margin-top: 6px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  border-radius: 4px;
  transition: width 0.3s ease;
}

/* 指标详情样式 */
.metric-detail {
  max-height: 70vh;
  overflow-y: auto;
}

.detail-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
  margin-bottom: 20px;
}

.detail-section {
  background: #fff;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  padding: 16px;
}

.detail-section.full-width {
  grid-column: 1 / -1;
}

.detail-section h4 {
  color: #333;
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 15px;
  padding-bottom: 8px;
  border-bottom: 2px solid #409eff;
}

.detail-item {
  display: flex;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.detail-item:last-child {
  border-bottom: none;
}

.detail-label {
  font-weight: 600;
  color: #666;
  min-width: 120px;
  margin-right: 10px;
}

.detail-value {
  color: #333;
  flex: 1;
}

/* 磁盘项样式 */
.disk-item {
  margin-bottom: 15px;
  padding: 10px;
  background: #f9f9f9;
  border-radius: 4px;
}

.disk-item:last-child {
  margin-bottom: 0;
}

.disk-header {
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
  font-size: 14px;
}

/* 版本信息网格 */
.version-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 10px;
}

/* 无数据样式 */
.no-data {
  text-align: center;
  color: #999;
  padding: 20px;
  font-style: italic;
}

/* 加载样式 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #409eff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  color: #666;
  font-size: 14px;
}

/* 告警级别样式 */
.alert-level {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: bold;
  color: white;
  margin-left: 8px;
}

.alert-level.critical {
  background: #ff4d4f;
}

.alert-level.high {
  background: #fa8c16;
}

.alert-level.medium {
  background: #faad14;
}

.alert-level.low {
  background: #52c41a;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .detail-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .device-info-section {
    flex-direction: column;
    gap: 15px;
  }

  .version-grid {
    grid-template-columns: 1fr;
  }

  .detail-modal {
    width: 95%;
    margin: 10px;
  }
}

/* 添加历史指标页面的样式 */
/* 网络流量信息样式 */
.network-info {
  margin-bottom: 20px;
}

.network-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px;
  border-radius: 12px;
  text-align: center;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.network-title {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 8px;
  opacity: 0.9;
}

.network-value {
  font-size: 28px;
  font-weight: 700;
  margin-bottom: 4px;
}

.network-subtitle {
  font-size: 12px;
  opacity: 0.8;
}

/* 时间信息样式 */
.time-info {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 20px;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 12px;
}

.time-label {
  font-weight: 600;
  color: #495057;
  margin-right: 8px;
}

.time-value {
  color: #212529;
  font-family: 'Courier New', monospace;
}

.history-badge {
  background: #17a2b8;
  color: white;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
}

.alert-badge {
  background: #dc3545;
  color: white;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
}

.latest-badge {
  background: #28a745;
  color: white;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
}

/* 内存信息样式 */
.memory-overview {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
}

.memory-summary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 12px;
  margin-bottom: 12px;
}

.memory-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: white;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.memory-label {
  font-size: 12px;
  color: #6c757d;
  font-weight: 500;
}

.memory-value {
  font-size: 14px;
  color: #212529;
  font-weight: 600;
}

/* 存储信息样式 */
.storage-device {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
  border: 1px solid #e9ecef;
}

.storage-header {
  font-size: 14px;
  font-weight: 600;
  color: #495057;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #dee2e6;
}

/* 终端与CPE信息样式 */
.terminal-overview {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
}

.overview-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
}

.overview-item {
  background: white;
  padding: 12px;
  border-radius: 6px;
  border: 1px solid #e9ecef;
  text-align: center;
}

.overview-label {
  font-size: 12px;
  color: #6c757d;
  margin-bottom: 4px;
}

.overview-value {
  font-size: 16px;
  font-weight: 600;
  color: #212529;
}

.cpe-groups {
  margin-top: 16px;
}

.cpe-groups-header {
  font-size: 14px;
  font-weight: 600;
  color: #495057;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #dee2e6;
}

.cpe-groups-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 12px;
}

.cpe-group-card {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 16px;
  transition: all 0.2s ease;
}

.cpe-group-card:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-color: #007bff;
}

.cpe-group-title {
  font-size: 14px;
  font-weight: 600;
  color: #495057;
  margin-bottom: 12px;
  text-align: center;
}

.cpe-group-stats {
  display: grid;
  grid-template-columns: 1fr;
  gap: 8px;
}

.cpe-stat {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 0;
  border-bottom: 1px solid #f1f3f4;
}

.cpe-stat:last-child {
  border-bottom: none;
}

.cpe-stat-label {
  font-size: 12px;
  color: #6c757d;
}

.cpe-stat-value {
  font-size: 13px;
  font-weight: 600;
  color: #212529;
}

/* 图标样式 */
.icon-system::before { content: "🖥️"; margin-right: 8px; }
.icon-cpu::before { content: "⚡"; margin-right: 8px; }
.icon-memory::before { content: "💾"; margin-right: 8px; }
.icon-storage::before { content: "💿"; margin-right: 8px; }
.icon-file::before { content: "📁"; margin-right: 8px; }
.icon-network::before { content: "🌐"; margin-right: 8px; }
</style>