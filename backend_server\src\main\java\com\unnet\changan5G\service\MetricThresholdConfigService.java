package com.unnet.changan5G.service;

import com.unnet.changan5G.entity.MetricThresholdConfigEntity;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 指标阈值配置服务接口
 * 
 * <AUTHOR>
 * @since 2024-07-21
 */
public interface MetricThresholdConfigService {

    /**
     * 根据指标类型获取阈值配置
     * 
     * @param metricType 指标类型
     * @return 配置信息
     */
    MetricThresholdConfigEntity getConfigByMetricType(String metricType);

    /**
     * 获取所有启用的配置
     * 
     * @return 配置列表
     */
    List<MetricThresholdConfigEntity> getAllEnabledConfigs();

    /**
     * 获取所有启用的配置映射（指标类型 -> 配置）
     * 
     * @return 配置映射
     */
    Map<String, MetricThresholdConfigEntity> getAllEnabledConfigsMap();

    /**
     * 检查数值是否超过阈值
     * 
     * @param currentValue 当前值
     * @param config 阈值配置
     * @return 是否超过阈值
     */
    boolean isThresholdExceeded(BigDecimal currentValue, MetricThresholdConfigEntity config);

    /**
     * 检查数值是否超过阈值
     * 
     * @param currentValue 当前值
     * @param config 阈值配置
     * @return 是否超过阈值
     */
    boolean isThresholdExceeded(Double currentValue, MetricThresholdConfigEntity config);

    /**
     * 刷新配置缓存
     */
    void refreshConfigCache();
}
