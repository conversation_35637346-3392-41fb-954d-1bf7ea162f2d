package com.unnet.jmanul.config;

import io.minio.MinioClient;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Random;
import java.util.concurrent.TimeUnit;

/**
 * MinIO生产环境配置类
 * 集群模式，支持负载均衡和故障转移
 * 
 * <AUTHOR>
 * @since 2025-07-28
 */
@Slf4j
@Data
@Configuration
@Profile("prod")
@ConfigurationProperties(prefix = "spring.minio")
public class MinioProdConfig {

    /**
     * MinIO服务地址（单节点模式兼容）
     */
    private String host;

    /**
     * MinIO集群节点地址列表（集群模式）
     */
    private List<String> hosts;

    /**
     * MinIO访问URL
     */
    private String url;

    /**
     * MinIO访问密钥
     */
    private String accessKey;

    /**
     * MinIO秘密密钥
     */
    private String secretKey;

    /**
     * MinIO存储桶名称
     */
    private String bucket;

    /**
     * 集群配置
     */
    private Cluster cluster = new Cluster();

    @Data
    public static class Cluster {
        /**
         * 是否启用集群模式
         */
        private Boolean enabled = true;

        /**
         * 重试次数
         */
        private Integer retryAttempts = 3;

        /**
         * 重试延迟（毫秒）
         */
        private Integer retryDelayMs = 1000;

        /**
         * 健康检查间隔（秒）
         */
        private Integer healthCheckIntervalSeconds = 30;
    }

    /**
     * 创建MinIO客户端 - 生产环境（集群模式）
     */
    @Bean
    public MinioClient minioClient() {
        String endpoint = getEndpoint();

        log.info("创建生产环境MinIO客户端 - 端点: {}, 集群模式: {}, 集群节点数: {}",
                endpoint, cluster.getEnabled(), getClusterNodeCount());

        // 创建自定义的OkHttpClient，禁用缓存
        OkHttpClient httpClient = new OkHttpClient.Builder()
                .connectTimeout(30, TimeUnit.SECONDS)
                .writeTimeout(60, TimeUnit.SECONDS)
                .readTimeout(60, TimeUnit.SECONDS)
                .cache(null) // 禁用HTTP缓存，解决文件覆盖后下载旧文件的问题
                .build();

        MinioClient client = MinioClient.builder()
                .endpoint(endpoint)
                .credentials(accessKey, secretKey)
                .httpClient(httpClient) // 使用自定义的HTTP客户端
                .build();

        log.info("生产环境MinIO客户端创建成功 - 集群节点: {}, 存储桶: {}, HTTP缓存: 已禁用",
                getAllEndpoints(), bucket);

        return client;
    }

    /**
     * 获取端点地址
     * 如果启用集群模式且配置了多个节点，则随机选择一个节点
     * 否则使用单节点配置
     */
    private String getEndpoint() {
        if (cluster.getEnabled() && !CollectionUtils.isEmpty(hosts)) {
            // 集群模式：随机选择一个节点
            Random random = new Random();
            String selectedHost = hosts.get(random.nextInt(hosts.size()));
            log.debug("生产环境集群模式选择节点: {}", selectedHost);
            return selectedHost;
        } else {
            // 单节点模式
            log.debug("生产环境单节点模式使用: {}", host);
            return host;
        }
    }

    /**
     * 获取所有可用的端点
     * 用于健康检查和故障转移
     */
    public List<String> getAllEndpoints() {
        if (cluster.getEnabled() && !CollectionUtils.isEmpty(hosts)) {
            return hosts;
        } else {
            return List.of(host);
        }
    }

    /**
     * 获取集群节点数量
     */
    private int getClusterNodeCount() {
        if (cluster.getEnabled() && !CollectionUtils.isEmpty(hosts)) {
            return hosts.size();
        }
        return 1;
    }

    /**
     * 获取MinIO配置信息
     */
    public MinioProperties getMinioProperties() {
        MinioProperties properties = new MinioProperties();
        properties.setHost(host);
        properties.setHosts(hosts);
        properties.setUrl(url);
        properties.setAccessKey(accessKey);
        properties.setSecretKey(secretKey);
        properties.setBucket(bucket);
        properties.setClusterEnabled(cluster.getEnabled());
        properties.setCluster(cluster);
        return properties;
    }

    /**
     * MinIO属性类
     */
    @Data
    public static class MinioProperties {
        private String host;
        private List<String> hosts;
        private String url;
        private String accessKey;
        private String secretKey;
        private String bucket;
        private Boolean clusterEnabled;
        private Cluster cluster;
    }
}
