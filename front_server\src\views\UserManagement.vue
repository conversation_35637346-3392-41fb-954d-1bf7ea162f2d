<template>
  <div class="user-management-page">
    <!-- 搜索过滤区域 -->
    <div class="card">
      <div class="card-header">
        <h3>用户管理</h3>
        <button class="btn btn-primary" @click="handleAddUser">
          <i>➕</i> 添加用户
        </button>
      </div>
      <div class="card-body">
        <div class="search-filters">
          <div class="form-group">
            <label>用户名</label>
            <input
              type="text"
              class="form-control"
              placeholder="输入用户名"
              v-model="searchForm.username"
              @keyup.enter="handleSearch"
            >
          </div>
          <div class="form-group">
            <label>姓名</label>
            <input
              type="text"
              class="form-control"
              placeholder="输入姓名"
              v-model="searchForm.name"
              @keyup.enter="handleSearch"
            >
          </div>
          <div class="form-group">
            <label>角色</label>
            <select class="form-control" v-model="searchForm.role">
              <option value="">全部</option>
              <option value="admin">管理员</option>
              <option value="user">普通用户</option>
            </select>
          </div>
          <div class="form-group">
            <label>状态</label>
            <select class="form-control" v-model="searchForm.status">
              <option value="">全部</option>
              <option value="1">启用</option>
              <option value="0">禁用</option>
            </select>
          </div>
          <div class="form-group button-group">
            <label>&nbsp;</label> <!-- 占位标签，保持高度一致 -->
            <div class="button-container">
              <button class="btn btn-primary" @click="handleSearch">搜索</button>
              <button class="btn btn-secondary" @click="handleReset">重置</button>
            </div>
          </div>
        </div>

        <table class="table">
          <thead>
            <tr>
              <th>用户名</th>
              <th>姓名</th>
              <th>角色</th>
              <th>状态</th>
              <th>账号来源</th>
              <th>创建时间</th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="user in userList" :key="user.id" v-show="!loading">
              <td>{{ user.username }}</td>
              <td>{{ user.name || '-' }}</td>
              <td>
                <span :class="user.primaryRole === 'admin' ? 'badge badge-danger' : 'badge badge-info'">
                  {{ user.primaryRole === 'admin' ? '管理员' : '普通用户' }}
                </span>
              </td>
              <td>
                <span :class="user.enable ? 'status-online' : 'status-offline'">
                  {{ user.enable ? '启用' : '禁用' }}
                </span>
              </td>
              <td>{{ user.accountSource || '-' }}</td>
              <td>{{ formatDateTime(user.createdAt) }}</td>
              <td>
                <div class="action-buttons">
                  <button
                    class="btn btn-info btn-sm"
                    @click="handleViewUserDetail(user)"
                  >
                    用户详情
                  </button>
                  <button
                    v-if="user.username !== 'admin'"
                    :class="user.enable ? 'btn btn-warning btn-sm' : 'btn btn-success btn-sm'"
                    @click="handleToggleUserStatus(user)"
                  >
                    {{ user.enable ? '禁用' : '启用' }}
                  </button>
                  <button
                    v-if="user.username !== 'admin'"
                    class="btn btn-primary btn-sm"
                    @click="handleEditUser(user)"
                  >
                    编辑用户
                  </button>
                </div>
              </td>
            </tr>
            <tr v-if="loading">
              <td colspan="7" style="text-align: center; padding: 40px;">
                <div style="font-size: 16px; color: #666;">加载中...</div>
              </td>
            </tr>
            <tr v-if="!loading && userList.length === 0">
              <td colspan="7" style="text-align: center; padding: 40px;">
                <div style="font-size: 16px; color: #666;">暂无数据</div>
              </td>
            </tr>
          </tbody>
        </table>

        <div class="pagination">
          <button @click="prevPage" :disabled="pagination.page <= 1">上一页</button>
          <button
            v-for="page in visiblePages"
            :key="page"
            :class="{ active: page === pagination.page }"
            @click="goToPage(page)"
          >
            {{ page }}
          </button>
          <button @click="nextPage" :disabled="pagination.page >= totalPages">下一页</button>
        </div>
      </div>
    </div>

    <!-- 用户详情模态框 -->
    <div v-if="userDetailDialog.visible" class="modal" @click="handleCloseUserDetail">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>用户详情</h3>
          <span class="close" @click="handleCloseUserDetail">&times;</span>
        </div>
        <div class="modal-body" v-if="userDetailDialog.user">
          <div class="user-detail-container">
            <div class="detail-row">
              <div class="detail-item">
                <span class="detail-label">用户名:</span>
                <span class="detail-value">{{ userDetailDialog.user.username }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">姓名:</span>
                <span class="detail-value">{{ userDetailDialog.user.name || '-' }}</span>
              </div>
            </div>

            <div class="detail-row">
              <div class="detail-item">
                <span class="detail-label">角色:</span>
                <span class="detail-value">{{ userDetailDialog.user.primaryRole === 'admin' ? '管理员' : '普通用户' }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">状态:</span>
                <span class="detail-value">{{ userDetailDialog.user.enable ? '启用' : '禁用' }}</span>
              </div>
            </div>

            <div class="detail-row">
              <div class="detail-item">
                <span class="detail-label">账号来源:</span>
                <span class="detail-value">{{ userDetailDialog.user.accountSource || '-' }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">创建时间:</span>
                <span class="detail-value">{{ formatDateTime(userDetailDialog.user.createdAt) }}</span>
              </div>
            </div>

            <div class="detail-row">
              <div class="detail-item">
                <span class="detail-label">账号过期时间:</span>
                <span class="detail-value">{{ formatDateTime(userDetailDialog.user.accountExpireDate) }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">密码过期时间:</span>
                <span class="detail-value">{{ formatDateTime(userDetailDialog.user.credentialExpireDate) }}</span>
              </div>
            </div>

            <div class="detail-row">
              <div class="detail-item">
                <span class="detail-label">账号锁定:</span>
                <span class="detail-value">{{ userDetailDialog.user.locked ? '是' : '否' }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">最后更新:</span>
                <span class="detail-value">{{ formatDateTime(userDetailDialog.user.updatedAt) }}</span>
              </div>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button class="btn btn-secondary" @click="handleCloseUserDetail">关闭</button>
        </div>
      </div>
    </div>

    <!-- 添加用户弹框 -->
    <div v-if="addUserDialog.visible" class="dialog-overlay" @click="handleCloseAddUser">
      <div class="dialog-container" @click.stop>
        <div class="dialog-header">
          <h3 class="dialog-title">
            <i class="icon">👤</i>
            添加用户
          </h3>
          <button class="dialog-close" @click="handleCloseAddUser">
            <i>✕</i>
          </button>
        </div>

        <div class="dialog-body">
          <div class="form-row">
            <div class="form-field">
              <label class="field-label required">用户名</label>
              <input
                type="text"
                class="field-input"
                placeholder="请输入用户名"
                v-model="addUserDialog.form.username"
                maxlength="50"
              >
            </div>
            <div class="form-field">
              <label class="field-label">姓名</label>
              <input
                type="text"
                class="field-input"
                placeholder="请输入姓名"
                v-model="addUserDialog.form.name"
                maxlength="50"
              >
            </div>
          </div>

          <div class="form-row">
            <div class="form-field">
              <label class="field-label required">密码</label>
              <input
                type="password"
                class="field-input"
                placeholder="请输入密码"
                v-model="addUserDialog.form.password"
                maxlength="100"
              >
            </div>
            <div class="form-field">
              <label class="field-label">角色</label>
              <select class="field-select" v-model="addUserDialog.form.role">
                <option value="user">普通用户</option>
                <option value="admin">管理员</option>
              </select>
            </div>
          </div>

          <div class="form-row">
            <div class="form-field">
              <label class="field-label">账号到期时间</label>
              <input
                type="datetime-local"
                class="field-input"
                v-model="addUserDialog.form.accountExpireDate"
              >
              <div class="field-hint">留空则设置为永不过期</div>
            </div>
            <div class="form-field">
              <label class="field-label">密码到期时间</label>
              <input
                type="datetime-local"
                class="field-input"
                v-model="addUserDialog.form.credentialExpireDate"
              >
              <div class="field-hint">留空则设置为永不过期</div>
            </div>
          </div>

          <div class="role-info">
            <div class="info-icon">ℹ️</div>
            <div class="info-text">
              <span v-if="addUserDialog.form.role === 'admin'">
                管理员拥有所有功能的完整权限，包括用户管理、数据修改等。
              </span>
              <span v-else>
                普通用户只能查看数据，无法进行修改、删除等操作。
              </span>
            </div>
          </div>
        </div>

        <div class="dialog-footer">
          <button class="btn-cancel" @click="handleCloseAddUser">取消</button>
          <button
            class="btn-confirm"
            @click="handleSaveUser"
            :disabled="addUserDialog.saving"
          >
            <span v-if="addUserDialog.saving">
              <i class="loading-icon">⏳</i> 保存中...
            </span>
            <span v-else>
              <i>✓</i> 保存
            </span>
          </button>
        </div>
      </div>
    </div>

    <!-- 编辑用户弹框 -->
    <div v-if="editUserDialog.visible" class="dialog-overlay" @click="handleCloseEditUser">
      <div class="dialog-container" @click.stop>
        <div class="dialog-header">
          <h3 class="dialog-title">
            <i class="icon">✏️</i>
            编辑用户信息
          </h3>
          <button class="dialog-close" @click="handleCloseEditUser">
            <i>✕</i>
          </button>
        </div>

        <div class="dialog-body">
          <div class="form-row">
            <div class="form-field">
              <label class="field-label">用户名</label>
              <input
                type="text"
                class="field-input"
                :value="editUserDialog.form.username"
                disabled
                style="background-color: #f5f5f5; cursor: not-allowed;"
              >
              <div class="field-hint">用户名不可修改</div>
            </div>
            <div class="form-field">
              <label class="field-label">姓名</label>
              <input
                type="text"
                class="field-input"
                placeholder="请输入姓名"
                v-model="editUserDialog.form.name"
                maxlength="50"
              >
            </div>
          </div>

          <div class="form-row">
            <div class="form-field">
              <label class="field-label">角色</label>
              <select class="field-select" v-model="editUserDialog.form.role">
                <option value="user">普通用户</option>
                <option value="admin">管理员</option>
              </select>
            </div>
            <div class="form-field">
              <label class="field-label">状态</label>
              <select class="field-select" v-model="editUserDialog.form.enable">
                <option :value="true">启用</option>
                <option :value="false">禁用</option>
              </select>
            </div>
          </div>

          <div class="form-row">
            <div class="form-field">
              <label class="field-label">账号到期时间</label>
              <input
                type="datetime-local"
                class="field-input"
                v-model="editUserDialog.form.accountExpireDate"
              >
              <div class="field-hint">留空则设置为永不过期</div>
            </div>
            <div class="form-field">
              <label class="field-label">密码到期时间</label>
              <input
                type="datetime-local"
                class="field-input"
                v-model="editUserDialog.form.credentialExpireDate"
              >
              <div class="field-hint">留空则设置为永不过期</div>
            </div>
          </div>

          <div class="role-info">
            <div class="info-icon">ℹ️</div>
            <div class="info-text">
              <span v-if="editUserDialog.form.role === 'admin'">
                管理员拥有所有功能的完整权限，包括用户管理、数据修改等。
              </span>
              <span v-else>
                普通用户只能查看数据，无法进行修改、删除等操作。
              </span>
            </div>
          </div>
        </div>

        <div class="dialog-footer">
          <button class="btn-cancel" @click="handleCloseEditUser">取消</button>
          <button
            class="btn-confirm"
            @click="handleSaveEditUser"
            :disabled="editUserDialog.saving"
          >
            <span v-if="editUserDialog.saving">
              <i class="loading-icon">⏳</i> 保存中...
            </span>
            <span v-else>
              <i>✓</i> 保存
            </span>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { userManagementApi } from '@/api/services'

// 搜索表单
const searchForm = reactive({
  username: '',
  name: '',
  role: '',
  status: ''
})

// 用户列表数据
const userList = ref([])
const loading = ref(false)

// 分页信息
const pagination = reactive({
  page: 1,
  pageSize: 10,
  total: 0
})

// 用户详情对话框
const userDetailDialog = reactive({
  visible: false,
  user: null
})

// 添加用户对话框
const addUserDialog = reactive({
  visible: false,
  saving: false,
  form: {
    username: '',
    name: '',
    password: '',
    role: 'user',
    accountExpireDate: '',
    credentialExpireDate: ''
  }
})

// 编辑用户对话框
const editUserDialog = reactive({
  visible: false,
  saving: false,
  currentUser: null,
  form: {
    username: '',
    name: '',
    role: 'user',
    enable: true,
    accountExpireDate: '',
    credentialExpireDate: ''
  }
})

// 计算属性
const totalPages = computed(() => {
  return Math.ceil(pagination.total / pagination.pageSize)
})

const visiblePages = computed(() => {
  const current = pagination.page
  const total = totalPages.value
  const pages = []

  let start = Math.max(1, current - 2)
  let end = Math.min(total, current + 2)

  for (let i = start; i <= end; i++) {
    pages.push(i)
  }

  return pages
})

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 将日期时间转换为datetime-local格式
const formatDateTimeLocal = (dateTime) => {
  if (!dateTime) return ''
  const date = new Date(dateTime)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  return `${year}-${month}-${day}T${hours}:${minutes}`
}

// 将datetime-local格式转换为后端期望的格式
const parseDateTimeLocal = (dateTimeLocal) => {
  if (!dateTimeLocal) return null
  // 创建Date对象并转换为后端期望的格式 (yyyy-MM-dd HH:mm:ss)
  const date = new Date(dateTimeLocal)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  const seconds = String(date.getSeconds()).padStart(2, '0')
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
}

// 获取用户列表
const getUserList = async () => {
  try {
    loading.value = true
    const params = {
      page: pagination.page,
      size: pagination.pageSize,
      username: searchForm.username || undefined,
      name: searchForm.name || undefined,
      role: searchForm.role || undefined,
      status: searchForm.status || undefined
    }

    const response = await userManagementApi.getUserList(params)

    if (response && response.records) {
      userList.value = response.records || []
      pagination.total = response.total || 0
    }
  } catch (error) {
    console.error('获取用户列表失败:', error)
    ElMessage.error('获取用户列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  getUserList()
}

// 重置
const handleReset = () => {
  Object.assign(searchForm, {
    username: '',
    name: '',
    role: '',
    status: ''
  })
  pagination.page = 1
  getUserList()
}

// 分页处理
const prevPage = () => {
  if (pagination.page > 1) {
    pagination.page--
    getUserList()
  }
}

const nextPage = () => {
  if (pagination.page < totalPages.value) {
    pagination.page++
    getUserList()
  }
}

const goToPage = (page) => {
  pagination.page = page
  getUserList()
}

// 查看用户详情
const handleViewUserDetail = (user) => {
  // 直接使用列表中的用户数据，因为它已经包含了角色信息
  userDetailDialog.user = user
  userDetailDialog.visible = true
  console.log('用户详情数据:', user) // 调试日志
}

// 关闭用户详情对话框
const handleCloseUserDetail = () => {
  userDetailDialog.visible = false
  userDetailDialog.user = null
}

// 添加用户
const handleAddUser = () => {
  addUserDialog.visible = true
  Object.assign(addUserDialog.form, {
    username: '',
    name: '',
    password: '',
    role: 'user',
    accountExpireDate: '',
    credentialExpireDate: ''
  })
}

// 关闭添加用户对话框
const handleCloseAddUser = () => {
  addUserDialog.visible = false
  addUserDialog.saving = false
}

// 保存用户
const handleSaveUser = async () => {
  if (!addUserDialog.form.username || !addUserDialog.form.password) {
    ElMessage.error('用户名和密码不能为空')
    return
  }

  try {
    addUserDialog.saving = true

    // 准备用户数据
    const userData = {
      username: addUserDialog.form.username,
      name: addUserDialog.form.name,
      password: addUserDialog.form.password
    }

    // 处理账号到期时间
    if (addUserDialog.form.accountExpireDate) {
      userData.accountExpireDate = parseDateTimeLocal(addUserDialog.form.accountExpireDate)
    }

    // 处理密码到期时间
    if (addUserDialog.form.credentialExpireDate) {
      userData.credentialExpireDate = parseDateTimeLocal(addUserDialog.form.credentialExpireDate)
    }

    // 创建用户
    await userManagementApi.createUser(userData)

    // 刷新列表来获取新用户
    await getUserList()
    const newUser = userList.value.find(u => u.username === addUserDialog.form.username)

    if (newUser) {
      // 为新用户分配角色（包括默认的user角色）
      await userManagementApi.assignUserRole(newUser.id, addUserDialog.form.role)
      console.log(`为新用户 ${newUser.username} 分配角色: ${addUserDialog.form.role}`)
    } else {
      console.error('创建用户后未找到新用户，无法分配角色')
    }

    ElMessage.success('用户创建成功')
    handleCloseAddUser()
    getUserList()

  } catch (error) {
    console.error('创建用户失败:', error)
    ElMessage.error('创建用户失败')
  } finally {
    addUserDialog.saving = false
  }
}

// 切换用户状态
const handleToggleUserStatus = async (user) => {
  const action = user.enable ? '禁用' : '启用'

  try {
    await ElMessageBox.confirm(
      `确定要${action}用户 "${user.username}" 吗？`,
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await userManagementApi.updateUserStatus(user.id, !user.enable)
    ElMessage.success(`用户${action}成功`)
    getUserList()

  } catch (error) {
    if (error !== 'cancel') {
      console.error(`${action}用户失败:`, error)
      ElMessage.error(`${action}用户失败`)
    }
  }
}

// 编辑用户信息
const handleEditUser = async (user) => {
  editUserDialog.visible = true
  editUserDialog.currentUser = user
  Object.assign(editUserDialog.form, {
    username: user.username,
    name: user.name || '',
    role: user.primaryRole || 'user',
    enable: user.enable,
    accountExpireDate: formatDateTimeLocal(user.accountExpireDate),
    credentialExpireDate: formatDateTimeLocal(user.credentialExpireDate)
  })
}

// 关闭编辑用户对话框
const handleCloseEditUser = () => {
  editUserDialog.visible = false
  editUserDialog.saving = false
  editUserDialog.currentUser = null
  Object.assign(editUserDialog.form, {
    username: '',
    name: '',
    role: 'user',
    enable: true,
    accountExpireDate: '',
    credentialExpireDate: ''
  })
}

// 保存编辑用户信息
const handleSaveEditUser = async () => {
  try {
    editUserDialog.saving = true

    const user = editUserDialog.currentUser
    const form = editUserDialog.form

    // 准备更新数据
    const updateData = {}
    let hasChanges = false

    // 检查基本信息变化
    if (form.name !== user.name) {
      updateData.name = form.name
      hasChanges = true
    }

    if (form.enable !== user.enable) {
      updateData.enable = form.enable
      hasChanges = true
    }

    // 检查账号到期时间变化
    const currentAccountExpire = formatDateTimeLocal(user.accountExpireDate)
    if (form.accountExpireDate !== currentAccountExpire) {
      updateData.accountExpireDate = form.accountExpireDate ?
        parseDateTimeLocal(form.accountExpireDate) : null
      hasChanges = true
    }

    // 检查密码到期时间变化
    const currentCredentialExpire = formatDateTimeLocal(user.credentialExpireDate)
    if (form.credentialExpireDate !== currentCredentialExpire) {
      updateData.credentialExpireDate = form.credentialExpireDate ?
        parseDateTimeLocal(form.credentialExpireDate) : null
      hasChanges = true
    }

    // 更新基本信息
    if (hasChanges) {
      await userManagementApi.updateUserInfo(user.id, updateData)
    }

    // 更新角色
    const currentRole = user.primaryRole
    const newRole = form.role

    if (currentRole !== newRole) {
      // 先移除旧角色，再分配新角色
      if (currentRole) {
        await userManagementApi.removeUserRole(user.id, currentRole)
      }
      await userManagementApi.assignUserRole(user.id, newRole)
    }

    ElMessage.success('用户信息更新成功')
    handleCloseEditUser()
    getUserList() // 刷新列表

  } catch (error) {
    console.error('保存用户信息失败:', error)
    ElMessage.error('保存用户信息失败')
  } finally {
    editUserDialog.saving = false
  }
}

// 组件挂载时获取数据
onMounted(() => {
  getUserList()
})
</script>

<style scoped>
.user-management-page {
  padding: 20px;
  background: #f5f7fa;
  min-height: 100vh;
}

/* 卡片样式 */
.card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.card-header {
  padding: 20px;
  border-bottom: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  color: #333;
  font-size: 18px;
  font-weight: 600;
}

.card-body {
  padding: 20px;
}

/* 搜索过滤样式 */
.search-filters {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.form-group {
  display: flex;
  flex-direction: column;
  min-width: 150px;
}

.form-group label {
  margin-bottom: 5px;
  font-weight: 500;
  color: #333;
  font-size: 14px;
}

.form-control {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  height: 38px;
  box-sizing: border-box;
}

.form-control:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
}

.button-group {
  justify-content: flex-end;
  align-items: flex-end;
}

.button-container {
  display: flex;
  gap: 10px;
  align-items: center;
  height: 38px; /* 与form-control高度一致 */
}

/* 按钮样式 */
.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
  transition: all 0.2s;
  height: 38px;
  box-sizing: border-box;
}

.btn-primary {
  background: #667eea;
  color: white;
}

.btn-primary:hover {
  background: #5a6fd8;
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background: #5a6268;
}

.btn-success {
  background: #28a745;
  color: white;
}

.btn-success:hover {
  background: #218838;
}

.btn-warning {
  background: #ffc107;
  color: #212529;
}

.btn-warning:hover {
  background: #e0a800;
}

.btn-info {
  background: #17a2b8;
  color: white;
}

.btn-info:hover {
  background: #138496;
}

.btn-sm {
  padding: 5px 10px;
  font-size: 12px;
}

/* 表格样式 */
.table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 15px;
}

.table th,
.table td {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid #eee;
}

.table th {
  background: #f8f9fa;
  font-weight: 600;
  color: #333;
}

.table tr:hover {
  background: #f8f9fa;
}

/* 状态样式 */
.status-online {
  color: #28a745;
  font-weight: 500;
}

.status-offline {
  color: #dc3545;
  font-weight: 500;
}

.badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.badge-info {
  background: #d1ecf1;
  color: #0c5460;
}

.badge-danger {
  background: #f8d7da;
  color: #721c24;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  gap: 5px;
  flex-wrap: wrap;
}

/* 分页样式 */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
  margin-top: 20px;
}

.pagination button {
  padding: 8px 12px;
  border: 1px solid #ddd;
  background: white;
  cursor: pointer;
  border-radius: 4px;
}

.pagination button:hover:not(:disabled) {
  background: #f8f9fa;
}

.pagination button.active {
  background: #667eea;
  color: white;
  border-color: #667eea;
}

.pagination button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  background: white !important;
  color: #999 !important;
  border-color: #ddd !important;
}

/* 新版弹框样式 */
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(2px);
}

.dialog-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow: hidden;
  animation: dialogSlideIn 0.3s ease-out;
}

@keyframes dialogSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.dialog-header {
  padding: 24px 24px 16px 24px;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.dialog-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
  display: flex;
  align-items: center;
  gap: 8px;
}

.dialog-title .icon {
  font-size: 18px;
}

.dialog-close {
  background: none;
  border: none;
  width: 32px;
  height: 32px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #6b7280;
  transition: all 0.2s;
}

.dialog-close:hover {
  background: #f3f4f6;
  color: #374151;
}

.dialog-body {
  padding: 24px;
  max-height: 60vh;
  overflow-y: auto;
}

.form-row {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.form-field {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.field-label {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 6px;
}

.field-label.required::after {
  content: ' *';
  color: #ef4444;
}

.field-input,
.field-select {
  padding: 10px 12px;
  border: 1.5px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  transition: all 0.2s;
  background: white;
}

.field-input:focus,
.field-select:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.field-input[type="datetime-local"] {
  color: #374151;
  cursor: pointer;
}

.field-input[type="datetime-local"]::-webkit-calendar-picker-indicator {
  cursor: pointer;
  filter: invert(0.5);
  transition: filter 0.2s;
}

.field-input[type="datetime-local"]::-webkit-calendar-picker-indicator:hover {
  filter: invert(0.3);
}

.field-hint {
  font-size: 12px;
  color: #6b7280;
  margin-top: 4px;
}

.role-info {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 12px;
  display: flex;
  gap: 8px;
  margin-top: 16px;
}

.info-icon {
  font-size: 16px;
  flex-shrink: 0;
}

.info-text {
  font-size: 13px;
  color: #64748b;
  line-height: 1.5;
}

.dialog-footer {
  padding: 16px 24px 24px 24px;
  border-top: 1px solid #e5e7eb;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.btn-cancel,
.btn-confirm {
  padding: 10px 20px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 6px;
  border: none;
}

.btn-cancel {
  background: #f9fafb;
  color: #374151;
  border: 1px solid #d1d5db;
}

.btn-cancel:hover {
  background: #f3f4f6;
  border-color: #9ca3af;
}

.btn-confirm {
  background: #667eea;
  color: white;
}

.btn-confirm:hover:not(:disabled) {
  background: #5a6fd8;
}

.btn-confirm:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.loading-icon {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 模态框样式（保留用于用户详情） */
.modal {
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0,0,0,0.5);
}

.modal-content {
  background-color: white;
  margin: 5% auto;
  padding: 0;
  border-radius: 8px;
  width: 85%;
  max-width: 900px;
  max-height: 85vh;
  overflow-y: auto;
  position: relative;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 20px 15px 20px;
  margin-bottom: 0;
  border-bottom: 1px solid #eee;
}

.modal-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.modal-footer {
  padding: 15px 20px 20px 20px;
  text-align: center;
  border-top: 1px solid #eee;
  margin-top: 20px;
}

.modal-footer .btn {
  margin: 0 5px;
}

.close {
  font-size: 24px;
  font-weight: bold;
  cursor: pointer;
  color: #999;
}

.close:hover {
  color: #333;
}

/* 用户详情容器 */
.user-detail-container {
  padding: 20px;
}

.detail-row {
  display: flex;
  margin-bottom: 16px;
  gap: 40px;
}

.detail-item {
  flex: 1;
  display: flex;
  align-items: flex-start;
  min-height: 24px;
}

.detail-label {
  font-weight: 600;
  color: #333;
  min-width: 100px;
  margin-right: 12px;
  flex-shrink: 0;
  text-align: right;
}

.detail-value {
  color: #666;
  word-break: break-all;
  line-height: 1.4;
}

.role-description {
  margin-top: 15px;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
  font-size: 14px;
  color: #606266;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .search-filters {
    flex-direction: column;
    gap: 15px;
  }

  .dialog-container {
    width: 95%;
    margin: 20px;
  }

  .form-row {
    flex-direction: column;
    gap: 16px;
  }

  .dialog-footer {
    flex-direction: column-reverse;
    gap: 8px;
  }

  .btn-cancel,
  .btn-confirm {
    width: 100%;
    justify-content: center;
  }

  .modal-content {
    width: 95%;
    margin: 2vh auto;
  }

  .detail-row {
    flex-direction: column;
    gap: 12px;
  }

  .detail-label {
    min-width: 70px;
    text-align: left;
  }

  .table {
    font-size: 12px;
  }

  .table th,
  .table td {
    padding: 8px;
  }

  .action-buttons {
    flex-direction: column;
    gap: 3px;
  }
}
</style>
