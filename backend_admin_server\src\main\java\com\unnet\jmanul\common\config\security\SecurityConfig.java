package com.unnet.jmanul.common.config.security;

import com.unnet.jmanul.system.service.IUserService;
import com.unnet.jmanul.common.utils.jwt.JwtComponent;
import lombok.RequiredArgsConstructor;
import org.casbin.jcasbin.main.Enforcer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.authentication.AuthenticationEventPublisher;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.DefaultAuthenticationEventPublisher;
import org.springframework.security.authentication.dao.DaoAuthenticationProvider;
import org.springframework.security.config.annotation.authentication.builders.AuthenticationManagerBuilder;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.www.BasicAuthenticationFilter;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.web.filter.CorsFilter;

import javax.annotation.Resource;

@Configuration
@EnableWebSecurity
@RequiredArgsConstructor
class SecurityConfig {

    private final Enforcer enforcer;
    private final IUserService userService;
    private final JwtComponent jwtComponent;
    private final PasswordEncoder passwordEncoder;

    @Qualifier("userDetailsService")
    private final UserDetailsService userDetailsService;

    @Resource
    private ApplicationContext applicationContext;


    @Bean
    @Autowired
    SecurityFilterChain getSecurityFilterChain(HttpSecurity httpSecurity) throws Exception {
        httpSecurity
                .addFilterBefore(new CasbinFilter(enforcer, userService, jwtComponent, applicationContext), BasicAuthenticationFilter.class)
                .antMatcher("/**")
                // enable CORS
                .cors().and()
                // Disable CSRF (not required for this demo)
                .csrf().disable()
                // 禁用默认的异常处理，让我们的Controller处理认证异常
                .exceptionHandling().disable()
                // Enable SecurityContext support
                .securityContext();

        return httpSecurity.build();
    }

    @Bean
    public AuthenticationEventPublisher authenticationEventPublisher(ApplicationEventPublisher applicationEventPublisher) {
        return new DefaultAuthenticationEventPublisher(applicationEventPublisher);
    }

    ////////////////////////////////////////////////////////////////////////////////
    // Authentication Provider Configuration                                      //
    ////////////////////////////////////////////////////////////////////////////////
    @Bean
    public DaoAuthenticationProvider authenticationProvider() {
        DaoAuthenticationProvider authProvider = new DaoAuthenticationProvider();
        authProvider.setUserDetailsService(userDetailsService);
        authProvider.setPasswordEncoder(passwordEncoder);
        return authProvider;
    }

    ////////////////////////////////////////////////////////////////////////////////
    // Expose authentication manager bean                                         //
    ////////////////////////////////////////////////////////////////////////////////
    @Bean
    public AuthenticationManager authenticationManager(HttpSecurity http) throws Exception {
        AuthenticationManagerBuilder authenticationManagerBuilder =
            http.getSharedObject(AuthenticationManagerBuilder.class);
        authenticationManagerBuilder.authenticationProvider(authenticationProvider());
        return authenticationManagerBuilder.build();
    }

    ////////////////////////////////////////////////////////////////////////////////
    // CORS                                                                       //
    ////////////////////////////////////////////////////////////////////////////////

    @Bean
    public CorsFilter corsFilter() {
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        CorsConfiguration config = new CorsConfiguration();
        config.setAllowCredentials(true);
        config.addAllowedOriginPattern("*");
        config.addAllowedHeader("*");
        config.addAllowedMethod("*");
        // 关键：暴露自定义响应头，让前端能够访问
        config.addExposedHeader("X-Auth-Error");
        config.addExposedHeader("X-Auth-Message");
        source.registerCorsConfiguration("/**", config);
        return new CorsFilter(source);
    }

}