package com.unnet.jmanul.system.controller;

import com.unnet.jmanul.common.annotation.rest.AnonymousGetMapping;
import com.unnet.jmanul.common.rest.ApiDef;
import com.unnet.jmanul.common.utils.Dict;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@Slf4j
@RequestMapping(ApiDef.V1)
@RestController
@Api(tags = {"Z. 版本 VersionController"})
public class VersionController {

    @AnonymousGetMapping("/version")
    public ResponseEntity<Map<String, Object>> version() {
        return ResponseEntity.ok(Dict.build().put("version", "jmanul-v1.0.0").getMap());
    }

}
