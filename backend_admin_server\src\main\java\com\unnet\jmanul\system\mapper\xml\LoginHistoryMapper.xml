<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unnet.jmanul.system.mapper.LoginHistoryMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.unnet.jmanul.system.entity.LoginHistory">
        <id column="id" property="id" />
        <result column="username" property="username" />
        <result column="ip_addr" property="ipAddr" />
        <result column="location" property="location" />
        <result column="browser" property="browser" />
        <result column="login_method" property="loginMethod" />
        <result column="success" property="success" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, username, ip_addr, location, browser, login_method, success
    </sql>

</mapper>
