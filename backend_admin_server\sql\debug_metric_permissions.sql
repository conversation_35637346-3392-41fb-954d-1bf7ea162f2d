-- 调试指标配置权限的SQL脚本
-- 用于检查和修复权限配置问题

-- 1. 查看当前所有与metric-threshold-config相关的权限
SELECT 
    id,
    ptype,
    v0 as role,
    v1 as resource,
    v2 as action,
    v3, v4, v5
FROM casbin_rule 
WHERE v1 LIKE '%metric-threshold-config%' 
ORDER BY v0, v2;

-- 2. 查看admin用户的所有权限
SELECT 
    id,
    ptype,
    v0 as role,
    v1 as resource,
    v2 as action
FROM casbin_rule 
WHERE ptype = 'p' AND v0 = 'admin'
ORDER BY v1, v2;

-- 3. 查看用户角色关系
SELECT 
    id,
    ptype,
    v0 as user,
    v1 as role
FROM casbin_rule 
WHERE ptype = 'g'
ORDER BY v0;

-- 4. 删除可能重复的权限规则
DELETE FROM casbin_rule 
WHERE ptype = 'p' 
AND v1 LIKE '%metric-threshold-config%';

-- 5. 重新添加正确的权限规则
INSERT INTO `casbin_rule` (`ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES 
('p', 'admin', '/api/v1/admin/metric-threshold-config*', 'GET', '', '', ''),
('p', 'admin', '/api/v1/admin/metric-threshold-config*', 'POST', '', '', ''),
('p', 'admin', '/api/v1/admin/metric-threshold-config*', 'PUT', '', '', ''),
('p', 'admin', '/api/v1/admin/metric-threshold-config*', 'DELETE', '', '', ''),
('p', 'user', '/api/v1/admin/metric-threshold-config*', 'GET', '', '', '');

-- 6. 验证权限是否正确添加
SELECT 
    ptype,
    v0 as role,
    v1 as resource,
    v2 as action
FROM casbin_rule 
WHERE v1 LIKE '%metric-threshold-config%' 
ORDER BY v0, v2;
