package com.unnet.jmanul.business.entity.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.*;
import java.math.BigDecimal;

/**
 * 指标阈值配置请求DTO
 * 
 * <AUTHOR>
 * @since 2024-07-21
 */
@Data
@Schema(description = "指标阈值配置请求")
public class MetricThresholdConfigRequest {

    @Schema(description = "配置ID（更新时必填）")
    private Long id;

    @NotBlank(message = "指标类型不能为空")
    @Schema(description = "指标类型", example = "CPU_TEMPERATURE")
    private String metricType;

    @NotBlank(message = "指标名称不能为空")
    @Size(max = 100, message = "指标名称长度不能超过100个字符")
    @Schema(description = "指标名称", example = "CPU温度")
    private String metricName;

    @Size(max = 200, message = "指标描述长度不能超过200个字符")
    @Schema(description = "指标描述", example = "CPU温度过高告警")
    private String metricDescription;

    @NotBlank(message = "JSON字段路径不能为空")
    @Size(max = 200, message = "JSON字段路径长度不能超过200个字符")
    @Schema(description = "JSON字段路径", example = "cpu_temp")
    private String jsonFieldPath;

    @NotNull(message = "阈值不能为空")
    @DecimalMin(value = "0", message = "阈值必须大于等于0")
    @DecimalMax(value = "999999.99", message = "阈值不能超过999999.99")
    @Schema(description = "阈值", example = "85.00")
    private BigDecimal thresholdValue;

    @NotBlank(message = "阈值单位不能为空")
    @Size(max = 20, message = "阈值单位长度不能超过20个字符")
    @Schema(description = "阈值单位", example = "°C")
    private String thresholdUnit;

    @NotBlank(message = "比较操作符不能为空")
    @Pattern(regexp = "^(>=|>|<=|<|=)$", message = "比较操作符只能是 >=, >, <=, <, = 中的一个")
    @Schema(description = "比较操作符", example = ">=")
    private String comparisonOperator;

    @NotBlank(message = "告警级别不能为空")
    @Pattern(regexp = "^(LOW|MEDIUM|HIGH|CRITICAL)$", message = "告警级别只能是 LOW, MEDIUM, HIGH, CRITICAL 中的一个")
    @Schema(description = "告警级别", example = "HIGH")
    private String alertLevel;

    @Size(max = 500, message = "告警消息模板长度不能超过500个字符")
    @Schema(description = "告警消息模板", example = "CPU温度过高，当前温度：{current_value}，阈值：{threshold_value}")
    private String alertMessage;

    @Schema(description = "是否启用", example = "true")
    private Boolean isEnabled = true;

    @Min(value = 0, message = "排序值不能小于0")
    @Schema(description = "排序", example = "1")
    private Integer sortOrder = 0;

    @Size(max = 500, message = "备注长度不能超过500个字符")
    @Schema(description = "备注")
    private String remark;
}
