# SSE连接配置说明

## 问题解决

### 原始问题
前端SSE服务无法连接到后端服务 `localhost:8081`，导致实时通知功能无法正常工作。

### 解决方案

#### 1. 添加Vite代理配置
在 `vite.config.js` 中添加代理配置，将前端的 `/api` 请求代理到后端服务：

```javascript
export default defineConfig({
  // ...其他配置
  server: {
    proxy: {
      '/api': {
        target: 'http://localhost:8081',
        changeOrigin: true,
        secure: false,
        ws: true, // 支持websocket和SSE
        configure: (proxy, _options) => {
          proxy.on('error', (err, _req, _res) => {
            console.log('proxy error', err);
          });
          proxy.on('proxyReq', (proxyReq, req, _res) => {
            console.log('Sending Request to the Target:', req.method, req.url);
          });
          proxy.on('proxyRes', (proxyRes, req, _res) => {
            console.log('Received Response from the Target:', proxyRes.statusCode, req.url);
          });
        },
      }
    }
  }
})
```

#### 2. 环境变量配置
创建环境配置文件来管理不同环境的API地址：

**开发环境 (.env.development):**
```env
VITE_API_BASE_URL=http://localhost:8081
VITE_SSE_BASE_URL=http://localhost:8081
```

**生产环境 (.env.production):**
```env
VITE_API_BASE_URL=/api
VITE_SSE_BASE_URL=/api
```

#### 3. 更新SSE服务配置
修改 `sseService.js` 使用环境变量：

```javascript
class SSEService {
  constructor() {
    // ...其他配置
    this.baseUrl = import.meta.env.VITE_SSE_BASE_URL || '/api'
    this.sseEndpoint = `${this.baseUrl}/device/notifications/sse`
  }
  
  connect() {
    console.log('正在连接SSE服务:', this.sseEndpoint)
    this.eventSource = new EventSource(this.sseEndpoint)
    // ...其他代码
  }
}
```

#### 4. 更新API配置
修改 `api/index.js` 使用环境变量和添加API密钥：

```javascript
const api = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || '/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
    'X-API-Key': 'api-SvardHNxjL87TMHbgS2JFDpUsyi1iq9e'
  }
})
```

## 连接流程

### 开发环境连接流程
```
前端 (localhost:3000) 
  ↓ 请求 /api/device/notifications/sse
Vite代理服务器
  ↓ 转发到 http://localhost:8081/api/device/notifications/sse
后端服务 (localhost:8081)
  ↓ 返回SSE事件流
前端接收实时通知
```

### 生产环境连接流程
```
前端 (同域名)
  ↓ 请求 /api/device/notifications/sse
后端服务 (同域名)
  ↓ 返回SSE事件流
前端接收实时通知
```

## 测试验证

### 1. 启动服务
```bash
# 启动后端服务
cd backend_server
mvn spring-boot:run

# 启动前端服务
cd front_server
npm run dev
```

### 2. 检查连接
1. 打开浏览器访问 `http://localhost:3000`
2. 打开开发者工具，查看Network标签
3. 查找 `device/notifications/sse` 请求
4. 确认请求状态为 `200` 且类型为 `text/event-stream`

### 3. 使用测试组件
访问测试页面（需要添加路由）查看详细的连接信息：
- 连接状态
- 环境配置
- 连接日志
- 接收到的消息

### 4. 发送测试数据
```bash
curl -X POST http://localhost:8081/api/agent/report \
  -H 'Content-Type: application/json' \
  -H 'X-API-Key: api-SvardHNxjL87TMHbgS2JFDpUsyi1iq9e' \
  -d '{
    "device_id": "test_device",
    "hostname": "test_terminal",
    "uptime": 3600,
    "cpu_temp": 65.5,
    ...
  }'
```

## 调试技巧

### 1. 浏览器开发者工具
- **Network标签**: 查看SSE连接状态
- **Console标签**: 查看连接日志和错误信息
- **Application标签**: 查看EventSource连接详情

### 2. 后端日志
查看后端控制台输出：
```
INFO - 新的客户端连接到设备通知SSE流
INFO - 发送设备注册通知 - 设备: xxx, 主机名: xxx
```

### 3. 前端调试信息
SSE服务会输出详细的调试信息：
```javascript
console.log('正在连接SSE服务:', this.sseEndpoint)
console.log('环境变量 VITE_SSE_BASE_URL:', import.meta.env.VITE_SSE_BASE_URL)
console.log('当前环境:', import.meta.env.MODE)
```

## 常见问题

### 1. 连接被拒绝
**症状**: `ERR_CONNECTION_REFUSED`
**解决**: 确保后端服务已启动且端口正确

### 2. 404错误
**症状**: `404 Not Found`
**解决**: 检查后端路由配置和前端请求路径

### 3. CORS错误
**症状**: `CORS policy` 错误
**解决**: 后端添加CORS配置或使用代理

### 4. 连接超时
**症状**: 连接建立后立即断开
**解决**: 检查防火墙和代理服务器配置

### 5. 代理不工作
**症状**: 请求没有被代理到后端
**解决**: 
- 重启前端开发服务器
- 检查vite.config.js配置
- 确认请求路径以 `/api` 开头

## 部署注意事项

### 1. 开发环境
- 使用Vite代理功能
- 后端服务独立运行在8081端口
- 前端开发服务器运行在3000端口

### 2. 生产环境
- 前后端部署在同一域名下
- 使用相对路径 `/api`
- 配置反向代理（如Nginx）

### 3. 网络配置
- 确保SSE端点可访问
- 配置负载均衡器支持长连接
- 设置合适的超时时间

## 性能优化

### 1. 连接管理
- 页面卸载时断开连接
- 避免重复连接
- 合理设置重连间隔

### 2. 消息处理
- 限制消息缓存数量
- 及时清理过期通知
- 避免内存泄漏

### 3. 错误处理
- 实现优雅降级
- 记录详细错误信息
- 提供用户友好的错误提示

通过以上配置，前端SSE服务应该能够成功连接到后端的 `localhost:8081` 服务，实现实时通知功能。
