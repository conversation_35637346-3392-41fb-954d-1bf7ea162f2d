//package com.unnet.changan5G.service;
//
//import com.unnet.changan5G.dto.terminal.TerminalAlertInfo;
//import com.unnet.changan5G.entity.MetricThresholdConfigEntity;
//import org.junit.jupiter.api.Test;
//import org.junit.jupiter.api.extension.ExtendWith;
//import org.mockito.InjectMocks;
//import org.mockito.Mock;
//import org.mockito.junit.jupiter.MockitoExtension;
//
//import java.time.LocalDateTime;
//
//import static org.junit.jupiter.api.Assertions.*;
//import static org.mockito.ArgumentMatchers.*;
//import static org.mockito.Mockito.*;
//
///**
// * 告警级别SSE推送测试
// */
//@ExtendWith(MockitoExtension.class)
//public class AlertLevelSSETest {
//
//    @Mock
//    private DeviceNotificationService deviceNotificationService;
//
//    @Mock
//    private MetricThresholdConfigService metricThresholdConfigService;
//
//    @InjectMocks
//    private MetricConsumerService metricConsumerService;
//
//    @Test
//    public void testAlertLevelInSSEPush() {
//        // 准备测试数据
//        TerminalAlertInfo alert = new TerminalAlertInfo();
//        alert.setAlertId("test-alert-001");
//        alert.setDeviceId("test-device-001");
//        alert.setAlertType("CPU告警");
//        alert.setAlertLevel("HIGH"); // 设置告警级别
//        alert.setAlertDetails("CPU温度过高");
//        alert.setMetricName("cpu_temp");
//        alert.setThreshold("80°C");
//        alert.setCurrentValue("85°C");
//        alert.setAlertTime(LocalDateTime.now());
//        alert.setAlertStatus(TerminalAlertInfo.AlertStatus.ACTIVE);
//
//        // 模拟配置服务返回
//        MetricThresholdConfigEntity config = new MetricThresholdConfigEntity();
//        config.setMetricType("CPU告警");
//        config.setAlertLevel("HIGH");
//        when(metricThresholdConfigService.getConfigByMetricType("CPU告警")).thenReturn(config);
//
//        // 执行测试方法
//        // 注意：这里需要调用实际的SSE推送方法
//        // metricConsumerService.sendAlertSSENotification(alert, "test-hostname");
//
//        // 验证SSE推送是否包含正确的告警级别
//        verify(deviceNotificationService).sendAlertNotification(
//                eq("test-device-001"),
//                eq("test-hostname"),
//                eq("CPU告警"),
//                eq("HIGH"), // 验证告警级别
//                anyString(),
//                anyString(),
//                eq("85°C"),
//                eq("80°C")
//        );
//    }
//
//    @Test
//    public void testAlertLevelFallbackToConfig() {
//        // 测试当告警对象中没有告警级别时，从配置获取
//        TerminalAlertInfo alert = new TerminalAlertInfo();
//        alert.setAlertId("test-alert-002");
//        alert.setDeviceId("test-device-002");
//        alert.setAlertType("内存告警");
//        alert.setAlertLevel(null); // 告警级别为空
//        alert.setAlertDetails("内存使用率过高");
//        alert.setMetricName("memory_percent");
//        alert.setThreshold("90%");
//        alert.setCurrentValue("95%");
//        alert.setAlertTime(LocalDateTime.now());
//        alert.setAlertStatus(TerminalAlertInfo.AlertStatus.ACTIVE);
//
//        // 模拟配置服务返回
//        MetricThresholdConfigEntity config = new MetricThresholdConfigEntity();
//        config.setMetricType("内存告警");
//        config.setAlertLevel("CRITICAL");
//        when(metricThresholdConfigService.getConfigByMetricType("内存告警")).thenReturn(config);
//
//        // 执行测试方法
//        // metricConsumerService.sendAlertSSENotification(alert, "test-hostname");
//
//        // 验证SSE推送使用了配置中的告警级别
//        verify(deviceNotificationService).sendAlertNotification(
//                eq("test-device-002"),
//                eq("test-hostname"),
//                eq("内存告警"),
//                eq("CRITICAL"), // 验证从配置获取的告警级别
//                anyString(),
//                anyString(),
//                eq("95%"),
//                eq("90%")
//        );
//    }
//
//    @Test
//    public void testAlertLevelDefaultFallback() {
//        // 测试当告警对象和配置都没有告警级别时，使用默认级别
//        TerminalAlertInfo alert = new TerminalAlertInfo();
//        alert.setAlertId("test-alert-003");
//        alert.setDeviceId("test-device-003");
//        alert.setAlertType("磁盘告警");
//        alert.setAlertLevel(null); // 告警级别为空
//        alert.setAlertDetails("磁盘使用率过高");
//        alert.setMetricName("disk_percent");
//        alert.setThreshold("80%");
//        alert.setCurrentValue("85%");
//        alert.setAlertTime(LocalDateTime.now());
//        alert.setAlertStatus(TerminalAlertInfo.AlertStatus.ACTIVE);
//
//        // 模拟配置服务返回空配置
//        when(metricThresholdConfigService.getConfigByMetricType("磁盘告警")).thenReturn(null);
//
//        // 执行测试方法
//        // metricConsumerService.sendAlertSSENotification(alert, "test-hostname");
//
//        // 验证SSE推送使用了默认告警级别
//        verify(deviceNotificationService).sendAlertNotification(
//                eq("test-device-003"),
//                eq("test-hostname"),
//                eq("磁盘告警"),
//                eq("MEDIUM"), // 验证默认告警级别
//                anyString(),
//                anyString(),
//                eq("85%"),
//                eq("80%")
//        );
//    }
//}
