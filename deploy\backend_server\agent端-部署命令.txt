// 上传src文件到/data/backend/backend_server

// 构建agent服务镜像
docker build -f ./dockerfile -t backend_server:1.0 .

docker run -d \
  --name backend_server \
  -p 8081:8081 \
  --add-host ecm-large-node1:******** \
  --add-host ecm-large-node2:******** \
  --add-host ecm-large-node3:******** \
  -e SPRING_PROFILES_ACTIVE=prod \
  -e SPRING_ELASTICSEARCH_URIS=********:9200,********:9200,********:9200 \
  -e SPRING_ELASTICSEARCH_USERNAME=elastic \
  -e SPRING_ELASTICSEARCH_PASSWORD=Changan5g.es \
  -e SPRING_KAFKA_BOOTSTRAP_SERVERS=********:9092,********:9092,********:9092 \
  -e KAFKA_USERNAME=admin \
  -e KAFKA_PASSWORD=Changan5g.kafka \
  -e JAVA_TOOL_OPTIONS="-Duser.timezone=Asia/Shanghai" \
  backend_server:1.0


// 查看容器日志
docker logs front_server