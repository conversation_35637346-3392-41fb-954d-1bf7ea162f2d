import { JSEncrypt } from 'jsencrypt'

/**
 * RSA加密工具类
 */
export class RSAUtil {
  constructor() {
    this.encrypt = new JSEncrypt()
  }

  /**
   * 设置公钥
   * @param {string} publicKey - RSA公钥
   */
  setPublicKey(publicKey) {
    // 格式化公钥
    const formattedKey = this.formatPublicKey(publicKey)
    this.encrypt.setPublicKey(formattedKey)
  }

  /**
   * 格式化公钥，添加必要的头尾标识
   * @param {string} publicKey - 原始公钥字符串
   * @returns {string} - 格式化后的公钥
   */
  formatPublicKey(publicKey) {
    // 如果已经包含头尾标识，直接返回
    if (publicKey.includes('-----BEGIN PUBLIC KEY-----')) {
      return publicKey
    }

    // 添加头尾标识和换行
    return `-----BEGIN PUBLIC KEY-----\n${publicKey}\n-----END PUBLIC KEY-----`
  }

  /**
   * 加密字符串
   * @param {string} text - 要加密的文本
   * @returns {string|false} - 加密后的Base64字符串，失败返回false
   */
  encryptText(text) {
    return this.encrypt.encrypt(text)
  }
}

/**
 * 创建RSA加密实例
 * @returns {RSAUtil} - RSA工具实例
 */
export function createRSAUtil() {
  return new RSAUtil()
}
