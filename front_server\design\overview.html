<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>概览页面</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            color: #333;
            padding: 20px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
            border-left: 4px solid #667eea;
        }

        .stat-number {
            font-size: 32px;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #666;
            font-size: 14px;
        }

        .card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            overflow: hidden;
        }

        .card-header {
            padding: 20px;
            border-bottom: 1px solid #eee;
            background: #fafafa;
        }

        .card-header h3 {
            font-size: 18px;
            font-weight: 600;
            color: #333;
        }

        .card-body {
            padding: 20px;
        }

        .content-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }

        .scroll-container {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #eee;
            border-radius: 4px;
            padding: 10px;
        }

        .log-item {
            padding: 12px;
            margin-bottom: 8px;
            border-radius: 4px;
            font-size: 14px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: opacity 0.3s ease;
        }

        .log-item.online {
            background: #d4edda;
            color: #155724;
            border-left: 4px solid #28a745;
        }

        .log-item.offline {
            background: #f8d7da;
            color: #721c24;
            border-left: 4px solid #dc3545;
        }

        .log-item.fade-out {
            opacity: 0.5;
        }

        .log-content {
            flex: 1;
        }

        .log-actions {
            margin-left: 10px;
        }

        .btn {
            padding: 6px 12px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s;
            text-decoration: none;
            display: inline-block;
        }

        .btn-primary {
            background: #667eea;
            color: white;
        }

        .btn-primary:hover {
            background: #5a6fd8;
        }

        .btn-info {
            background: #17a2b8;
            color: white;
        }

        .btn-warning {
            background: #ffc107;
            color: #212529;
        }

        .btn-danger {
            background: #dc3545;
            color: white;
        }

        .btn-sm {
            padding: 4px 8px;
            font-size: 11px;
        }

        .list-item {
            padding: 12px;
            margin-bottom: 8px;
            border-radius: 4px;
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .list-item:hover {
            background: #e9ecef;
        }

        .item-info {
            flex: 1;
        }

        .item-title {
            font-weight: 600;
            color: #333;
            margin-bottom: 4px;
        }

        .item-subtitle {
            font-size: 12px;
            color: #666;
        }

        .item-actions {
            margin-left: 10px;
        }

        .status-badge {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
            margin-right: 8px;
        }

        .status-offline {
            background: #f8d7da;
            color: #721c24;
        }

        .status-alert {
            background: #fff3cd;
            color: #856404;
        }

        .empty-message {
            text-align: center;
            color: #999;
            font-style: italic;
            padding: 20px;
        }

        .alert-level {
            display: inline-block;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 10px;
            font-weight: 500;
            margin-left: 8px;
        }

        .alert-level.high {
            background: #dc3545;
            color: white;
        }

        .alert-level.medium {
            background: #ffc107;
            color: #212529;
        }

        .alert-level.low {
            background: #6c757d;
            color: white;
        }
    </style>
</head>
<body>
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-number" id="totalTerminals">1,234</div>
            <div class="stat-label">终端总数</div>
        </div>
        <div class="stat-card">
            <div class="stat-number" id="onlineTerminals">1,156</div>
            <div class="stat-label">在线终端</div>
        </div>
        <div class="stat-card">
            <div class="stat-number" id="offlineTerminals">78</div>
            <div class="stat-label">离线终端</div>
        </div>
        <div class="stat-card">
            <div class="stat-number" id="alertCount">12</div>
            <div class="stat-label">告警数量</div>
        </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="content-grid">
        <!-- 上下线通知 -->
        <div class="card">
            <div class="card-header">
                <h3>终端上下线实时动态</h3>
            </div>
            <div class="card-body">
                <div class="scroll-container" id="logContainer">
                    <div class="empty-message" id="emptyLogMessage">暂无上下线记录</div>
                </div>
            </div>
        </div>

        <!-- 离线终端列表 -->
        <div class="card">
            <div class="card-header">
                <h3>离线终端列表</h3>
            </div>
            <div class="card-body">
                <div class="scroll-container" id="offlineContainer">
                    <div class="list-item">
                        <div class="item-info">
                            <div class="item-title">
                                <span class="status-badge status-offline">离线</span>
                                T002
                            </div>
                            <div class="item-subtitle">主机名002 - 离线时间: 2024-07-14 18:28:15</div>
                        </div>
                        <div class="item-actions">
                            <button class="btn btn-info btn-sm" onclick="viewTerminalDetail('T002')">查看详情</button>
                        </div>
                    </div>
                    <div class="list-item">
                        <div class="item-info">
                            <div class="item-title">
                                <span class="status-badge status-offline">离线</span>
                                T005
                            </div>
                            <div class="item-subtitle">主机名005 - 离线时间: 2024-07-14 18:20:55</div>
                        </div>
                        <div class="item-actions">
                            <button class="btn btn-info btn-sm" onclick="viewTerminalDetail('T005')">查看详情</button>
                        </div>
                    </div>
                    <div class="list-item">
                        <div class="item-info">
                            <div class="item-title">
                                <span class="status-badge status-offline">离线</span>
                                T008
                            </div>
                            <div class="item-subtitle">主机名008 - 离线时间: 2024-07-14 18:15:20</div>
                        </div>
                        <div class="item-actions">
                            <button class="btn btn-info btn-sm" onclick="viewTerminalDetail('T008')">查看详情</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 告警终端列表 -->
    <div class="card">
        <div class="card-header">
            <h3>告警终端列表</h3>
        </div>
        <div class="card-body">
            <div class="scroll-container" id="alertContainer">
                <div class="list-item">
                    <div class="item-info">
                        <div class="item-title">
                            <span class="status-badge status-alert">告警</span>
                            _0fd94938951f4a64bb11c6817a81f7e7
                            <span class="alert-level high">高</span>
                        </div>
                        <div class="item-subtitle">CPU温度过高 - 告警时间: 2024-07-14 18:25:30</div>
                    </div>
                    <div class="item-actions">
                        <button class="btn btn-danger btn-sm" onclick="viewAlertDetail('A001')">查看告警</button>
                        <button class="btn btn-warning btn-sm" onclick="viewTerminalMetrics('_0fd94938951f4a64bb11c6817a81f7e7')">实时指标</button>
                    </div>
                </div>
                <div class="list-item">
                    <div class="item-info">
                        <div class="item-title">
                            <span class="status-badge status-alert">告警</span>
                            T003
                            <span class="alert-level medium">中</span>
                        </div>
                        <div class="item-subtitle">系统盘使用率过高 - 告警时间: 2024-07-14 18:18:45</div>
                    </div>
                    <div class="item-actions">
                        <button class="btn btn-danger btn-sm" onclick="viewAlertDetail('A003')">查看告警</button>
                        <button class="btn btn-warning btn-sm" onclick="viewTerminalMetrics('T003')">实时指标</button>
                    </div>
                </div>
                <div class="list-item">
                    <div class="item-info">
                        <div class="item-title">
                            <span class="status-badge status-alert">告警</span>
                            _0fd94938951f4a64bb11c6817a81f7e7
                            <span class="alert-level medium">中</span>
                        </div>
                        <div class="item-subtitle">软件许可证即将过期 - 告警时间: 2024-07-14 18:15:20</div>
                    </div>
                    <div class="item-actions">
                        <button class="btn btn-danger btn-sm" onclick="viewAlertDetail('A004')">查看告警</button>
                        <button class="btn btn-warning btn-sm" onclick="viewTerminalMetrics('_0fd94938951f4a64bb11c6817a81f7e7')">实时指标</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 存储日志项目的定时器
        const logTimers = new Map();
        
        // 模拟终端状态变化和上下线通知
        function simulateTerminalStatusChange() {
            const terminals = [
                { id: 'T006', hostname: '主机名006', type: 'online' },
                { id: 'T007', hostname: '主机名007', type: 'offline' },
                { id: 'T008', hostname: '主机名008', type: 'online' },
                { id: '_0fd94938951f4a64bb11c6817a81f7e7', hostname: 'ec_3568_25030031', type: 'online' },
                { id: 'T010', hostname: '主机名010', type: 'offline' }
            ];
            
            const randomTerminal = terminals[Math.floor(Math.random() * terminals.length)];
            addLogItem(randomTerminal.id, randomTerminal.hostname, randomTerminal.type);
        }

        // 添加日志项目
        function addLogItem(deviceId, hostname, type) {
            const logContainer = document.getElementById('logContainer');
            const emptyMessage = document.getElementById('emptyLogMessage');
            
            // 隐藏空消息
            if (emptyMessage) {
                emptyMessage.style.display = 'none';
            }
            
            const now = new Date();
            const timeString = now.getFullYear() + '-' + 
                             String(now.getMonth() + 1).padStart(2, '0') + '-' + 
                             String(now.getDate()).padStart(2, '0') + ' ' + 
                             String(now.getHours()).padStart(2, '0') + ':' + 
                             String(now.getMinutes()).padStart(2, '0') + ':' + 
                             String(now.getSeconds()).padStart(2, '0');
            
            const logItem = document.createElement('div');
            const logId = 'log_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
            logItem.id = logId;
            logItem.className = `log-item ${type}`;
            
            const statusText = type === 'online' ? '上线' : '离线';
            
            logItem.innerHTML = `
                <div class="log-content">
                    <strong>${timeString}</strong> - 终端 [${deviceId}-${hostname}] ${statusText}
                </div>
                <div class="log-actions">
                    <button class="btn btn-primary btn-sm" onclick="viewTerminalDetail('${deviceId}')">查看详情</button>
                </div>
            `;
            
            logContainer.insertBefore(logItem, logContainer.firstChild);
            
            // 设置10秒后开始淡出效果
            const fadeTimer = setTimeout(() => {
                logItem.classList.add('fade-out');
            }, 8000);
            
            // 设置10秒后删除
            const removeTimer = setTimeout(() => {
                if (logItem.parentNode) {
                    logItem.parentNode.removeChild(logItem);
                }
                logTimers.delete(logId);
                
                // 如果没有日志项目了，显示空消息
                if (logContainer.children.length === 0 || 
                    (logContainer.children.length === 1 && logContainer.children[0].id === 'emptyLogMessage')) {
                    if (emptyMessage) {
                        emptyMessage.style.display = 'block';
                    }
                }
            }, 10000);
            
            // 存储定时器引用
            logTimers.set(logId, { fadeTimer, removeTimer });
            
            // 保持最多10条日志
            const logItems = logContainer.querySelectorAll('.log-item');
            if (logItems.length > 10) {
                const oldestItem = logItems[logItems.length - 1];
                const oldestId = oldestItem.id;
                
                // 清除旧的定时器
                if (logTimers.has(oldestId)) {
                    const timers = logTimers.get(oldestId);
                    clearTimeout(timers.fadeTimer);
                    clearTimeout(timers.removeTimer);
                    logTimers.delete(oldestId);
                }
                
                oldestItem.remove();
            }
        }

        // 查看终端详情
        function viewTerminalDetail(deviceId) {
            // 在实际Vue应用中，这里会使用路由跳转并传递查询参数
            const url = `terminals.html?search=${encodeURIComponent(deviceId)}`;
            window.open(url, '_blank');
        }

        // 查看告警详情
        function viewAlertDetail(alertId) {
            // 在实际Vue应用中，这里会使用路由跳转并传递查询参数
            const url = `alerts.html?alertId=${encodeURIComponent(alertId)}`;
            window.open(url, '_blank');
        }

        // 查看终端实时指标
        function viewTerminalMetrics(deviceId) {
            // 在实际Vue应用中，这里会使用路由跳转
            const url = `real-time-metrics.html?deviceId=${encodeURIComponent(deviceId)}`;
            window.open(url, '_blank');
        }

        // 更新统计数据
        function updateStats() {
            // 模拟统计数据更新
            const totalChange = Math.floor(Math.random() * 5) - 2; // -2 到 2 的变化
            const onlineChange = Math.floor(Math.random() * 10) - 5; // -5 到 5 的变化
            const alertChange = Math.floor(Math.random() * 3) - 1; // -1 到 1 的变化
            
            const totalElement = document.getElementById('totalTerminals');
            const onlineElement = document.getElementById('onlineTerminals');
            const offlineElement = document.getElementById('offlineTerminals');
            const alertElement = document.getElementById('alertCount');
            
            let total = parseInt(totalElement.textContent.replace(',', '')) + totalChange;
            let online = parseInt(onlineElement.textContent.replace(',', '')) + onlineChange;
            let alerts = parseInt(alertElement.textContent) + alertChange;
            
            // 确保数据合理性
            total = Math.max(1000, total);
            online = Math.max(900, Math.min(total, online));
            alerts = Math.max(0, alerts);
            
            const offline = total - online;
            
            totalElement.textContent = total.toLocaleString();
            onlineElement.textContent = online.toLocaleString();
            offlineElement.textContent = offline.toLocaleString();
            alertElement.textContent = alerts.toString();
        }

        // 初始化页面
        function initPage() {
            // 每15秒模拟一次终端状态变化
            setInterval(simulateTerminalStatusChange, 15000);
            
            // 每30秒更新一次统计数据
            setInterval(updateStats, 30000);
            
            // 初始化时显示空消息
            const logContainer = document.getElementById('logContainer');
            const emptyMessage = document.getElementById('emptyLogMessage');
            if (logContainer.children.length === 1 && emptyMessage) {
                emptyMessage.style.display = 'block';
            }
        }

        // 页面加载时初始化
        window.onload = function() {
            initPage();
        };

        // 页面卸载时清理定时器
        window.onbeforeunload = function() {
            logTimers.forEach((timers) => {
                clearTimeout(timers.fadeTimer);
                clearTimeout(timers.removeTimer);
            });
            logTimers.clear();
        };
    </script>
</body>
</html> 