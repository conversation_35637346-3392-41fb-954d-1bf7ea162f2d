package com.unnet.changan5G.config;

import org.springframework.core.convert.converter.Converter;
import org.springframework.data.convert.ReadingConverter;
import org.springframework.data.convert.WritingConverter;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;

/**
 * Elasticsearch日期时间转换器
 * 
 * <AUTHOR>
 * @date 2024-07-15
 */
@Component
public class ElasticsearchDateTimeConverter {

    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    
    /**
     * 字符串转LocalDateTime转换器
     */
    @ReadingConverter
    public static class StringToLocalDateTimeConverter implements Converter<String, LocalDateTime> {
        @Override
        public LocalDateTime convert(String source) {
            if (source == null || source.isEmpty()) {
                return null;
            }
            
            try {
                // 尝试使用完整的日期时间格式
                return LocalDateTime.parse(source, DATE_TIME_FORMATTER);
            } catch (Exception e) {
                try {
                    // 尝试使用日期格式，并添加时间部分
                    return LocalDateTime.parse(source + " 00:00:00", DATE_TIME_FORMATTER);
                } catch (Exception ex) {
                    throw new IllegalArgumentException("无法将字符串 '" + source + "' 转换为LocalDateTime", ex);
                }
            }
        }
    }
    
    /**
     * LocalDateTime转字符串转换器
     */
    @WritingConverter
    public static class LocalDateTimeToStringConverter implements Converter<LocalDateTime, String> {
        @Override
        public String convert(LocalDateTime source) {
            return source == null ? null : source.format(DATE_TIME_FORMATTER);
        }
    }
    
    /**
     * Date转LocalDateTime转换器
     */
    @ReadingConverter
    public static class DateToLocalDateTimeConverter implements Converter<Date, LocalDateTime> {
        @Override
        public LocalDateTime convert(Date source) {
            return source == null ? null : 
                LocalDateTime.ofInstant(source.toInstant(), ZoneId.systemDefault());
        }
    }
    
    /**
     * LocalDateTime转Date转换器
     */
    @WritingConverter
    public static class LocalDateTimeToDateConverter implements Converter<LocalDateTime, Date> {
        @Override
        public Date convert(LocalDateTime source) {
            return source == null ? null : 
                Date.from(source.atZone(ZoneId.systemDefault()).toInstant());
        }
    }
    
    /**
     * Long转LocalDateTime转换器
     */
    @ReadingConverter
    public static class LongToLocalDateTimeConverter implements Converter<Long, LocalDateTime> {
        @Override
        public LocalDateTime convert(Long source) {
            return source == null ? null : 
                LocalDateTime.ofInstant(new Date(source).toInstant(), ZoneId.systemDefault());
        }
    }
}
