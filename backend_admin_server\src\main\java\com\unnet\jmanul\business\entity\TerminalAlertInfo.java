package com.unnet.jmanul.business.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 终端告警信息实体类
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("terminal_alert_info")
public class TerminalAlertInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 告警唯一ID（UUID）
     */
    @TableField("alert_id")
    private String alertId;

    /**
     * 告警终端设备ID
     */
    @TableField("identity_mac")
    private String identityMac;

    /**
     * 告警类型：CPU告警,内存告警,磁盘告警,数据磁盘告警,系统磁盘告警,授权过期告警等
     */
    @TableField("alert_type")
    private String alertType;

    /**
     * 告警级别：LOW,MEDIUM,HIGH,CRITICAL
     */
    @TableField("alert_level")
    private String alertLevel;

    /**
     * 告警详情
     */
    @TableField("alert_details")
    private String alertDetails;

    /**
     * 告警指标名称
     */
    @TableField("metric_name")
    private String metricName;

    /**
     * 告警阈值
     */
    @TableField("threshold")
    private String threshold;

    /**
     * 当前值
     */
    @TableField("current_value")
    private String currentValue;

    /**
     * 关联的指标记录ID（Elasticsearch文档ID）
     */
    @TableField("metric_id")
    private String metricId;

    /**
     * 告警时间
     */
    @TableField("alert_time")
    private LocalDateTime alertTime;

    /**
     * 告警状态：ACTIVE,RESOLVED,ACKNOWLEDGED
     */
    @TableField("alert_status")
    private String alertStatus;

    /**
     * 告警解决时间
     */
    @TableField("resolved_time")
    private LocalDateTime resolvedTime;

    /**
     * 告警确认时间
     */
    @TableField("acknowledged_time")
    private LocalDateTime acknowledgedTime;

    /**
     * 告警确认人
     */
    @TableField("acknowledged_by")
    private String acknowledgedBy;

    /**
     * 解决备注
     */
    @TableField("resolve_comment")
    private String resolveComment;

    /**
     * 是否已发送通知：0-未发送，1-已发送
     */
    @TableField("notification_sent")
    private Boolean notificationSent;

    /**
     * 通知发送时间
     */
    @TableField("notification_time")
    private LocalDateTime notificationTime;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    // 告警类型现在直接使用数据库中的中文描述，不再使用枚举

    /**
     * 告警状态枚举
     */
    public enum AlertStatus {
        ACTIVE("ACTIVE", "激活"),
        ACKNOWLEDGED("ACKNOWLEDGED", "已确认"),
        RESOLVED("RESOLVED", "已解决");

        private final String code;
        private final String description;

        AlertStatus(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }
    }
}
