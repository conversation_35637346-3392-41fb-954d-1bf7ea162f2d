package com.unnet.jmanul.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.unnet.jmanul.common.entity.BaseEntity;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-11
 */
@TableName("user")
@ApiModel(value = "User对象", description = "")
public class User extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("username")
    private String username;

    @TableField("name")
    private String name;

    @TableField("password")
    private String password;

    @TableField("account_source")
    private String accountSource;

    @TableField("enable")
    private Boolean enable;

    @TableField("locked")
    private Boolean locked;

    @TableField("account_expire_date")
    private LocalDateTime accountExpireDate;

    @TableField("credential_expire_date")
    private LocalDateTime credentialExpireDate;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getAccountSource() {
        return accountSource;
    }

    public void setAccountSource(String accountSource) {
        this.accountSource = accountSource;
    }

    public Boolean getEnable() {
        return enable;
    }

    public void setEnable(Boolean enable) {
        this.enable = enable;
    }

    public Boolean getLocked() {
        return locked;
    }

    public void setLocked(Boolean locked) {
        this.locked = locked;
    }

    public LocalDateTime getAccountExpireDate() {
        return accountExpireDate;
    }

    public void setAccountExpireDate(LocalDateTime accountExpireDate) {
        this.accountExpireDate = accountExpireDate;
    }

    public LocalDateTime getCredentialExpireDate() {
        return credentialExpireDate;
    }

    public void setCredentialExpireDate(LocalDateTime credentialExpireDate) {
        this.credentialExpireDate = credentialExpireDate;
    }

    @Override
    public String toString() {
        return "User{" +
        "id = " + id +
        ", username = " + username +
        ", name = " + name +
        ", password = " + password +
        ", accountSource = " + accountSource +
        ", enable = " + enable +
        ", locked = " + locked +
        ", accountExpireDate = " + accountExpireDate +
        ", credentialExpireDate = " + credentialExpireDate +
        "}";
    }
}
