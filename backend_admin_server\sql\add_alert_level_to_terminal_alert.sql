-- 为终端告警表添加告警级别字段
-- 执行时间：2025-07-25

START TRANSACTION;

-- 1. 添加告警级别字段
ALTER TABLE `terminal_alert_info` 
ADD COLUMN `alert_level` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '告警级别：LOW,MEDIUM,HIGH,CRITICAL' 
AFTER `alert_type`;

-- 2. 更新现有数据的告警级别（基于告警类型推断）
UPDATE `terminal_alert_info` SET `alert_level` = 'HIGH' 
WHERE `alert_type` IN ('CPU_TEMPERATURE', 'MEMORY_USAGE', 'CPU告警', '内存告警');

UPDATE `terminal_alert_info` SET `alert_level` = 'MEDIUM' 
WHERE `alert_type` IN ('DISK_USAGE', 'DISK_DATA_USAGE', 'DISK_SYSTEM_USAGE', '磁盘告警', '数据磁盘告警', '系统磁盘告警');

UPDATE `terminal_alert_info` SET `alert_level` = 'CRITICAL' 
WHERE `alert_type` IN ('LICENSE_EXPIRY', '授权过期告警');

-- 3. 将现有的英文告警类型更新为中文（与指标配置表保持一致）
UPDATE `terminal_alert_info` SET `alert_type` = 'CPU告警' WHERE `alert_type` = 'CPU_TEMPERATURE';
UPDATE `terminal_alert_info` SET `alert_type` = '内存告警' WHERE `alert_type` = 'MEMORY_USAGE';
UPDATE `terminal_alert_info` SET `alert_type` = '磁盘告警' WHERE `alert_type` = 'DISK_USAGE';
UPDATE `terminal_alert_info` SET `alert_type` = '数据磁盘告警' WHERE `alert_type` = 'DISK_DATA_USAGE';
UPDATE `terminal_alert_info` SET `alert_type` = '系统磁盘告警' WHERE `alert_type` = 'DISK_SYSTEM_USAGE';
UPDATE `terminal_alert_info` SET `alert_type` = '授权过期告警' WHERE `alert_type` = 'LICENSE_EXPIRY';

-- 4. 更新表注释，反映新的中文告警类型
ALTER TABLE `terminal_alert_info` 
MODIFY COLUMN `alert_type` varchar(30) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '告警类型：CPU告警,内存告警,磁盘告警,数据磁盘告警,系统磁盘告警,授权过期告警等';

-- 5. 添加告警级别索引以提高查询性能
CREATE INDEX `idx_alert_level` ON `terminal_alert_info` (`alert_level`);

-- 6. 添加复合索引：设备ID + 告警级别 + 告警状态
CREATE INDEX `idx_device_level_status` ON `terminal_alert_info` (`device_id`, `alert_level`, `alert_status`);

COMMIT;

-- 验证修改结果
SELECT 
    alert_type,
    alert_level,
    COUNT(*) as count
FROM terminal_alert_info 
GROUP BY alert_type, alert_level
ORDER BY alert_type;
