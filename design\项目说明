我现在想做一个用于终端运维的管理平台，方便运维人员对于终端的控制和状态信息查看，注意目前先给出对应的原型即可包含三个页面，页面包含概览、终端管理、站内告警页面功能，你给我对应的html样式的原型吧。
 主要功能：
   概览：
     终端统计信息(终端数量，在线数量，离线数量)、终端上下线的滚动提醒
   终端管理：
     终端列表信息，包含终端基本字段，添加过滤查询功能，添加按钮：查看对应终端历史指标信息、查看终端详情(一个主终端，多个cpe服务端，需要显示对应关系图)、编辑终端(包含本身的信息，以及支持自定义字段)、支持标签(用户设置键值，然后后续可以根据用户的标签信息进行过滤查询)
   对于自定义字段、以及标签功能，实现方案就是在终端表添加两个字段，一个字段专门用于存储自定义字段，一个字段专门用于存储标签信息，然后后续可以根据标签信息进行过滤查询，自定义字段相当于实际的数据(动态的添加)
   站内告警：
     告警列表信息，包含告警基本字段，添加过滤查询功能，添加按钮：查看告警详情(显示告警所有信息)，实时指标查看(点击查看后，跳转到一个新的页面，显示这条告警终端的实时指标，是否还是持续告警状态)，对应终端信息钉钉提醒(目前先只做简单提醒，提醒用户已发送提醒了哦)

以下是涉及到的mysql表

-- ========================================
--  终端监控相关表初始化脚本
-- 创建时间: 2024-07-14
-- 说明: 包含终端基本信息、指标信息、告警信息三张核心表
-- ========================================

-- 设置字符集
SET NAMES utf8mb4;

-- ========================================
-- 1. 终端基本信息表
-- ========================================
DROP TABLE IF EXISTS `terminal_basic_info`;
CREATE TABLE `terminal_basic_info` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `device_id` varchar(100) NOT NULL COMMENT '设备唯一标识符',
  `hostname` varchar(100) DEFAULT NULL COMMENT '主机名',
  `identity_mac` varchar(20) DEFAULT NULL COMMENT '对接长安车辆所在网口的MAC地址',
  `app_version` json DEFAULT NULL COMMENT '当前运行的组件版本号(JSON格式)',
  `expired_date` varchar(30) DEFAULT NULL COMMENT '软件授权过期时间',
  `receive_time` datetime DEFAULT NULL COMMENT '数据接收时间',
  `data_source` varchar(20) DEFAULT 'kafka' COMMENT '数据来源',
  `status` tinyint(1) DEFAULT 1 COMMENT '设备状态：0-离线，1-在线',
  `first_register_time` datetime DEFAULT NULL COMMENT '首次注册时间',
  `last_update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  `tags` json DEFAULT NULL COMMENT '终端标签信息(JSON格式)，用于标签过滤',
  `custom_fields` json DEFAULT NULL COMMENT '自定义字段信息(JSON格式)，用于动态扩展字段',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_deleted` tinyint(1) DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_device_id` (`device_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='终端基本信息表';

-- ========================================
-- 2. 终端指标信息表
-- ========================================
DROP TABLE IF EXISTS `terminal_metric_info`;
CREATE TABLE `terminal_metric_info` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `device_id` varchar(100) NOT NULL COMMENT '设备唯一标识符',
  `uptime` decimal(10,2) DEFAULT NULL COMMENT '系统运行时长（单位：秒）',
  `cpu_temp` decimal(5,2) DEFAULT NULL COMMENT 'CPU温度（单位：摄氏度）',
  `temperatures` json DEFAULT NULL COMMENT '温度信息(JSON格式)',
  `cpu_usage` json DEFAULT NULL COMMENT 'CPU使用率信息(JSON格式)',
  `cpu_percent` decimal(5,2) DEFAULT NULL COMMENT 'CPU总使用率（%）',
  `memory_percent` decimal(5,2) DEFAULT NULL COMMENT '内存使用率（%）',
  `memory_usage` json DEFAULT NULL COMMENT '内存详细信息(JSON格式)',
  `disk_usage` json DEFAULT NULL COMMENT '磁盘使用情况数组(JSON格式)',
  `disk_data_percent` decimal(5,2) DEFAULT NULL COMMENT '采集数据磁盘分区使用率',
  `disk_system_percent` decimal(5,2) DEFAULT NULL COMMENT '系统盘使用率',
  `cdata` json DEFAULT NULL COMMENT '长安程序落盘文件信息(JSON格式)',
  `zdata` json DEFAULT NULL COMMENT '压缩文件信息(JSON格式)',
  `group_usage` json DEFAULT NULL COMMENT 'CPE集合信息(JSON格式)',
  `group_bps` bigint(20) DEFAULT NULL COMMENT '所有CPE的带宽占用（单位：Byte/s）',
  `metric_time` datetime DEFAULT NULL COMMENT '指标采集时间',
  `receive_time` datetime DEFAULT NULL COMMENT '数据接收时间',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_device_id` (`device_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='终端指标信息表';

-- ========================================
-- 3. 终端告警信息表
-- ========================================
DROP TABLE IF EXISTS `terminal_alert_info`;
CREATE TABLE `terminal_alert_info` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `alert_id` varchar(50) NOT NULL COMMENT '告警唯一ID（UUID）',
  `device_id` varchar(100) NOT NULL COMMENT '告警终端设备ID',
  `alert_type` varchar(30) NOT NULL COMMENT '告警类型：CPU_TEMPERATURE,MEMORY_USAGE,DISK_USAGE,DISK_DATA_USAGE,DISK_SYSTEM_USAGE,LICENSE_EXPIRY',
  `alert_details` varchar(500) DEFAULT NULL COMMENT '告警详情',
  `metric_name` varchar(50) DEFAULT NULL COMMENT '告警指标名称',
  `threshold` varchar(20) DEFAULT NULL COMMENT '告警阈值',
  `current_value` varchar(20) DEFAULT NULL COMMENT '当前值',
  `alert_time` datetime DEFAULT NULL COMMENT '告警时间',
  `alert_status` varchar(20) DEFAULT 'ACTIVE' COMMENT '告警状态：ACTIVE,RESOLVED,ACKNOWLEDGED',
  `resolved_time` datetime DEFAULT NULL COMMENT '告警解决时间',
  `acknowledged_time` datetime DEFAULT NULL COMMENT '告警确认时间',
  `acknowledged_by` varchar(50) DEFAULT NULL COMMENT '告警确认人',
  `resolve_comment` varchar(500) DEFAULT NULL COMMENT '解决备注',
  `notification_sent` tinyint(1) DEFAULT 0 COMMENT '是否已发送通知：0-未发送，1-已发送',
  `notification_time` datetime DEFAULT NULL COMMENT '通知发送时间',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_alert_id` (`alert_id`),
  KEY `idx_device_id` (`device_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='终端告警信息表';


-- ========================================
-- 基于实际指标数据的插入示例
-- 设备ID: _0fd94938951f4a64bb11c6817a81f7e7
-- 主机名: ec_3568_25030031
-- 时间: 2024-07-14 18:30:45
-- ========================================

-- 1. 插入终端基本信息
INSERT INTO `terminal_basic_info` (
    `device_id`, `hostname`, `identity_mac`, `app_version`, `expired_date`, 
    `receive_time`, `data_source`, `status`, `first_register_time`, `last_update_time`,
    `tags`, `custom_fields`
) VALUES (
    '_0fd94938951f4a64bb11c6817a81f7e7',
    'ec_3568_25030031',
    'de0765523e60',
    '{"controller": "1.0.1", "detector": "1.0.48", "web": "1.0.55", "agent": "1.0.0", "front": "1.0.40"}',
    '2035-03-09 22:47:20',
    '2024-07-14 18:30:45',
    'kafka',
    1,
    '2024-07-14 18:30:45',
    '2024-07-14 18:30:45',
    '{"location": "未知", "department": "运维部", "environment": "生产", "priority": "高"}',
    '{"vehicle_model": "长安车型", "installation_date": "2024-07-14", "maintenance_contact": "技术支持", "notes": "生产环境设备"}'
) ON DUPLICATE KEY UPDATE
    `hostname` = VALUES(`hostname`),
    `identity_mac` = VALUES(`identity_mac`),
    `app_version` = VALUES(`app_version`),
    `expired_date` = VALUES(`expired_date`),
    `receive_time` = VALUES(`receive_time`),
    `status` = VALUES(`status`),
    `last_update_time` = VALUES(`last_update_time`);

-- 2. 插入终端指标信息
INSERT INTO `terminal_metric_info` (
    `device_id`, `uptime`, `cpu_temp`, `temperatures`, `cpu_usage`, `cpu_percent`,
    `memory_percent`, `memory_usage`, `disk_usage`, `disk_data_percent`, 
    `disk_system_percent`, `cdata`, `zdata`, `group_usage`, `group_bps`,
    `metric_time`, `receive_time`
) VALUES (
    '_0fd94938951f4a64bb11c6817a81f7e7',
    5916.29,
    85.888,
    '{"soc-thermal": 58.888, "gpu-thermal": 57.777}',
    '{"num": "4", "core": "4", "thread": "4", "user": "46.3", "sys": "9.7", "idle": "43.7"}',
    43.2,
    50.9,
    '{"total": 4085002240, "available": 2006622208, "percent": 50.9, "used": 2024525824, "free": 1373265920, "active": 229298176, "inactive": 2192863232, "buffers": 56995840, "cached": 630214656, "shared": 1032192, "slab": 136617984}',
    '[{"device": "/dev/mmcblk0p3", "mountpoint": "/", "fstype": "ext4", "total": 30602608640, "used": 15916904448, "free": 13409980416, "percent": 54.3}]',
    0,
    54.3,
    '{"count": 1, "size": 3627078580}',
    '{"count": 0, "size": 0}',
    '{}',
    0,
    '2024-07-14 18:30:45',
    '2024-07-14 18:30:45'
);

-- 3. 插入告警信息（CPU温度告警）
INSERT INTO `terminal_alert_info` (
    `alert_id`, `device_id`, `alert_type`, `alert_details`, `metric_name`,
    `threshold`, `current_value`, `alert_time`, `alert_status`, `notification_sent`
) VALUES (
    CONCAT('alert-cpu-temp-', DATE_FORMAT(NOW(), '%Y%m%d%H%i%s'), '-', LPAD(FLOOR(RAND() * 1000), 3, '0')),
    '_0fd94938951f4a64bb11c6817a81f7e7',
    'CPU_TEMPERATURE',
    'CPU温度过高',
    'cpu_temp',
    '85°C',
    '85.888°C',
    '2024-07-14 18:30:45',
    'ACTIVE',
    0
);

-- 验证插入结果
SELECT '数据插入完成' AS message;

-- 查询验证
SELECT 
    tbi.device_id,
    tbi.hostname,
    tbi.status,
    tmi.cpu_temp,
    tmi.cpu_percent,
    tmi.memory_percent,
    tai.alert_type,
    tai.alert_details,
    tai.current_value
FROM terminal_basic_info tbi
LEFT JOIN terminal_metric_info tmi ON tbi.device_id = tmi.device_id
LEFT JOIN terminal_alert_info tai ON tbi.device_id = tai.device_id
WHERE tbi.device_id = '_0fd94938951f4a64bb11c6817a81f7e7'
ORDER BY tmi.metric_time DESC, tai.alert_time DESC
LIMIT 5;