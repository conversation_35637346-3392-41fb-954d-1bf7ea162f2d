package com.unnet.jmanul.utils;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;

/**
 * 密码加密测试工具类
 * 用于生成BCrypt加密后的密码，便于直接在数据库中更新管理员密码
 * 
 * <AUTHOR>
 * @date 2025-07-26
 */
@SpringBootTest
public class PasswordEncryptionTest {

    private final PasswordEncoder passwordEncoder = new BCryptPasswordEncoder();

    /**
     * 测试密码加密 - 可以修改这里的密码来生成对应的加密值
     */
    @Test
    public void testPasswordEncryption() {
        // 在这里修改你想要的实际密码
        String[] passwords = {
            "cadxylt@2025!@#",
        };

        System.out.println("=".repeat(80));
        System.out.println("密码加密测试结果");
        System.out.println("=".repeat(80));

        for (String password : passwords) {
            String encryptedPassword = passwordEncoder.encode(password);
            System.out.printf("原始密码: %-15s -> 加密后: %s%n", password, encryptedPassword);
            
            // 验证加密是否正确
            boolean matches = passwordEncoder.matches(password, encryptedPassword);
            System.out.printf("验证结果: %s%n", matches ? "✓ 正确" : "✗ 错误");
            System.out.println("-".repeat(80));
        }

        System.out.println("使用说明:");
        System.out.println("1. 复制上面生成的加密密码");
        System.out.println("2. 在数据库中执行以下SQL更新管理员密码:");
        System.out.println("   UPDATE user SET password = '加密后的密码' WHERE username = '管理员用户名';");
        System.out.println("3. 重启应用后即可使用新密码登录");
        System.out.println("=".repeat(80));
    }

    /**
     * 生成指定密码的加密值
     * 
     * @param plainPassword 明文密码
     * @return 加密后的密码
     */
    public String encryptPassword(String plainPassword) {
        return passwordEncoder.encode(plainPassword);
    }

    /**
     * 验证密码是否匹配
     * 
     * @param plainPassword 明文密码
     * @param encryptedPassword 加密后的密码
     * @return 是否匹配
     */
    public boolean verifyPassword(String plainPassword, String encryptedPassword) {
        return passwordEncoder.matches(plainPassword, encryptedPassword);
    }

    /**
     * 批量生成密码测试
     */
    @Test
    public void batchPasswordGeneration() {
        System.out.println("=".repeat(80));
        System.out.println("批量密码生成测试");
        System.out.println("=".repeat(80));

        // 常用的管理员密码示例
        String[] commonPasswords = {
            "admin",
            "admin123",
            "administrator",
            "password",
            "123456",
            "root",
            "test",
            "changan5g",
            "changan123"
        };

        for (String password : commonPasswords) {
            String encrypted = passwordEncoder.encode(password);
            System.out.printf("%-15s -> %s%n", password, encrypted);
        }

        System.out.println("=".repeat(80));
    }

    /**
     * 自定义密码加密测试 - 在这里输入你想要的密码
     */
    @Test
    public void customPasswordEncryption() {
        // 🔥 在这里修改为你想要设置的实际密码
        String customPassword = "admin123";
        
        System.out.println("=".repeat(80));
        System.out.println("自定义密码加密");
        System.out.println("=".repeat(80));
        
        String encryptedPassword = passwordEncoder.encode(customPassword);
        
        System.out.println("原始密码: " + customPassword);
        System.out.println("加密密码: " + encryptedPassword);
        
        // 验证
        boolean isValid = passwordEncoder.matches(customPassword, encryptedPassword);
        System.out.println("验证结果: " + (isValid ? "✓ 正确" : "✗ 错误"));
        
        System.out.println();
        System.out.println("数据库更新SQL:");
        System.out.println("UPDATE user SET password = '" + encryptedPassword + "' WHERE username = 'admin';");
        System.out.println("-- 请将 'admin' 替换为实际的管理员用户名");
        
        System.out.println("=".repeat(80));
    }

    /**
     * 验证现有密码测试
     */
    @Test
    public void verifyExistingPassword() {
        System.out.println("=".repeat(80));
        System.out.println("验证现有密码");
        System.out.println("=".repeat(80));

        // 从README.md中看到的示例
        String knownPassword = "test";
        String knownHash = "$2a$10$ejcbB/aQQpSIUlytviOHiuDLONPFsJo87.DySFVcLT5.pkh4jqASu";
        
        boolean matches = passwordEncoder.matches(knownPassword, knownHash);
        System.out.println("已知密码验证:");
        System.out.println("密码: " + knownPassword);
        System.out.println("哈希: " + knownHash);
        System.out.println("匹配: " + (matches ? "✓ 正确" : "✗ 错误"));
        
        System.out.println("=".repeat(80));
    }

    /**
     * 生成多个版本的相同密码（BCrypt每次生成的哈希都不同）
     */
    @Test
    public void generateMultipleVersions() {
        String password = "admin123";
        
        System.out.println("=".repeat(80));
        System.out.println("生成同一密码的多个版本（BCrypt特性：每次生成的哈希都不同）");
        System.out.println("密码: " + password);
        System.out.println("=".repeat(80));
        
        for (int i = 1; i <= 5; i++) {
            String encrypted = passwordEncoder.encode(password);
            boolean matches = passwordEncoder.matches(password, encrypted);
            System.out.printf("版本%d: %s (验证: %s)%n", i, encrypted, matches ? "✓" : "✗");
        }
        
        System.out.println("=".repeat(80));
        System.out.println("说明: 以上任意一个哈希值都可以用于数据库更新");
    }
}
