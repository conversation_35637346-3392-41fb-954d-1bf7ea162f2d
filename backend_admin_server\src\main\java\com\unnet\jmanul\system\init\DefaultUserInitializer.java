package com.unnet.jmanul.system.init;

import com.unnet.jmanul.common.utils.DateTimeUtils;
import com.unnet.jmanul.system.constants.AccountSource;
import com.unnet.jmanul.system.entity.User;
import com.unnet.jmanul.system.service.IUserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomUtils;
import org.casbin.jcasbin.main.Enforcer;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.core.annotation.Order;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.UUID;

@Component
@Slf4j
@Order(20)
public class DefaultUserInitializer implements ApplicationRunner {

    @Resource
    private IUserService userService;
    @Resource
    private PasswordEncoder passwordEncoder;
    @Resource
    private Enforcer enforcer;

    private static final String DEFAULT_USER_PREFIX = "msysu";

    @Override
    public void run(ApplicationArguments args) throws Exception {

        String username = String.format("%s%d", DEFAULT_USER_PREFIX, RandomUtils.nextInt(100, 1000) % 100);
        String password = UUID.randomUUID().toString().split("-")[0];


        List<User> users = userService.getUserWithUsernamePrefix(DEFAULT_USER_PREFIX);
        if (users.isEmpty()) {
            log.info("DefaultUserInitializer create system user if not exists [{}, {}]", username, password);
            User item = new User();
            item.setUsername(username);
            item.setName(username);
            item.setPassword(passwordEncoder.encode(password));

            item.setAccountSource(AccountSource.INTERNAL);
            item.setEnable(true);
            item.setAccountExpireDate(DateTimeUtils.getMaxLocalDate());
            item.setLocked(false);
            item.setCredentialExpireDate(DateTimeUtils.getMaxLocalDate());

            boolean ok = userService.save(item);
            if (ok) {
                enforcer.addRoleForUser(username, "admin");
                log.info("DefaultUserInitializer create system user success [{}, {}]", username, password);
            } else {
                log.error("DefaultUserInitializer create system user failed [{}, {}]", username, password);
            }
        } else {
            log.info("DefaultUserInitializer system user already exists [{}]", users.get(0).getUsername());
        }

    }

}
