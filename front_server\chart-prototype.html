<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>实时指标监控图表原型</title>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f7fa;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .page-title {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 24px;
            font-weight: 600;
        }

        .trends-container {
            background: #ffffff;
            border: 1px solid #e8eaed;
            border-radius: 12px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
        }

        .trends-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px 20px;
            border-bottom: 1px solid #f1f3f4;
            background: #fafbfc;
            border-radius: 12px 12px 0 0;
        }

        .trends-title {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 16px;
            font-weight: 600;
            color: #1a73e8;
        }

        .trends-controls {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .time-range-selector {
            padding: 6px 12px;
            border: 1px solid #dadce0;
            border-radius: 6px;
            background: #ffffff;
            color: #5f6368;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .time-range-selector:hover {
            border-color: #1a73e8;
            color: #1a73e8;
        }

        .time-range-selector.active {
            background: #1a73e8;
            border-color: #1a73e8;
            color: #ffffff;
        }

        .refresh-btn {
            display: flex;
            align-items: center;
            gap: 5px;
            padding: 6px 12px;
            border: 1px solid #34a853;
            border-radius: 6px;
            background: #34a853;
            color: #ffffff;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .refresh-btn:hover {
            background: #2d8f47;
            border-color: #2d8f47;
        }

        .trends-body {
            padding: 20px;
        }

        .chart-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            flex-wrap: wrap;
            gap: 15px;
        }

        .chart-title {
            font-size: 14px;
            font-weight: 600;
            color: #202124;
        }

        .live-indicator {
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 12px;
            color: #34a853;
        }

        .live-dot {
            width: 8px;
            height: 8px;
            background: #34a853;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .chart-legend {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            align-items: center;
        }

        .legend-item {
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 12px;
            color: #5f6368;
        }

        .legend-color {
            width: 12px;
            height: 3px;
            border-radius: 2px;
        }

        .legend-upload { background: #1890ff; }
        .legend-cpu-temp { background: #ff4d4f; }
        .legend-cpu-usage { background: #52c41a; }
        .legend-memory { background: #faad14; }
        .legend-disk { background: #722ed1; }

        .chart-canvas {
            width: 100%;
            height: 400px;
            position: relative;
        }

        .current-values {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .value-card {
            background: #ffffff;
            border: 1px solid #e8eaed;
            border-radius: 8px;
            padding: 16px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: all 0.2s ease;
        }

        .value-card:hover {
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            border-color: #1a73e8;
        }

        .value-info {
            flex: 1;
        }

        .value-title {
            font-size: 12px;
            color: #5f6368;
            margin-bottom: 4px;
        }

        .value-number {
            font-size: 18px;
            font-weight: 600;
            color: #202124;
        }

        .value-upload-num { color: #1890ff; }
        .value-cpu-temp-num { color: #ff4d4f; }
        .value-cpu-usage-num { color: #52c41a; }
        .value-memory-num { color: #faad14; }
        .value-disk-num { color: #722ed1; }

        .value-change {
            font-size: 12px;
            font-weight: 600;
            padding: 4px 8px;
            border-radius: 4px;
            min-width: 50px;
            text-align: center;
        }

        .value-change.increase {
            background: #f6ffed;
            color: #52c41a;
        }

        .value-change.decrease {
            background: #fff2f0;
            color: #ff4d4f;
        }

        .value-change.no-change {
            background: #f5f5f5;
            color: #8c8c8c;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .trends-header {
                flex-direction: column;
                gap: 15px;
                align-items: stretch;
            }

            .trends-controls {
                justify-content: center;
                flex-wrap: wrap;
            }

            .chart-header {
                flex-direction: column;
                align-items: flex-start;
            }

            .chart-legend {
                justify-content: center;
            }

            .chart-canvas {
                height: 300px;
            }

            .current-values {
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
                gap: 10px;
            }

            .value-card {
                padding: 12px;
            }

            .value-number {
                font-size: 16px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="page-title">实时指标监控图表原型</h1>
        
        <div class="trends-container">
            <div class="trends-header">
                <div class="trends-title">
                    <span>📈</span>
                    <span>实时指标监控</span>
                </div>
                <div class="trends-controls">
                    <button class="time-range-selector" onclick="switchTimeRange('最近10分钟')">最近10分钟</button>
                    <button class="time-range-selector active" onclick="switchTimeRange('最近1小时')">最近1小时</button>
                    <button class="time-range-selector" onclick="switchTimeRange('最近6小时')">最近6小时</button>
                    <button class="time-range-selector" onclick="switchTimeRange('今日')">今日</button>
                    <button class="refresh-btn" onclick="refreshData()">
                        <span>🔄</span>
                        <span>刷新</span>
                    </button>
                </div>
            </div>

            <div class="trends-body">
                <div class="chart-header">
                    <div class="chart-title">综合指标趋势 (每10秒更新)</div>
                    <div class="live-indicator">
                        <div class="live-dot"></div>
                        <span>实时监控</span>
                    </div>
                    <div class="chart-legend">
                        <div class="legend-item">
                            <div class="legend-color legend-upload"></div>
                            <span>上行速率 (MB/s)</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color legend-cpu-temp"></div>
                            <span>CPU温度 (°C)</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color legend-cpu-usage"></div>
                            <span>CPU使用率 (%)</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color legend-memory"></div>
                            <span>内存使用率 (%)</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color legend-disk"></div>
                            <span>磁盘使用率 (%)</span>
                        </div>
                    </div>
                </div>

                <div id="chart" class="chart-canvas"></div>

                <!-- 当前值显示 -->
                <div class="current-values">
                    <div class="value-card">
                        <div class="value-info">
                            <div class="value-title">上行实时速率</div>
                            <div class="value-number value-upload-num" id="current-upload">12.5 MB/s</div>
                        </div>
                        <div class="value-change increase" id="change-upload">+2.1</div>
                    </div>
                    <div class="value-card">
                        <div class="value-info">
                            <div class="value-title">CPU温度</div>
                            <div class="value-number value-cpu-temp-num" id="current-cpu-temp">68.5°C</div>
                        </div>
                        <div class="value-change decrease" id="change-cpu-temp">-1.2</div>
                    </div>
                    <div class="value-card">
                        <div class="value-info">
                            <div class="value-title">CPU使用率</div>
                            <div class="value-number value-cpu-usage-num" id="current-cpu-usage">45.2%</div>
                        </div>
                        <div class="value-change increase" id="change-cpu-usage">+3.5</div>
                    </div>
                    <div class="value-card">
                        <div class="value-info">
                            <div class="value-title">内存使用率</div>
                            <div class="value-number value-memory-num" id="current-memory">72.8%</div>
                        </div>
                        <div class="value-change no-change" id="change-memory">0</div>
                    </div>
                    <div class="value-card">
                        <div class="value-info">
                            <div class="value-title">磁盘使用率</div>
                            <div class="value-number value-disk-num" id="current-disk">58.3%</div>
                        </div>
                        <div class="value-change decrease" id="change-disk">-0.5</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let chart;
        let currentTimeRange = '最近1小时';
        let dataUpdateTimer;

        // 模拟数据生成
        function generateTimePoints(range) {
            const now = new Date();
            const points = [];
            let interval, count;

            switch (range) {
                case '最近10分钟':
                    interval = 10 * 1000; // 10秒
                    count = 60; // 60个点
                    break;
                case '最近1小时':
                    interval = 60 * 1000; // 1分钟
                    count = 60; // 60个点
                    break;
                case '最近6小时':
                    interval = 6 * 60 * 1000; // 6分钟
                    count = 60; // 60个点
                    break;
                case '今日':
                    interval = 24 * 60 * 1000; // 24分钟
                    count = 60; // 60个点
                    break;
                default:
                    interval = 60 * 1000;
                    count = 60;
            }

            for (let i = count - 1; i >= 0; i--) {
                const time = new Date(now.getTime() - i * interval);
                points.push(time);
            }

            return points;
        }

        function generateMetricData(baseValue, variance, count) {
            const data = [];
            let current = baseValue;

            for (let i = 0; i < count; i++) {
                // 添加一些随机波动
                const change = (Math.random() - 0.5) * variance;
                current = Math.max(0, current + change);
                
                // 添加一些趋势性变化
                if (i > count * 0.7) {
                    current += Math.sin(i * 0.1) * variance * 0.3;
                }
                
                data.push(parseFloat(current.toFixed(2)));
            }

            return data;
        }

        function initChart() {
            const chartDom = document.getElementById('chart');
            chart = echarts.init(chartDom);

            updateChartData();

            // 窗口大小改变时重新调整图表
            window.addEventListener('resize', () => {
                if (chart) {
                    chart.resize();
                }
            });
        }

        function updateChartData() {
            const timePoints = generateTimePoints(currentTimeRange);
            const count = timePoints.length;

            // 生成模拟数据
            const uploadData = generateMetricData(10, 5, count);
            const cpuTempData = generateMetricData(65, 8, count);
            const cpuUsageData = generateMetricData(40, 15, count);
            const memoryData = generateMetricData(70, 10, count);
            const diskData = generateMetricData(55, 5, count);

            const option = {
                title: {
                    text: '实时指标监控',
                    left: 'center',
                    textStyle: {
                        color: '#333',
                        fontSize: 16,
                        fontWeight: 'bold'
                    }
                },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'cross',
                        label: {
                            backgroundColor: '#6a7985'
                        }
                    },
                    formatter: function(params) {
                        let result = params[0].axisValue + '<br/>';
                        params.forEach(param => {
                            const unit = getMetricUnit(param.seriesName);
                            result += `${param.marker}${param.seriesName}: ${param.value}${unit}<br/>`;
                        });
                        return result;
                    }
                },
                legend: {
                    data: ['上行速率', 'CPU温度', 'CPU使用率', '内存使用率', '磁盘使用率'],
                    top: 30,
                    textStyle: {
                        fontSize: 12
                    }
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    top: '15%',
                    containLabel: true
                },
                toolbox: {
                    feature: {
                        saveAsImage: {
                            title: '保存为图片'
                        },
                        dataZoom: {
                            title: {
                                zoom: '区域缩放',
                                back: '区域缩放还原'
                            }
                        }
                    }
                },
                xAxis: {
                    type: 'category',
                    boundaryGap: false,
                    data: timePoints.map(time => time.toLocaleTimeString('zh-CN', { 
                        hour: '2-digit', 
                        minute: '2-digit',
                        second: '2-digit'
                    })),
                    axisLabel: {
                        rotate: 45,
                        fontSize: 10
                    }
                },
                yAxis: [
                    {
                        type: 'value',
                        name: '速率 (MB/s)',
                        position: 'left',
                        axisLabel: {
                            formatter: '{value} MB/s',
                            fontSize: 10
                        },
                        nameTextStyle: {
                            fontSize: 10
                        }
                    },
                    {
                        type: 'value',
                        name: '温度 (°C)',
                        position: 'right',
                        axisLabel: {
                            formatter: '{value}°C',
                            fontSize: 10
                        },
                        nameTextStyle: {
                            fontSize: 10
                        }
                    },
                    {
                        type: 'value',
                        name: '使用率 (%)',
                        position: 'right',
                        offset: 60,
                        axisLabel: {
                            formatter: '{value}%',
                            fontSize: 10
                        },
                        nameTextStyle: {
                            fontSize: 10
                        }
                    }
                ],
                series: [
                    {
                        name: '上行速率',
                        type: 'line',
                        yAxisIndex: 0,
                        data: uploadData,
                        smooth: true,
                        symbol: 'circle',
                        symbolSize: 4,
                        lineStyle: {
                            color: '#1890ff',
                            width: 2
                        },
                        itemStyle: {
                            color: '#1890ff'
                        },
                        areaStyle: {
                            color: {
                                type: 'linear',
                                x: 0, y: 0, x2: 0, y2: 1,
                                colorStops: [
                                    { offset: 0, color: 'rgba(24, 144, 255, 0.3)' },
                                    { offset: 1, color: 'rgba(24, 144, 255, 0.1)' }
                                ]
                            }
                        }
                    },
                    {
                        name: 'CPU温度',
                        type: 'line',
                        yAxisIndex: 1,
                        data: cpuTempData,
                        smooth: true,
                        symbol: 'circle',
                        symbolSize: 4,
                        lineStyle: {
                            color: '#ff4d4f',
                            width: 2
                        },
                        itemStyle: {
                            color: '#ff4d4f'
                        }
                    },
                    {
                        name: 'CPU使用率',
                        type: 'line',
                        yAxisIndex: 2,
                        data: cpuUsageData,
                        smooth: true,
                        symbol: 'circle',
                        symbolSize: 4,
                        lineStyle: {
                            color: '#52c41a',
                            width: 2
                        },
                        itemStyle: {
                            color: '#52c41a'
                        }
                    },
                    {
                        name: '内存使用率',
                        type: 'line',
                        yAxisIndex: 2,
                        data: memoryData,
                        smooth: true,
                        symbol: 'circle',
                        symbolSize: 4,
                        lineStyle: {
                            color: '#faad14',
                            width: 2
                        },
                        itemStyle: {
                            color: '#faad14'
                        }
                    },
                    {
                        name: '磁盘使用率',
                        type: 'line',
                        yAxisIndex: 2,
                        data: diskData,
                        smooth: true,
                        symbol: 'circle',
                        symbolSize: 4,
                        lineStyle: {
                            color: '#722ed1',
                            width: 2
                        },
                        itemStyle: {
                            color: '#722ed1'
                        }
                    }
                ],
                dataZoom: [
                    {
                        type: 'inside',
                        start: 70,
                        end: 100
                    },
                    {
                        start: 70,
                        end: 100,
                        handleIcon: 'M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4v1.3h1.3v-1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7V23.1h6.6V24.4z M13.3,19.6H6.7v-1.4h6.6V19.6z',
                        handleSize: '80%',
                        handleStyle: {
                            color: '#fff',
                            shadowBlur: 3,
                            shadowColor: 'rgba(0, 0, 0, 0.6)',
                            shadowOffsetX: 2,
                            shadowOffsetY: 2
                        }
                    }
                ]
            };

            chart.setOption(option);

            // 更新当前值显示
            updateCurrentValues(uploadData, cpuTempData, cpuUsageData, memoryData, diskData);
        }

        function updateCurrentValues(uploadData, cpuTempData, cpuUsageData, memoryData, diskData) {
            const current = {
                upload: uploadData[uploadData.length - 1],
                cpuTemp: cpuTempData[cpuTempData.length - 1],
                cpuUsage: cpuUsageData[cpuUsageData.length - 1],
                memory: memoryData[memoryData.length - 1],
                disk: diskData[diskData.length - 1]
            };

            const previous = {
                upload: uploadData[uploadData.length - 2] || current.upload,
                cpuTemp: cpuTempData[cpuTempData.length - 2] || current.cpuTemp,
                cpuUsage: cpuUsageData[cpuUsageData.length - 2] || current.cpuUsage,
                memory: memoryData[memoryData.length - 2] || current.memory,
                disk: diskData[diskData.length - 2] || current.disk
            };

            // 更新显示值
            document.getElementById('current-upload').textContent = current.upload + ' MB/s';
            document.getElementById('current-cpu-temp').textContent = current.cpuTemp + '°C';
            document.getElementById('current-cpu-usage').textContent = current.cpuUsage + '%';
            document.getElementById('current-memory').textContent = current.memory + '%';
            document.getElementById('current-disk').textContent = current.disk + '%';

            // 更新变化值
            updateChangeValue('change-upload', current.upload - previous.upload);
            updateChangeValue('change-cpu-temp', current.cpuTemp - previous.cpuTemp);
            updateChangeValue('change-cpu-usage', current.cpuUsage - previous.cpuUsage);
            updateChangeValue('change-memory', current.memory - previous.memory);
            updateChangeValue('change-disk', current.disk - previous.disk);
        }

        function updateChangeValue(elementId, change) {
            const element = document.getElementById(elementId);
            const absChange = Math.abs(change);
            
            if (absChange < 0.1) {
                element.textContent = '0';
                element.className = 'value-change no-change';
            } else {
                const sign = change > 0 ? '+' : '';
                element.textContent = sign + change.toFixed(1);
                element.className = change > 0 ? 'value-change increase' : 'value-change decrease';
            }
        }

        function getMetricUnit(seriesName) {
            switch (seriesName) {
                case '上行速率': return ' MB/s';
                case 'CPU温度': return '°C';
                case 'CPU使用率':
                case '内存使用率':
                case '磁盘使用率': return '%';
                default: return '';
            }
        }

        function switchTimeRange(range) {
            currentTimeRange = range;
            
            // 更新按钮状态
            document.querySelectorAll('.time-range-selector').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
            
            // 更新图表
            updateChartData();
        }

        function refreshData() {
            updateChartData();
        }

        function startAutoUpdate() {
            // 每10秒自动更新数据
            dataUpdateTimer = setInterval(() => {
                updateChartData();
            }, 10000);
        }

        // 页面加载完成后初始化
        window.addEventListener('load', () => {
            initChart();
            startAutoUpdate();
        });

        // 页面卸载时清理定时器
        window.addEventListener('beforeunload', () => {
            if (dataUpdateTimer) {
                clearInterval(dataUpdateTimer);
            }
        });
    </script>
</body>
</html>