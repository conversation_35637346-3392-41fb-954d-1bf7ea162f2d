package com.unnet.changan5G.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 终端指标信息实体类
 * 
 * <AUTHOR>
 * @date 2024-07-14
 */
@Data
@TableName(value = "terminal_metric_info", autoResultMap = true)
public class TerminalMetricInfoEntity {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("device_id")
    private String deviceId;

    @TableField("uptime")
    private BigDecimal uptime;

    @TableField("cpu_temp")
    private BigDecimal cpuTemp;

    @TableField(value = "temperatures", typeHandler = JacksonTypeHandler.class)
    private Object temperatures;

    @TableField(value = "cpu_usage", typeHandler = JacksonTypeHandler.class)
    private Object cpuUsage;

    @TableField("cpu_percent")
    private BigDecimal cpuPercent;

    @TableField("memory_percent")
    private BigDecimal memoryPercent;

    @TableField(value = "memory_usage", typeHandler = JacksonTypeHandler.class)
    private Object memoryUsage;

    @TableField(value = "disk_usage", typeHandler = JacksonTypeHandler.class)
    private Object diskUsage;

    @TableField("disk_data_percent")
    private BigDecimal diskDataPercent;

    @TableField("disk_system_percent")
    private BigDecimal diskSystemPercent;

    @TableField(value = "cdata", typeHandler = JacksonTypeHandler.class)
    private Object cdata;

    @TableField(value = "zdata", typeHandler = JacksonTypeHandler.class)
    private Object zdata;

    @TableField(value = "group_usage", typeHandler = JacksonTypeHandler.class)
    private Object groupUsage;

    @TableField("group_bps")
    private Long groupBps;

    @TableField("metric_time")
    private LocalDateTime metricTime;

    @TableField("receive_time")
    private LocalDateTime receiveTime;

    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
}
