package com.unnet.jmanul.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.unnet.jmanul.business.entity.TerminalBasicInfo;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 终端基本信息服务接口
 *
 * <AUTHOR>
 * @date 2024-07-16
 */
public interface ITerminalBasicInfoService extends IService<TerminalBasicInfo> {

    /**
     * 根据设备ID查询终端基本信息
     */
    TerminalBasicInfo getByDeviceId(String deviceId);

    /**
     * 根据MAC地址查询终端基本信息
     */
    TerminalBasicInfo getByIdentityMac(String identityMac);

    /**
     * 保存或更新终端基本信息
     */
    boolean saveOrUpdateTerminalInfo(TerminalBasicInfo terminalBasicInfo);

    /**
     * 更新设备状态
     */
    boolean updateDeviceStatus(String deviceId, Integer status);

    /**
     * 批量更新设备状态
     */
    boolean batchUpdateDeviceStatus(List<String> deviceIds, Integer status);

    /**
     * 检查设备是否存在
     */
    boolean existsByDeviceId(String deviceId);

    /**
     * 获取在线设备数量
     */
    long getOnlineDeviceCount();

    /**
     * 获取离线设备数量
     */
    long getOfflineDeviceCount();

    /**
     * 获取所有设备的最后更新时间
     */
    List<TerminalBasicInfo> getDeviceLastUpdateTimes();

    /**
     * 根据标签查询设备
     */
    List<TerminalBasicInfo> getDevicesByTag(String tagKey, String tagValue);

    /**
     * 查询即将过期的设备
     */
    List<TerminalBasicInfo> getExpiringDevices(int days);

    /**
     * 注册新设备
     */
    boolean registerNewDevice(TerminalBasicInfo terminalBasicInfo);

    /**
     * 更新设备最后上报时间
     */
    boolean updateLastReportTime(String deviceId, LocalDateTime reportTime);

    /**
     * 统计在线设备数量
     */
    int countOnlineDevices();

    /**
     * 统计离线设备数量
     */
    int countOfflineDevices();
}
