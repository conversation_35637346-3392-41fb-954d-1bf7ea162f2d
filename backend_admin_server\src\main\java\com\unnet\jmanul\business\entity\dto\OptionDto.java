package com.unnet.jmanul.business.entity.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 选项DTO
 * 
 * <AUTHOR>
 * @since 2024-07-21
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "选项DTO")
public class OptionDto {

    @Schema(description = "选项代码")
    private String code;

    @Schema(description = "选项描述")
    private String description;
}
