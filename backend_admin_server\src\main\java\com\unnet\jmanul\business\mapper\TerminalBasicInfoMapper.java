package com.unnet.jmanul.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.unnet.jmanul.business.entity.TerminalBasicInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 终端基本信息Mapper
 * 
 * <AUTHOR>
 * @date 2024-07-16
 */
@Mapper
public interface TerminalBasicInfoMapper extends BaseMapper<TerminalBasicInfo> {

    /**
     * 根据设备ID查询终端基本信息
     */
    @Select("SELECT * FROM terminal_basic_info WHERE device_id = #{deviceId}")
    TerminalBasicInfo selectByDeviceId(@Param("deviceId") String deviceId);

    /**
     * 根据MAC地址查询终端基本信息
     */
    @Select("SELECT * FROM terminal_basic_info WHERE identity_mac = #{identityMac}")
    TerminalBasicInfo selectByIdentityMac(@Param("identityMac") String identityMac);

    /**
     * 更新设备状态
     */
    @Update("UPDATE terminal_basic_info SET status = #{status}, last_update_time = #{updateTime}, updated_at = NOW() " +
            "WHERE device_id = #{deviceId}")
    int updateStatusByDeviceId(@Param("deviceId") String deviceId, 
                              @Param("status") Integer status, 
                              @Param("updateTime") LocalDateTime updateTime);

    /**
     * 批量更新设备状态
     */
    @Update("<script>" +
            "UPDATE terminal_basic_info SET status = #{status}, last_update_time = #{updateTime}, updated_at = NOW() " +
            "WHERE device_id IN " +
            "<foreach collection='deviceIds' item='deviceId' open='(' separator=',' close=')'>" +
            "#{deviceId}" +
            "</foreach>" +
            "</script>")
    int batchUpdateStatusByDeviceIds(@Param("deviceIds") List<String> deviceIds,
                                    @Param("status") Integer status,
                                    @Param("updateTime") LocalDateTime updateTime);

    /**
     * 更新设备最后上报时间
     */
    @Update("UPDATE terminal_basic_info SET last_update_time = #{reportTime}, updated_at = NOW() " +
            "WHERE device_id = #{deviceId}")
    int updateLastReportTime(@Param("deviceId") String deviceId, 
                            @Param("reportTime") LocalDateTime reportTime);

    /**
     * 查询在线设备数量
     */
    @Select("SELECT COUNT(*) FROM terminal_basic_info WHERE status = 1 AND")
    long countOnlineDevices();

    /**
     * 查询离线设备数量
     */
    @Select("SELECT COUNT(*) FROM terminal_basic_info WHERE status = 0")
    long countOfflineDevices();

    /**
     * 根据状态统计设备数量
     */
    @Select("SELECT COUNT(*) FROM terminal_basic_info WHERE status = #{status}")
    int countByStatus(@Param("status") Integer status);

    /**
     * 查询所有设备的最后更新时间
     */
    @Select("SELECT device_id, last_update_time FROM terminal_basic_info")
    List<TerminalBasicInfo> selectDeviceLastUpdateTimes();

    /**
     * 根据标签查询设备（精确匹配）
     */
    @Select("SELECT * FROM terminal_basic_info WHERE JSON_EXTRACT(tags, '$.${tagKey}') = #{tagValue}")
    List<TerminalBasicInfo> selectByTag(@Param("tagKey") String tagKey, @Param("tagValue") String tagValue);

    /**
     * 根据标签内容模糊搜索设备（搜索键名和值）
     */
    @Select("SELECT * FROM terminal_basic_info WHERE " +
            "JSON_UNQUOTE(tags) LIKE #{tagValue} ")
    List<TerminalBasicInfo> selectByTagSearch(@Param("tagValue") String tagValue);

    /**
     * 查询即将过期的设备
     */
    @Select("SELECT * FROM terminal_basic_info " +
            "WHERE STR_TO_DATE(expired_date, '%Y-%m-%d %H:%i:%s') BETWEEN NOW() AND DATE_ADD(NOW(), INTERVAL #{days} DAY) ")
    List<TerminalBasicInfo> selectExpiringDevices(@Param("days") int days);
}
