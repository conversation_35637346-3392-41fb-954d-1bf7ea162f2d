<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>终端管理页面</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            color: #333;
            padding: 20px;
        }

        .card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            overflow: hidden;
        }

        .card-header {
            padding: 20px;
            border-bottom: 1px solid #eee;
            background: #fafafa;
        }

        .card-header h3 {
            font-size: 18px;
            font-weight: 600;
            color: #333;
        }

        .card-body {
            padding: 20px;
        }

        .search-filters {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
            flex-wrap: wrap;
            align-items: center;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            margin-bottom: 5px;
            font-weight: 500;
            color: #555;
        }

        .form-control {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        .form-control:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s;
            text-decoration: none;
            display: inline-block;
        }

        .btn-primary {
            background: #667eea;
            color: white;
        }

        .btn-primary:hover {
            background: #5a6fd8;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-info {
            background: #17a2b8;
            color: white;
        }

        .btn-warning {
            background: #ffc107;
            color: #212529;
        }

        .btn-danger {
            background: #dc3545;
            color: white;
        }

        .btn-success {
            background: #28a745;
            color: white;
        }

        .btn-sm {
            padding: 5px 10px;
            font-size: 12px;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }

        .table th,
        .table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }

        .table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #495057;
        }

        .table tr:hover {
            background: #f8f9fa;
        }

        .status-online {
            color: #28a745;
            font-weight: bold;
        }

        .status-offline {
            color: #dc3545;
            font-weight: bold;
        }

        .badge {
            display: inline-block;
            padding: 4px 8px;
            font-size: 12px;
            font-weight: 500;
            border-radius: 12px;
            text-align: center;
            white-space: nowrap;
        }

        .badge-success {
            background: #28a745;
            color: white;
        }

        .badge-danger {
            background: #dc3545;
            color: white;
        }

        .badge-warning {
            background: #ffc107;
            color: #212529;
        }

        .badge-info {
            background: #17a2b8;
            color: white;
        }

        .action-buttons {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 10px;
            margin-top: 20px;
        }

        .pagination button {
            padding: 8px 12px;
            border: 1px solid #ddd;
            background: white;
            cursor: pointer;
            border-radius: 4px;
        }

        .pagination button:hover {
            background: #f8f9fa;
        }

        .pagination button.active {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 20px;
            border-radius: 8px;
            width: 80%;
            max-width: 800px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }

        .close {
            font-size: 24px;
            font-weight: bold;
            cursor: pointer;
            color: #999;
        }

        .close:hover {
            color: #333;
        }

        .tag-input {
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            min-height: 40px;
        }

        .tag {
            background: #667eea;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .tag-remove {
            cursor: pointer;
            font-weight: bold;
        }

        .json-display {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }

        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 4px;
            border-left: 4px solid;
        }

        .alert-warning {
            background: #fff3cd;
            border-color: #ffc107;
            color: #856404;
        }

        .detail-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }

        .detail-item {
            display: flex;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .detail-item:last-child {
            border-bottom: none;
        }

        .detail-label {
            font-weight: 600;
            color: #666;
            min-width: 120px;
            margin-right: 10px;
        }

        .detail-value {
            color: #333;
            flex: 1;
        }

        .topology-container {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            position: relative;
        }

        .topology-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 20px;
            color: #495057;
            text-align: center;
        }

        .topology-graph {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 30px;
        }

        .main-terminal {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 25px;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
            text-align: center;
            min-width: 200px;
        }

        .main-terminal-title {
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .main-terminal-id {
            font-size: 12px;
            opacity: 0.9;
        }

        .connection-line {
            width: 2px;
            height: 40px;
            background: linear-gradient(to bottom, #667eea, #ddd);
            position: relative;
        }

        .connection-line::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 8px;
            height: 8px;
            background: #667eea;
            border-radius: 50%;
        }

        .cpe-container {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            justify-content: center;
            max-width: 600px;
        }

        .cpe-item {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 12px;
            min-width: 120px;
            text-align: center;
            transition: all 0.3s;
            position: relative;
        }

        .cpe-item:hover {
            border-color: #667eea;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .cpe-item.active {
            border-color: #28a745;
            background: #f8fff9;
        }

        .cpe-item.inactive {
            border-color: #dc3545;
            background: #fff8f8;
        }

        .cpe-title {
            font-size: 12px;
            font-weight: 600;
            color: #666;
            margin-bottom: 5px;
        }

        .cpe-bucket {
            font-size: 14px;
            font-weight: bold;
            color: #333;
            margin-bottom: 3px;
        }

        .cpe-stats {
            font-size: 10px;
            color: #999;
            line-height: 1.2;
        }

        .cpe-status {
            position: absolute;
            top: -5px;
            right: -5px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            border: 2px solid white;
        }

        .cpe-status.online {
            background: #28a745;
        }

        .cpe-status.offline {
            background: #dc3545;
        }

        .topology-summary {
            background: #e3f2fd;
            border: 1px solid #bbdefb;
            border-radius: 6px;
            padding: 15px;
            margin-top: 20px;
        }

        .summary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
        }

        .summary-item {
            text-align: center;
        }

        .summary-value {
            font-size: 18px;
            font-weight: bold;
            color: #1976d2;
            margin-bottom: 5px;
        }

        .summary-label {
            font-size: 12px;
            color: #666;
        }

        .no-cpe-message {
            text-align: center;
            color: #999;
            font-style: italic;
            padding: 20px;
        }
    </style>
</head>
<body>
    <div class="card">
        <div class="card-header">
            <h3>终端列表</h3>
        </div>
        <div class="card-body">
            <div class="search-filters">
                <div class="form-group">
                    <label>设备ID</label>
                    <input type="text" class="form-control" placeholder="输入设备ID">
                </div>
                <div class="form-group">
                    <label>主机名</label>
                    <input type="text" class="form-control" placeholder="输入主机名">
                </div>
                <div class="form-group">
                    <label>状态</label>
                    <select class="form-control">
                        <option value="">全部</option>
                        <option value="1">在线</option>
                        <option value="0">离线</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>标签</label>
                    <input type="text" class="form-control" placeholder="输入标签">
                </div>
                <button class="btn btn-primary" style="margin-top: 25px;">搜索</button>
                <button class="btn btn-secondary" style="margin-top: 25px;">重置</button>
            </div>

            <table class="table">
                <thead>
                    <tr>
                        <th>设备ID</th>
                        <th>主机名</th>
                        <th>MAC地址</th>
                        <th>状态</th>
                        <th>标签</th>
                        <th>最后更新</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- 数据将通过JavaScript动态生成 -->
                </tbody>
            </table>

            <div class="pagination">
                <button>上一页</button>
                <button class="active">1</button>
                <button>2</button>
                <button>3</button>
                <button>下一页</button>
            </div>
        </div>
    </div>

    <!-- 终端详情模态框 -->
    <div id="terminalDetailModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>终端详情</h3>
                <span class="close" onclick="closeModal('terminalDetailModal')">&times;</span>
            </div>
            <div class="modal-body">
                <!-- 基本信息 -->
                <div class="card">
                    <div class="card-header">
                        <h4>基本信息</h4>
                    </div>
                    <div class="card-body">
                        <div class="detail-grid">
                            <div>
                                <div class="detail-item">
                                    <div class="detail-label">设备ID:</div>
                                    <div class="detail-value">_0fd94938951f4a64bb11c6817a81f7e7</div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">主机名:</div>
                                    <div class="detail-value">ec_3568_25030031</div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">MAC地址:</div>
                                    <div class="detail-value">de:07:65:52:3e:60</div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">状态:</div>
                                    <div class="detail-value"><span class="status-online">在线</span></div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">数据源:</div>
                                    <div class="detail-value">kafka</div>
                                </div>
                            </div>
                            <div>
                                <div class="detail-item">
                                    <div class="detail-label">首次注册:</div>
                                    <div class="detail-value">2024-07-14 18:30:45</div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">最后更新:</div>
                                    <div class="detail-value">2024-07-14 18:30:45</div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">授权到期:</div>
                                    <div class="detail-value">2035-03-09 22:47:20</div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">接收时间:</div>
                                    <div class="detail-value">2024-07-14 18:30:45</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 终端拓扑关系 -->
                <div class="card">
                    <div class="card-header">
                        <h4>终端拓扑关系</h4>
                    </div>
                    <div class="card-body">
                        <div class="topology-container">
                            <div class="topology-title">终端系统架构图</div>
                            <div class="topology-graph">
                                <!-- 主终端 -->
                                <div class="main-terminal">
                                    <div class="main-terminal-title">主终端服务</div>
                                    <div class="main-terminal-id">ec_3568_25030031</div>
                                </div>
                                
                                <!-- 连接线 -->
                                <div class="connection-line"></div>
                                
                                <!-- CPE集合 -->
                                <div class="cpe-container" id="cpeContainer">
                                    <div class="cpe-item active">
                                        <div class="cpe-status online"></div>
                                        <div class="cpe-title">CPE</div>
                                        <div class="cpe-bucket">Bucket 0</div>
                                        <div class="cpe-stats">
                                            连接: 0<br>
                                            PPS: 0.0<br>
                                            BPS: 0.0
                                        </div>
                                    </div>
                                    <div class="cpe-item active">
                                        <div class="cpe-status online"></div>
                                        <div class="cpe-title">CPE</div>
                                        <div class="cpe-bucket">Bucket 1</div>
                                        <div class="cpe-stats">
                                            连接: 0<br>
                                            PPS: 0.0<br>
                                            BPS: 0.0
                                        </div>
                                    </div>
                                    <div class="cpe-item active">
                                        <div class="cpe-status online"></div>
                                        <div class="cpe-title">CPE</div>
                                        <div class="cpe-bucket">Bucket 2</div>
                                        <div class="cpe-stats">
                                            连接: 0<br>
                                            PPS: 0.0<br>
                                            BPS: 0.0
                                        </div>
                                    </div>
                                    <div class="cpe-item active">
                                        <div class="cpe-status online"></div>
                                        <div class="cpe-title">CPE</div>
                                        <div class="cpe-bucket">Bucket 3</div>
                                        <div class="cpe-stats">
                                            连接: 0<br>
                                            PPS: 0.0<br>
                                            BPS: 0.0
                                        </div>
                                    </div>
                                    <div class="cpe-item active">
                                        <div class="cpe-status online"></div>
                                        <div class="cpe-title">CPE</div>
                                        <div class="cpe-bucket">Bucket 4</div>
                                        <div class="cpe-stats">
                                            连接: 0<br>
                                            PPS: 0.0<br>
                                            BPS: 0.0
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 拓扑统计信息 -->
                            <div class="topology-summary">
                                <div class="summary-grid">
                                    <div class="summary-item">
                                        <div class="summary-value">105</div>
                                        <div class="summary-label">组ID</div>
                                    </div>
                                    <div class="summary-item">
                                        <div class="summary-value">5</div>
                                        <div class="summary-label">CPE数量</div>
                                    </div>
                                    <div class="summary-item">
                                        <div class="summary-value">0.0</div>
                                        <div class="summary-label">总PPS</div>
                                    </div>
                                    <div class="summary-item">
                                        <div class="summary-value">0 B/s</div>
                                        <div class="summary-label">总带宽</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 版本信息 -->
                <div class="card">
                    <div class="card-header">
                        <h4>版本信息</h4>
                    </div>
                    <div class="card-body">
                        <div class="detail-grid">
                            <div>
                                <div class="detail-item">
                                    <div class="detail-label">Controller:</div>
                                    <div class="detail-value">1.0.1</div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">Detector:</div>
                                    <div class="detail-value">1.0.48</div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">Web:</div>
                                    <div class="detail-value">1.0.55</div>
                                </div>
                            </div>
                            <div>
                                <div class="detail-item">
                                    <div class="detail-label">Agent:</div>
                                    <div class="detail-value">1.0.0</div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">Front:</div>
                                    <div class="detail-value">1.0.40</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 标签信息 -->
                <div class="card">
                    <div class="card-header">
                        <h4>标签信息</h4>
                    </div>
                    <div class="card-body">
                        <div class="detail-grid">
                            <div>
                                <div class="detail-item">
                                    <div class="detail-label">位置:</div>
                                    <div class="detail-value">未知</div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">部门:</div>
                                    <div class="detail-value">运维部</div>
                                </div>
                            </div>
                            <div>
                                <div class="detail-item">
                                    <div class="detail-label">环境:</div>
                                    <div class="detail-value">生产</div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">优先级:</div>
                                    <div class="detail-value">高</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 自定义字段 -->
                <div class="card">
                    <div class="card-header">
                        <h4>自定义字段</h4>
                    </div>
                    <div class="card-body">
                        <div class="detail-grid">
                            <div>
                                <div class="detail-item">
                                    <div class="detail-label">车辆型号:</div>
                                    <div class="detail-value">长安车型</div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">安装日期:</div>
                                    <div class="detail-value">2024-07-14</div>
                                </div>
                            </div>
                            <div>
                                <div class="detail-item">
                                    <div class="detail-label">维护联系人:</div>
                                    <div class="detail-value">技术支持</div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">备注:</div>
                                    <div class="detail-value">生产环境设备</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 编辑终端模态框 -->
    <div id="editTerminalModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>编辑终端</h3>
                <span class="close" onclick="closeModal('editTerminalModal')">&times;</span>
            </div>
            <div class="modal-body">
                <div class="card">
                    <div class="card-header">
                        <h4>基本信息</h4>
                    </div>
                    <div class="card-body">
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                            <div class="form-group">
                                <label>设备ID</label>
                                <input type="text" class="form-control" value="_0fd94938951f4a64bb11c6817a81f7e7" readonly>
                            </div>
                            <div class="form-group">
                                <label>主机名</label>
                                <input type="text" class="form-control" value="ec_3568_25030031">
                            </div>
                            <div class="form-group">
                                <label>MAC地址</label>
                                <input type="text" class="form-control" value="de:07:65:52:3e:60">
                            </div>
                            <div class="form-group">
                                <label>状态</label>
                                <select class="form-control">
                                    <option value="1" selected>在线</option>
                                    <option value="0">离线</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h4>标签管理</h4>
                    </div>
                    <div class="card-body">
                        <div class="form-group">
                            <label>标签</label>
                            <div class="tag-input" id="tagInput">
                                <div class="tag">
                                    生产 <span class="tag-remove" onclick="removeTag(this)">×</span>
                                </div>
                                <div class="tag">
                                    高 <span class="tag-remove" onclick="removeTag(this)">×</span>
                                </div>
                                <div class="tag">
                                    运维部 <span class="tag-remove" onclick="removeTag(this)">×</span>
                                </div>
                                <input type="text" style="border: none; outline: none; flex: 1;" placeholder="输入标签后按回车" onkeypress="addTag(event)">
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h4>自定义字段</h4>
                    </div>
                    <div class="card-body">
                        <div id="customFields">
                            <div style="display: flex; gap: 10px; margin-bottom: 10px; align-items: center;">
                                <input type="text" class="form-control" placeholder="字段名" value="车辆型号">
                                <input type="text" class="form-control" placeholder="字段值" value="长安车型">
                                <button class="btn btn-danger btn-sm" onclick="removeCustomField(this)">删除</button>
                            </div>
                            <div style="display: flex; gap: 10px; margin-bottom: 10px; align-items: center;">
                                <input type="text" class="form-control" placeholder="字段名" value="安装日期">
                                <input type="text" class="form-control" placeholder="字段值" value="2024-07-14">
                                <button class="btn btn-danger btn-sm" onclick="removeCustomField(this)">删除</button>
                            </div>
                            <div style="display: flex; gap: 10px; margin-bottom: 10px; align-items: center;">
                                <input type="text" class="form-control" placeholder="字段名" value="维护联系人">
                                <input type="text" class="form-control" placeholder="字段值" value="技术支持">
                                <button class="btn btn-danger btn-sm" onclick="removeCustomField(this)">删除</button>
                            </div>
                        </div>
                        <button class="btn btn-secondary" onclick="addCustomField()">添加字段</button>
                    </div>
                </div>

                <div style="text-align: center; margin-top: 20px;">
                    <button class="btn btn-primary">保存</button>
                    <button class="btn btn-secondary" onclick="closeModal('editTerminalModal')">取消</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 删除确认模态框 -->
    <div id="deleteConfirmModal" class="modal">
        <div class="modal-content" style="max-width: 400px;">
            <div class="modal-header">
                <h3>确认删除</h3>
                <span class="close" onclick="closeModal('deleteConfirmModal')">&times;</span>
            </div>
            <div class="modal-body">
                <div class="alert alert-warning">
                    <strong>警告!</strong> 您确定要删除这个离线终端吗？此操作不可恢复。
                </div>
                <div style="text-align: center; margin-top: 20px;">
                    <button class="btn btn-danger" onclick="confirmDelete()">确认删除</button>
                    <button class="btn btn-secondary" onclick="closeModal('deleteConfirmModal')">取消</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentDeleteDeviceId = null;

        // 模态框函数
        function openModal(modalId) {
            document.getElementById(modalId).style.display = 'block';
        }

        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }

        // 点击模态框外部关闭
        window.onclick = function(event) {
            if (event.target.classList.contains('modal')) {
                event.target.style.display = 'none';
            }
        }

        // 终端管理相关函数
        function viewRealTimeMetrics(deviceId) {
            // 在实际Vue应用中，这里会使用路由跳转
            window.open(`real-time-metrics.html?deviceId=${deviceId}`, '_blank');
        }

        function viewHistoryMetrics(deviceId) {
            // 在实际Vue应用中，这里会使用路由跳转
            window.open(`history-metrics.html?deviceId=${deviceId}`, '_blank');
        }

        function viewDetails(deviceId) {
            // 模拟获取group_usage数据
            const mockGroupUsage = {
                group_id: 105,
                pps_total: 0.0,
                bps_total: 0.0,
                buckets: [
                    { bucket: 0, conn: 0, pps: 0.0, bps: 0.0 },
                    { bucket: 1, conn: 0, pps: 0.0, bps: 0.0 },
                    { bucket: 2, conn: 0, pps: 0.0, bps: 0.0 },
                    { bucket: 3, conn: 0, pps: 0.0, bps: 0.0 },
                    { bucket: 4, conn: 0, pps: 0.0, bps: 0.0 }
                ]
            };
            
            // 渲染CPE拓扑图
            renderCPETopology(mockGroupUsage);
            
            openModal('terminalDetailModal');
        }

        function editTerminal(deviceId) {
            openModal('editTerminalModal');
        }

        function deleteTerminal(deviceId) {
            currentDeleteDeviceId = deviceId;
            openModal('deleteConfirmModal');
        }

        function confirmDelete() {
            if (currentDeleteDeviceId) {
                alert('终端 ' + currentDeleteDeviceId + ' 已删除');
                closeModal('deleteConfirmModal');
                currentDeleteDeviceId = null;
                // 这里可以添加实际的删除逻辑
            }
        }

        // 标签管理
        function addTag(event) {
            if (event.key === 'Enter' && event.target.value.trim()) {
                const tagInput = document.getElementById('tagInput');
                const newTag = document.createElement('div');
                newTag.className = 'tag';
                newTag.innerHTML = `${event.target.value.trim()} <span class="tag-remove" onclick="removeTag(this)">×</span>`;
                tagInput.insertBefore(newTag, event.target);
                event.target.value = '';
            }
        }

        function removeTag(element) {
            element.parentElement.remove();
        }

        // 自定义字段管理
        function addCustomField() {
            const customFields = document.getElementById('customFields');
            const newField = document.createElement('div');
            newField.style.cssText = 'display: flex; gap: 10px; margin-bottom: 10px; align-items: center;';
            newField.innerHTML = `
                <input type="text" class="form-control" placeholder="字段名">
                <input type="text" class="form-control" placeholder="字段值">
                <button class="btn btn-danger btn-sm" onclick="removeCustomField(this)">删除</button>
            `;
            customFields.appendChild(newField);
        }

        function removeCustomField(element) {
            element.parentElement.remove();
        }

        // 渲染CPE拓扑图
        function renderCPETopology(groupUsage) {
            const container = document.getElementById('cpeContainer');
            if (!groupUsage || !groupUsage.buckets || groupUsage.buckets.length === 0) {
                container.innerHTML = '<div class="no-cpe-message">暂无CPE设备信息</div>';
                return;
            }

            container.innerHTML = '';
            
            groupUsage.buckets.forEach((bucket, index) => {
                const cpeItem = document.createElement('div');
                const isActive = bucket.conn > 0 || bucket.pps > 0 || bucket.bps > 0;
                
                cpeItem.className = `cpe-item ${isActive ? 'active' : 'inactive'}`;
                cpeItem.innerHTML = `
                    <div class="cpe-status ${isActive ? 'online' : 'offline'}"></div>
                    <div class="cpe-title">CPE</div>
                    <div class="cpe-bucket">Bucket ${bucket.bucket}</div>
                    <div class="cpe-stats">
                        连接: ${bucket.conn}<br>
                        PPS: ${bucket.pps.toFixed(1)}<br>
                        BPS: ${bucket.bps.toFixed(1)}
                    </div>
                `;
                
                container.appendChild(cpeItem);
            });
            
            // 更新统计信息
            updateTopologySummary(groupUsage);
        }

        // 更新拓扑统计信息
        function updateTopologySummary(groupUsage) {
            const summaryItems = document.querySelectorAll('.summary-item .summary-value');
            if (summaryItems.length >= 4) {
                summaryItems[0].textContent = groupUsage.group_id || 'N/A';
                summaryItems[1].textContent = groupUsage.buckets ? groupUsage.buckets.length : 0;
                summaryItems[2].textContent = groupUsage.pps_total ? groupUsage.pps_total.toFixed(1) : '0.0';
                summaryItems[3].textContent = groupUsage.bps_total ? formatBytes(groupUsage.bps_total) + '/s' : '0 B/s';
            }
        }

        // 格式化字节数
        function formatBytes(bytes) {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
        }

        // 模拟终端数据
        const mockTerminalData = [
            {
                deviceId: '_0fd94938951f4a64bb11c6817a81f7e7',
                hostname: 'ec_3568_25030031',
                mac: 'de:07:65:52:3e:60',
                status: 1,
                tags: ['生产', '高', '运维部'],
                lastUpdate: '2024-07-14 18:30:45'
            },
            {
                deviceId: 'T002',
                hostname: '主机名002',
                mac: 'AA:BB:CC:DD:EE:02',
                status: 0,
                tags: ['测试'],
                lastUpdate: '2024-07-14 18:28:15'
            },
            {
                deviceId: 'T003',
                hostname: '主机名003',
                mac: 'AA:BB:CC:DD:EE:03',
                status: 1,
                tags: ['生产'],
                lastUpdate: '2024-07-14 18:25:42'
            },
            {
                deviceId: 'T004',
                hostname: '主机名004',
                mac: 'AA:BB:CC:DD:EE:04',
                status: 1,
                tags: ['生产', '中', '开发部'],
                lastUpdate: '2024-07-14 18:22:30'
            },
            {
                deviceId: 'T005',
                hostname: '主机名005',
                mac: 'AA:BB:CC:DD:EE:05',
                status: 0,
                tags: ['测试', '低'],
                lastUpdate: '2024-07-14 18:20:15'
            }
        ];

        // 渲染终端列表
        function renderTerminalList(data = mockTerminalData) {
            const tbody = document.querySelector('.table tbody');
            tbody.innerHTML = '';
            
            data.forEach(terminal => {
                const row = document.createElement('tr');
                
                // 状态显示
                const statusClass = terminal.status === 1 ? 'status-online' : 'status-offline';
                const statusText = terminal.status === 1 ? '在线' : '离线';
                
                // 标签显示
                const tagBadges = terminal.tags.map(tag => {
                    let badgeClass = 'badge-info';
                    if (tag === '生产') badgeClass = 'badge-success';
                    if (tag === '高') badgeClass = 'badge-danger';
                    if (tag === '中') badgeClass = 'badge-warning';
                    if (tag === '测试') badgeClass = 'badge-success';
                    return `<span class="badge ${badgeClass}">${tag}</span>`;
                }).join(' ');
                
                // 操作按钮
                let actionButtons = `
                    <button class="btn btn-success btn-sm" onclick="viewRealTimeMetrics('${terminal.deviceId}')">实时指标</button>
                    <button class="btn btn-info btn-sm" onclick="viewHistoryMetrics('${terminal.deviceId}')">历史指标</button>
                    <button class="btn btn-primary btn-sm" onclick="viewDetails('${terminal.deviceId}')">详情</button>
                    <button class="btn btn-warning btn-sm" onclick="editTerminal('${terminal.deviceId}')">编辑</button>
                `;
                
                // 离线设备添加删除按钮
                if (terminal.status === 0) {
                    actionButtons += `<button class="btn btn-danger btn-sm" onclick="deleteTerminal('${terminal.deviceId}')">删除</button>`;
                }
                
                row.innerHTML = `
                    <td>${terminal.deviceId}</td>
                    <td>${terminal.hostname}</td>
                    <td>${terminal.mac}</td>
                    <td><span class="${statusClass}">${statusText}</span></td>
                    <td>${tagBadges}</td>
                    <td>${terminal.lastUpdate}</td>
                    <td>
                        <div class="action-buttons">
                            ${actionButtons}
                        </div>
                    </td>
                `;
                
                tbody.appendChild(row);
            });
        }

        // 筛选终端数据
        function filterTerminals() {
            const deviceIdFilter = document.querySelector('.search-filters input[placeholder="输入设备ID"]').value.toLowerCase();
            const hostnameFilter = document.querySelector('.search-filters input[placeholder="输入主机名"]').value.toLowerCase();
            const statusFilter = document.querySelector('.search-filters select').value;
            const tagFilter = document.querySelector('.search-filters input[placeholder="输入标签"]').value.toLowerCase();
            
            const filteredData = mockTerminalData.filter(terminal => {
                // 设备ID筛选
                if (deviceIdFilter && !terminal.deviceId.toLowerCase().includes(deviceIdFilter)) {
                    return false;
                }
                
                // 主机名筛选
                if (hostnameFilter && !terminal.hostname.toLowerCase().includes(hostnameFilter)) {
                    return false;
                }
                
                // 状态筛选
                if (statusFilter && terminal.status.toString() !== statusFilter) {
                    return false;
                }
                
                // 标签筛选
                if (tagFilter && !terminal.tags.some(tag => tag.toLowerCase().includes(tagFilter))) {
                    return false;
                }
                
                return true;
            });
            
            renderTerminalList(filteredData);
        }

        // 重置筛选
        function resetTerminalFilters() {
            document.querySelector('.search-filters input[placeholder="输入设备ID"]').value = '';
            document.querySelector('.search-filters input[placeholder="输入主机名"]').value = '';
            document.querySelector('.search-filters select').value = '';
            document.querySelector('.search-filters input[placeholder="输入标签"]').value = '';
            renderTerminalList();
        }

        // 绑定筛选事件
        function bindTerminalFilterEvents() {
            const searchButton = document.querySelector('.search-filters .btn-primary');
            const resetButton = document.querySelector('.search-filters .btn-secondary');
            
            if (searchButton) {
                searchButton.onclick = filterTerminals;
            }
            
            if (resetButton) {
                resetButton.onclick = resetTerminalFilters;
            }
            
            // 绑定回车键搜索
            const inputs = document.querySelectorAll('.search-filters input');
            inputs.forEach(input => {
                input.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        filterTerminals();
                    }
                });
            });
        }

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化终端列表
            renderTerminalList();
            // 绑定筛选事件
            bindTerminalFilterEvents();
            // 检查URL参数并自动搜索
            handleUrlParams();
        });

        // 处理URL参数
        function handleUrlParams() {
            const urlParams = new URLSearchParams(window.location.search);
            const searchParam = urlParams.get('search');
            
            if (searchParam) {
                // 将搜索参数填入设备ID输入框
                const deviceIdInput = document.querySelector('.search-filters input[placeholder="输入设备ID"]');
                if (deviceIdInput) {
                    deviceIdInput.value = searchParam;
                    // 自动执行搜索
                    filterTerminals();
                    // 高亮显示搜索结果
                    highlightSearchResult(searchParam);
                }
            }
        }

        // 高亮显示搜索结果
        function highlightSearchResult(searchTerm) {
            setTimeout(() => {
                const rows = document.querySelectorAll('.table tbody tr');
                rows.forEach(row => {
                    const deviceIdCell = row.cells[0];
                    if (deviceIdCell && deviceIdCell.textContent.includes(searchTerm)) {
                        row.style.backgroundColor = '#fff3cd';
                        row.style.border = '2px solid #ffc107';
                        // 3秒后移除高亮
                        setTimeout(() => {
                            row.style.backgroundColor = '';
                            row.style.border = '';
                        }, 3000);
                    }
                });
            }, 100);
        }
    </script>
</body>
</html> 