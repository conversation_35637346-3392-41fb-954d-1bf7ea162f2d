package com.unnet.jmanul.business.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 日志文件记录实体类
 * 
 * <AUTHOR>
 * @since 2025-07-28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("log_file")
@ApiModel(value = "LogFile对象", description = "日志文件记录表")
public class LogFile implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "终端MAC地址（唯一标识）")
    @TableField("identity_mac")
    private String identityMac;

    @ApiModelProperty(value = "原始文件名")
    @TableField("original_filename")
    private String originalFilename;

    @ApiModelProperty(value = "存储文件名（MinIO中的文件名）")
    @TableField("stored_filename")
    private String storedFilename;

    @ApiModelProperty(value = "文件大小（字节）")
    @TableField("file_size")
    private Long fileSize;

    @ApiModelProperty(value = "文件类型/扩展名")
    @TableField("file_type")
    private String fileType;

    @ApiModelProperty(value = "MinIO存储路径")
    @TableField("storage_path")
    private String storagePath;

    @ApiModelProperty(value = "文件MD5值")
    @TableField("file_md5")
    private String fileMd5;

    @ApiModelProperty(value = "上传时间")
    @TableField("upload_time")
    private LocalDateTime uploadTime;

    @ApiModelProperty(value = "文件描述")
    @TableField("description")
    private String description;

    @ApiModelProperty(value = "文件状态：0-正常，1-已删除")
    @TableField("status")
    private Integer status;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "逻辑删除标记：0-未删除，1-已删除")
    @TableLogic
    @TableField("deleted")
    private Integer deleted;
}
