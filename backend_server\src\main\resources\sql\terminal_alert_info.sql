-- 终端告警信息表
CREATE TABLE `terminal_alert_info` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `alert_id` varchar(50) NOT NULL COMMENT '告警唯一ID（UUID）',
  `device_id` varchar(100) NOT NULL COMMENT '告警终端设备ID',
  `alert_type` varchar(30) NOT NULL COMMENT '告警类型：CPU_TEMPERATURE,MEMORY_USAGE,DISK_USAGE,DISK_DATA_USAGE,DISK_SYSTEM_USAGE,LICENSE_EXPIRY',
  `alert_details` varchar(500) DEFAULT NULL COMMENT '告警详情',
  `metric_name` varchar(50) DEFAULT NULL COMMENT '告警指标名称',
  `threshold` varchar(20) DEFAULT NULL COMMENT '告警阈值',
  `current_value` varchar(20) DEFAULT NULL COMMENT '当前值',
  `metric_id` varchar(100) DEFAULT NULL COMMENT '关联的指标记录ID（Elasticsearch文档ID）',
  `alert_time` datetime DEFAULT NULL COMMENT '告警时间',
  `alert_status` varchar(20) DEFAULT 'ACTIVE' COMMENT '告警状态：ACTIVE,RESOLVED,ACKNOWLEDGED',
  `resolved_time` datetime DEFAULT NULL COMMENT '告警解决时间',
  `acknowledged_time` datetime DEFAULT NULL COMMENT '告警确认时间',
  `acknowledged_by` varchar(50) DEFAULT NULL COMMENT '告警确认人',
  `resolve_comment` varchar(500) DEFAULT NULL COMMENT '解决备注',
  `notification_sent` tinyint(1) DEFAULT 0 COMMENT '是否已发送通知：0-未发送，1-已发送',
  `notification_time` datetime DEFAULT NULL COMMENT '通知发送时间',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_alert_id` (`alert_id`),
  KEY `idx_device_id` (`device_id`),
  KEY `idx_alert_type` (`alert_type`),
  KEY `idx_alert_status` (`alert_status`),
  KEY `idx_alert_time` (`alert_time`),
  KEY `idx_metric_id` (`metric_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='终端告警信息表';

-- 添加复合索引优化查询性能
CREATE INDEX `idx_device_alert_time` ON `terminal_alert_info` (`device_id`, `alert_time`);
CREATE INDEX `idx_device_status` ON `terminal_alert_info` (`device_id`, `alert_status`);
CREATE INDEX `idx_type_time` ON `terminal_alert_info` (`alert_type`, `alert_time`);
CREATE INDEX `idx_status_time` ON `terminal_alert_info` (`alert_status`, `alert_time`);
