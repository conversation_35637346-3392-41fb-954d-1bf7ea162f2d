<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>站内告警页面</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            color: #333;
            padding: 20px;
        }

        .card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            overflow: hidden;
        }

        .card-header {
            padding: 20px;
            border-bottom: 1px solid #eee;
            background: #fafafa;
        }

        .card-header h3 {
            font-size: 18px;
            font-weight: 600;
            color: #333;
        }

        .card-body {
            padding: 20px;
        }

        .search-filters {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
            flex-wrap: wrap;
            align-items: center;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            margin-bottom: 5px;
            font-weight: 500;
            color: #555;
        }

        .form-control {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        .form-control:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s;
            text-decoration: none;
            display: inline-block;
        }

        .btn-primary {
            background: #667eea;
            color: white;
        }

        .btn-primary:hover {
            background: #5a6fd8;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-info {
            background: #17a2b8;
            color: white;
        }

        .btn-warning {
            background: #ffc107;
            color: #212529;
        }

        .btn-sm {
            padding: 5px 10px;
            font-size: 12px;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }

        .table th,
        .table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }

        .table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #495057;
        }

        .table tr:hover {
            background: #f8f9fa;
        }

        .badge {
            display: inline-block;
            padding: 4px 8px;
            font-size: 12px;
            font-weight: 500;
            border-radius: 12px;
            text-align: center;
            white-space: nowrap;
        }

        .badge-success {
            background: #28a745;
            color: white;
        }

        .badge-danger {
            background: #dc3545;
            color: white;
        }

        .badge-warning {
            background: #ffc107;
            color: #212529;
        }

        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 10px;
            margin-top: 20px;
        }

        .pagination button {
            padding: 8px 12px;
            border: 1px solid #ddd;
            background: white;
            cursor: pointer;
            border-radius: 4px;
        }

        .pagination button:hover {
            background: #f8f9fa;
        }

        .pagination button.active {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 20px;
            border-radius: 8px;
            width: 80%;
            max-width: 800px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }

        .close {
            font-size: 24px;
            font-weight: bold;
            cursor: pointer;
            color: #999;
        }

        .close:hover {
            color: #333;
        }

        .btn-success {
            background: #28a745;
            color: white;
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            background-color: #6c757d !important;
        }
    </style>
</head>
<body>
    <div class="card">
        <div class="card-header">
            <h3>告警列表</h3>
        </div>
        <div class="card-body">
            <div class="search-filters">
                <div class="form-group">
                    <label>设备ID</label>
                    <input type="text" class="form-control" placeholder="输入设备ID">
                </div>
                <div class="form-group">
                    <label>告警类型</label>
                    <select class="form-control">
                        <option value="">全部</option>
                        <option value="CPU_TEMPERATURE">CPU温度</option>
                        <option value="MEMORY_USAGE">内存使用率</option>
                        <option value="DISK_USAGE">磁盘使用率</option>
                        <option value="LICENSE_EXPIRY">许可证过期</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>告警状态</label>
                    <select class="form-control">
                        <option value="">全部</option>
                        <option value="ACTIVE">活跃</option>
                        <option value="RESOLVED">已解决</option>
                        <option value="ACKNOWLEDGED">已确认</option>
                    </select>
                </div>
                <button class="btn btn-primary" style="margin-top: 25px;">搜索</button>
                <button class="btn btn-secondary" style="margin-top: 25px;">重置</button>
            </div>

            <table class="table">
                <thead>
                    <tr>
                        <th>告警ID</th>
                        <th>设备ID</th>
                        <th>告警类型</th>
                        <th>告警详情</th>
                        <th>当前值</th>
                        <th>阈值</th>
                        <th>状态</th>
                        <th>告警时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- 数据将通过JavaScript动态生成 -->
                </tbody>
            </table>

            <div class="pagination">
                <button>上一页</button>
                <button class="active">1</button>
                <button>2</button>
                <button>3</button>
                <button>下一页</button>
            </div>
        </div>
    </div>

    <!-- 告警详情模态框 -->
    <div id="alertDetailModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>告警详情</h3>
                <span class="close" onclick="closeModal('alertDetailModal')">&times;</span>
            </div>
            <div class="modal-body">
                <div class="card">
                    <div class="card-body">
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                            <div>
                                <strong>告警ID:</strong> <span id="alertId">A001</span><br>
                                <strong>设备ID:</strong> <span id="deviceId">_0fd94938951f4a64bb11c6817a81f7e7</span><br>
                                <strong>告警类型:</strong> <span id="alertType">CPU温度</span><br>
                                <strong>告警详情:</strong> <span id="alertDetails">CPU温度过高</span><br>
                                <strong>当前值:</strong> <span id="currentValue">85.9°C</span><br>
                                <strong>阈值:</strong> <span id="threshold">80°C</span>
                            </div>
                            <div>
                                <strong>告警时间:</strong> <span id="alertTime">2024-07-14 18:25:30</span><br>
                                <strong>状态:</strong> <span id="alertStatus" class="badge badge-danger">活跃</span><br>
                                <strong>通知状态:</strong> <span id="notificationStatus">已发送</span><br>
                                <strong>通知时间:</strong> <span id="notificationTime">2024-07-14 18:25:35</span><br>
                                <strong>确认人:</strong> <span id="acknowledgedBy">未确认</span><br>
                                <strong>解决时间:</strong> <span id="resolvedTime">未解决</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div style="text-align: center; margin-top: 20px;">
                    <button class="btn btn-warning" onclick="acknowledgeAlert()">确认告警</button>
                    <button class="btn btn-success" onclick="resolveAlert()">标记已解决</button>
                    <button class="btn btn-secondary" onclick="closeModal('alertDetailModal')">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentAlertId = null;

        // 模拟告警数据
        const mockAlertData = [
            {
                alertId: 'A001',
                deviceId: '_0fd94938951f4a64bb11c6817a81f7e7',
                alertType: 'CPU_TEMPERATURE',
                alertTypeDisplay: 'CPU温度',
                alertDetails: 'CPU温度过高',
                currentValue: '85.9°C',
                threshold: '80°C',
                alertStatus: 'ACTIVE',
                alertStatusDisplay: '活跃',
                alertTime: '2024-07-14 18:25:30',
                notificationStatus: '已发送',
                notificationTime: '2024-07-14 18:25:35',
                acknowledgedBy: '未确认',
                resolvedTime: '未解决'
            },
            {
                alertId: 'A002',
                deviceId: 'T002',
                alertType: 'MEMORY_USAGE',
                alertTypeDisplay: '内存使用率',
                alertDetails: '内存使用率过高',
                currentValue: '92%',
                threshold: '90%',
                alertStatus: 'RESOLVED',
                alertStatusDisplay: '已解决',
                alertTime: '2024-07-14 18:20:15',
                notificationStatus: '已发送',
                notificationTime: '2024-07-14 18:20:20',
                acknowledgedBy: '运维人员',
                resolvedTime: '2024-07-14 18:35:10'
            },
            {
                alertId: 'A003',
                deviceId: 'T003',
                alertType: 'DISK_USAGE',
                alertTypeDisplay: '磁盘使用率',
                alertDetails: '系统盘使用率过高',
                currentValue: '88%',
                threshold: '85%',
                alertStatus: 'ACKNOWLEDGED',
                alertStatusDisplay: '已确认',
                alertTime: '2024-07-14 18:18:45',
                notificationStatus: '已发送',
                notificationTime: '2024-07-14 18:18:50',
                acknowledgedBy: '运维人员',
                resolvedTime: '未解决'
            },
            {
                alertId: 'A004',
                deviceId: '_0fd94938951f4a64bb11c6817a81f7e7',
                alertType: 'LICENSE_EXPIRY',
                alertTypeDisplay: '许可证过期',
                alertDetails: '软件许可证即将过期',
                currentValue: '30天',
                threshold: '90天',
                alertStatus: 'ACKNOWLEDGED',
                alertStatusDisplay: '已确认',
                alertTime: '2024-07-14 18:15:20',
                notificationStatus: '已发送',
                notificationTime: '2024-07-14 18:15:25',
                acknowledgedBy: '运维人员',
                resolvedTime: '未解决'
            },
            {
                alertId: 'A005',
                deviceId: 'T005',
                alertType: 'CPU_TEMPERATURE',
                alertTypeDisplay: 'CPU温度',
                alertDetails: 'CPU温度异常',
                currentValue: '78.5°C',
                threshold: '80°C',
                alertStatus: 'RESOLVED',
                alertStatusDisplay: '已解决',
                alertTime: '2024-07-14 18:10:30',
                notificationStatus: '已发送',
                notificationTime: '2024-07-14 18:10:35',
                acknowledgedBy: '运维人员',
                resolvedTime: '2024-07-14 18:25:10'
            }
        ];

        // 渲染告警列表
        function renderAlertList(data = mockAlertData) {
            const tbody = document.querySelector('.table tbody');
            tbody.innerHTML = '';
            
            data.forEach(alert => {
                const row = document.createElement('tr');
                
                // 状态徽章样式
                let badgeClass = 'badge-info';
                if (alert.alertStatus === 'ACTIVE') badgeClass = 'badge-danger';
                if (alert.alertStatus === 'RESOLVED') badgeClass = 'badge-success';
                if (alert.alertStatus === 'ACKNOWLEDGED') badgeClass = 'badge-warning';
                
                row.innerHTML = `
                    <td>${alert.alertId}</td>
                    <td>${alert.deviceId}</td>
                    <td>${alert.alertTypeDisplay}</td>
                    <td>${alert.alertDetails}</td>
                    <td>${alert.currentValue}</td>
                    <td>${alert.threshold}</td>
                    <td><span class="badge ${badgeClass}">${alert.alertStatusDisplay}</span></td>
                    <td>${alert.alertTime}</td>
                    <td>
                        <button class="btn btn-info btn-sm" onclick="viewAlertDetails('${alert.alertId}')">详情</button>
                        <button class="btn btn-warning btn-sm" onclick="viewRealTimeMetrics('${alert.deviceId}')">实时指标</button>
                        <button class="btn btn-primary btn-sm" onclick="sendDingTalkAlert('${alert.alertId}')">钉钉提醒</button>
                    </td>
                `;
                
                tbody.appendChild(row);
            });
        }

        // 筛选告警数据
        function filterAlerts() {
            const deviceIdFilter = document.querySelector('.search-filters input[placeholder="输入设备ID"]').value.toLowerCase();
            const alertTypeFilter = document.querySelector('.search-filters select').value;
            const alertStatusFilter = document.querySelectorAll('.search-filters select')[1].value;
            
            const filteredData = mockAlertData.filter(alert => {
                // 设备ID筛选
                if (deviceIdFilter && !alert.deviceId.toLowerCase().includes(deviceIdFilter)) {
                    return false;
                }
                
                // 告警类型筛选
                if (alertTypeFilter && alert.alertType !== alertTypeFilter) {
                    return false;
                }
                
                // 告警状态筛选
                if (alertStatusFilter && alert.alertStatus !== alertStatusFilter) {
                    return false;
                }
                
                return true;
            });
            
            renderAlertList(filteredData);
        }

        // 重置筛选
        function resetAlertFilters() {
            document.querySelector('.search-filters input[placeholder="输入设备ID"]').value = '';
            document.querySelector('.search-filters select').value = '';
            document.querySelectorAll('.search-filters select')[1].value = '';
            renderAlertList();
        }

        // 绑定筛选事件
        function bindAlertFilterEvents() {
            const searchButton = document.querySelector('.search-filters .btn-primary');
            const resetButton = document.querySelector('.search-filters .btn-secondary');
            
            if (searchButton) {
                searchButton.onclick = filterAlerts;
            }
            
            if (resetButton) {
                resetButton.onclick = resetAlertFilters;
            }
            
            // 绑定回车键搜索
            const inputs = document.querySelectorAll('.search-filters input');
            inputs.forEach(input => {
                input.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        filterAlerts();
                    }
                });
            });
        }

        // 模态框函数
        function openModal(modalId) {
            document.getElementById(modalId).style.display = 'block';
        }

        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }

        // 点击模态框外部关闭
        window.onclick = function(event) {
            if (event.target.classList.contains('modal')) {
                event.target.style.display = 'none';
            }
        }

        // 告警相关函数
        function viewAlertDetails(alertId) {
            currentAlertId = alertId;
            
            // 从模拟数据中获取详细信息
            const alertData = mockAlertData.find(alert => alert.alertId === alertId);
            
            if (alertData) {
                // 填充模态框数据
                document.getElementById('alertId').textContent = alertData.alertId;
                document.getElementById('deviceId').textContent = alertData.deviceId;
                document.getElementById('alertType').textContent = alertData.alertTypeDisplay;
                document.getElementById('alertDetails').textContent = alertData.alertDetails;
                document.getElementById('currentValue').textContent = alertData.currentValue;
                document.getElementById('threshold').textContent = alertData.threshold;
                document.getElementById('alertTime').textContent = alertData.alertTime;
                
                // 状态徽章
                let badgeClass = 'badge-info';
                if (alertData.alertStatus === 'ACTIVE') badgeClass = 'badge-danger';
                if (alertData.alertStatus === 'RESOLVED') badgeClass = 'badge-success';
                if (alertData.alertStatus === 'ACKNOWLEDGED') badgeClass = 'badge-warning';
                
                document.getElementById('alertStatus').innerHTML = `<span class="badge ${badgeClass}">${alertData.alertStatusDisplay}</span>`;
                document.getElementById('notificationStatus').textContent = alertData.notificationStatus;
                document.getElementById('notificationTime').textContent = alertData.notificationTime;
                document.getElementById('acknowledgedBy').textContent = alertData.acknowledgedBy;
                document.getElementById('resolvedTime').textContent = alertData.resolvedTime;
                
                // 根据告警状态管理按钮显示
                updateAlertButtons(alertData.alertStatus);
            }
            
            openModal('alertDetailModal');
        }

        // 更新告警按钮状态
        function updateAlertButtons(alertStatus) {
            const acknowledgeBtn = document.querySelector('.btn-warning');
            const resolveBtn = document.querySelector('.btn-success');
            
            if (alertStatus === 'RESOLVED') {
                // 已解决的告警，禁用所有操作按钮
                acknowledgeBtn.disabled = true;
                acknowledgeBtn.textContent = '已解决';
                resolveBtn.disabled = true;
                resolveBtn.textContent = '已解决';
            } else if (alertStatus === 'ACKNOWLEDGED') {
                // 已确认的告警，禁用确认按钮，启用解决按钮
                acknowledgeBtn.disabled = true;
                acknowledgeBtn.textContent = '已确认';
                resolveBtn.disabled = false;
                resolveBtn.textContent = '标记已解决';
            } else {
                // 活跃的告警，启用所有按钮
                acknowledgeBtn.disabled = false;
                acknowledgeBtn.textContent = '确认告警';
                resolveBtn.disabled = false;
                resolveBtn.textContent = '标记已解决';
            }
        }

        function viewRealTimeMetrics(deviceId) {
            // 在实际Vue应用中，这里会使用路由跳转
            window.open(`real-time-metrics.html?deviceId=${deviceId}`, '_blank');
        }

        function sendDingTalkAlert(alertId) {
            alert('钉钉提醒已发送！告警ID: ' + alertId);
        }

        function acknowledgeAlert() {
            if (currentAlertId) {
                // 找到对应的告警数据并更新状态
                const alertIndex = mockAlertData.findIndex(alert => alert.alertId === currentAlertId);
                if (alertIndex !== -1) {
                    mockAlertData[alertIndex].alertStatus = 'ACKNOWLEDGED';
                    mockAlertData[alertIndex].alertStatusDisplay = '已确认';
                    mockAlertData[alertIndex].acknowledgedBy = '当前用户';
                    
                    const now = new Date();
                    const timeString = now.getFullYear() + '-' + 
                                     String(now.getMonth() + 1).padStart(2, '0') + '-' + 
                                     String(now.getDate()).padStart(2, '0') + ' ' + 
                                     String(now.getHours()).padStart(2, '0') + ':' + 
                                     String(now.getMinutes()).padStart(2, '0') + ':' + 
                                     String(now.getSeconds()).padStart(2, '0');
                    
                    // 更新模态框显示
                    document.getElementById('acknowledgedBy').textContent = '当前用户';
                    document.getElementById('alertStatus').innerHTML = '<span class="badge badge-warning">已确认</span>';
                    
                    // 更新按钮状态
                    updateAlertButtons('ACKNOWLEDGED');
                    
                    // 重新渲染列表以更新状态
                    renderAlertList();
                    
                    alert('告警 ' + currentAlertId + ' 已确认');
                }
            }
        }

        function resolveAlert() {
            if (currentAlertId) {
                // 找到对应的告警数据并更新状态
                const alertIndex = mockAlertData.findIndex(alert => alert.alertId === currentAlertId);
                if (alertIndex !== -1) {
                    mockAlertData[alertIndex].alertStatus = 'RESOLVED';
                    mockAlertData[alertIndex].alertStatusDisplay = '已解决';
                    mockAlertData[alertIndex].acknowledgedBy = '当前用户';
                    
                    const now = new Date();
                    const timeString = now.getFullYear() + '-' + 
                                     String(now.getMonth() + 1).padStart(2, '0') + '-' + 
                                     String(now.getDate()).padStart(2, '0') + ' ' + 
                                     String(now.getHours()).padStart(2, '0') + ':' + 
                                     String(now.getMinutes()).padStart(2, '0') + ':' + 
                                     String(now.getSeconds()).padStart(2, '0');
                    
                    mockAlertData[alertIndex].resolvedTime = timeString;
                    
                    // 更新模态框显示
                    document.getElementById('resolvedTime').textContent = timeString;
                    document.getElementById('acknowledgedBy').textContent = '当前用户';
                    document.getElementById('alertStatus').innerHTML = '<span class="badge badge-success">已解决</span>';
                    
                    // 更新按钮状态
                    updateAlertButtons('RESOLVED');
                    
                    // 重新渲染列表以更新状态
                    renderAlertList();
                    
                    alert('告警 ' + currentAlertId + ' 已标记为解决');
                }
            }
        }

        // 处理URL参数
        function handleUrlParams() {
            const urlParams = new URLSearchParams(window.location.search);
            const alertId = urlParams.get('alertId');
            
            if (alertId) {
                // 高亮显示对应的告警行
                highlightAlertRow(alertId);
                // 自动显示告警详情
                setTimeout(() => {
                    viewAlertDetails(alertId);
                }, 500);
            }
        }

        // 高亮显示告警行
        function highlightAlertRow(alertId) {
            const rows = document.querySelectorAll('.table tbody tr');
            rows.forEach(row => {
                const alertIdCell = row.cells[0];
                if (alertIdCell && alertIdCell.textContent === alertId) {
                    row.style.backgroundColor = '#fff3cd';
                    row.style.border = '2px solid #ffc107';
                    // 滚动到该行
                    row.scrollIntoView({ behavior: 'smooth', block: 'center' });
                    // 3秒后移除高亮
                    setTimeout(() => {
                        row.style.backgroundColor = '';
                        row.style.border = '';
                    }, 3000);
                }
            });
        }

        // 页面加载时初始化
        window.onload = function() {
            // 初始化告警列表
            renderAlertList();
            // 绑定筛选事件
            bindAlertFilterEvents();
            // 处理URL参数
            handleUrlParams();
        };
    </script>
</body>
</html> 