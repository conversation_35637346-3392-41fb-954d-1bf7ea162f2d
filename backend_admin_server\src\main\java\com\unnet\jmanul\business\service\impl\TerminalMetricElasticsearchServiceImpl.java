package com.unnet.jmanul.business.service.impl;

import com.unnet.jmanul.business.document.TerminalMetricDocument;
import com.unnet.jmanul.business.repository.TerminalMetricRepository;
import com.unnet.jmanul.business.service.ITerminalMetricElasticsearchService;
import com.unnet.jmanul.business.entity.dto.TerminalMetricInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.elasticsearch.core.ElasticsearchOperations;
import org.springframework.data.elasticsearch.core.IndexOperations;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Optional;

/**
 * 终端指标信息Elasticsearch服务实现
 * 
 * <AUTHOR>
 * @date 2024-07-16
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class TerminalMetricElasticsearchServiceImpl implements ITerminalMetricElasticsearchService {

    private final TerminalMetricRepository terminalMetricRepository;
    private final ElasticsearchOperations elasticsearchOperations;

    @Override
    public boolean saveMetricData(TerminalMetricInfo metricInfo) {
        try {
            TerminalMetricDocument document = convertToDocument(metricInfo);
            
            // 设置创建时间和更新时间
            LocalDateTime now = LocalDateTime.now();
            document.setCreateTime(now);
            document.setUpdateTime(now);

            // 生成文档ID
            document.generateId();

            // 保存到Elasticsearch
            TerminalMetricDocument savedDocument = terminalMetricRepository.save(document);

            if (savedDocument != null) {
                log.debug("保存指标数据到Elasticsearch成功 - 设备: {}, 文档ID: {}",
                        metricInfo.getDeviceId(), savedDocument.getId());
                return true;
            }
            return false;
        } catch (Exception e) {
            log.error("保存指标数据到Elasticsearch失败 - 设备: {}, 错误: {}",
                    metricInfo.getDeviceId(), e.getMessage(), e);
            return false;
        }
    }



    @Override
    public Optional<TerminalMetricDocument> getMetricById(String metricId) {
        try {
            return terminalMetricRepository.findById(metricId);
        } catch (Exception e) {
            log.error("根据指标ID获取指标详情失败 - 指标ID: {}, 错误: {}", metricId, e.getMessage(), e);
            return Optional.empty();
        }
    }



    @Override
    public boolean deleteHistoryData(LocalDateTime beforeTime) {
        try {
            terminalMetricRepository.deleteByMetricTimeBefore(beforeTime);
            log.info("删除Elasticsearch历史指标数据完成 - 删除时间点: {}", beforeTime);
            return true;
        } catch (Exception e) {
            log.error("删除Elasticsearch历史指标数据失败 - 删除时间点: {}, 错误: {}", beforeTime, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean indexExists() {
        try {
            IndexOperations indexOperations = elasticsearchOperations.indexOps(TerminalMetricDocument.class);
            return indexOperations.exists();
        } catch (Exception e) {
            log.error("检查Elasticsearch索引是否存在失败 - 错误: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean createIndex() {
        try {
            IndexOperations indexOperations = elasticsearchOperations.indexOps(TerminalMetricDocument.class);
            if (!indexOperations.exists()) {
                // 创建索引
                boolean created = indexOperations.create();
                if (created) {
                    // 创建映射
                    indexOperations.putMapping();
                    log.info("创建Elasticsearch索引成功 - 索引: terminal_metrics");
                    return true;
                } else {
                    log.error("创建Elasticsearch索引失败");
                    return false;
                }
            } else {
                log.info("Elasticsearch索引已存在 - 索引: terminal_metrics");
                return true;
            }
        } catch (Exception e) {
            log.error("创建Elasticsearch索引失败 - 错误: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 将TerminalMetricInfo转换为TerminalMetricDocument
     */
    private TerminalMetricDocument convertToDocument(TerminalMetricInfo metricInfo) {
        TerminalMetricDocument document = new TerminalMetricDocument();

        // 复制基本字段
        document.setIdentityMac(metricInfo.getDeviceId()); // deviceId现在存储的是格式化的MAC地址
        document.setTemperatures(metricInfo.getTemperatures());
        document.setCpuUsage(metricInfo.getCpuUsage());
        document.setMemoryUsage(metricInfo.getMemoryUsage());
        document.setDiskUsage(metricInfo.getDiskUsage());
        document.setCdata(metricInfo.getCdata());
        document.setZdata(metricInfo.getZdata());
        document.setGroupUsage(metricInfo.getGroupUsage());
        document.setGroupBps(metricInfo.getGroupBps());
        document.setMetricTime(metricInfo.getMetricTime());
        document.setReceiveTime(metricInfo.getReceiveTime());

        // 转换Double类型字段为BigDecimal
        if (metricInfo.getUptime() != null) {
            document.setUptime(BigDecimal.valueOf(metricInfo.getUptime()));
        }
        if (metricInfo.getCpuTemp() != null) {
            document.setCpuTemp(BigDecimal.valueOf(metricInfo.getCpuTemp()));
        }
        if (metricInfo.getCpuPercent() != null) {
            document.setCpuPercent(BigDecimal.valueOf(metricInfo.getCpuPercent()));
        }
        if (metricInfo.getMemoryPercent() != null) {
            document.setMemoryPercent(BigDecimal.valueOf(metricInfo.getMemoryPercent()));
        }
        if (metricInfo.getDiskDataPercent() != null) {
            document.setDiskDataPercent(BigDecimal.valueOf(metricInfo.getDiskDataPercent()));
        }
        if (metricInfo.getDiskSystemPercent() != null) {
            document.setDiskSystemPercent(BigDecimal.valueOf(metricInfo.getDiskSystemPercent()));
        }

        return document;
    }

    @Override
    public Page<TerminalMetricDocument> getMetricsByIdentityMac(String identityMac, Pageable pageable) {
        try {
            // 按采集时间倒序排列
            Pageable sortedPageable = PageRequest.of(
                    pageable.getPageNumber(),
                    pageable.getPageSize(),
                    Sort.by(Sort.Direction.DESC, "metricTime")
            );

            return terminalMetricRepository.findByIdentityMacOrderByMetricTimeDesc(identityMac, sortedPageable);
        } catch (Exception e) {
            log.error("根据设备MAC地址分页查询指标数据失败 - 设备MAC: {}, 错误: {}", identityMac, e.getMessage(), e);
            return Page.empty();
        }
    }

    @Override
    public Optional<TerminalMetricDocument> getLatestMetricByIdentityMac(String identityMac) {
        try {
            return terminalMetricRepository.findFirstByIdentityMacOrderByMetricTimeDesc(identityMac);
        } catch (Exception e) {
            log.error("根据设备MAC地址获取最新指标数据失败 - 设备MAC: {}, 错误: {}", identityMac, e.getMessage(), e);
            return Optional.empty();
        }
    }

    @Override
    public Page<TerminalMetricDocument> getMetricsByIdentityMacAndTimeRange(String identityMac,
                                                                           LocalDateTime startTime,
                                                                           LocalDateTime endTime,
                                                                           Pageable pageable) {
        try {
            // 按采集时间倒序排列
            Pageable sortedPageable = PageRequest.of(
                    pageable.getPageNumber(),
                    pageable.getPageSize(),
                    Sort.by(Sort.Direction.DESC, "metricTime")
            );

            return terminalMetricRepository.findByIdentityMacAndMetricTimeBetween(
                    identityMac, startTime, endTime, sortedPageable);
        } catch (Exception e) {
            log.error("根据设备MAC地址和时间范围分页查询指标数据失败 - 设备MAC: {}, 时间范围: {} - {}, 错误: {}",
                    identityMac, startTime, endTime, e.getMessage(), e);
            return Page.empty();
        }
    }

    @Override
    public long countByIdentityMac(String identityMac) {
        try {
            return terminalMetricRepository.countByIdentityMac(identityMac);
        } catch (Exception e) {
            log.error("根据设备MAC地址查询数据总数失败 - 设备MAC: {}, 错误: {}", identityMac, e.getMessage(), e);
            return 0;
        }
    }

    @Override
    public long countByIdentityMacAndTimeRange(String identityMac, LocalDateTime startTime, LocalDateTime endTime) {
        try {
            return terminalMetricRepository.countByIdentityMacAndMetricTimeBetween(identityMac, startTime, endTime);
        } catch (Exception e) {
            log.error("根据设备MAC地址和时间范围查询数据总数失败 - 设备MAC: {}, 时间范围: {} - {}, 错误: {}",
                    identityMac, startTime, endTime, e.getMessage(), e);
            return 0;
        }
    }
}
