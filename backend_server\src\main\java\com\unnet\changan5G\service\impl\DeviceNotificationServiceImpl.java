package com.unnet.changan5G.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.unnet.changan5G.service.DeviceNotificationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.ConcurrentHashMap;
import java.util.UUID;

/**
 * 设备通知服务实现
 * 
 * <AUTHOR>
 * @date 2024-07-15
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class DeviceNotificationServiceImpl implements DeviceNotificationService {

    private final ObjectMapper objectMapper;
    
    // 存储所有活跃的SSE连接
    private final CopyOnWriteArrayList<SseEmitter> emitters = new CopyOnWriteArrayList<>();

    // 存储连接ID和SseEmitter的映射，用于更好的连接管理
    private final ConcurrentHashMap<String, SseEmitter> emitterMap = new ConcurrentHashMap<>();

    // 存储客户端IP和连接的映射，用于防止重复连接
    private final ConcurrentHashMap<String, String> clientConnectionMap = new ConcurrentHashMap<>();
    
    // SSE连接超时时间（60分钟）
    private static final long SSE_TIMEOUT = 60 * 60 * 1000L;
    
    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Override
    public SseEmitter subscribe() {
        return subscribe(null);
    }

    public SseEmitter subscribe(String clientIp) {
        // 生成唯一的连接ID
        String connectionId = UUID.randomUUID().toString();
        SseEmitter emitter = new SseEmitter(SSE_TIMEOUT);

        // 添加到连接列表和映射
        emitters.add(emitter);
        emitterMap.put(connectionId, emitter);

        // 如果提供了客户端IP，记录映射关系
        if (clientIp != null && !clientIp.isEmpty()) {
            // 检查是否已有来自同一IP的连接
            String existingConnectionId = clientConnectionMap.get(clientIp);
            if (existingConnectionId != null) {
                // 检查旧连接是否仍然有效
                SseEmitter oldEmitter = emitterMap.get(existingConnectionId);
                if (oldEmitter != null && !isEmitterCompleted(oldEmitter)) {
                    log.debug("检测到来自同一IP的活跃连接，关闭旧连接 - IP: {}, 旧连接ID: {}, 新连接ID: {}",
                        clientIp, existingConnectionId, connectionId);

                    try {
                        oldEmitter.complete();
                        log.debug("已关闭旧的SSE连接 - IP: {}, 连接ID: {}", clientIp, existingConnectionId);
                    } catch (Exception e) {
                        log.debug("关闭旧连接失败 - IP: {}, 连接ID: {}, 错误: {}",
                            clientIp, existingConnectionId, e.getMessage());
                    }
                } else {
                    log.debug("来自同一IP的旧连接已失效，直接替换 - IP: {}, 旧连接ID: {}, 新连接ID: {}",
                        clientIp, existingConnectionId, connectionId);
                }
            }
            clientConnectionMap.put(clientIp, connectionId);
        }

        log.debug("新的SSE连接建立，连接ID: {}, 客户端IP: {}", connectionId, clientIp);

        // 设置连接完成和超时的回调
        emitter.onCompletion(() -> {
            removeConnection(connectionId, emitter, "连接完成");
        });

        emitter.onTimeout(() -> {
            removeConnection(connectionId, emitter, "连接超时");
        });

        emitter.onError((ex) -> {
            removeConnection(connectionId, emitter, "连接错误: " + ex.getMessage());
        });

        // 发送连接成功消息，包含连接ID
        try {
            emitter.send(SseEmitter.event()
                    .name("connected")
                    .data("连接成功，连接ID: " + connectionId));
            log.debug("新的SSE连接建立成功，连接ID: {}, 当前连接数: {}", connectionId, emitters.size());
        } catch (IOException e) {
            log.error("发送连接成功消息失败，连接ID: {}, 错误: {}", connectionId, e.getMessage());
            removeConnection(connectionId, emitter, "发送消息失败");
        }

        return emitter;
    }

    /**
     * 移除连接
     */
    private void removeConnection(String connectionId, SseEmitter emitter, String reason) {
        emitters.remove(emitter);
        emitterMap.remove(connectionId);

        // 清理客户端IP映射
        clientConnectionMap.entrySet().removeIf(entry -> entry.getValue().equals(connectionId));

        log.debug("SSE连接移除，连接ID: {}, 原因: {}, 当前连接数: {}", connectionId, reason, emitters.size());
    }

    /**
     * 清理无效连接
     */
    public void cleanupInvalidConnections() {
        log.info("开始清理无效SSE连接，当前连接数: {}", emitters.size());

        // 创建要移除的连接列表
        List<SseEmitter> deadEmitters = new ArrayList<>();

        for (SseEmitter emitter : new ArrayList<>(emitters)) {
            try {
                // 检查连接是否已完成
                if (isEmitterCompleted(emitter)) {
                    log.debug("检测到已完成的SSE连接，将被移除");
                    deadEmitters.add(emitter);
                    continue;
                }

                // 发送轻量级心跳消息来检测连接是否有效
                emitter.send(SseEmitter.event()
                        .name("heartbeat")
                        .data("ping"));

            } catch (IllegalStateException e) {
                log.debug("检测到已完成的SSE连接，将被移除: {}", e.getMessage());
                deadEmitters.add(emitter);
            } catch (IOException e) {
                log.debug("检测到无效SSE连接，将被移除: {}", e.getMessage());
                deadEmitters.add(emitter);
            } catch (Exception e) {
                log.warn("心跳检测时发生未知错误，移除连接: {}", e.getMessage());
                deadEmitters.add(emitter);
            }
        }

        // 移除失效的连接
        if (!deadEmitters.isEmpty()) {
            emitters.removeAll(deadEmitters);
            // 同步清理映射表
            emitterMap.entrySet().removeIf(entry -> deadEmitters.contains(entry.getValue()));
            log.info("清理了 {} 个无效的SSE连接", deadEmitters.size());
        }

        log.info("SSE连接清理完成，当前连接数: {}", emitters.size());
    }

    @Override
    public void sendDeviceRegisteredNotification(String identityMac, String hostname) {
        DeviceNotification notification = DeviceNotification.builder()
                .type("DEVICE_REGISTERED")
                .deviceId(identityMac) // 现在存储的是MAC地址
                .hostname(hostname)
                .message(String.format("终端 %s 注册成功", identityMac))
                .timestamp(LocalDateTime.now().format(DATE_TIME_FORMATTER))
                .build();

        sendNotification("device-registered", notification);
        log.info("发送设备注册通知 - 设备MAC: {}, 主机名: {}", identityMac, hostname);
    }

    @Override
    public void sendDeviceOnlineNotification(String identityMac, String hostname) {
        DeviceNotification notification = DeviceNotification.builder()
                .type("DEVICE_ONLINE")
                .deviceId(identityMac) // 现在存储的是MAC地址
                .hostname(hostname)
                .message(String.format("终端 %s 上线", identityMac))
                .timestamp(LocalDateTime.now().format(DATE_TIME_FORMATTER))
                .build();

        sendNotification("device-online", notification);
        log.info("发送设备上线通知 - 设备MAC: {}, 主机名: {}", identityMac, hostname);
    }

    @Override
    public void sendDeviceOfflineNotification(String identityMac, String hostname) {
        log.info("=== 开始发送设备离线SSE通知 ===");
        log.info("设备MAC: {}, 主机名: {}", identityMac, hostname);

        DeviceNotification notification = DeviceNotification.builder()
                .type("DEVICE_OFFLINE")
                .deviceId(identityMac) // 现在存储的是MAC地址
                .hostname(hostname)
                .message(String.format("终端 %s 离线", identityMac))
                .timestamp(LocalDateTime.now().format(DATE_TIME_FORMATTER))
                .build();

        log.info("离线通知对象: {}", notification);

        sendNotification("device-offline", notification);
        log.info("设备离线SSE通知发送完成 - 设备MAC: {}, 主机名: {}", identityMac, hostname);
    }

    @Override
    public void sendStatisticsUpdateNotification(int onlineCount, int offlineCount, int alertCount) {
        StatisticsNotification notification = StatisticsNotification.builder()
                .type("STATISTICS_UPDATE")
                .onlineCount(onlineCount)
                .offlineCount(offlineCount)
                .alertCount(alertCount)
                .totalCount(onlineCount + offlineCount)
                .timestamp(LocalDateTime.now().format(DATE_TIME_FORMATTER))
                .build();

        sendNotification("statistics-update", notification);
        log.debug("发送统计信息更新通知 - 在线: {}, 离线: {}, 告警: {}", onlineCount, offlineCount, alertCount);
    }

    @Override
    public void sendAlertNotification(String identityMac, String hostname, String alertType,
                                    String alertLevel, String message, String details,
                                    String currentValue, String threshold) {
        log.info("=== 开始发送告警SSE通知 ===");
        log.info("设备MAC: {}, 主机名: {}, 告警类型: {}, 告警级别: {}", identityMac, hostname, alertType, alertLevel);

        AlertNotification notification = AlertNotification.builder()
                .type("DEVICE_ALERT")
                .deviceId(identityMac) // 现在存储的是MAC地址
                .hostname(hostname)
                .alertType(alertType)
                .alertLevel(alertLevel)
                .message(message)
                .details(details)
                .currentValue(currentValue)
                .threshold(threshold)
                .timestamp(LocalDateTime.now().format(DATE_TIME_FORMATTER))
                .build();

        log.info("告警通知对象: {}", notification);

        sendNotification("device-alert", notification);
        log.info("告警SSE通知发送完成 - 设备MAC: {}, 告警类型: {}", identityMac, alertType);
    }
    
    /**
     * 发送通知到所有连接的客户端
     */
    private void sendNotification(String eventName, Object data) {
        if (emitters.isEmpty()) {
            log.warn("没有活跃的SSE连接，跳过通知发送 - 事件: {}", eventName);
            return;
        }

        log.info("发送SSE通知: {}, 当前连接数: {}", eventName, emitters.size());

        // 创建要移除的连接列表
        CopyOnWriteArrayList<SseEmitter> deadEmitters = new CopyOnWriteArrayList<>();

        int successCount = 0;
        // 创建emitters的副本以避免并发修改异常
        List<SseEmitter> emittersCopy = new ArrayList<>(emitters);

        for (SseEmitter emitter : emittersCopy) {
            try {
                // 检查emitter是否已经完成
                if (isEmitterCompleted(emitter)) {
                    log.debug("跳过已完成的SSE连接");
                    deadEmitters.add(emitter);
                    continue;
                }

                String jsonData = objectMapper.writeValueAsString(data);
                log.debug("发送SSE消息到客户端 - 事件: {}, 数据: {}", eventName, jsonData);

                emitter.send(SseEmitter.event()
                        .name(eventName)
                        .data(jsonData));

                successCount++;
                log.debug("SSE消息发送成功 - 事件: {}", eventName);
            } catch (IllegalStateException e) {
                // 连接已完成或已关闭
                log.warn("SSE连接已完成，移除连接 - 事件: {}, 错误: {}", eventName, e.getMessage());
                deadEmitters.add(emitter);
            } catch (IOException e) {
                // 网络IO异常
                log.warn("发送SSE消息失败，移除连接 - 事件: {}, 错误: {}", eventName, e.getMessage());
                deadEmitters.add(emitter);
            } catch (Exception e) {
                // 其他异常
                log.error("发送SSE消息时发生未知错误，移除连接 - 事件: {}, 错误: {}", eventName, e.getMessage(), e);
                deadEmitters.add(emitter);
            }
        }

        log.info("SSE消息发送完成 - 事件: {}, 成功: {}, 失败: {}", eventName, successCount, deadEmitters.size());

        // 移除失效的连接
        if (!deadEmitters.isEmpty()) {
            emitters.removeAll(deadEmitters);
            // 同步清理映射表
            emitterMap.entrySet().removeIf(entry -> deadEmitters.contains(entry.getValue()));
            log.info("移除了 {} 个失效的SSE连接，当前连接数: {}", deadEmitters.size(), emitters.size());
        }
    }

    /**
     * 检查SseEmitter是否已完成
     * 通过反射检查内部状态，避免调用可能抛出异常的方法
     */
    private boolean isEmitterCompleted(SseEmitter emitter) {
        try {
            // 使用反射检查SseEmitter的内部状态
            java.lang.reflect.Field field = emitter.getClass().getSuperclass().getDeclaredField("complete");
            field.setAccessible(true);
            return (Boolean) field.get(emitter);
        } catch (Exception e) {
            // 如果反射失败，假设连接仍然有效
            return false;
        }
    }

    /**
     * 定期清理无效连接
     * 每5分钟执行一次
     */
    @Scheduled(fixedRate = 5 * 60 * 1000) // 5分钟
    public void scheduledCleanup() {
        if (!emitters.isEmpty()) {
            log.debug("定期清理SSE连接任务开始，当前连接数: {}", emitters.size());
            cleanupInvalidConnections();
        }
    }

    /**
     * 获取当前连接统计信息
     */
    public String getConnectionStats() {
        return String.format("当前SSE连接数: %d, 映射表大小: %d", emitters.size(), emitterMap.size());
    }

    /**
     * 设备通知数据结构
     */
    @lombok.Data
    @lombok.Builder
    public static class DeviceNotification {
        private String type;
        private String deviceId;
        private String hostname;
        private String message;
        private String timestamp;
    }
    
    /**
     * 统计信息通知数据结构
     */
    @lombok.Data
    @lombok.Builder
    public static class StatisticsNotification {
        private String type;
        private int onlineCount;
        private int offlineCount;
        private int alertCount;
        private int totalCount;
        private String timestamp;
    }

    /**
     * 告警通知数据结构
     */
    @lombok.Data
    @lombok.Builder
    public static class AlertNotification {
        private String type;
        private String deviceId;
        private String hostname;
        private String alertType;
        private String alertLevel;
        private String message;
        private String details;
        private String currentValue;
        private String threshold;
        private String timestamp;
    }
}
