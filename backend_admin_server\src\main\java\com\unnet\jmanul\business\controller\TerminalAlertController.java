package com.unnet.jmanul.business.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.unnet.jmanul.business.entity.TerminalAlertInfo;
import com.unnet.jmanul.business.entity.dto.AlertAcknowledgeRequest;
import com.unnet.jmanul.business.entity.dto.AlertListRequest;
import com.unnet.jmanul.business.entity.dto.AlertResolveRequest;
import com.unnet.jmanul.business.service.TerminalAlertInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Map;

/**
 * 终端告警管理控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/admin/alerts")
@RequiredArgsConstructor
@Api(tags = "终端告警管理")
public class TerminalAlertController {

    private final TerminalAlertInfoService terminalAlertInfoService;

    /**
     * 分页查询告警列表
     */
    @GetMapping
    @ApiOperation(value = "分页查询告警列表", notes = "支持按告警ID、设备MAC地址、告警类型、告警级别、告警状态等条件筛选")
    public ResponseEntity<IPage<TerminalAlertInfo>> getAlerts(
            @ApiParam(value = "告警ID") @RequestParam(required = false) String alertId,
            @ApiParam(value = "设备MAC地址") @RequestParam(required = false) String identityMac,
            @ApiParam(value = "告警类型") @RequestParam(required = false) String alertType,
            @ApiParam(value = "告警级别") @RequestParam(required = false) String alertLevel,
            @ApiParam(value = "告警状态") @RequestParam(required = false) String alertStatus,
            @ApiParam(value = "开始时间") @RequestParam(required = false) String startTime,
            @ApiParam(value = "结束时间") @RequestParam(required = false) String endTime,
            @ApiParam(value = "页码，从1开始", defaultValue = "1") @RequestParam(defaultValue = "1") Integer page,
            @ApiParam(value = "每页大小，默认10条", defaultValue = "10") @RequestParam(defaultValue = "10") Integer pageSize) {

        try {
            log.info("分页查询告警列表 - 告警ID: {}, 设备MAC: {}, 告警类型: {}, 告警级别: {}, 告警状态: {}, 页码: {}, 每页大小: {}",
                    alertId, identityMac, alertType, alertLevel, alertStatus, page, pageSize);

            AlertListRequest request = new AlertListRequest();
            request.setAlertId(alertId);
            request.setIdentityMac(identityMac);
            request.setAlertType(alertType);
            request.setAlertLevel(alertLevel);
            request.setAlertStatus(alertStatus);
            request.setStartTime(startTime);
            request.setEndTime(endTime);
            request.setPage(page);
            request.setPageSize(pageSize);

            IPage<TerminalAlertInfo> result = terminalAlertInfoService.getAlertPage(request);

            log.info("查询完成 - 总数: {}, 当前页数据量: {}", result.getTotal(), result.getRecords().size());
            return ResponseEntity.ok(result);

        } catch (Exception e) {
            log.error("分页查询告警列表失败 - 错误: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 根据告警ID获取告警详情
     */
    @GetMapping("/{alertId}")
    @ApiOperation(value = "获取告警详情", notes = "根据告警ID获取告警的详细信息")
    public ResponseEntity<TerminalAlertInfo> getAlertDetail(
            @ApiParam(value = "告警ID", required = true) @PathVariable String alertId) {

        try {
            log.info("获取告警详情 - 告警ID: {}", alertId);

            TerminalAlertInfo alert = terminalAlertInfoService.getAlertById(alertId);

            if (alert != null) {
                log.info("获取告警详情成功 - 告警ID: {}, 设备MAC: {}", alertId, alert.getIdentityMac());
                return ResponseEntity.ok(alert);
            } else {
                log.warn("未找到告警信息 - 告警ID: {}", alertId);
                return ResponseEntity.notFound().build();
            }

        } catch (Exception e) {
            log.error("获取告警详情失败 - 告警ID: {}, 错误: {}", alertId, e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 确认告警
     */
    @PostMapping("/{alertId}/acknowledge")
    @ApiOperation(value = "确认告警", notes = "将告警状态设置为已确认")
    public ResponseEntity<Void> acknowledgeAlert(
            @ApiParam(value = "告警ID", required = true) @PathVariable String alertId,
            @ApiParam(value = "确认请求", required = true) @Valid @RequestBody AlertAcknowledgeRequest request) {

        try {
            log.info("确认告警 - 告警ID: {}, 确认人: {}", alertId, request.getAcknowledgedBy());

            boolean success = terminalAlertInfoService.acknowledgeAlert(alertId, request.getAcknowledgedBy());

            if (success) {
                log.info("确认告警成功 - 告警ID: {}", alertId);
                return ResponseEntity.ok().build();
            } else {
                log.warn("确认告警失败 - 告警ID: {}", alertId);
                return ResponseEntity.badRequest().build();
            }

        } catch (Exception e) {
            log.error("确认告警失败 - 告警ID: {}, 错误: {}", alertId, e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 解决告警
     */
    @PostMapping("/{alertId}/resolve")
    @ApiOperation(value = "解决告警", notes = "将告警状态设置为已解决")
    public ResponseEntity<Void> resolveAlert(
            @ApiParam(value = "告警ID", required = true) @PathVariable String alertId,
            @ApiParam(value = "解决请求", required = true) @Valid @RequestBody AlertResolveRequest request) {

        try {
            log.info("解决告警 - 告警ID: {}, 解决人: {}", alertId, request.getResolvedBy());

            boolean success = terminalAlertInfoService.resolveAlert(alertId, request.getResolvedBy(), request.getResolveComment());

            if (success) {
                log.info("解决告警成功 - 告警ID: {}", alertId);
                return ResponseEntity.ok().build();
            } else {
                log.warn("解决告警失败 - 告警ID: {}", alertId);
                return ResponseEntity.badRequest().build();
            }

        } catch (Exception e) {
            log.error("解决告警失败 - 告警ID: {}, 错误: {}", alertId, e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 发送钉钉通知
     */
    @PostMapping("/{alertId}/notify")
    @ApiOperation(value = "发送钉钉通知", notes = "为指定告警发送钉钉通知")
    public ResponseEntity<Void> sendDingTalkNotification(
            @ApiParam(value = "告警ID", required = true) @PathVariable String alertId) {

        try {
            log.info("发送钉钉通知 - 告警ID: {}", alertId);

            boolean success = terminalAlertInfoService.sendDingTalkNotification(alertId);

            if (success) {
                log.info("发送钉钉通知成功 - 告警ID: {}", alertId);
                return ResponseEntity.ok().build();
            } else {
                log.warn("发送钉钉通知失败 - 告警ID: {}", alertId);
                return ResponseEntity.badRequest().build();
            }

        } catch (Exception e) {
            log.error("发送钉钉通知失败 - 告警ID: {}, 错误: {}", alertId, e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 获取告警统计信息
     */
    @GetMapping("/statistics")
    @ApiOperation(value = "获取告警统计信息", notes = "获取各种状态的告警数量统计")
    public ResponseEntity<Map<String, Object>> getAlertStatistics() {

        try {
            log.info("获取告警统计信息");

            Map<String, Object> statistics = terminalAlertInfoService.getAlertStatistics();

            log.info("获取告警统计信息成功 - 统计数据: {}", statistics);
            return ResponseEntity.ok(statistics);

        } catch (Exception e) {
            log.error("获取告警统计信息失败 - 错误: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 根据设备MAC地址获取活跃告警数量
     */
    @GetMapping("/device/{identityMac}/count")
    @ApiOperation(value = "获取设备活跃告警数量", notes = "获取指定设备的活跃告警数量")
    public ResponseEntity<Integer> getActiveAlertCountByIdentityMac(
            @ApiParam(value = "设备MAC地址", required = true) @PathVariable String identityMac) {

        try {
            log.info("获取设备活跃告警数量 - 设备MAC: {}", identityMac);

            int count = terminalAlertInfoService.getActiveAlertCountByIdentityMac(identityMac);

            log.info("获取设备活跃告警数量成功 - 设备MAC: {}, 数量: {}", identityMac, count);
            return ResponseEntity.ok(count);

        } catch (Exception e) {
            log.error("获取设备活跃告警数量失败 - 设备MAC: {}, 错误: {}", identityMac, e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }
}
