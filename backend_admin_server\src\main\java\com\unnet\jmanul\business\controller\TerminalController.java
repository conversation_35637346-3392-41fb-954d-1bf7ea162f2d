package com.unnet.jmanul.business.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.unnet.jmanul.business.entity.TerminalBasicInfo;
import com.unnet.jmanul.business.document.TerminalMetricDocument;
import com.unnet.jmanul.business.mapper.TerminalBasicInfoMapper;
import com.unnet.jmanul.business.service.ITerminalBasicInfoService;
import com.unnet.jmanul.business.service.ITerminalMetricElasticsearchService;
import com.unnet.jmanul.business.entity.dto.TerminalDetailResponse;
import com.unnet.jmanul.business.entity.dto.TerminalListResponse;
import com.unnet.jmanul.business.entity.dto.TerminalUpdateRequest;
import com.unnet.jmanul.common.rest.ApiDef;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 终端管理Controller - 长安5G业务模块
 * 
 * <AUTHOR>
 * @date 2024-07-16
 */
@RestController
@RequestMapping(ApiDef.V1_ADMIN_TERMINAL)
@Slf4j
@RequiredArgsConstructor
@Api(tags = {"长安5G - 终端管理"})
public class TerminalController {

    private final ITerminalBasicInfoService terminalBasicInfoService;
    private final ITerminalMetricElasticsearchService terminalMetricElasticsearchService;
    private final TerminalBasicInfoMapper terminalBasicInfoMapper;

    /**
     * 分页查询终端列表
     */
    @GetMapping
    @ApiOperation(value = "分页查询终端列表", notes = "支持按设备ID、主机名、状态、标签等条件过滤查询")
    public ResponseEntity<TerminalListResponse> getTerminalList(
            @ApiParam(value = "页码，从1开始", defaultValue = "1") @RequestParam(defaultValue = "1") int page,
            @ApiParam(value = "每页大小", defaultValue = "10") @RequestParam(defaultValue = "10") int pageSize,
            @ApiParam(value = "设备ID") @RequestParam(required = false) String deviceId,
            @ApiParam(value = "主机名") @RequestParam(required = false) String hostname,
            @ApiParam(value = "状态：0-离线，1-在线") @RequestParam(required = false) Integer status,
            @ApiParam(value = "标签搜索") @RequestParam(required = false) String tag) {

        try {
            log.info("查询终端列表 - 页码: {}, 每页大小: {}, 设备ID: {}, 主机名: {}, 状态: {}, 标签: {}", 
                    page, pageSize, deviceId, hostname, status, tag);

            // 参数验证
            if (page < 1) page = 1;
            if (pageSize < 1 || pageSize > 100) pageSize = 10;

            // 如果有标签搜索，使用自定义方法
            if (StringUtils.hasText(tag)) {
                return handleTagSearch(page, pageSize, deviceId, hostname, status, tag);
            }

            // 构建查询条件
            QueryWrapper<TerminalBasicInfo> queryWrapper = new QueryWrapper<>();

            if (StringUtils.hasText(deviceId)) {
                queryWrapper.like("device_id", deviceId);
            }
            if (StringUtils.hasText(hostname)) {
                queryWrapper.like("hostname", hostname);
            }
            if (status != null) {
                queryWrapper.eq("status", status);
            }

            // 按最后更新时间倒序排列
            queryWrapper.orderByDesc("last_update_time");

            // 分页查询
            Page<TerminalBasicInfo> pageRequest = new Page<>(page, pageSize);
            IPage<TerminalBasicInfo> pageResult = terminalBasicInfoService.page(pageRequest, queryWrapper);

            // 转换为响应对象
            List<TerminalListResponse.TerminalItem> terminalItems = pageResult.getRecords().stream()
                    .map(this::convertToTerminalItem)
                    .collect(Collectors.toList());

            TerminalListResponse response = new TerminalListResponse();
            response.setList(terminalItems);
            response.setTotal(pageResult.getTotal());
            response.setPage(page);
            response.setPageSize(pageSize);

            log.info("查询终端列表成功 - 总数: {}, 当前页数据量: {}", pageResult.getTotal(), terminalItems.size());
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("查询终端列表失败 - 错误: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 查询终端详情
     */
    @GetMapping("/{identityMac}")
    @ApiOperation(value = "查询终端详情", notes = "根据MAC地址查询终端的基本信息和最新指标数据")
    public ResponseEntity<TerminalDetailResponse> getTerminalDetail(
            @ApiParam(value = "设备MAC地址", required = true) @PathVariable String identityMac) {

        try {
            log.info("查询终端详情 - 设备MAC: {}", identityMac);

            // 查询基本信息（根据MAC地址）
            TerminalBasicInfo basicInfo = terminalBasicInfoService.getByIdentityMac(identityMac);
            if (basicInfo == null) {
                log.warn("终端不存在 - 设备MAC: {}", identityMac);
                return ResponseEntity.notFound().build();
            }

            // 查询最新指标信息（使用MAC地址）
            Optional<TerminalMetricDocument> latestMetric = terminalMetricElasticsearchService
                    .getLatestMetricByIdentityMac(identityMac);

            // 构建响应对象
            TerminalDetailResponse response = new TerminalDetailResponse();
            BeanUtils.copyProperties(basicInfo, response);

            if (latestMetric.isPresent()) {
                TerminalMetricDocument metric = latestMetric.get();
                response.setLatestMetric(metric);
                response.setGroupUsage(metric.getGroupUsage());
                response.setGroupBps(metric.getGroupBps());
                log.info("获取到最新指标数据 - 设备MAC: {}, 采集时间: {}", identityMac, metric.getMetricTime());
            } else {
                log.warn("未找到最新指标数据 - 设备MAC: {}", identityMac);
                response.setLatestMetric(null);
                response.setGroupUsage(null);
                response.setGroupBps(null);
            }

            log.info("查询终端详情成功 - 设备MAC: {}", identityMac);
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("查询终端详情失败 - 设备MAC: {}, 错误: {}", identityMac, e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 更新终端信息
     */
    @PutMapping("/{identityMac}")
    @ApiOperation(value = "更新终端信息", notes = "更新终端的主机名、过期时间、标签、自定义字段等信息")
    public ResponseEntity<TerminalBasicInfo> updateTerminal(
            @ApiParam(value = "设备MAC地址", required = true) @PathVariable String identityMac,
            @ApiParam(value = "更新请求", required = true) @Valid @RequestBody TerminalUpdateRequest request) {

        try {
            log.info("更新终端信息 - 设备MAC: {}, 请求: {}", identityMac, request);

            // 查询终端是否存在
            TerminalBasicInfo existingTerminal = terminalBasicInfoService.getByIdentityMac(identityMac);
            if (existingTerminal == null) {
                log.warn("终端不存在 - 设备MAC: {}", identityMac);
                return ResponseEntity.notFound().build();
            }

            // 更新字段
            if (StringUtils.hasText(request.getDeviceId())) {
                existingTerminal.setDeviceId(request.getDeviceId());
            }
            if (StringUtils.hasText(request.getHostname())) {
                existingTerminal.setHostname(request.getHostname());
            }
            if (StringUtils.hasText(request.getExpiredDate())) {
                existingTerminal.setExpiredDate(request.getExpiredDate());
            }
            if (request.getTags() != null) {
                existingTerminal.setTags(request.getTags());
            }
            if (request.getCustomFields() != null) {
                existingTerminal.setCustomFields(request.getCustomFields());
            }

            // 更新最后修改时间
            existingTerminal.setLastUpdateTime(LocalDateTime.now());

            // 保存更新
            boolean updateResult = terminalBasicInfoService.updateById(existingTerminal);
            if (!updateResult) {
                log.error("更新终端信息失败 - 设备MAC: {}", identityMac);
                return ResponseEntity.internalServerError().build();
            }

            log.info("更新终端信息成功 - 设备MAC: {}", identityMac);
            return ResponseEntity.ok(existingTerminal);

        } catch (Exception e) {
            log.error("更新终端信息失败 - 设备MAC: {}, 错误: {}", identityMac, e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 删除终端
     */
    @DeleteMapping("/{identityMac}")
    @ApiOperation(value = "删除终端", notes = "删除离线状态的终端（逻辑删除）")
    public ResponseEntity<String> deleteTerminal(
            @ApiParam(value = "设备MAC地址", required = true) @PathVariable String identityMac) {

        try {
            log.info("删除终端 - 设备MAC: {}", identityMac);

            // 查询终端是否存在
            TerminalBasicInfo terminal = terminalBasicInfoService.getByIdentityMac(identityMac);
            if (terminal == null) {
                log.warn("终端不存在 - 设备MAC: {}", identityMac);
                return ResponseEntity.notFound().build();
            }

            // 检查终端状态，只能删除离线终端
            if (terminal.getStatus() != 0) {
                log.warn("只能删除离线终端 - 设备MAC: {}, 当前状态: {}", identityMac, terminal.getStatus());
                return ResponseEntity.badRequest().body("只能删除离线状态的终端");
            }

            // 逻辑删除
            boolean deleteResult = terminalBasicInfoService.removeById(terminal.getId());
            if (!deleteResult) {
                log.error("删除终端失败 - 设备MAC: {}", identityMac);
                return ResponseEntity.internalServerError().build();
            }

            log.info("删除终端成功 - 设备MAC: {}", identityMac);
            return ResponseEntity.ok("删除终端成功");

        } catch (Exception e) {
            log.error("删除终端失败 - 设备MAC: {}, 错误: {}", identityMac, e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 处理标签搜索的特殊方法
     */
    private ResponseEntity<TerminalListResponse> handleTagSearch(int page, int pageSize, String deviceId,
                                                               String hostname, Integer status, String tag) {
        try {
            // 使用自定义Mapper方法进行标签搜索
            List<TerminalBasicInfo> allResults = terminalBasicInfoMapper
                    .selectByTagSearch("%" + tag + "%");

            // 在内存中进行其他条件过滤
            List<TerminalBasicInfo> filteredResults = allResults.stream()
                    .filter(entity -> {
                        if (StringUtils.hasText(deviceId) && !entity.getDeviceId().contains(deviceId)) {
                            return false;
                        }
                        if (StringUtils.hasText(hostname) && !entity.getHostname().contains(hostname)) {
                            return false;
                        }
                        if (status != null && !status.equals(entity.getStatus())) {
                            return false;
                        }
                        return true;
                    })
                    .sorted((a, b) -> b.getLastUpdateTime().compareTo(a.getLastUpdateTime()))
                    .collect(Collectors.toList());

            // 手动分页
            int total = filteredResults.size();
            int startIndex = (page - 1) * pageSize;
            int endIndex = Math.min(startIndex + pageSize, total);

            List<TerminalBasicInfo> pageResults = filteredResults.subList(startIndex, endIndex);

            // 转换为响应对象
            List<TerminalListResponse.TerminalItem> terminalItems = pageResults.stream()
                    .map(this::convertToTerminalItem)
                    .collect(Collectors.toList());

            TerminalListResponse response = new TerminalListResponse();
            response.setList(terminalItems);
            response.setTotal((long) total);
            response.setPage(page);
            response.setPageSize(pageSize);

            log.info("标签搜索完成 - 总数: {}, 当前页数据量: {}", total, terminalItems.size());
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("标签搜索失败 - 错误: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 转换为终端列表项
     */
    private TerminalListResponse.TerminalItem convertToTerminalItem(TerminalBasicInfo entity) {
        TerminalListResponse.TerminalItem item = new TerminalListResponse.TerminalItem();
        BeanUtils.copyProperties(entity, item);
        return item;
    }
}
