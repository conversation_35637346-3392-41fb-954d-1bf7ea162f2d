package com.unnet.jmanul.business.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.unnet.jmanul.business.entity.MetricThresholdConfig;
import com.unnet.jmanul.business.entity.dto.MetricThresholdConfigRequest;
import com.unnet.jmanul.business.entity.dto.MetricThresholdConfigResponse;
import com.unnet.jmanul.business.entity.dto.OptionDto;

import java.util.List;

/**
 * 指标阈值配置服务接口
 * 
 * <AUTHOR>
 * @since 2024-07-21
 */
public interface IMetricThresholdConfigService extends IService<MetricThresholdConfig> {

    /**
     * 分页查询指标阈值配置
     * 
     * @param current 当前页
     * @param size 每页大小
     * @param metricType 指标类型（可选）
     * @param metricName 指标名称（可选）
     * @param isEnabled 是否启用（可选）
     * @param alertLevel 告警级别（可选）
     * @return 分页结果
     */
    IPage<MetricThresholdConfigResponse> getConfigPage(Long current, Long size, 
                                                      String metricType, String metricName, 
                                                      Boolean isEnabled, String alertLevel);

    /**
     * 根据ID获取配置详情
     * 
     * @param id 配置ID
     * @return 配置详情
     */
    MetricThresholdConfigResponse getConfigById(Long id);

    /**
     * 创建指标阈值配置
     * 
     * @param request 创建请求
     * @param operator 操作人
     * @return 是否成功
     */
    boolean createConfig(MetricThresholdConfigRequest request, String operator);

    /**
     * 更新指标阈值配置
     * 
     * @param id 配置ID
     * @param request 更新请求
     * @param operator 操作人
     * @return 是否成功
     */
    boolean updateConfig(Long id, MetricThresholdConfigRequest request, String operator);

    /**
     * 删除指标阈值配置
     * 
     * @param id 配置ID
     * @return 是否成功
     */
    boolean deleteConfig(Long id);

    /**
     * 批量删除指标阈值配置
     * 
     * @param ids 配置ID列表
     * @return 是否成功
     */
    boolean batchDeleteConfig(List<Long> ids);

    /**
     * 启用/禁用配置
     * 
     * @param id 配置ID
     * @param enabled 是否启用
     * @param operator 操作人
     * @return 是否成功
     */
    boolean toggleConfig(Long id, Boolean enabled, String operator);

    /**
     * 根据指标类型获取配置
     * 
     * @param metricType 指标类型
     * @return 配置信息
     */
    MetricThresholdConfig getConfigByMetricType(String metricType);

    /**
     * 获取所有启用的配置
     * 
     * @return 配置列表
     */
    List<MetricThresholdConfig> getAllEnabledConfigs();

    /**
     * 获取指标类型选项
     *
     * @return 指标类型选项列表
     */
    List<OptionDto> getMetricTypeOptions();

    /**
     * 获取告警级别选项
     *
     * @return 告警级别选项列表
     */
    List<OptionDto> getAlertLevelOptions();
}
