package com.unnet.changan5G.dto.terminal;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 终端列表查询响应
 * 
 * <AUTHOR>
 * @date 2024-07-16
 */
@Data
@Schema(description = "终端列表查询响应")
public class TerminalListResponse {

    @Schema(description = "终端列表")
    private List<TerminalItem> list;

    @Schema(description = "总数")
    private Long total;

    @Schema(description = "当前页码")
    private Integer page;

    @Schema(description = "每页大小")
    private Integer pageSize;

    /**
     * 终端列表项
     */
    @Data
    @Schema(description = "终端列表项")
    public static class TerminalItem {

        @Schema(description = "设备ID")
        private String deviceId;

        @Schema(description = "主机名")
        private String hostname;

        @Schema(description = "MAC地址")
        private String identityMac;

        @Schema(description = "状态：0-离线，1-在线")
        private Integer status;

        @Schema(description = "标签信息")
        private Object tags;

        @Schema(description = "自定义字段")
        private Object customFields;

        @Schema(description = "版本信息")
        private Object appVersion;

        @Schema(description = "过期时间")
        private String expiredDate;

        @Schema(description = "数据来源")
        private String dataSource;

        @Schema(description = "首次注册时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        private LocalDateTime firstRegisterTime;

        @Schema(description = "最后更新时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        private LocalDateTime lastUpdateTime;

        @Schema(description = "数据接收时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        private LocalDateTime receiveTime;
    }
}
