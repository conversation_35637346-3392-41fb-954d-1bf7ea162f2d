package com.unnet.changan5G.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.unnet.changan5G.dto.terminal.TerminalAlertInfo;
import com.unnet.changan5G.entity.TerminalAlertInfoEntity;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 终端告警信息服务接口
 * 
 * <AUTHOR>
 * @date 2024-07-14
 */
public interface TerminalAlertInfoService extends IService<TerminalAlertInfoEntity> {

    /**
     * 保存告警信息
     */
    boolean saveAlertInfo(TerminalAlertInfo terminalAlertInfo);

    /**
     * 批量保存告警信息
     */
    boolean batchSaveAlertInfo(List<TerminalAlertInfo> alertInfoList);

    /**
     * 获取设备活跃告警
     */
    List<TerminalAlertInfoEntity> getActiveAlertsByDeviceId(String deviceId);

    /**
     * 获取指定类型的活跃告警
     */
    TerminalAlertInfoEntity getActiveAlertByDeviceIdAndType(String deviceId, String alertType);

    /**
     * 获取所有活跃告警
     */
    List<TerminalAlertInfoEntity> getAllActiveAlerts();

    /**
     * 获取指定时间范围内的告警
     */
    List<TerminalAlertInfoEntity> getByTimeRange(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 获取指定设备和时间范围内的告警
     */
    List<TerminalAlertInfoEntity> getByDeviceIdAndTimeRange(String deviceId, 
                                                           LocalDateTime startTime, 
                                                           LocalDateTime endTime);

    /**
     * 更新告警状态
     */
    boolean updateAlertStatus(String alertId, String status, String comment);

    /**
     * 确认告警
     */
    boolean acknowledgeAlert(String alertId, String acknowledgedBy);

    /**
     * 批量解决同类型告警
     */
    boolean batchResolveAlertsByType(String deviceId, String alertType, String comment);

    /**
     * 获取告警统计信息
     */
    List<TerminalAlertInfoEntity> getAlertStatistics(LocalDateTime startTime);

    /**
     * 获取未发送通知的告警
     */
    List<TerminalAlertInfoEntity> getUnsentNotificationAlerts();

    /**
     * 更新通知发送状态
     */
    boolean updateNotificationSent(String alertId);

    /**
     * 自动解决已恢复的告警
     */
    boolean autoResolveRecoveredAlerts(String deviceId, String alertType);

    /**
     * 统计活跃告警数量
     */
    int countActiveAlerts();
}
