package com.unnet.jmanul.common.codegen;

import com.baomidou.mybatisplus.generator.FastAutoGenerator;
import com.baomidou.mybatisplus.generator.config.rules.DbColumnType;
import com.unnet.jmanul.common.entity.BaseEntity;

import java.nio.file.Paths;
import java.sql.Types;

public class BusinessCodeGenerator {
    public static void main(String[] args) {
        FastAutoGenerator.create(
                        "**************************************************************************************************************************",
                        "jmanul",
                        "pwd4jmanul2unnet")
                .globalConfig(builder -> {
                    builder.author("jmanul") // 设置作者
                            .enableSwagger() // 开启 swagger 模式
                            .outputDir(Paths.get(System.getProperty("user.dir")) + "/src/main/java"); // 指定输出目录
                })
                .dataSourceConfig(builder ->
                        builder.typeConvertHandler((globalConfig, typeRegistry, metaInfo) -> {
                            int typeCode = metaInfo.getJdbcType().TYPE_CODE;
                            if (typeCode == Types.SMALLINT) {
                                // 自定义类型转换
                                return DbColumnType.INTEGER;
                            }
                            return typeRegistry.getColumnType(metaInfo);
                        })
                )
                .packageConfig(builder ->
                                builder.parent("com.unnet.jmanul") // 设置父包名
                                        .moduleName("business") // 设置父包模块名
                        // .pathInfo(Collections.singletonMap(OutputFile.xml, "D://")) // 设置mapperXml生成路径
                )
                .strategyConfig(builder ->
                        builder.addInclude("sample") // 设置需要生成的表名
                                .addTablePrefix("t_", "c_") // 设置过滤表前缀
                                .entityBuilder()
                                .superClass(BaseEntity.class) //改为自己封装的实体对应的固定项
                                .addIgnoreColumns("created_at", "created_by", "updated_at", "updated_by", "deleted_at")
                                // 开启生成实体时生成字段注解。
                                // 会在实体类的属性前，添加[@TableField("nickname")]
                                .enableTableFieldAnnotation()
                                // 阶段2：Mapper策略配置
                                .mapperBuilder()
                                // 启用 BaseResultMap 生成。
                                // 会在mapper.xml文件生成[通用查询映射结果]配置。
                                .enableBaseResultMap()
                                // 启用 BaseColumnList。
                                // 会在mapper.xml文件生成[通用查询结果列 ]配置
                                .enableBaseColumnList()
                                // 阶段4：Controller策略配置
                                .controllerBuilder()
                                // 会在控制类中加[@RestController]注解。
                                .enableRestStyle()
                                // 开启驼峰转连字符
                                .enableHyphenStyle()
                                .build()
                )
                // .templateEngine(new FreemarkerTemplateEngine()) // 使用Freemarker引擎模板，默认的是Velocity引擎模板
                .execute();
    }

}
