package com.unnet.changan5G.dto.metric;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 磁盘使用信息
 * 
 * <AUTHOR>
 * @date 2024-07-14
 */
@Data
@Schema(description = "磁盘使用信息")
public class DiskUsageInfo {

    @Schema(description = "存储设备名", example = "/dev/mmcblk0p3")
    private String device;

    @Schema(description = "挂载点", example = "/")
    private String mountpoint;

    @Schema(description = "文件系统类型", example = "ext4")
    private String fstype;

    @Schema(description = "总容量（字节）", example = "30602608640")
    private Long total;

    @Schema(description = "已使用容量（字节）", example = "15916904448")
    private Long used;

    @Schema(description = "剩余容量（字节）", example = "13409980416")
    private Long free;

    @Schema(description = "使用率（%）", example = "54.3")
    private Double percent;
}
