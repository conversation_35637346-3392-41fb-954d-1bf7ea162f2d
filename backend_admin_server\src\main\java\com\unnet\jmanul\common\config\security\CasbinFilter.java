package com.unnet.jmanul.common.config.security;

import com.unnet.jmanul.system.service.IUserService;
import com.unnet.jmanul.common.annotation.AnonymousAccess;
import com.unnet.jmanul.common.utils.RequestMethodEnum;
import com.unnet.jmanul.common.utils.jwt.JwtComponent;
import lombok.extern.slf4j.Slf4j;
import org.casbin.jcasbin.main.Enforcer;
import org.springframework.context.ApplicationContext;
import org.springframework.http.HttpStatus;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.util.AntPathMatcher;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.mvc.method.RequestMappingInfo;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.util.*;

import static com.unnet.jmanul.common.rest.ApiDef.ANT_WHITE_LIST;
import static com.unnet.jmanul.common.rest.ApiDef.ANT_WHITE_LIST_ANY;


@Slf4j
public class CasbinFilter implements Filter {

    private final Enforcer enforcer;
    private final IUserService userService;
    private final JwtComponent jwtComponent;
    private final ApplicationContext applicationContext;

    private static final AntPathMatcher pathMatcher = new AntPathMatcher();

    public CasbinFilter(
            Enforcer enforcer,
            IUserService userService,
            JwtComponent jwtComponent,
            ApplicationContext applicationContext
    ) {
        this.enforcer = enforcer;
        this.jwtComponent = jwtComponent;
        this.userService = userService;
        this.applicationContext = applicationContext;
    }

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        log.info("Casbin filter initializing ...");
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
        HttpServletRequest httpServletRequest = (HttpServletRequest) request;

        String requestURI = httpServletRequest.getRequestURI();
        String requestMethod = httpServletRequest.getMethod();

        log.info("CasbinFilter processing request: {} {}", requestMethod, requestURI);

        // 特殊处理登录接口 - 确保登录接口始终允许访问
        if ("POST".equalsIgnoreCase(requestMethod) && "/api/v1/auth/login".equals(requestURI)) {
            log.info("Login request detected, allowing access: {} {}", requestMethod, requestURI);
            chain.doFilter(request, response);
            return;
        }

        // Check ANT_WHITE_LIST
        boolean isAllowAnonymousAccess = ANT_WHITE_LIST.stream().anyMatch(pair -> {
            boolean methodMatches = pair.getLeft().name().equalsIgnoreCase(requestMethod);
            boolean pathMatches = pathMatcher.match(pair.getRight(), requestURI);
            if (methodMatches && pathMatches) {
                log.info("Request matched white list: {} {} -> {} {}", requestMethod, requestURI, pair.getLeft().name(), pair.getRight());
            }
            return methodMatches && pathMatches;
        });

        if (isAllowAnonymousAccess) {
            log.info("Request is allowed by ANT_WHITE_LIST: {} {}", requestMethod, requestURI);
        } else {
            log.info("Request is NOT allowed by ANT_WHITE_LIST: {} {}", requestMethod, requestURI);
        }

        // Check ANT_WHITE_LIST_ANY
        if (!isAllowAnonymousAccess) {
            isAllowAnonymousAccess = ANT_WHITE_LIST_ANY.stream().anyMatch(antPattern ->
                    pathMatcher.match(antPattern, requestURI)
            );
            if (isAllowAnonymousAccess) {
                log.info("Request is allowed by ANT_WHITE_LIST_ANY: {} {}", requestMethod, requestURI);
            }
        }

        // Check annotations
        if (!isAllowAnonymousAccess) {
            RequestMappingHandlerMapping requestMappingHandlerMapping = (RequestMappingHandlerMapping) applicationContext.getBean("requestMappingHandlerMapping");
            Map<RequestMappingInfo, HandlerMethod> handlerMethodMap = requestMappingHandlerMapping.getHandlerMethods();
            Map<String, Set<String>> anonymousUrls = getAnonymousUrl(handlerMethodMap);
            for (Map.Entry<String, Set<String>> entry : anonymousUrls.entrySet()) {
                if (entry.getKey().equalsIgnoreCase(requestMethod)) {
                    if (entry.getValue().contains(requestURI)) {
                        log.debug("Request is allowed by annotation: {} {}", requestMethod, requestURI);
                        isAllowAnonymousAccess = true;
                        break;
                    }
                }
            }
        }

        if (isAllowAnonymousAccess) {
            chain.doFilter(request, response);
        } else {
            Optional<String> token = jwtComponent.getToken(httpServletRequest);
            if (token.isPresent()) {
                Optional<String> u = userService.isAuthenticated(token.get());
                if (u.isPresent()) {
                    String user = u.get();
                    String method = httpServletRequest.getMethod();
                    String path = httpServletRequest.getRequestURI();

                    // 特殊处理：cqcadevice用户跳过权限验证
                    boolean isAuthorized = false;
                    if ("cqcadevice".equals(user)) {
                        log.info("Special user 'cqcadevice' detected, skipping permission check: {} {} {}", user, method, path);
                        isAuthorized = true;
                    } else {
                        isAuthorized = enforcer.enforce(user, path, method);
                    }

                    if (isAuthorized) {
                        log.debug("Request is authorized: {} {} {}", user, method, path);
                        List<String> rolesForUser = enforcer.getRolesForUser(user);
                        SecurityContext securityContext = SecurityContextHolder.getContext();
                        securityContext.setAuthentication(new AuthenticationImpl(u.get(), rolesForUser));
                        HttpSession session = httpServletRequest.getSession();
                        session.setAttribute("SPRING_SECURITY_CONTEXT", securityContext);
                        chain.doFilter(request, response);
                    } else {
                        log.error("Request is not authorized: {} {} {}", user, method, path);
                        HttpServletResponse httpServletResponse = (HttpServletResponse) response;
                        httpServletResponse.getWriter().write("{}");
                        httpServletResponse.setStatus(HttpStatus.FORBIDDEN.value());
                    }
                } else {
                    log.error("Request is not authenticated due to get user by token failed: {}", token.get());
                    HttpServletResponse httpServletResponse = (HttpServletResponse) response;
                    httpServletResponse.getWriter().write("{}");
                    httpServletResponse.setStatus(HttpStatus.FORBIDDEN.value());
                }
            } else {
                log.error("Request is not authenticated due to jwt token not present: {} {}", requestMethod, requestURI);
                HttpServletResponse httpServletResponse = (HttpServletResponse) response;
                httpServletResponse.getWriter().write("{}");
                httpServletResponse.setStatus(HttpStatus.FORBIDDEN.value());
            }
        }

    }

    private Map<String, Set<String>> getAnonymousUrl(Map<RequestMappingInfo, HandlerMethod> handlerMethodMap) {
        Map<String, Set<String>> anonymousUrls = new HashMap<>(8);
        Set<String> get = new HashSet<>();
        Set<String> post = new HashSet<>();
        Set<String> put = new HashSet<>();
        Set<String> patch = new HashSet<>();
        Set<String> delete = new HashSet<>();
        Set<String> all = new HashSet<>();
        for (Map.Entry<RequestMappingInfo, HandlerMethod> infoEntry : handlerMethodMap.entrySet()) {
            HandlerMethod handlerMethod = infoEntry.getValue();
            AnonymousAccess anonymousAccess = handlerMethod.getMethodAnnotation(AnonymousAccess.class);
            if (null != anonymousAccess) {
                List<RequestMethod> requestMethods = new ArrayList<>(infoEntry.getKey().getMethodsCondition().getMethods());
                RequestMethodEnum request = RequestMethodEnum.find(requestMethods.isEmpty() ? RequestMethodEnum.ALL.getType() : requestMethods.get(0).name());
                switch (Objects.requireNonNull(request)) {
                    case GET:
                        if (infoEntry.getKey().getPatternsCondition() != null) {
                            get.addAll(infoEntry.getKey().getPatternsCondition().getPatterns());
                        }
                        break;
                    case POST:
                        if (infoEntry.getKey().getPatternsCondition() != null) {
                            post.addAll(infoEntry.getKey().getPatternsCondition().getPatterns());
                        }
                        break;
                    case PUT:
                        if (infoEntry.getKey().getPatternsCondition() != null) {
                            put.addAll(infoEntry.getKey().getPatternsCondition().getPatterns());
                        }
                        break;
                    case PATCH:
                        if (infoEntry.getKey().getPatternsCondition() != null) {
                            patch.addAll(infoEntry.getKey().getPatternsCondition().getPatterns());
                        }
                        break;
                    case DELETE:
                        if (infoEntry.getKey().getPatternsCondition() != null) {
                            delete.addAll(infoEntry.getKey().getPatternsCondition().getPatterns());
                        }
                        break;
                    default:
                        if (infoEntry.getKey().getPatternsCondition() != null) {
                            all.addAll(infoEntry.getKey().getPatternsCondition().getPatterns());
                        }
                        break;
                }
            }
        }
        anonymousUrls.put(RequestMethodEnum.GET.getType(), get);
        anonymousUrls.put(RequestMethodEnum.POST.getType(), post);
        anonymousUrls.put(RequestMethodEnum.PUT.getType(), put);
        anonymousUrls.put(RequestMethodEnum.PATCH.getType(), patch);
        anonymousUrls.put(RequestMethodEnum.DELETE.getType(), delete);
        anonymousUrls.put(RequestMethodEnum.ALL.getType(), all);
        return anonymousUrls;
    }

    @Override
    public void destroy() {
        log.info("Casbin filter destroy ...");
    }
}
