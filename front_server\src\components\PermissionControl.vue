<template>
  <div v-if="hasPermission">
    <slot></slot>
  </div>
  <div v-else-if="showNoPermission" class="no-permission">
    <el-empty 
      description="您没有权限访问此功能"
      :image-size="100"
    >
      <template #image>
        <el-icon size="100" color="#909399">
          <Lock />
        </el-icon>
      </template>
    </el-empty>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { Lock } from '@element-plus/icons-vue'
import { useAuthStore } from '@/stores/auth'

const props = defineProps({
  // 需要的权限
  permission: {
    type: String,
    required: true
  },
  // 是否显示无权限提示
  showNoPermission: {
    type: Boolean,
    default: false
  }
})

const authStore = useAuthStore()

// 检查权限
const hasPermission = computed(() => {
  return authStore.hasPermission(props.permission)
})
</script>

<style scoped>
.no-permission {
  padding: 40px;
  text-align: center;
}
</style>
