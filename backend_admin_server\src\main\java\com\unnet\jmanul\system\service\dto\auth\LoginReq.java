package com.unnet.jmanul.system.service.dto.auth;

import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LoginReq {

    @NotNull
    @Size(min = 3, max = 32)
    @Schema(minLength = 3, maxLength = 32, pattern = "[a-zA-Z0-9]+", defaultValue = "test", required = true)
    private String username;

    @NotNull
    @Size(min = 3, max = 32)
    @Schema(defaultValue = "test", required = true)
    private String credential;

    @NotNull
    @Schema(defaultValue = "123456789", required = true)
    @Parameter(name = "验证码ID", description = "验证码ID", required = true)
    private String captchaId;

    @NotNull
    @Schema(defaultValue = "666666", required = true)
    private String captchaCode;
}
