# 适用于Docker Alpine环境的nginx.conf
worker_processes auto;
pid /var/run/nginx.pid;

events {
    worker_connections 768;
}

http {
    ##
    # Basic Settings
    ##
    sendfile on;
    tcp_nopush on;
    types_hash_max_size 2048;
    server_tokens off;

    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    ##
    # Logging Settings
    ##
    access_log /var/log/nginx/access.log;
    error_log /var/log/nginx/error.log;

    ##
    # Gzip Settings
    ##
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;

    server {
        listen 80;
        server_name localhost;
        client_max_body_size 256M;
        server_tokens off;
        access_log /var/log/nginx/access.log;

        # 前端静态文件
        location / {
            root /usr/share/nginx/html;  # ✅ 修复：Docker容器中的正确路径
            index index.html index.htm;
            try_files $uri $uri/ /index.html;  # ✅ 添加：支持Vue Router
            
            # 移动端检测（如果需要不同的前端版本）
            # if ($http_user_agent ~* "(mobile|nokia|iphone|ipad|android|samsung|htc|blackberry)") {
            #     root /usr/share/nginx/html/mobile;
            # }
        }

        # 管理后台API代理 - backend_admin_server (8080)
        location /admin-api/ {
            proxy_pass http://********:8080/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;

            # 增加超时配置
            proxy_connect_timeout 60s;
            proxy_send_timeout 300s;
            proxy_read_timeout 300s;

            # 缓冲配置
            proxy_buffering on;
            proxy_buffer_size 8k;
            proxy_buffers 16 8k;
            proxy_busy_buffers_size 16k;
            proxy_max_temp_file_size 1024m;

            # CORS配置
            add_header Access-Control-Allow-Origin *;
            add_header Access-Control-Allow-Methods 'GET, POST, PUT, DELETE, OPTIONS';
            add_header Access-Control-Allow-Headers 'DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization,X-API-KEY';
            add_header Access-Control-Expose-Headers 'Content-Disposition,Content-Length,Content-Type';

            if ($request_method = 'OPTIONS') {
                return 204;
            }
        }

        # 数据服务API代理 - backend_server (8081)
        location /data-api/ {
            proxy_pass http://********:8081/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;

            # 超时配置
            proxy_connect_timeout 30s;
            proxy_send_timeout 30s;
            proxy_read_timeout 30s;

            # 缓冲配置
            proxy_buffering on;
            proxy_buffer_size 4k;
            proxy_buffers 8 4k;

            # CORS配置
            add_header Access-Control-Allow-Origin *;
            add_header Access-Control-Allow-Methods 'GET, POST, PUT, DELETE, OPTIONS';
            add_header Access-Control-Allow-Headers 'DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization,X-API-KEY';

            if ($request_method = 'OPTIONS') {
                return 204;
            }
        }

        # SSE支持 - 用于实时数据推送
        location /data-api/api/device/notifications/sse {
            proxy_pass http://********:8081/api/device/notifications/sse;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;

            # SSE关键配置 - 确保事件流正确传输
            proxy_buffering off;
            proxy_cache off;
            proxy_request_buffering off;
            proxy_http_version 1.1;

            # 关键修复：正确设置Connection头和缓存控制
            proxy_set_header Connection "keep-alive";
            proxy_set_header Cache-Control "no-cache";

            # 禁用分块传输，让SSE事件立即传输
            chunked_transfer_encoding off;

            # 设置小缓冲区，减少延迟
            proxy_buffer_size 4k;
            proxy_buffers 8 4k;
            proxy_max_temp_file_size 0;

            # 超时配置 - SSE需要长连接
            proxy_connect_timeout 60s;
            proxy_send_timeout 3600s;
            proxy_read_timeout 3600s;

            # 关键：添加SSE专用头，禁用nginx缓冲
            add_header X-Accel-Buffering no;

            # CORS配置
            add_header Access-Control-Allow-Origin *;
            add_header Access-Control-Allow-Methods 'GET, POST, PUT, DELETE, OPTIONS';
            add_header Access-Control-Allow-Headers 'DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization,X-API-KEY';
        }

        # 静态资源缓存
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            root /usr/share/nginx/html;
            expires 1y;
            add_header Cache-Control "public, immutable";
        }

        # 错误页面
        error_page 500 502 503 504 /50x.html;
        location = /50x.html {
            root /usr/share/nginx/html;
        }
    }
}