package com.unnet.jmanul.common.config;

import com.maxmind.db.Reader;
import com.maxmind.geoip2.DatabaseReader;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;

import java.io.IOException;
import java.io.InputStream;

@Configuration
@RequiredArgsConstructor
public class GeoConfig {

    private final ResourceLoader resourceLoader;

    @Bean
    public DatabaseReader cityDatabaseReader() throws IOException {
        Resource resource = resourceLoader.getResource("classpath:maxmind/GeoLite2-City.mmdb");
        InputStream dbStream = resource.getInputStream();

        return new DatabaseReader.Builder(dbStream)
                .fileMode(Reader.FileMode.MEMORY)
                .build();
    }
}
