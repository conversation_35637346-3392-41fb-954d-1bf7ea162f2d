# ========================================
# 长安5G管理平台 - 数据收集服务生产环境配置
# ========================================

# 生产环境配置
server:
  port: 8081
  servlet:
    context-path: /api
    encoding:
      charset: UTF-8
      force: true
      enabled: true
  # 支持HTTP和HTTPS
  forward-headers-strategy: native
  # Tomcat配置优化
  tomcat:
    uri-encoding: UTF-8
    max-connections: ${SERVER_MAX_CONNECTIONS:8192}
    threads:
      max: ${SERVER_MAX_THREADS:200}
      min-spare: ${SERVER_MIN_SPARE_THREADS:10}

spring:
  # 数据库配置 - 支持环境变量覆盖
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: jdbc:${DB_TYPE:mysql}://${DB_HOST:********}:${DB_PORT:3306}/${DB_NAME:changan_5g}?useUnicode=true&characterEncoding=utf-8&useSSL=false&serverTimezone=Asia/Shanghai&allowPublicKeyRetrieval=true
    username: ${DB_USERNAME:root}
    password: ${DB_PASSWORD:ABcd12#$}
    hikari:
      maximum-pool-size: ${DB_POOL_MAX_SIZE:20}
      minimum-idle: ${DB_POOL_MIN_IDLE:5}
      connection-timeout: ${DB_CONNECTION_TIMEOUT:30000}
      idle-timeout: ${DB_IDLE_TIMEOUT:600000}
      max-lifetime: ${DB_MAX_LIFETIME:1800000}
      leak-detection-threshold: ${DB_LEAK_DETECTION:60000}

  # Redis集群配置 - 主从模式
  redis:
    host: ${REDIS_HOST:********}
    port: ${REDIS_PORT:7000}
    database: ${REDIS_DB:0}
    password: ${REDIS_PASSWORD:Changan5g.redis}
    timeout: ${REDIS_TIMEOUT:5000ms}

    # 连接池配置
    lettuce:
      pool:
        max-active: ${REDIS_POOL_MAX_ACTIVE:20}
        max-idle: ${REDIS_POOL_MAX_IDLE:10}
        min-idle: ${REDIS_POOL_MIN_IDLE:5}
        max-wait: ${REDIS_POOL_MAX_WAIT:5000ms}
      cluster:
        refresh:
          adaptive: true
          period: 30s

    # Redis集群节点配置（如果使用集群模式，取消注释）
    cluster:
      nodes:
        - ********:7000
        - ********:7001
        - ********:7000
        - ********:7001
        - ********:7000
        - ********:7001
      max-redirects: 3

  # Elasticsearch集群配置
  elasticsearch:
    uris:
      - ********:9200
      - ********:9200
      - ********:9200
    username: ${ES_USERNAME:elastic}
    password: ${ES_PASSWORD:Changan5g.es}
    connection-timeout: ${ES_CONNECTION_TIMEOUT:10s}
    socket-timeout: ${ES_SOCKET_TIMEOUT:30s}
    # 集群相关配置
    cluster-name: ${ES_CLUSTER_NAME:changan5g-cluster}

  # Kafka集群配置 - 带认证
  kafka:
    bootstrap-servers:
      - ********:9092
      - ********:9092
      - ********:9092

    # 安全认证配置
    security:
      protocol: SASL_PLAINTEXT

    # SASL配置
    properties:
      sasl.mechanism: PLAIN
      sasl.jaas.config: org.apache.kafka.common.security.plain.PlainLoginModule required username="${KAFKA_USERNAME:admin}" password="${KAFKA_PASSWORD:Changan5g.kafka}";
      security.protocol: SASL_PLAINTEXT

    # 生产者配置
    producer:
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.apache.kafka.common.serialization.StringSerializer
      acks: ${KAFKA_PRODUCER_ACKS:1}
      retries: ${KAFKA_PRODUCER_RETRIES:3}
      batch-size: ${KAFKA_PRODUCER_BATCH_SIZE:16384}
      linger-ms: ${KAFKA_PRODUCER_LINGER_MS:5}
      buffer-memory: ${KAFKA_PRODUCER_BUFFER_MEMORY:33554432}
      compression-type: ${KAFKA_PRODUCER_COMPRESSION:snappy}

    # 消费者配置
    consumer:
      group-id: ${KAFKA_CONSUMER_GROUP:changan5g-data-consumer}
      auto-offset-reset: ${KAFKA_CONSUMER_OFFSET_RESET:earliest}
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      enable-auto-commit: ${KAFKA_CONSUMER_AUTO_COMMIT:false}
      max-poll-records: ${KAFKA_CONSUMER_MAX_POLL_RECORDS:500}
      fetch-min-size: ${KAFKA_CONSUMER_FETCH_MIN_SIZE:1}
      fetch-max-wait: ${KAFKA_CONSUMER_FETCH_MAX_WAIT:500ms}
      session-timeout-ms: ${KAFKA_CONSUMER_SESSION_TIMEOUT:30000}
      heartbeat-interval-ms: ${KAFKA_CONSUMER_HEARTBEAT_INTERVAL:3000}

    # 监听器配置
    listener:
      ack-mode: ${KAFKA_LISTENER_ACK_MODE:manual_immediate}
      missing-topics-fatal: ${KAFKA_LISTENER_MISSING_TOPICS_FATAL:false}
      concurrency: ${KAFKA_LISTENER_CONCURRENCY:3}

  # MinIO集群配置 - 生产环境
  minio:
    # MinIO集群节点配置（负载均衡）
    hosts:
      - http://********:9000
      - http://********:9000
      - http://********:9000
    # 主节点配置（用于兼容单节点配置的代码）
    host: ${MINIO_HOST:http://********:9000}
    url: ${MINIO_URL:${spring.minio.host}/${spring.minio.bucket}/}
    # 认证配置
    access-key: ${MINIO_ACCESS_KEY:admin}
    secret-key: ${MINIO_SECRET_KEY:Changan5g.minio}
    # 存储桶配置
    bucket: ${MINIO_BUCKET:log-files}
    # 集群配置
    cluster:
      enabled: ${MINIO_CLUSTER_ENABLED:true}
      retry-attempts: ${MINIO_CLUSTER_RETRY_ATTEMPTS:3}
      retry-delay-ms: ${MINIO_CLUSTER_RETRY_DELAY:1000}
      health-check-interval-seconds: ${MINIO_CLUSTER_HEALTH_CHECK_INTERVAL:30}

  # 文件上传配置 - 生产环境
  servlet:
    multipart:
      max-file-size: ${FILE_UPLOAD_MAX_SIZE:500MB}
      max-request-size: ${FILE_UPLOAD_MAX_REQUEST_SIZE:500MB}
      file-size-threshold: ${FILE_UPLOAD_THRESHOLD:10KB}
      location: ${FILE_UPLOAD_TEMP_DIR:${java.io.tmpdir}}
      resolve-lazily: ${FILE_UPLOAD_RESOLVE_LAZILY:false}

# 应用自定义配置
app:
  # API密钥配置
  api:
    keys:
      - key: "${API_KEY_1:api-SvardHNxjL87TMHbgS2JFDpUsyi1iq9e}"
        clientId: "${API_CLIENT_1:haiyun-ai-live}"
      - key: "${API_KEY_2:api-key-client2}"
        clientId: "${API_CLIENT_2:client2}"
  security:
    # 防重放攻击配置
    replay-protection:
      enabled: ${SECURITY_REPLAY_PROTECTION_ENABLED:false}
      timestamp-tolerance-seconds: ${SECURITY_TIMESTAMP_TOLERANCE:300}
      nonce-cache-minutes: ${SECURITY_NONCE_CACHE_MINUTES:10}

# 生产环境禁用Swagger
springdoc:
  api-docs:
    enabled: false
  swagger-ui:
    enabled: false

# 日志配置
logging:
  level:
    root: ${LOG_LEVEL_ROOT:INFO}
    com.unnet.changan5G: ${LOG_LEVEL_APP:INFO}
    org.springframework.kafka: ${LOG_LEVEL_KAFKA:WARN}
    org.elasticsearch: ${LOG_LEVEL_ES:WARN}
    org.springframework.data.redis: ${LOG_LEVEL_REDIS:WARN}
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: ${LOG_FILE_PATH:./logs/changan5g-data.log}
    max-size: ${LOG_FILE_MAX_SIZE:100MB}
    max-history: ${LOG_FILE_MAX_HISTORY:30}

# 监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: when-authorized
  metrics:
    export:
      prometheus:
        enabled: true