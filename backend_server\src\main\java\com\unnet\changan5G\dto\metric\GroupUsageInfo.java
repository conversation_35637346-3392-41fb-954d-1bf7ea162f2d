package com.unnet.changan5G.dto.metric;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * CPE集合使用信息
 * 
 * <AUTHOR>
 * @date 2024-07-14
 */
@Data
@Schema(description = "CPE集合使用信息")
public class GroupUsageInfo {

    @Schema(description = "组ID", example = "105")
    @JsonProperty("group_id")
    private Integer groupId;

    @Schema(description = "总包速率", example = "0.0")
    @JsonProperty("pps_total")
    private Double ppsTotal;

    @Schema(description = "总带宽速率", example = "0.0")
    @JsonProperty("bps_total")
    private Double bpsTotal;

    @Schema(description = "桶信息列表")
    private List<BucketInfo> buckets;

    /**
     * 桶信息
     */
    @Data
    @Schema(description = "桶信息")
    public static class BucketInfo {

        @Schema(description = "桶编号", example = "0")
        private Integer bucket;

        @Schema(description = "连接数", example = "0")
        private Integer conn;

        @Schema(description = "包速率", example = "0.0")
        private Double pps;

        @Schema(description = "带宽速率", example = "0.0")
        private Double bps;
    }
}
