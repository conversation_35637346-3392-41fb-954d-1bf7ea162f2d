package com.unnet.jmanul.business.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.unnet.jmanul.business.entity.TerminalAlertInfo;
import com.unnet.jmanul.business.entity.dto.AlertListRequest;
import com.unnet.jmanul.business.mapper.TerminalAlertInfoMapper;
import com.unnet.jmanul.business.service.TerminalAlertInfoService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * 终端告警信息服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TerminalAlertInfoServiceImpl extends ServiceImpl<TerminalAlertInfoMapper, TerminalAlertInfo> 
        implements TerminalAlertInfoService {

    private final TerminalAlertInfoMapper terminalAlertInfoMapper;

    @Override
    public IPage<TerminalAlertInfo> getAlertPage(AlertListRequest request) {
        try {
            log.info("分页查询告警列表 - 请求参数: {}", request);

            Page<TerminalAlertInfo> page = new Page<>(request.getPage(), request.getPageSize());
            IPage<TerminalAlertInfo> result = terminalAlertInfoMapper.selectAlertPage(page, request);

            log.info("查询完成 - 总数: {}, 当前页数据量: {}", result.getTotal(), result.getRecords().size());
            return result;

        } catch (Exception e) {
            log.error("分页查询告警列表失败 - 请求参数: {}, 错误: {}", request, e.getMessage(), e);

            // 如果是表不存在的错误，返回空结果
            if (e.getMessage() != null && e.getMessage().contains("doesn't exist")) {
                log.warn("告警表不存在，返回空结果");
                Page<TerminalAlertInfo> emptyPage = new Page<>(request.getPage(), request.getPageSize());
                emptyPage.setTotal(0);
                return emptyPage;
            }

            // 其他错误也返回空结果，避免前端报错
            Page<TerminalAlertInfo> emptyPage = new Page<>(request.getPage(), request.getPageSize());
            emptyPage.setTotal(0);
            return emptyPage;
        }
    }

    @Override
    public TerminalAlertInfo getAlertById(String alertId) {
        try {
            log.info("根据告警ID获取告警详情 - 告警ID: {}", alertId);
            
            LambdaQueryWrapper<TerminalAlertInfo> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(TerminalAlertInfo::getAlertId, alertId);
            
            TerminalAlertInfo alert = this.getOne(wrapper);
            if (alert != null) {
                log.info("获取告警详情成功 - 告警ID: {}, 设备MAC: {}", alertId, alert.getIdentityMac());
            } else {
                log.warn("未找到告警信息 - 告警ID: {}", alertId);
            }
            
            return alert;
            
        } catch (Exception e) {
            log.error("获取告警详情失败 - 告警ID: {}, 错误: {}", alertId, e.getMessage(), e);
            return null;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean acknowledgeAlert(String alertId, String acknowledgedBy) {
        try {
            log.info("确认告警 - 告警ID: {}, 确认人: {}", alertId, acknowledgedBy);
            
            LocalDateTime now = LocalDateTime.now();
            int result = terminalAlertInfoMapper.acknowledgeAlert(alertId, now, acknowledgedBy, now);
            
            if (result > 0) {
                log.info("确认告警成功 - 告警ID: {}, 确认人: {}", alertId, acknowledgedBy);
                return true;
            } else {
                log.warn("确认告警失败，未找到对应的告警 - 告警ID: {}", alertId);
                return false;
            }
            
        } catch (Exception e) {
            log.error("确认告警失败 - 告警ID: {}, 确认人: {}, 错误: {}", alertId, acknowledgedBy, e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean resolveAlert(String alertId, String resolvedBy, String resolveComment) {
        try {
            log.info("解决告警 - 告警ID: {}, 解决人: {}, 备注: {}", alertId, resolvedBy, resolveComment);
            
            LocalDateTime now = LocalDateTime.now();
            int result = terminalAlertInfoMapper.resolveAlert(alertId, now, resolveComment, now);
            
            if (result > 0) {
                log.info("解决告警成功 - 告警ID: {}, 解决人: {}", alertId, resolvedBy);
                return true;
            } else {
                log.warn("解决告警失败，未找到对应的告警 - 告警ID: {}", alertId);
                return false;
            }
            
        } catch (Exception e) {
            log.error("解决告警失败 - 告警ID: {}, 解决人: {}, 错误: {}", alertId, resolvedBy, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean sendDingTalkNotification(String alertId) {
        try {
            log.info("发送钉钉通知 - 告警ID: {}", alertId);
            
            // 获取告警信息
            TerminalAlertInfo alert = getAlertById(alertId);
            if (alert == null) {
                log.warn("发送钉钉通知失败，未找到告警信息 - 告警ID: {}", alertId);
                return false;
            }
            
            // TODO: 实现钉钉通知发送逻辑
            // 这里可以集成钉钉机器人API或其他通知方式
            
            // 更新通知状态
            LocalDateTime now = LocalDateTime.now();
            int result = terminalAlertInfoMapper.updateNotificationStatus(alertId, true, now, now);
            
            if (result > 0) {
                log.info("钉钉通知发送成功 - 告警ID: {}", alertId);
                return true;
            } else {
                log.warn("更新通知状态失败 - 告警ID: {}", alertId);
                return false;
            }
            
        } catch (Exception e) {
            log.error("发送钉钉通知失败 - 告警ID: {}, 错误: {}", alertId, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public Map<String, Object> getAlertStatistics() {
        try {
            log.info("获取告警统计信息");

            Map<String, Object> statistics = new HashMap<>();

            // 获取各种状态的告警数量
            int activeCount = terminalAlertInfoMapper.countActiveAlerts();
            int resolvedCount = terminalAlertInfoMapper.countResolvedAlerts();
            int acknowledgedCount = terminalAlertInfoMapper.countAcknowledgedAlerts();

            statistics.put("activeAlerts", activeCount);
            statistics.put("resolvedAlerts", resolvedCount);
            statistics.put("acknowledgedAlerts", acknowledgedCount);
            statistics.put("totalAlerts", activeCount + resolvedCount + acknowledgedCount);
            // 按类型统计活跃告警
            Map<String, Integer> alertsByType = new HashMap<>();
            // 使用新的中文告警类型列表
            String[] alertTypes = {"CPU告警", "内存告警", "磁盘告警", "数据磁盘告警", "系统磁盘告警", "授权过期告警"};
            for (String type : alertTypes) {
                int count = terminalAlertInfoMapper.countActiveAlertsByType(type);
                alertsByType.put(type, count);
            }
            statistics.put("alertsByType", alertsByType);

            log.info("获取告警统计信息成功 - 活跃: {}, 已解决: {}, 已确认: {}",
                    activeCount, resolvedCount, acknowledgedCount);

            return statistics;

        } catch (Exception e) {
            log.error("获取告警统计信息失败 - 错误: {}", e.getMessage(), e);

            // 如果表不存在，返回默认统计信息
            Map<String, Object> defaultStats = new HashMap<>();
            defaultStats.put("activeAlerts", 0);
            defaultStats.put("resolvedAlerts", 0);
            defaultStats.put("acknowledgedAlerts", 0);
            defaultStats.put("totalAlerts", 0);

            Map<String, Integer> alertsByType = new HashMap<>();
            // 使用新的中文告警类型列表
            String[] alertTypes = {"CPU告警", "内存告警", "磁盘告警", "数据磁盘告警", "系统磁盘告警", "授权过期告警"};
            for (String type : alertTypes) {
                alertsByType.put(type, 0);
            }
            defaultStats.put("alertsByType", alertsByType);

            return defaultStats;
        }
    }

    @Override
    public int getActiveAlertCountByIdentityMac(String identityMac) {
        try {
            return terminalAlertInfoMapper.countActiveAlertsByIdentityMac(identityMac);
        } catch (Exception e) {
            log.error("获取设备活跃告警数量失败 - 设备MAC: {}, 错误: {}", identityMac, e.getMessage(), e);
            return 0;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createAlert(TerminalAlertInfo alert) {
        try {
            log.info("创建新告警 - 设备ID: {}, 告警类型: {}", alert.getIdentityMac(), alert.getAlertType());
            
            // 设置告警ID
            if (!StringUtils.hasText(alert.getAlertId())) {
                alert.setAlertId(UUID.randomUUID().toString());
            }
            
            // 设置默认状态
            if (!StringUtils.hasText(alert.getAlertStatus())) {
                alert.setAlertStatus(TerminalAlertInfo.AlertStatus.ACTIVE.getCode());
            }
            
            // 设置告警时间
            if (alert.getAlertTime() == null) {
                alert.setAlertTime(LocalDateTime.now());
            }
            
            boolean result = this.save(alert);
            
            if (result) {
                log.info("创建告警成功 - 告警ID: {}, 设备mac: {}", alert.getAlertId(), alert.getIdentityMac());
            } else {
                log.warn("创建告警失败 - 设备ID: {}, 告警类型: {}", alert.getIdentityMac(), alert.getAlertType());
            }
            
            return result;
            
        } catch (Exception e) {
            log.error("创建告警失败 - 设备MAC: {}, 告警类型: {}, 错误: {}",
                    alert.getIdentityMac(), alert.getAlertType(), e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean hasActiveAlert(String identityMac, String alertType) {
        try {
            TerminalAlertInfo alert = terminalAlertInfoMapper.selectLatestActiveAlert(identityMac, alertType);
            return alert != null;
        } catch (Exception e) {
            log.error("检查活跃告警失败 - 设备MAC: {}, 告警类型: {}, 错误: {}",
                    identityMac, alertType, e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean cleanHistoryAlerts(int keepDays) {
        try {
            log.info("清理历史告警数据 - 保留天数: {}", keepDays);
            
            LocalDateTime beforeTime = LocalDateTime.now().minusDays(keepDays);
            int deletedCount = terminalAlertInfoMapper.deleteHistoryAlerts(beforeTime);
            
            log.info("清理历史告警数据完成 - 删除数量: {}, 保留天数: {}", deletedCount, keepDays);
            return deletedCount >= 0;
            
        } catch (Exception e) {
            log.error("清理历史告警数据失败 - 保留天数: {}, 错误: {}", keepDays, e.getMessage(), e);
            return false;
        }
    }
}
