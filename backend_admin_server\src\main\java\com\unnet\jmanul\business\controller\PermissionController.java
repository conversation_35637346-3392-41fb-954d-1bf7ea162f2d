package com.unnet.jmanul.business.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.casbin.jcasbin.main.Enforcer;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 权限管理控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/admin/permissions")
@RequiredArgsConstructor
@Api(tags = "权限管理")
public class PermissionController {

    private final Enforcer enforcer;

    /**
     * 重新加载权限规则
     */
    @PostMapping("/reload")
    @ApiOperation(value = "重新加载权限规则", notes = "从数据库重新加载所有权限规则")
    public ResponseEntity<Map<String, Object>> reloadPermissions() {
        try {
            log.info("开始重新加载权限规则...");
            
            // 重新加载权限规则
            enforcer.loadPolicy();
            
            // 添加告警管理权限（如果不存在）
            addAlertPermissionsIfNotExists();
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "权限规则重新加载成功");
            result.put("timestamp", System.currentTimeMillis());
            
            log.info("权限规则重新加载完成");
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            log.error("重新加载权限规则失败: {}", e.getMessage(), e);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "权限规则重新加载失败: " + e.getMessage());
            result.put("timestamp", System.currentTimeMillis());
            
            return ResponseEntity.internalServerError().body(result);
        }
    }

    /**
     * 获取当前权限规则
     */
    @GetMapping("/rules")
    @ApiOperation(value = "获取当前权限规则", notes = "获取当前系统中的所有权限规则")
    public ResponseEntity<Map<String, Object>> getPermissionRules() {
        try {
            log.info("获取当前权限规则...");
            
            List<List<String>> policies = enforcer.getPolicy();
            List<List<String>> groupingPolicies = enforcer.getGroupingPolicy();
            
            Map<String, Object> result = new HashMap<>();
            result.put("policies", policies);
            result.put("groupingPolicies", groupingPolicies);
            result.put("totalPolicies", policies.size());
            result.put("totalGroupingPolicies", groupingPolicies.size());
            result.put("timestamp", System.currentTimeMillis());
            
            log.info("获取权限规则完成 - 策略数量: {}, 角色关系数量: {}", 
                    policies.size(), groupingPolicies.size());
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            log.error("获取权限规则失败: {}", e.getMessage(), e);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "获取权限规则失败: " + e.getMessage());
            result.put("timestamp", System.currentTimeMillis());
            
            return ResponseEntity.internalServerError().body(result);
        }
    }

    /**
     * 检查特定权限
     */
    @GetMapping("/check")
    @ApiOperation(value = "检查特定权限", notes = "检查用户是否有特定资源的访问权限")
    public ResponseEntity<Map<String, Object>> checkPermission(
            @RequestParam String user,
            @RequestParam String resource,
            @RequestParam String action) {
        
        try {
            log.info("检查权限 - 用户: {}, 资源: {}, 操作: {}", user, resource, action);
            
            boolean hasPermission = enforcer.enforce(user, resource, action);
            
            Map<String, Object> result = new HashMap<>();
            result.put("user", user);
            result.put("resource", resource);
            result.put("action", action);
            result.put("hasPermission", hasPermission);
            result.put("timestamp", System.currentTimeMillis());
            
            log.info("权限检查结果 - 用户: {}, 资源: {}, 操作: {}, 结果: {}", 
                    user, resource, action, hasPermission);
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            log.error("权限检查失败: {}", e.getMessage(), e);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "权限检查失败: " + e.getMessage());
            result.put("timestamp", System.currentTimeMillis());
            
            return ResponseEntity.internalServerError().body(result);
        }
    }

    /**
     * 添加告警管理权限（如果不存在）
     */
    private void addAlertPermissionsIfNotExists() {
        try {
            // 检查admin是否有告警权限
            boolean hasAlertPermission = enforcer.hasPolicy("admin", "/api/v1/admin/alerts*", "GET");
            
            if (!hasAlertPermission) {
                log.info("添加告警管理权限...");
                
                // 为admin角色添加告警管理权限
                enforcer.addPolicy("admin", "/api/v1/admin/alerts*", "GET");
                enforcer.addPolicy("admin", "/api/v1/admin/alerts*", "POST");
                enforcer.addPolicy("admin", "/api/v1/admin/alerts*", "PUT");
                enforcer.addPolicy("admin", "/api/v1/admin/alerts*", "DELETE");
                
                // 为auditor角色添加告警查看权限
                enforcer.addPolicy("auditor", "/api/v1/admin/alerts*", "GET");
                
                // 为user角色添加告警查看权限
                enforcer.addPolicy("user", "/api/v1/admin/alerts*", "GET");
                
                // 保存权限规则到数据库
                enforcer.savePolicy();
                
                log.info("告警管理权限添加完成");
            } else {
                log.info("告警管理权限已存在，跳过添加");
            }
            
        } catch (Exception e) {
            log.error("添加告警管理权限失败: {}", e.getMessage(), e);
        }
    }
}
