package com.unnet.jmanul.business.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.unnet.jmanul.business.entity.UserRole;
import com.unnet.jmanul.business.mapper.UserRoleMapper;
import com.unnet.jmanul.business.service.IUserRoleService;
import com.unnet.jmanul.system.entity.User;
import com.unnet.jmanul.system.service.IUserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.casbin.jcasbin.main.Enforcer;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * 用户角色关系服务实现
 * 
 * <AUTHOR>
 * @date 2024-07-16
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class UserRoleServiceImpl extends ServiceImpl<UserRoleMapper, UserRole> implements IUserRoleService {

    private final UserRoleMapper userRoleMapper;
    private final Enforcer enforcer;
    private final IUserService userService;

    @Override
    public List<UserRole> getRolesByUserId(Long userId) {
        return userRoleMapper.selectByUserId(userId);
    }

    @Override
    public List<UserRole> getUsersByRoleName(String roleName) {
        return userRoleMapper.selectByRoleName(roleName);
    }

    @Override
    public boolean hasRole(Long userId, String roleName) {
        int count = userRoleMapper.countByUserIdAndRoleName(userId, roleName);
        return count > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean assignRole(Long userId, String roleName) {
        try {
            // 检查是否已经存在
            if (hasRole(userId, roleName)) {
                log.info("用户已拥有该角色 - 用户ID: {}, 角色: {}", userId, roleName);
                // 确保Casbin中也有对应的角色关系
                syncUserRoleToCasbin(userId, roleName);
                return true;
            }

            UserRole userRole = new UserRole();
            userRole.setUserId(userId);
            userRole.setRoleName(roleName);

            boolean result = save(userRole);
            if (result) {
                log.info("分配角色成功 - 用户ID: {}, 角色: {}", userId, roleName);
                // 同步到Casbin
                syncUserRoleToCasbin(userId, roleName);
            }
            return result;
        } catch (Exception e) {
            log.error("分配角色失败 - 用户ID: {}, 角色: {}, 错误: {}", userId, roleName, e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeRole(Long userId, String roleName) {
        try {
            QueryWrapper<UserRole> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("user_id", userId)
                       .eq("role_name", roleName)
                       .eq("deleted_at", 0);

            boolean result = remove(queryWrapper);
            if (result) {
                log.info("移除角色成功 - 用户ID: {}, 角色: {}", userId, roleName);
                // 从Casbin中移除角色关系
                removeUserRoleFromCasbin(userId, roleName);
            }
            return result;
        } catch (Exception e) {
            log.error("移除角色失败 - 用户ID: {}, 角色: {}, 错误: {}", userId, roleName, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public String getPrimaryRole(Long userId) {
        List<UserRole> roles = getRolesByUserId(userId);
        if (roles.isEmpty()) {
            return "user"; // 默认角色
        }

        // 优先返回admin角色
        for (UserRole role : roles) {
            if ("admin".equals(role.getRoleName())) {
                return "admin";
            }
        }

        // 返回第一个角色
        return roles.get(0).getRoleName();
    }

    /**
     * 同步用户角色关系到Casbin
     */
    private void syncUserRoleToCasbin(Long userId, String roleName) {
        try {
            // 获取用户信息
            Optional<User> userOpt = userService.getById(userId) != null ?
                Optional.of(userService.getById(userId)) : Optional.empty();

            if (userOpt.isPresent()) {
                String username = userOpt.get().getUsername();

                // 检查Casbin中是否已存在该用户角色关系
                if (!enforcer.hasGroupingPolicy(username, roleName)) {
                    // 添加用户角色关系到Casbin
                    boolean added = enforcer.addGroupingPolicy(username, roleName);
                    if (added) {
                        log.info("成功添加Casbin用户角色关系 - 用户: {}, 角色: {}", username, roleName);
                        // 保存策略到数据库
                        enforcer.savePolicy();
                    } else {
                        log.warn("添加Casbin用户角色关系失败 - 用户: {}, 角色: {}", username, roleName);
                    }
                } else {
                    log.debug("Casbin中已存在用户角色关系 - 用户: {}, 角色: {}", username, roleName);
                }
            } else {
                log.error("用户不存在，无法同步到Casbin - 用户ID: {}", userId);
            }
        } catch (Exception e) {
            log.error("同步用户角色到Casbin失败 - 用户ID: {}, 角色: {}, 错误: {}", userId, roleName, e.getMessage(), e);
        }
    }

    /**
     * 从Casbin中移除用户角色关系
     */
    private void removeUserRoleFromCasbin(Long userId, String roleName) {
        try {
            // 获取用户信息
            Optional<User> userOpt = userService.getById(userId) != null ?
                Optional.of(userService.getById(userId)) : Optional.empty();

            if (userOpt.isPresent()) {
                String username = userOpt.get().getUsername();

                // 从Casbin中移除用户角色关系
                boolean removed = enforcer.removeGroupingPolicy(username, roleName);
                if (removed) {
                    log.info("成功移除Casbin用户角色关系 - 用户: {}, 角色: {}", username, roleName);
                    // 保存策略到数据库
                    enforcer.savePolicy();
                } else {
                    log.warn("移除Casbin用户角色关系失败 - 用户: {}, 角色: {}", username, roleName);
                }
            } else {
                log.error("用户不存在，无法从Casbin中移除 - 用户ID: {}", userId);
            }
        } catch (Exception e) {
            log.error("从Casbin移除用户角色失败 - 用户ID: {}, 角色: {}, 错误: {}", userId, roleName, e.getMessage(), e);
        }
    }
}
