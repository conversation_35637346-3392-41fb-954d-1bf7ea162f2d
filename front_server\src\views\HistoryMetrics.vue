<template>
  <div class="history-metrics">
    <!-- 面包屑导航 -->
    <div class="breadcrumb">
      <router-link to="/terminals" class="breadcrumb-link">终端管理</router-link>
      <span class="breadcrumb-separator">></span>
      <span class="breadcrumb-current">历史指标</span>
    </div>

    <div class="card">
      <div class="card-header">
        <h3>终端历史指标 - <span class="device-id">{{ identityMac }}</span></h3>
      </div>
      <div class="card-body">
        <!-- 时间筛选器 -->
        <div class="history-filters">
          <div class="form-group">
            <label>开始时间</label>
            <el-date-picker
              v-model="filters.startTime"
              type="datetime"
              placeholder="选择开始时间"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              class="datetime-input"
            />
          </div>
          <div class="form-group">
            <label>结束时间</label>
            <el-date-picker
              v-model="filters.endTime"
              type="datetime"
              placeholder="选择结束时间"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              class="datetime-input"
            />
          </div>
          <div class="filter-actions">
            <el-button type="primary" @click="searchHistoryMetrics" :loading="loading">
              <el-icon><Search /></el-icon>
              查询
            </el-button>
            <el-button @click="resetHistoryFilters">
              <el-icon><Refresh /></el-icon>
              重置
            </el-button>
          </div>
        </div>

        <!-- 历史指标表格 -->
        <table class="table">
          <thead>
            <tr>
              <th>采集时间</th>
              <th>CPU温度</th>
              <th>CPU使用率</th>
              <th>内存使用率</th>
              <th>磁盘使用率</th>
              <th>运行时长</th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="row in historyData" :key="row.id">
              <td>{{ formatDateTime(row.metricTime) }}</td>
              <td>
                <span :class="getTempClass(row.cpuTemp)">{{ formatTemperature(row.cpuTemp) }}</span>
              </td>
              <td>
                <span :class="getUsageClass(row.cpuUsage)">{{ formatPercentage(row.cpuUsage) }}</span>
              </td>
              <td>
                <span :class="getUsageClass(row.memoryUsage)">{{ formatPercentage(row.memoryUsage) }}</span>
              </td>
              <td>
                <span :class="getUsageClass(row.diskUsage)">{{ formatPercentage(row.diskUsage) }}</span>
              </td>
              <td>{{ formatUptime(row.uptime) }}</td>
              <td>
                <div class="action-buttons">
                  <button
                    class="btn btn-primary btn-sm"
                    @click="viewHistoryDetail(row)"
                  >
                    查看详情
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>

        <!-- 加载状态 -->
        <div v-if="loading" class="loading-container">
          <div class="loading-text">正在加载历史数据...</div>
        </div>

        <!-- 空数据状态 -->
        <div v-if="!loading && historyData.length === 0" class="empty-container">
          <div class="empty-text">暂无历史数据</div>
        </div>

        <!-- 分页 -->
        <div class="pagination">
          <button @click="prevPage" :disabled="pagination.currentPage <= 1">上一页</button>
          <button
            v-for="page in visiblePages"
            :key="page"
            :class="{ active: page === pagination.currentPage }"
            @click="goToPage(page)"
          >
            {{ page }}
          </button>
          <button @click="nextPage" :disabled="pagination.currentPage >= totalPages">下一页</button>
        </div>
      </div>
    </div>

    <!-- 历史详情弹窗 -->
    <div v-if="detailDialogVisible" class="modal" @click="handleDetailClose">
      <div class="modal-content detail-modal" @click.stop>
        <div class="modal-header">
          <h3>历史指标详情</h3>
          <span class="close" @click="handleDetailClose">&times;</span>
        </div>
        <div class="modal-body" v-if="currentHistoryDetail">
          <!-- 时间信息 -->
          <div class="time-info">
            <div>
              <span class="time-label">信息采集时间：</span>
              <span class="time-value">{{ formatDateTime(currentHistoryDetail.metricTime) }}</span>
            </div>
            <div>
              <span class="time-label">数据接收时间：</span>
              <span class="time-value">{{ formatDateTime(currentHistoryDetail.receiveTime) }}</span>
            </div>
            <div>
              <span class="time-label">数据类型：</span>
              <span class="time-value history-badge">历史数据</span>
            </div>
          </div>

          <!-- 网络流量信息 -->
          <div class="network-info">
            <div class="network-card">
              <div class="network-title">上行实时速率</div>
              <div class="network-value">{{ formatNetworkSpeed(getDetailGroupBps(currentHistoryDetail)) }}</div>
<!--              <div class="network-subtitle">总流量速率 ({{ getDetailGroupBps(currentHistoryDetail) }} bps)</div>-->
            </div>
          </div>

          <!-- 概览指标 -->
          <div class="stats-grid">
            <div class="stat-card">
              <div class="stat-number" :style="{ color: getTempColor(getDetailCpuTemp(currentHistoryDetail)) }">
                {{ formatTemperature(getDetailCpuTemp(currentHistoryDetail)) }}
              </div>
              <div class="stat-label">CPU温度</div>
            </div>
            <div class="stat-card">
              <div class="stat-number" :style="{ color: getCpuColor(getDetailCpuUsage(currentHistoryDetail)) }">
                {{ formatPercentage(getDetailCpuUsage(currentHistoryDetail)) }}
              </div>
              <div class="stat-label">CPU使用率</div>
            </div>
            <div class="stat-card">
              <div class="stat-number" :style="{ color: getMemoryColor(getDetailMemoryUsage(currentHistoryDetail)) }">
                {{ formatPercentage(getDetailMemoryUsage(currentHistoryDetail)) }}
              </div>
              <div class="stat-label">内存使用率</div>
            </div>
            <div class="stat-card">
              <div class="stat-number" :style="{ color: getDiskColor(getDetailDiskUsage(currentHistoryDetail)) }">
                {{ formatPercentage(getDetailDiskUsage(currentHistoryDetail)) }}
              </div>
              <div class="stat-label">磁盘使用率</div>
            </div>
          </div>

          <!-- 详细指标信息 -->
          <div class="card">
            <div class="card-header">
              <h4>详细指标信息</h4>
            </div>
            <div class="card-body">
              <div class="metrics-grid">
                <div class="metric-section system-section">
                  <h5><i class="icon-system"></i>系统信息</h5>
                  <div class="metric-row">
                    <div class="metric-label">系统运行时长</div>
                    <div class="metric-value">{{ formatUptime(currentHistoryDetail.uptime) }}</div>
                  </div>
                  <div class="metric-row">
                    <div class="metric-label">设备温度</div>
                    <div class="metric-value">
                      SOC温度: {{ formatTemperature(getDetailSocTemp(currentHistoryDetail)) }}<br>
                      GPU温度: {{ formatTemperature(getDetailGpuTemp(currentHistoryDetail)) }}
                    </div>
                  </div>
                </div>

                <div class="metric-section cpu-section">
                  <h5><i class="icon-cpu"></i>CPU信息</h5>
                  <div class="metric-row">
                    <div class="metric-label">CPU核心</div>
                    <div class="metric-value">{{ getDetailCpuCores(currentHistoryDetail) }}核{{ getDetailCpuCores(currentHistoryDetail) }}线程</div>
                  </div>
                  <div class="metric-row">
                    <div class="metric-label">CPU使用率</div>
                    <div class="metric-value">
                      用户态: {{ formatPercentage(getDetailCpuUser(currentHistoryDetail)) }} |
                      系统态: {{ formatPercentage(getDetailCpuSys(currentHistoryDetail)) }} |
                      空闲: {{ formatPercentage(getDetailCpuIdle(currentHistoryDetail)) }}
                      <div class="progress-bar">
                        <div class="progress-fill" :style="{
                          width: getDetailCpuUsage(currentHistoryDetail) + '%',
                          backgroundColor: getCpuColor(getDetailCpuUsage(currentHistoryDetail))
                        }"></div>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="metric-section memory-section">
                  <h5><i class="icon-memory"></i>内存信息</h5>

                  <!-- 内存总览 -->
                  <div class="memory-overview">
                    <div class="memory-summary">
                      <div class="memory-item">
                        <span class="memory-label">总内存</span>
                        <span class="memory-value">{{ formatBytes(getDetailMemoryTotal(currentHistoryDetail)) }}</span>
                      </div>
                      <div class="memory-item">
                        <span class="memory-label">已用内存</span>
                        <span class="memory-value">{{ formatBytes(getDetailMemoryUsed(currentHistoryDetail)) }}</span>
                      </div>
                      <div class="memory-item">
                        <span class="memory-label">可用内存</span>
                        <span class="memory-value">{{ formatBytes(getDetailMemoryAvailable(currentHistoryDetail)) }}</span>
                      </div>
                      <div class="memory-item">
                        <span class="memory-label">使用率</span>
                        <span class="memory-value">{{ formatPercentage(getDetailMemoryUsage(currentHistoryDetail)) }}</span>
                      </div>
                    </div>
                    <div class="progress-bar">
                      <div class="progress-fill" :style="{ width: getDetailMemoryUsage(currentHistoryDetail) + '%', backgroundColor: getMemoryColor(getDetailMemoryUsage(currentHistoryDetail)) }"></div>
                    </div>
                  </div>

                  <!-- 内存详细分布 -->
<!--                  <div class="memory-details">-->
<!--                    <div class="memory-detail-title">内存分布详情</div>-->
<!--                    <div class="memory-detail-grid">-->
<!--                      <div class="memory-detail-item">-->
<!--                        <span class="detail-label">空闲内存</span>-->
<!--                        <span class="detail-value">{{ formatBytes(getDetailMemoryFree(currentHistoryDetail)) }}</span>-->
<!--                      </div>-->
<!--                      <div class="memory-detail-item">-->
<!--                        <span class="detail-label">活跃内存</span>-->
<!--                        <span class="detail-value">{{ formatBytes(getDetailMemoryActive(currentHistoryDetail)) }}</span>-->
<!--                      </div>-->
<!--                      <div class="memory-detail-item">-->
<!--                        <span class="detail-label">非活跃内存</span>-->
<!--                        <span class="detail-value">{{ formatBytes(getDetailMemoryInactive(currentHistoryDetail)) }}</span>-->
<!--                      </div>-->
<!--                      <div class="memory-detail-item">-->
<!--                        <span class="detail-label">缓冲区</span>-->
<!--                        <span class="detail-value">{{ formatBytes(getDetailMemoryBuffers(currentHistoryDetail)) }}</span>-->
<!--                      </div>-->
<!--                      <div class="memory-detail-item">-->
<!--                        <span class="detail-label">文件缓存</span>-->
<!--                        <span class="detail-value">{{ formatBytes(getDetailMemoryCached(currentHistoryDetail)) }}</span>-->
<!--                      </div>-->
<!--                      <div class="memory-detail-item">-->
<!--                        <span class="detail-label">共享内存</span>-->
<!--                        <span class="detail-value">{{ formatBytes(getDetailMemoryShared(currentHistoryDetail)) }}</span>-->
<!--                      </div>-->
<!--                      <div class="memory-detail-item">-->
<!--                        <span class="detail-label">内核缓存</span>-->
<!--                        <span class="detail-value">{{ formatBytes(getDetailMemorySlab(currentHistoryDetail)) }}</span>-->
<!--                      </div>-->
<!--                    </div>-->
<!--                  </div>-->
                </div>

                <div class="metric-section storage-section">
                  <h5><i class="icon-storage"></i>存储信息</h5>

                  <!-- 内置存储 -->
                  <div class="storage-device">
                    <div class="storage-header">内置存储 ({{ getDetailInternalStorageDevice(currentHistoryDetail) }})</div>
                    <div class="metric-row">
                      <div class="metric-label">存储使用</div>
                      <div class="metric-value">
                        总空间: {{ formatBytes(getDetailInternalStorageTotal(currentHistoryDetail)) }} | 已用: {{ formatBytes(getDetailInternalStorageUsed(currentHistoryDetail)) }} | 可用: {{ formatBytes(getDetailInternalStorageFree(currentHistoryDetail)) }}
                        <div class="progress-bar">
                          <div class="progress-fill" :class="getDiskClass(getDetailInternalStoragePercent(currentHistoryDetail))" :style="{ width: getDetailInternalStoragePercent(currentHistoryDetail) + '%' }"></div>
                        </div>
                      </div>
                    </div>
                    <div class="metric-row">
                      <div class="metric-label">IO性能</div>
                      <div class="metric-value">
                        读取: {{ formatDiskIOSpeed(getDetailInternalStorageReadSpeed(currentHistoryDetail)) }} | 写入: {{ formatDiskIOSpeed(getDetailInternalStorageWriteSpeed(currentHistoryDetail)) }}<br>
                        读取频率: {{ getDetailInternalStorageReadRate(currentHistoryDetail) }}/s | 写入频率: {{ getDetailInternalStorageWriteRate(currentHistoryDetail) }}/s
                      </div>
                    </div>
                  </div>

                  <!-- 外置存储 -->
                  <div v-if="hasDetailExternalStorage(currentHistoryDetail)" class="storage-device">
                    <div class="storage-header">外置存储 ({{ getDetailExternalStorageDevice(currentHistoryDetail) }})</div>
                    <div class="metric-row">
                      <div class="metric-label">存储使用</div>
                      <div class="metric-value">
                        总空间: {{ formatBytes(getDetailExternalStorageTotal(currentHistoryDetail)) }} | 已用: {{ formatBytes(getDetailExternalStorageUsed(currentHistoryDetail)) }} | 可用: {{ formatBytes(getDetailExternalStorageFree(currentHistoryDetail)) }}
                        <div class="progress-bar">
                          <div class="progress-fill" :class="getDiskClass(getDetailExternalStoragePercent(currentHistoryDetail))" :style="{ width: getDetailExternalStoragePercent(currentHistoryDetail) + '%' }"></div>
                        </div>
                      </div>
                    </div>
                    <div class="metric-row">
                      <div class="metric-label">IO性能</div>
                      <div class="metric-value">
                        读取: {{ formatDiskIOSpeed(getDetailExternalStorageReadSpeed(currentHistoryDetail)) }} | 写入: {{ formatDiskIOSpeed(getDetailExternalStorageWriteSpeed(currentHistoryDetail)) }}<br>
                        读取频率: {{ getDetailExternalStorageReadRate(currentHistoryDetail) }}/s | 写入频率: {{ getDetailExternalStorageWriteRate(currentHistoryDetail) }}/s
                      </div>
                    </div>
                  </div>
                </div>

                <div class="metric-section data-section">
                  <h5><i class="icon-file"></i>数据文件</h5>
                  <div class="metric-row">
                    <div class="metric-label">原始文件(原始数据待压缩文件)</div>
                    <div class="metric-value">{{ getDetailCdataCount(currentHistoryDetail) }}个文件，总大小: {{ formatBytes(getDetailCdataSize(currentHistoryDetail)) }}</div>
                  </div>
                  <div class="metric-row">
                    <div class="metric-label">压缩文件(已被压缩的待上传文件)</div>
                    <div class="metric-value">{{ getDetailZdataCount(currentHistoryDetail) }}个文件，总大小: {{ formatBytes(getDetailZdataSize(currentHistoryDetail)) }}</div>
                  </div>
                </div>

                <div class="metric-section terminal-cpe-section">
                  <h5><i class="icon-network"></i>终端与CPE信息</h5>

                  <!-- 终端总览信息 -->
                  <div class="terminal-overview">
                    <div class="overview-grid">
                      <div class="overview-item">
                        <div class="overview-label">终端组ID</div>
                        <div class="overview-value">{{ getDetailTerminalGroupId(currentHistoryDetail) }}</div>
                      </div>
                      <div class="overview-item">
                        <div class="overview-label">总连接数</div>
                        <div class="overview-value">{{ getDetailTotalConnections(currentHistoryDetail) }}</div>
                      </div>
                      <div class="overview-item">
                        <div class="overview-label">总包速率</div>
                        <div class="overview-value">{{ formatPacketRate(getDetailTotalPps(currentHistoryDetail)) }}</div>
                      </div>
                      <div class="overview-item">
                        <div class="overview-label">总流量速率</div>
                        <div class="overview-value">{{ formatNetworkSpeed(getDetailTotalBps(currentHistoryDetail)) }}</div>
                      </div>
                    </div>
                  </div>

                  <!-- CPE分组详情 -->
                  <div class="cpe-groups">
                    <div class="cpe-groups-header">CPE分组详情</div>
                    <div class="cpe-groups-grid">
                      <div v-for="(bucket, index) in getDetailCpeBuckets(currentHistoryDetail)" :key="index" class="cpe-group-card">
                        <div class="cpe-group-title">分组 {{ bucket.bucket }}</div>
                        <div class="cpe-group-stats">
                          <div class="cpe-stat">
                            <span class="cpe-stat-label">连接</span>
                            <span class="cpe-stat-value">{{ bucket.conn }}</span>
                          </div>
                          <div class="cpe-stat">
                            <span class="cpe-stat-label">包速率</span>
                            <span class="cpe-stat-value">{{ formatPacketRate(bucket.pps) }}</span>
                          </div>
                          <div class="cpe-stat">
                            <span class="cpe-stat-label">流量</span>
                            <span class="cpe-stat-value">{{ formatNetworkSpeed(bucket.bps) }}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

<!--                <div class="metric-section data-section">-->
<!--                  <h5><i class="icon-file"></i>数据文件</h5>-->
<!--                  <div class="metric-row">-->
<!--                    <div class="metric-label">程序生成文件</div>-->
<!--                    <div class="metric-value">{{ getDetailCdataCount(currentHistoryDetail) }}个文件，总大小: {{ formatBytes(getDetailCdataSize(currentHistoryDetail)) }}</div>-->
<!--                  </div>-->
<!--                  <div class="metric-row">-->
<!--                    <div class="metric-label">压缩数据</div>-->
<!--                    <div class="metric-value">{{ getDetailZdataCount(currentHistoryDetail) }}个文件，总大小: {{ formatBytes(getDetailZdataSize(currentHistoryDetail)) }}</div>-->
<!--                  </div>-->
<!--                </div>-->
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Search, Refresh } from '@element-plus/icons-vue'
import { terminalApi } from '@/api/services'

const route = useRoute()
const router = useRouter()

// 设备MAC地址
const identityMac = computed(() => route.params.identityMac || route.query.identityMac)

// 筛选条件
const filters = reactive({
  startTime: '',
  endTime: ''
})

// 历史数据
const historyData = ref([])
const loading = ref(false)

// 分页
const pagination = reactive({
  currentPage: 1,
  pageSize: 20,
  total: 0
})

// 详情弹窗
const detailDialogVisible = ref(false)
const currentHistoryDetail = ref(null)

// 初始化时间范围（默认查询最近10分钟）
const initTimeRange = () => {
  const now = new Date()
  const tenMinutesAgo = new Date(now.getTime() - 10 * 60 * 1000) // 10分钟前
  
  // 格式化为本地时间字符串 YYYY-MM-DD HH:mm:ss
  const formatLocalDateTime = (date) => {
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const hours = String(date.getHours()).padStart(2, '0')
    const minutes = String(date.getMinutes()).padStart(2, '0')
    const seconds = String(date.getSeconds()).padStart(2, '0')
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
  }
  
  filters.endTime = formatLocalDateTime(now)
  filters.startTime = formatLocalDateTime(tenMinutesAgo)
}

// 搜索历史指标
const searchHistoryMetrics = async () => {
  if (!identityMac.value) {
    ElMessage.error('设备MAC地址不能为空')
    return
  }

  if (!filters.startTime || !filters.endTime) {
    ElMessage.error('请选择时间范围')
    return
  }

  loading.value = true
  try {
    const params = {
      startTime: filters.startTime,
      endTime: filters.endTime,
      page: pagination.currentPage - 1, // 后端页码从0开始
      size: pagination.pageSize
    }

    console.log('History Metrics API Request:', params)
    const response = await terminalApi.getHistoryMetrics(identityMac.value, params)
    console.log('History Metrics API Response:', response)

    // 处理不同的响应结构
    let pageData = null
    if (response.data && response.data.content) {
      // Mock数据或包装的Spring Page结构
      pageData = response.data
    } else if (response.content) {
      // 真实API直接返回的Spring Page结构
      pageData = response
    } else if (response.data && response.data.list) {
      // 其他Mock数据结构
      pageData = {
        content: response.data.list,
        totalElements: response.data.total || 0
      }
    } else {
      console.error('Unknown response structure:', response)
      historyData.value = []
      pagination.total = 0
      return
    }

    // 处理历史指标数据
    if (pageData.content && Array.isArray(pageData.content)) {
      historyData.value = pageData.content.map(item => ({
        id: item.id,
        identityMac: item.identityMac,
        metricTime: item.metricTime,
        receiveTime: item.receiveTime,
        // 从后端数据中提取指标
        cpuTemp: item.temperatures ?
          (item.temperatures['soc-thermal'] || item.temperatures['gpu-thermal'] || 0) : 0,
        cpuUsage: item.cpuUsage ?
          (100 - parseFloat(item.cpuUsage.idle || 0)) : 0,
        memoryUsage: item.memoryUsage?.percent || 0,
        diskUsage: item.diskUsage?.[0]?.percent || 0,
        uptime: item.uptime || 0,
        // 保留原始数据用于详情显示
        originalData: item
      }))
      pagination.total = pageData.totalElements || 0
    } else {
      historyData.value = []
      pagination.total = 0
    }
  } catch (error) {
    console.error('获取历史指标失败:', error)
    ElMessage.error('获取历史指标失败')
    historyData.value = []
    pagination.total = 0
  } finally {
    loading.value = false
  }
}

// 重置筛选条件
const resetHistoryFilters = () => {
  initTimeRange()
  pagination.currentPage = 1
  searchHistoryMetrics()
}

// 查看历史详情
const viewHistoryDetail = (row) => {
  // 使用原始数据或当前行数据
  currentHistoryDetail.value = row.originalData || row
  detailDialogVisible.value = true
}

// 关闭详情弹窗
const handleDetailClose = () => {
  detailDialogVisible.value = false
  currentHistoryDetail.value = null
}

// 分页计算
const totalPages = computed(() => {
  return Math.ceil(pagination.total / pagination.pageSize)
})

const visiblePages = computed(() => {
  const current = pagination.currentPage
  const total = totalPages.value
  const pages = []

  let start = Math.max(1, current - 2)
  let end = Math.min(total, current + 2)

  for (let i = start; i <= end; i++) {
    pages.push(i)
  }

  return pages
})

// 分页处理
const prevPage = () => {
  if (pagination.currentPage > 1) {
    pagination.currentPage--
    searchHistoryMetrics()
  }
}

const nextPage = () => {
  if (pagination.currentPage < totalPages.value) {
    pagination.currentPage++
    searchHistoryMetrics()
  }
}

const goToPage = (page) => {
  pagination.currentPage = page
  searchHistoryMetrics()
}

// 格式化时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 格式化运行时长
const formatUptime = (seconds) => {
  if (!seconds || seconds === 0) return '0分钟'

  const totalSeconds = Math.floor(seconds)
  const days = Math.floor(totalSeconds / 86400)
  const hours = Math.floor((totalSeconds % 86400) / 3600)
  const minutes = Math.floor((totalSeconds % 3600) / 60)
  const secs = totalSeconds % 60

  if (days > 0) {
    return `${days}天${hours}小时${minutes}分钟`
  } else if (hours > 0) {
    return `${hours}小时${minutes}分钟`
  } else if (minutes > 0) {
    return `${minutes}分钟${secs}秒`
  } else {
    return `${secs}秒`
  }
}

// 获取温度样式类
const getTempClass = (temp) => {
  if (temp >= 80) return 'temp-danger'
  if (temp >= 70) return 'temp-warning'
  return 'temp-normal'
}

// 获取使用率样式类
const getUsageClass = (usage) => {
  if (usage >= 90) return 'usage-danger'
  if (usage >= 70) return 'usage-warning'
  return 'usage-normal'
}

// 详情数据提取方法
const getDetailCpuTemp = (detail) => {
  if (detail.temperatures) {
    return detail.temperatures['soc-thermal'] || detail.temperatures['gpu-thermal'] || 0
  }
  return 0
}

const getDetailSocTemp = (detail) => {
  return detail.temperatures?.['soc-thermal'] || 0
}

const getDetailGpuTemp = (detail) => {
  return detail.temperatures?.['gpu-thermal'] || 0
}

const getDetailCpuUsage = (detail) => {
  if (detail.cpuUsage?.idle) {
    return 100 - parseFloat(detail.cpuUsage.idle)
  }
  return 0
}

const getDetailCpuUser = (detail) => {
  return parseFloat(detail.cpuUsage?.user || 0)
}

const getDetailCpuSys = (detail) => {
  return parseFloat(detail.cpuUsage?.sys || 0)
}

const getDetailCpuIdle = (detail) => {
  return parseFloat(detail.cpuUsage?.idle || 100)
}

const getDetailCpuCores = (detail) => {
  return parseInt(detail.cpuUsage?.core || 4)
}

const getDetailMemoryUsage = (detail) => {
  return detail.memoryUsage?.percent || 0
}

const getDetailMemoryTotal = (detail) => {
  return detail.memoryUsage?.total || 0
}

const getDetailMemoryUsed = (detail) => {
  return detail.memoryUsage?.used || 0
}

const getDetailMemoryAvailable = (detail) => {
  return detail.memoryUsage?.available || 0
}

const getDetailMemoryCached = (detail) => {
  return detail.memoryUsage?.cached || 0
}

const getDetailMemoryBuffers = (detail) => {
  return detail.memoryUsage?.buffers || 0
}

const getDetailDiskUsage = (detail) => {
  return detail.diskUsage?.[0]?.percent || 0
}

const getDetailDiskTotal = (detail) => {
  return detail.diskUsage?.[0]?.total || 0
}

const getDetailDiskUsed = (detail) => {
  return detail.diskUsage?.[0]?.used || 0
}

const getDetailDiskFree = (detail) => {
  return detail.diskUsage?.[0]?.free || 0
}

const getDetailDiskDevice = (detail) => {
  const device = detail.diskUsage?.[0]?.device || '/dev/unknown'
  const fstype = detail.diskUsage?.[0]?.fstype || 'unknown'
  return `${device} (${fstype})`
}

const getDetailCdataCount = (detail) => {
  return detail.cdata?.count || 0
}

// 已移动到下面的新函数中

const getDetailZdataCount = (detail) => {
  return detail.zdata?.count || 0
}

const getDetailZdataSize = (detail) => {
  return detail.zdata?.['size:'] || detail.zdata?.size || 0
}

// 新增的内存详细信息函数
const getDetailMemoryFree = (detail) => {
  return detail.memoryUsage?.free || detail.memory_usage?.free || 0
}

const getDetailMemoryActive = (detail) => {
  return detail.memoryUsage?.active || detail.memory_usage?.active || 0
}

const getDetailMemoryInactive = (detail) => {
  return detail.memoryUsage?.inactive || detail.memory_usage?.inactive || 0
}

const getDetailMemoryShared = (detail) => {
  return detail.memoryUsage?.shared || detail.memory_usage?.shared || 0
}

const getDetailMemorySlab = (detail) => {
  return detail.memoryUsage?.slab || detail.memory_usage?.slab || 0
}

// 存储相关函数
const getDetailInternalStorageDevice = (detail) => {
  return detail.diskUsage?.[0]?.device || '/dev/mmcblk0p6'
}

const getDetailInternalStorageTotal = (detail) => {
  return detail.diskUsage?.[0]?.total || 0
}

const getDetailInternalStorageUsed = (detail) => {
  return detail.diskUsage?.[0]?.used || 0
}

const getDetailInternalStorageFree = (detail) => {
  return detail.diskUsage?.[0]?.free || 0
}

const getDetailInternalStoragePercent = (detail) => {
  return detail.diskUsage?.[0]?.percent || 0
}

const getDetailInternalStorageReadSpeed = (detail) => {
  return detail.diskUsage?.[0]?.io_rate?.['MBr/s'] || 0
}

const getDetailInternalStorageWriteSpeed = (detail) => {
  return detail.diskUsage?.[0]?.io_rate?.['MBw/s'] || 0
}

const getDetailInternalStorageReadRate = (detail) => {
  return detail.diskUsage?.[0]?.io_rate?.['read/s'] || 0
}

const getDetailInternalStorageWriteRate = (detail) => {
  return detail.diskUsage?.[0]?.io_rate?.['write/s'] || 0
}

// 外置存储函数
const hasDetailExternalStorage = (detail) => {
  return detail.diskUsage && detail.diskUsage.length > 1
}

const getDetailExternalStorageDevice = (detail) => {
  return detail.diskUsage?.[1]?.device || '/dev/nvme0n1p1'
}

const getDetailExternalStorageTotal = (detail) => {
  return detail.diskUsage?.[1]?.total || 0
}

const getDetailExternalStorageUsed = (detail) => {
  return detail.diskUsage?.[1]?.used || 0
}

const getDetailExternalStorageFree = (detail) => {
  return detail.diskUsage?.[1]?.free || 0
}

const getDetailExternalStoragePercent = (detail) => {
  return detail.diskUsage?.[1]?.percent || 0
}

const getDetailExternalStorageReadSpeed = (detail) => {
  return detail.diskUsage?.[1]?.io_rate?.['MBr/s'] || 0
}

const getDetailExternalStorageWriteSpeed = (detail) => {
  return detail.diskUsage?.[1]?.io_rate?.['MBw/s'] || 0
}

const getDetailExternalStorageReadRate = (detail) => {
  return detail.diskUsage?.[1]?.io_rate?.['read/s'] || 0
}

const getDetailExternalStorageWriteRate = (detail) => {
  return detail.diskUsage?.[1]?.io_rate?.['write/s'] || 0
}

// 网络流量相关函数
const getDetailGroupBps = (detail) => {
  return detail.groupBps || 0
}

// 终端和CPE相关函数
const getDetailTerminalGroupId = (detail) => {
  return detail.groupUsage?.group_id || 'N/A'
}

const getDetailTotalConnections = (detail) => {
  if (detail.groupUsage?.buckets) {
    return detail.groupUsage.buckets.reduce((total, bucket) => total + bucket.conn, 0)
  }
  return 0
}

const getDetailTotalPps = (detail) => {
  return detail.groupUsage?.pps_total || 0
}

const getDetailTotalBps = (detail) => {
  return detail.groupUsage?.bps_total || 0
}

const getDetailCpeBuckets = (detail) => {
  return detail.groupUsage?.buckets || []
}

// 修复cdata和zdata的size字段
const getDetailCdataSize = (detail) => {
  return detail.cdata?.size || detail.cdata?.['size:'] || 0
}

// 颜色方法
const getCpuColor = (usage) => {
  if (usage >= 90) return '#F56C6C'
  if (usage >= 70) return '#E6A23C'
  return '#67C23A'
}

const getMemoryColor = (usage) => {
  if (usage >= 85) return '#F56C6C'
  if (usage >= 70) return '#E6A23C'
  return '#67C23A'
}

const getDiskColor = (usage) => {
  if (usage >= 90) return '#F56C6C'
  if (usage >= 80) return '#E6A23C'
  return '#67C23A'
}

const getTempColor = (temp) => {
  if (temp >= 80) return '#F56C6C'
  if (temp >= 70) return '#E6A23C'
  return '#67C23A'
}

const getDiskClass = (usage) => {
  if (usage >= 90) return 'danger'
  if (usage >= 80) return 'warning'
  return 'success'
}

// 格式化数值 - 浮点数指标固定2位小数，其他数值四舍五入
const formatMetricValue = (value, isFloatMetric = true) => {
  if (value === null || value === undefined || isNaN(value)) return '0'

  if (isFloatMetric) {
    // 浮点数指标固定为2位小数
    return parseFloat(value).toFixed(2)
  } else {
    // 其他数值四舍五入
    return Math.round(parseFloat(value)).toString()
  }
}

// 格式化温度值
const formatTemperature = (temp) => {
  return formatMetricValue(temp, true) + '°C'
}

// 格式化百分比值
const formatPercentage = (percent) => {
  return formatMetricValue(percent, true) + '%'
}

// 格式化字节数
const formatBytes = (bytes, unit = 'auto') => {
  if (bytes === 0) return '0 B'

  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']

  if (unit === 'MB') {
    return parseFloat((bytes / Math.pow(k, 2)).toFixed(2)) + ' MB'
  }

  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 格式化网络速度 (bps转换为合适的单位)
const formatNetworkSpeed = (bps) => {
  if (!bps || bps === 0) return '0 bps'

  // 8 bit = 1 byte，所以 bps / 8 = Bps (字节每秒)
  const bytesPerSecond = bps

  if (bytesPerSecond >= 1024 * 1024 * 1024) {
    return (bytesPerSecond / (1024 * 1024 * 1024)).toFixed(2) + ' GB/s'
  } else if (bytesPerSecond >= 1024 * 1024) {
    return (bytesPerSecond / (1024 * 1024)).toFixed(2) + ' MB/s'
  } else if (bytesPerSecond >= 1024) {
    return (bytesPerSecond / 1024).toFixed(2) + ' KB/s'
  } else {
    return bytesPerSecond.toFixed(2) + ' B/s'
  }
}

// 格式化包速率
const formatPacketRate = (pps) => {
  if (!pps || pps === 0) return '0 pps'

  if (pps >= 1000000) {
    return (pps / 1000000).toFixed(2) + ' Mpps'
  } else if (pps >= 1000) {
    return (pps / 1000).toFixed(2) + ' Kpps'
  } else {
    return pps.toFixed(0) + ' pps'
  }
}

// 格式化磁盘IO速度
const formatDiskIOSpeed = (mbPerSecond) => {
  if (!mbPerSecond || mbPerSecond === 0) return '0 MB/s'
  return mbPerSecond.toFixed(2) + ' MB/s'
}

// 组件挂载时初始化
onMounted(() => {
  if (!identityMac.value) {
    ElMessage.error('设备MAC地址参数缺失')
    router.push('/terminals')
    return
  }

  initTimeRange()
  searchHistoryMetrics()
})
</script>

<style scoped>
.history-metrics {
  padding: 20px;
}

.breadcrumb {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  font-size: 14px;
}

.breadcrumb-link {
  color: #409eff;
  text-decoration: none;
  cursor: pointer;
}

.breadcrumb-link:hover {
  text-decoration: underline;
}

.breadcrumb-separator {
  margin: 0 8px;
  color: #999;
}

.breadcrumb-current {
  color: #666;
}

.card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  overflow: hidden;
}

.card-header {
  padding: 20px;
  border-bottom: 1px solid #eee;
  background: #fafafa;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.device-id {
  color: #409eff;
  font-family: monospace;
}

.card-body {
  padding: 20px;
}

.history-filters {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
  align-items: end;
  flex-wrap: wrap;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group label {
  margin-bottom: 5px;
  font-weight: 500;
  color: #555;
  font-size: 14px;
}

.datetime-input {
  width: 200px;
}

.filter-actions {
  display: flex;
  gap: 10px;
}

/* 表格样式 */
.table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 20px;
}

.table th,
.table td {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid #eee;
}

.table th {
  background: #f8f9fa;
  font-weight: 600;
  color: #333;
}

.table tr:hover {
  background: #f8f9fa;
}

/* 操作按钮组 */
.action-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.btn {
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  text-decoration: none;
  display: inline-block;
  transition: all 0.2s;
}

.btn-sm {
  padding: 4px 8px;
  font-size: 11px;
}

.btn-primary {
  background: #667eea;
  color: white;
}

.btn-primary:hover {
  background: #5a6fd8;
}

/* 加载和空状态 */
.loading-container,
.empty-container {
  text-align: center;
  padding: 40px;
  color: #666;
}

.loading-text,
.empty-text {
  font-size: 16px;
}

/* 分页样式 */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
  margin-top: 20px;
}

.pagination button {
  padding: 8px 12px;
  border: 1px solid #ddd;
  background: white;
  cursor: pointer;
  border-radius: 4px;
}

.pagination button:hover:not(:disabled) {
  background: #f8f9fa;
}

.pagination button.active {
  background: #667eea;
  color: white;
  border-color: #667eea;
}

.pagination button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  background: white !important;
  color: #999 !important;
  border-color: #ddd !important;
}

.history-detail {
  max-height: 70vh;
  overflow-y: auto;
}

.detail-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 24px;
  margin-bottom: 20px;
}

.detail-section h4 {
  color: #333;
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 15px;
  padding-bottom: 8px;
  border-bottom: 2px solid #409eff;
}

.detail-item {
  display: flex;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.detail-item:last-child {
  border-bottom: none;
}

.detail-label {
  font-weight: 600;
  color: #666;
  min-width: 120px;
  margin-right: 10px;
}

.detail-value {
  color: #333;
  flex: 1;
}

.json-section h4 {
  color: #333;
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 15px;
  padding-bottom: 8px;
  border-bottom: 2px solid #409eff;
}

.json-display {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  padding: 15px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  max-height: 300px;
  overflow-y: auto;
}

.json-display pre {
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
}

/* 模态框样式 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 8px;
  max-width: 90%;
  max-height: 90%;
  overflow-y: auto;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.detail-modal {
  width: 1200px;
  max-width: 95%;
}

.modal-header {
  padding: 20px;
  border-bottom: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #f8f9fa;
  border-radius: 8px 8px 0 0;
}

.modal-header h3 {
  margin: 0;
  color: #333;
  font-size: 18px;
  font-weight: 600;
}

.close {
  font-size: 24px;
  cursor: pointer;
  color: #999;
  line-height: 1;
}

.close:hover {
  color: #333;
}

.modal-body {
  padding: 20px;
}

/* 时间信息样式 */
.time-info {
  display: flex;
  gap: 30px;
  margin-bottom: 20px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 6px;
  flex-wrap: wrap;
}

.time-label {
  font-weight: 600;
  color: #666;
  margin-right: 8px;
}

.time-value {
  color: #333;
  font-family: monospace;
}

.history-badge {
  background: #e6a23c;
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-family: sans-serif;
}

/* 概览指标卡片 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  background: white;
  border: 1px solid #eee;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.stat-number {
  font-size: 32px;
  font-weight: bold;
  margin-bottom: 8px;
}

.stat-label {
  color: #666;
  font-size: 14px;
  font-weight: 500;
}

/* 详细指标网格 */
.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 20px;
}

.metric-section {
  background: #f8f9fa;
  border-radius: 6px;
  padding: 15px;
}

.metric-section h5 {
  color: #333;
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 15px;
  padding-bottom: 8px;
  border-bottom: 2px solid #667eea;
}

.metric-row {
  display: flex;
  align-items: flex-start;
  padding: 8px 0;
  border-bottom: 1px solid #e0e0e0;
}

.metric-row:last-child {
  border-bottom: none;
}

.metric-label {
  font-weight: 600;
  color: #666;
  min-width: 120px;
  margin-right: 15px;
  flex-shrink: 0;
}

.metric-value {
  color: #333;
  flex: 1;
  line-height: 1.4;
}

/* 进度条 */
.progress-bar {
  width: 100%;
  height: 8px;
  background: #f0f0f0;
  border-radius: 4px;
  margin-top: 8px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  border-radius: 4px;
  transition: width 0.3s ease;
}

.progress-fill.success {
  background-color: #67C23A;
}

.progress-fill.warning {
  background-color: #E6A23C;
}

.progress-fill.danger {
  background-color: #F56C6C;
}

/* 状态样式 */
.temp-normal { color: #67c23a; }
.temp-warning { color: #e6a23c; }
.temp-danger { color: #f56c6c; }

.usage-normal { color: #67c23a; }
.usage-warning { color: #e6a23c; }
.usage-danger { color: #f56c6c; }

/* 响应式设计 */
@media (max-width: 768px) {
  .history-filters {
    flex-direction: column;
    align-items: stretch;
  }
  
  .filter-actions {
    justify-content: center;
  }
  
  .detail-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }
}

/* 网络流量信息样式 */
.network-info {
  margin-bottom: 20px;
}

.network-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px;
  border-radius: 12px;
  text-align: center;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.network-title {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 8px;
  opacity: 0.9;
}

.network-value {
  font-size: 28px;
  font-weight: 700;
  margin-bottom: 5px;
}

.network-subtitle {
  font-size: 12px;
  opacity: 0.8;
}

/* 详细指标信息优化样式 */
.metric-section {
  background: #ffffff !important;
  border: 1px solid #e8eaed !important;
  border-radius: 12px !important;
  padding: 20px !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04) !important;
  transition: all 0.3s ease;
}

.metric-section:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  transform: translateY(-2px);
}

.metric-section h5 {
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
  font-size: 16px !important;
  font-weight: 600 !important;
  color: #1a73e8 !important;
  margin-bottom: 16px !important;
  padding-bottom: 8px !important;
  border-bottom: 2px solid #f1f3f4 !important;
}

/* 图标样式 */
.metric-section h5 i::before {
  content: "●";
  font-style: normal;
  font-size: 12px;
}

.system-section h5 i::before { color: #34a853; }
.cpu-section h5 i::before { color: #ea4335; }
.memory-section h5 i::before { color: #fbbc04; }
.storage-section h5 i::before { color: #4285f4; }
.terminal-cpe-section h5 i::before { color: #9c27b0; }
.data-section h5 i::before { color: #ff9800; }

/* 终端与CPE信息样式 */
.terminal-cpe-section {
  grid-column: 1 / -1; /* 占满整行 */
}

.terminal-overview {
  margin-bottom: 20px;
}

.overview-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
  margin-bottom: 20px;
}

.overview-item {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 15px;
  border-radius: 8px;
  text-align: center;
}

.overview-label {
  font-size: 12px;
  opacity: 0.9;
  margin-bottom: 5px;
}

.overview-value {
  font-size: 18px;
  font-weight: 600;
}

.cpe-groups-header {
  font-size: 14px;
  font-weight: 600;
  color: #5f6368;
  margin-bottom: 12px;
  padding-left: 8px;
  border-left: 3px solid #1a73e8;
}

.cpe-groups-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 15px;
}

.cpe-group-card {
  background: #f8f9fa;
  border: 1px solid #e8eaed;
  border-radius: 8px;
  padding: 15px;
  transition: all 0.2s ease;
}

.cpe-group-card:hover {
  background: #f1f3f4;
  border-color: #1a73e8;
}

.cpe-group-title {
  font-weight: 600;
  color: #1a73e8;
  margin-bottom: 12px;
  font-size: 14px;
}

.cpe-group-stats {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.cpe-stat {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 0;
  border-bottom: 1px solid #e8eaed;
}

.cpe-stat:last-child {
  border-bottom: none;
}

.cpe-stat-label {
  font-size: 12px;
  color: #5f6368;
  font-weight: 500;
}

.cpe-stat-value {
  font-size: 13px;
  color: #202124;
  font-weight: 600;
}

/* 内存信息样式 */
.memory-overview {
  margin-bottom: 20px;
}

.memory-summary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 12px;
  margin-bottom: 12px;
}

.memory-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px;
  background: linear-gradient(135deg, #fbbc04 0%, #f9ab00 100%);
  color: white;
  border-radius: 8px;
  text-align: center;
}

.memory-label {
  font-size: 11px;
  opacity: 0.9;
  margin-bottom: 4px;
}

.memory-value {
  font-size: 14px;
  font-weight: 600;
}

.memory-details {
  margin-top: 16px;
}

.memory-detail-title {
  font-size: 13px;
  font-weight: 600;
  color: #5f6368;
  margin-bottom: 12px;
  padding-left: 8px;
  border-left: 3px solid #fbbc04;
}

.memory-detail-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
  gap: 8px;
}

.memory-detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 6px;
  border-left: 3px solid #fbbc04;
}

.detail-label {
  font-size: 12px;
  color: #5f6368;
  font-weight: 500;
}

.detail-value {
  font-size: 12px;
  color: #202124;
  font-weight: 600;
}

/* 存储设备样式 */
.storage-device {
  margin-bottom: 15px;
  padding: 12px;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
}

.storage-device:last-child {
  margin-bottom: 0;
}

.storage-header {
  font-weight: 600;
  color: #495057;
  margin-bottom: 10px;
  padding-bottom: 5px;
  border-bottom: 1px solid #dee2e6;
  font-size: 13px;
}

/* 响应式设计优化 */
@media (max-width: 768px) {
  .network-card {
    padding: 15px;
  }

  .network-value {
    font-size: 24px;
  }

  .overview-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 10px;
  }

  .overview-item {
    padding: 12px;
  }

  .overview-value {
    font-size: 16px;
  }

  .cpe-groups-grid {
    grid-template-columns: 1fr;
  }

  .memory-summary {
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: 8px;
  }

  .memory-item {
    padding: 10px;
  }

  .memory-value {
    font-size: 12px;
  }

  .memory-detail-grid {
    grid-template-columns: 1fr;
    gap: 6px;
  }

  .memory-detail-item {
    padding: 6px 10px;
  }

  .metric-section {
    padding: 15px !important;
  }

  .metric-section:hover {
    transform: none;
  }
}
</style>
