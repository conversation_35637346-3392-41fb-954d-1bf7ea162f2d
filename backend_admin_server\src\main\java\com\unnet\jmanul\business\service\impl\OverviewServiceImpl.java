package com.unnet.jmanul.business.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.unnet.jmanul.business.entity.TerminalAlertInfo;
import com.unnet.jmanul.business.entity.TerminalBasicInfo;
import com.unnet.jmanul.business.service.IOverviewService;
import com.unnet.jmanul.business.service.TerminalAlertInfoService;
import com.unnet.jmanul.business.service.ITerminalBasicInfoService;
import com.unnet.jmanul.business.service.dto.overview.AlertTerminalResp;
import com.unnet.jmanul.business.service.dto.overview.OfflineTerminalResp;
import com.unnet.jmanul.business.service.dto.overview.OverviewStatsResp;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class OverviewServiceImpl implements IOverviewService {

    private final ITerminalBasicInfoService terminalBasicInfoService;
    private final TerminalAlertInfoService terminalAlertInfoService;

    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Override
    public OverviewStatsResp getOverviewStats() {
        try {
            log.info("开始获取概览统计信息");

            // 获取终端统计信息 - 不使用逻辑删除条件，因为MyBatis-Plus会自动处理
            List<TerminalBasicInfo> allTerminals = terminalBasicInfoService.list();
            int totalTerminals = allTerminals.size();

            log.info("查询到的终端总数: {}", totalTerminals);

            // 打印前几个终端的详细信息用于调试
            if (!allTerminals.isEmpty()) {
                for (int i = 0; i < Math.min(3, allTerminals.size()); i++) {
                    TerminalBasicInfo terminal = allTerminals.get(i);
                    log.info("终端[{}]: deviceId={}, hostname={}, status={}",
                            i, terminal.getDeviceId(), terminal.getHostname(), terminal.getStatus());
                }
            }

            // 统计在线和离线终端
            int onlineTerminals = (int) allTerminals.stream()
                    .filter(terminal -> terminal.getStatus() != null && terminal.getStatus() == 1)
                    .count();
            int offlineTerminals = totalTerminals - onlineTerminals;

            log.debug("在线终端数: {}, 离线终端数: {}", onlineTerminals, offlineTerminals);

            // 获取告警统计信息 - 不使用逻辑删除条件，因为MyBatis-Plus会自动处理
            List<TerminalAlertInfo> allAlerts = terminalAlertInfoService.list();
            int totalAlerts = allAlerts.size();

            log.info("查询到的告警总数: {}", totalAlerts);

            // 打印前几个告警的详细信息用于调试
            if (!allAlerts.isEmpty()) {
                for (int i = 0; i < Math.min(3, allAlerts.size()); i++) {
                    TerminalAlertInfo alert = allAlerts.get(i);
                    log.info("告警[{}]: identityMac={}, alertType={}, alertStatus={}, alertTime={}",
                            i, alert.getIdentityMac(), alert.getAlertType(), alert.getAlertStatus(), alert.getAlertTime());
                }
            }

            // 统计不同状态的告警
            int criticalAlerts = (int) allAlerts.stream()
                    .filter(alert -> "ACTIVE".equals(alert.getAlertStatus()) &&
                            (alert.getAlertType() != null &&
                             (alert.getAlertType().contains("TEMPERATURE") || alert.getAlertType().contains("CPU"))))
                    .count();
            int warningAlerts = (int) allAlerts.stream()
                    .filter(alert -> "ACTIVE".equals(alert.getAlertStatus()) &&
                            (alert.getAlertType() != null &&
                             (alert.getAlertType().contains("MEMORY") || alert.getAlertType().contains("DISK"))))
                    .count();
            int resolvedAlerts = (int) allAlerts.stream()
                    .filter(alert -> "RESOLVED".equals(alert.getAlertStatus()))
                    .count();

            log.debug("严重告警数: {}, 警告告警数: {}, 已解决告警数: {}", criticalAlerts, warningAlerts, resolvedAlerts);

            OverviewStatsResp stats = OverviewStatsResp.builder()
                    .totalTerminals(totalTerminals)
                    .onlineTerminals(onlineTerminals)
                    .offlineTerminals(offlineTerminals)
                    .totalAlerts(totalAlerts)
                    .criticalAlerts(criticalAlerts)
                    .warningAlerts(warningAlerts)
                    .resolvedAlerts(resolvedAlerts)
                    .timestamp(LocalDateTime.now().format(DATE_TIME_FORMATTER))
                    .build();

            log.debug("获取概览统计信息成功: {}", stats);
            return stats;

        } catch (Exception e) {
            log.error("获取概览统计信息失败: {}", e.getMessage(), e);
            // 返回默认值，避免前端报错
            return OverviewStatsResp.builder()
                    .totalTerminals(0)
                    .onlineTerminals(0)
                    .offlineTerminals(0)
                    .totalAlerts(0)
                    .criticalAlerts(0)
                    .warningAlerts(0)
                    .resolvedAlerts(0)
                    .timestamp(LocalDateTime.now().format(DATE_TIME_FORMATTER))
                    .build();
        }
    }

    @Override
    public List<OfflineTerminalResp> getOfflineTerminals(int limit) {
        try {
            log.debug("开始获取离线终端列表，限制数量: {}", limit);

            // 查询离线终端 - 不使用逻辑删除条件，因为MyBatis-Plus会自动处理
            QueryWrapper<TerminalBasicInfo> query = new QueryWrapper<>();
            query.and(wrapper -> wrapper.isNull("status").or().eq("status", 0))
                 .orderByDesc("update_time");

            Page<TerminalBasicInfo> page = new Page<>(1, limit);
            Page<TerminalBasicInfo> result = terminalBasicInfoService.page(page, query);

            List<OfflineTerminalResp> offlineTerminals = result.getRecords().stream()
                    .map(this::convertToOfflineTerminalResp)
                    .collect(Collectors.toList());

            log.debug("获取离线终端列表成功，数量: {}", offlineTerminals.size());
            return offlineTerminals;

        } catch (Exception e) {
            log.error("获取离线终端列表失败: {}", e.getMessage(), e);
            return List.of(); // 返回空列表，避免前端报错
        }
    }

    @Override
    public List<AlertTerminalResp> getAlertTerminals(int limit) {
        try {
            log.debug("开始获取告警终端列表，限制数量: {}", limit);

            // 查询活跃告警
            QueryWrapper<TerminalAlertInfo> query = new QueryWrapper<>();
            query
                 .orderByDesc("alert_time");

            Page<TerminalAlertInfo> page = new Page<>(1, limit);
            Page<TerminalAlertInfo> result = terminalAlertInfoService.page(page, query);

            List<AlertTerminalResp> alertTerminals = result.getRecords().stream()
                    .map(this::convertToAlertTerminalResp)
                    .collect(Collectors.toList());

            log.debug("获取告警终端列表成功，数量: {}", alertTerminals.size());
            return alertTerminals;

        } catch (Exception e) {
            log.error("获取告警终端列表失败: {}", e.getMessage(), e);
            return List.of(); // 返回空列表，避免前端报错
        }
    }

    /**
     * 转换为离线终端响应对象
     */
    private OfflineTerminalResp convertToOfflineTerminalResp(TerminalBasicInfo terminal) {
        // 计算离线时长
        Long offlineDurationMinutes = 0L;
        if (terminal.getUpdateTime() != null) {
            offlineDurationMinutes = ChronoUnit.MINUTES.between(terminal.getUpdateTime(), LocalDateTime.now());
        }

        return OfflineTerminalResp.builder()
                .deviceId(terminal.getDeviceId())
                .hostname(terminal.getHostname())
                .status(terminal.getStatus() != null ? terminal.getStatus() : 0)
                .lastUpdateTime(terminal.getUpdateTime() != null ?
                        terminal.getUpdateTime().format(DATE_TIME_FORMATTER) : "")
                .offlineDurationMinutes(offlineDurationMinutes)
                .userTags(terminal.getTags() != null ? terminal.getTags().toString() : null)
                .customFields(terminal.getCustomFields() != null ? terminal.getCustomFields().toString() : null)
                .build();
    }

    /**
     * 转换为告警终端响应对象
     */
    private AlertTerminalResp convertToAlertTerminalResp(TerminalAlertInfo alert) {
        log.debug("转换告警数据 - 告警ID: {}, 设备ID: {}, 指标ID: {}",
                alert.getId(), alert.getIdentityMac(), alert.getMetricId());

        return AlertTerminalResp.builder()
                .id(alert.getId())
                .alertId(alert.getAlertId()) // 添加告警业务ID字段
                .identityMac(alert.getIdentityMac())
                .hostname(getHostnameByDeviceId(alert.getIdentityMac()))
                .alertType(alert.getAlertType())
                .alertLevel(alert.getAlertLevel() != null ? alert.getAlertLevel() : "MEDIUM") // 从alert中获取告警级别，默认为MEDIUM
                .alertMessage(alert.getAlertDetails()) // 使用alertDetails作为消息
                .alertDetails(alert.getAlertDetails())
                .currentValue(alert.getCurrentValue())
                .threshold(alert.getThreshold())
                .metricId(alert.getMetricId()) // 添加指标ID字段
                .alertStatus(alert.getAlertStatus())
                .firstOccurrence(alert.getAlertTime() != null ?
                        alert.getAlertTime().format(DATE_TIME_FORMATTER) : "")
                .lastOccurrence(alert.getAlertTime() != null ?
                        alert.getAlertTime().format(DATE_TIME_FORMATTER) : "")
                .occurrenceCount(1) // 默认为1，实际应该从数据库统计
                .build();
    }

    /**
     * 根据设备ID获取主机名
     */
    private String getHostnameByDeviceId(String identityMac) {
        try {
            QueryWrapper<TerminalBasicInfo> query = new QueryWrapper<>();
            query.eq("identity_mac", identityMac);
            TerminalBasicInfo terminal = terminalBasicInfoService.getOne(query);
            return terminal != null ? terminal.getHostname() : identityMac;
        } catch (Exception e) {
            log.warn("获取设备主机名失败，设备ID: {}, 错误: {}", identityMac, e.getMessage());
            return identityMac;
        }
    }
}
