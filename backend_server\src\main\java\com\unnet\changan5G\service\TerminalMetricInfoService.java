package com.unnet.changan5G.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.unnet.changan5G.dto.terminal.TerminalMetricInfo;
import com.unnet.changan5G.entity.TerminalMetricInfoEntity;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 终端指标信息服务接口
 * 
 * <AUTHOR>
 * @date 2024-07-14
 */
public interface TerminalMetricInfoService extends IService<TerminalMetricInfoEntity> {

    /**
     * 保存终端指标信息
     */
    boolean saveMetricInfo(TerminalMetricInfo terminalMetricInfo);

    /**
     * 获取设备最新指标信息
     */
    TerminalMetricInfoEntity getLatestByDeviceId(String deviceId);

    /**
     * 获取设备指定时间范围内的指标信息
     */
    List<TerminalMetricInfoEntity> getByDeviceIdAndTimeRange(String deviceId, 
                                                            LocalDateTime startTime, 
                                                            LocalDateTime endTime);

    /**
     * 获取CPU温度异常的设备
     */
    List<TerminalMetricInfoEntity> getByCpuTempThreshold(Double threshold, LocalDateTime startTime);

    /**
     * 获取内存使用率异常的设备
     */
    List<TerminalMetricInfoEntity> getByMemoryPercentThreshold(Double threshold, LocalDateTime startTime);

    /**
     * 获取磁盘使用率异常的设备
     */
    List<TerminalMetricInfoEntity> getByDiskPercentThreshold(Double threshold, LocalDateTime startTime);

    /**
     * 删除历史数据
     */
    boolean deleteHistoryData(LocalDateTime beforeTime);

    /**
     * 获取设备指标统计信息
     */
    TerminalMetricInfoEntity getStatsByDeviceIdAndTimeRange(String deviceId, 
                                                           LocalDateTime startTime, 
                                                           LocalDateTime endTime);
}
