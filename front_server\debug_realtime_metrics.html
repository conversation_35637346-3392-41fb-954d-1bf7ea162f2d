<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>实时指标调试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .debug-section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .debug-section h3 {
            margin-top: 0;
            color: #333;
        }
        .api-test {
            background-color: #f8f9fa;
        }
        .data-display {
            background-color: #e8f5e8;
        }
        .error-display {
            background-color: #ffe6e6;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 10px;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>实时指标调试页面</h1>
        <p>这个页面用于调试实时指标API和数据处理逻辑</p>

        <div class="debug-section api-test">
            <h3>API测试</h3>
            <button onclick="testLatestAPI()">测试最新指标API</button>
            <button onclick="testHistoryAPI()">测试历史数据API</button>
            <button onclick="testDataProcessing()">测试数据处理</button>
            <div id="api-status"></div>
            <pre id="api-response"></pre>
        </div>

        <div class="debug-section data-display">
            <h3>数据显示</h3>
            <button onclick="showProcessedData()">显示处理后的数据</button>
            <button onclick="showTimePoints()">显示时间点</button>
            <div id="data-status"></div>
            <pre id="data-display"></pre>
        </div>

        <div class="debug-section error-display">
            <h3>错误信息</h3>
            <button onclick="clearErrors()">清除错误</button>
            <div id="error-status"></div>
            <pre id="error-display"></pre>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:8080';
        const IDENTITY_MAC = 'cc:dd:ee:52:3e:60';
        
        let errors = [];
        let latestData = null;
        let historyData = null;

        function showStatus(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        function showData(elementId, data) {
            const element = document.getElementById(elementId);
            element.textContent = JSON.stringify(data, null, 2);
        }

        function addError(error) {
            errors.push({
                timestamp: new Date().toISOString(),
                error: error
            });
            updateErrorDisplay();
        }

        function updateErrorDisplay() {
            showData('error-display', errors);
            if (errors.length > 0) {
                showStatus('error-status', `共有 ${errors.length} 个错误`, 'error');
            }
        }

        function clearErrors() {
            errors = [];
            document.getElementById('error-display').textContent = '';
            showStatus('error-status', '错误已清除', 'success');
        }

        async function testLatestAPI() {
            try {
                showStatus('api-status', '正在测试最新指标API...', 'info');
                
                const url = `${API_BASE_URL}/api/v1/admin/terminal/metrics/device/${IDENTITY_MAC}/latest`;
                console.log('请求URL:', url);
                
                const response = await fetch(url);
                const data = await response.json();
                
                latestData = data;
                showData('api-response', data);
                showStatus('api-status', '最新指标API测试成功', 'success');
                
            } catch (error) {
                console.error('最新指标API测试失败:', error);
                addError(`最新指标API测试失败: ${error.message}`);
                showStatus('api-status', `API测试失败: ${error.message}`, 'error');
            }
        }

        async function testHistoryAPI() {
            try {
                showStatus('api-status', '正在测试历史数据API...', 'info');
                
                const today = new Date();
                const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 0, 0, 0);
                const endOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 23, 59, 59);
                
                const formatDateTime = (date) => {
                    const year = date.getFullYear();
                    const month = String(date.getMonth() + 1).padStart(2, '0');
                    const day = String(date.getDate()).padStart(2, '0');
                    const hours = String(date.getHours()).padStart(2, '0');
                    const minutes = String(date.getMinutes()).padStart(2, '0');
                    const seconds = String(date.getSeconds()).padStart(2, '0');
                    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
                };

                const startTime = formatDateTime(startOfDay);
                const endTime = formatDateTime(endOfDay);
                
                const url = `${API_BASE_URL}/api/v1/admin/terminal/metrics/device/${IDENTITY_MAC}/range?startTime=${encodeURIComponent(startTime)}&endTime=${encodeURIComponent(endTime)}&page=0&size=1000`;
                console.log('请求URL:', url);
                
                const response = await fetch(url);
                const data = await response.json();
                
                historyData = data;
                showData('api-response', data);
                showStatus('api-status', `历史数据API测试成功，获取到 ${data.content?.length || 0} 条记录`, 'success');
                
            } catch (error) {
                console.error('历史数据API测试失败:', error);
                addError(`历史数据API测试失败: ${error.message}`);
                showStatus('api-status', `API测试失败: ${error.message}`, 'error');
            }
        }

        function testDataProcessing() {
            try {
                showStatus('data-status', '正在测试数据处理...', 'info');
                
                if (!historyData || !historyData.content) {
                    throw new Error('没有历史数据，请先测试历史数据API');
                }
                
                // 模拟前端数据处理逻辑
                const processedData = [];
                const sortedData = historyData.content.sort((a, b) => {
                    const timeA = new Date(a.metricTime);
                    const timeB = new Date(b.metricTime);
                    return timeA.getTime() - timeB.getTime();
                });
                
                for (const item of sortedData) {
                    const metricTime = new Date(item.metricTime);
                    
                    const processedItem = {
                        time: metricTime,
                        cpuTemp: item.cpuTemp || 0,
                        cpuUsage: item.cpuPercent || 0,
                        memory: item.memoryPercent || 0,
                        disk: (item.diskUsage && item.diskUsage[0]) ? item.diskUsage[0].percent : 0,
                        cpuUserUsage: item.cpuUsage ? parseFloat(item.cpuUsage.user || 0) : 0,
                        cpuSystemUsage: item.cpuUsage ? parseFloat(item.cpuUsage.sys || 0) : 0,
                        socTemp: item.temperatures ? (item.temperatures['soc-thermal'] || 0) : 0,
                        gpuTemp: item.temperatures ? (item.temperatures['gpu-thermal'] || 0) : 0,
                        memoryUsed: item.memoryUsage ? (item.memoryUsage.used / (1024 * 1024 * 1024)) : 0,
                        memoryAvailable: item.memoryUsage ? (item.memoryUsage.available / (1024 * 1024 * 1024)) : 0,
                        collectFileCount: item.cdata ? item.cdata.count : 0,
                        compressFileCount: item.zdata ? item.zdata.count : 0,
                        totalConnections: item.groupUsage ? Object.keys(item.groupUsage).length : 0,
                        totalPps: item.groupBps || 0
                    };
                    processedData.push(processedItem);
                }
                
                showData('data-display', {
                    originalCount: historyData.content.length,
                    processedCount: processedData.length,
                    sampleProcessed: processedData.slice(0, 2),
                    timeRange: {
                        start: processedData[0]?.time,
                        end: processedData[processedData.length - 1]?.time
                    }
                });
                showStatus('data-status', `数据处理成功，处理了 ${processedData.length} 条记录`, 'success');
                
            } catch (error) {
                console.error('数据处理测试失败:', error);
                addError(`数据处理测试失败: ${error.message}`);
                showStatus('data-status', `数据处理失败: ${error.message}`, 'error');
            }
        }

        function showProcessedData() {
            if (historyData) {
                testDataProcessing();
            } else {
                showStatus('data-status', '请先测试历史数据API', 'error');
            }
        }

        function showTimePoints() {
            if (!historyData || !historyData.content) {
                showStatus('data-status', '没有历史数据', 'error');
                return;
            }
            
            const timePoints = historyData.content.map(item => ({
                original: item.metricTime,
                parsed: new Date(item.metricTime),
                formatted: new Date(item.metricTime).toLocaleString('zh-CN')
            }));
            
            showData('data-display', {
                count: timePoints.length,
                timePoints: timePoints
            });
            showStatus('data-status', `显示了 ${timePoints.length} 个时间点`, 'success');
        }

        // 页面加载完成后自动测试
        window.addEventListener('load', () => {
            console.log('调试页面加载完成');
            showStatus('api-status', '页面已加载，可以开始测试', 'info');
        });
    </script>
</body>
</html>
