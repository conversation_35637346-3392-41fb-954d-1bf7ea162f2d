package com.unnet.jmanul.business.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.unnet.jmanul.business.entity.TerminalAlertInfo;
import com.unnet.jmanul.business.entity.dto.AlertListRequest;

import java.util.Map;

/**
 * 终端告警信息服务接口
 */
public interface TerminalAlertInfoService extends IService<TerminalAlertInfo> {

    /**
     * 分页查询告警列表
     */
    IPage<TerminalAlertInfo> getAlertPage(AlertListRequest request);

    /**
     * 根据告警ID获取告警详情
     */
    TerminalAlertInfo getAlertById(String alertId);

    /**
     * 确认告警
     */
    boolean acknowledgeAlert(String alertId, String acknowledgedBy);

    /**
     * 解决告警
     */
    boolean resolveAlert(String alertId, String resolvedBy, String resolveComment);

    /**
     * 发送钉钉通知
     */
    boolean sendDingTalkNotification(String alertId);

    /**
     * 获取告警统计信息
     */
    Map<String, Object> getAlertStatistics();

    /**
     * 根据设备MAC地址获取活跃告警数量
     */
    int getActiveAlertCountByIdentityMac(String identityMac);

    /**
     * 创建新告警
     */
    boolean createAlert(TerminalAlertInfo alert);

    /**
     * 检查是否存在相同的活跃告警
     */
    boolean hasActiveAlert(String deviceId, String alertType);

    /**
     * 清理历史告警数据
     */
    boolean cleanHistoryAlerts(int keepDays);
}
