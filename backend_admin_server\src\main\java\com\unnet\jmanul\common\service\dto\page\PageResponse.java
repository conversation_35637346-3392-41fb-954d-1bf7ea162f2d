package com.unnet.jmanul.common.service.dto.page;

import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PageResponse<T> {
    List<T> items;
    long total;
    long pages;

    public PageResponse<T> of(final IPage<T> page) {
        this.items = page.getRecords();
        this.total = page.getTotal();
        this.pages = page.getPages();
        return this;
    }
}
