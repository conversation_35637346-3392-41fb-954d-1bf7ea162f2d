-- 长安5G管理平台用户认证初始化
-- 创建时间：2024-07-16
-- 说明：基于jmanul现有用户表，初始化管理员和普通用户账号，配置权限

-- 1. 清理现有数据（可选，根据需要决定是否执行）
-- DELETE FROM `user` WHERE username IN ('admin', 'user');
-- DELETE FROM `casbin_rule` WHERE v0 IN ('admin', 'user');

-- 2. 插入默认用户账号
-- 管理员账号：admin/admin123
INSERT INTO `user` (
    `username`, 
    `name`, 
    `password`, 
    `account_source`, 
    `enable`, 
    `locked`, 
    `account_expire_date`, 
    `credential_expire_date`,
    `created_by`,
    `created_at`,
    `updated_at`,
    `deleted_at`
) VALUES 
(
    'admin', 
    '系统管理员', 
    '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBOsl7iKTVEFDa', -- admin123
    'INTERNAL', 
    1, 
    0, 
    '2099-12-31 23:59:59', 
    '2099-12-31 23:59:59',
    'system',
    NOW(),
    NOW(),
    0
),
(
    'user', 
    '普通用户', 
    '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBOsl7iKTVEFDa', -- admin123
    'INTERNAL', 
    1, 
    0, 
    '2099-12-31 23:59:59', 
    '2099-12-31 23:59:59',
    'system',
    NOW(),
    NOW(),
    0
) ON DUPLICATE KEY UPDATE 
    `name` = VALUES(`name`),
    `enable` = VALUES(`enable`),
    `locked` = VALUES(`locked`),
    `updated_at` = NOW();

-- 3. 配置用户角色关系（使用Casbin的g规则）
-- 为用户分配角色
INSERT INTO `casbin_rule` (`ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES 
('g', 'admin', 'admin', '', '', '', ''),
('g', 'user', 'user', '', '', '', '')
ON DUPLICATE KEY UPDATE 
    `v0` = VALUES(`v0`),
    `v1` = VALUES(`v1`);

-- 4. 配置角色权限（p规则）
-- 清理现有的长安5G相关权限
DELETE FROM `casbin_rule` WHERE `ptype` = 'p' AND `v1` LIKE '%terminals%';
DELETE FROM `casbin_rule` WHERE `ptype` = 'p' AND `v1` LIKE '%terminal/metrics%';
DELETE FROM `casbin_rule` WHERE `ptype` = 'p' AND `v1` LIKE '%alerts%';
DELETE FROM `casbin_rule` WHERE `ptype` = 'p' AND `v1` LIKE '%changan5g%';

-- 管理员权限：拥有所有权限
INSERT INTO `casbin_rule` (`ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES 
-- 长安5G终端管理权限
('p', 'admin', '/api/v1/admin/terminals*', 'GET', '', '', ''),
('p', 'admin', '/api/v1/admin/terminals*', 'POST', '', '', ''),
('p', 'admin', '/api/v1/admin/terminals*', 'PUT', '', '', ''),
('p', 'admin', '/api/v1/admin/terminals*', 'DELETE', '', '', ''),

-- 长安5G终端指标查询权限
('p', 'admin', '/api/v1/admin/terminal/metrics*', 'GET', '', '', ''),
('p', 'admin', '/api/v1/admin/terminal/metrics*', 'POST', '', '', ''),

-- 长安5G告警管理权限
('p', 'admin', '/api/v1/admin/alerts*', 'GET', '', '', ''),
('p', 'admin', '/api/v1/admin/alerts*', 'POST', '', '', ''),
('p', 'admin', '/api/v1/admin/alerts*', 'PUT', '', '', ''),
('p', 'admin', '/api/v1/admin/alerts*', 'DELETE', '', '', ''),

-- 长安5G健康检查权限
('p', 'admin', '/api/v1/admin/changan5g*', 'GET', '', '', ''),

-- 用户管理权限
('p', 'admin', '/api/v1/admin/users*', 'GET', '', '', ''),
('p', 'admin', '/api/v1/admin/users*', 'POST', '', '', ''),
('p', 'admin', '/api/v1/admin/users*', 'PUT', '', '', ''),
('p', 'admin', '/api/v1/admin/users*', 'DELETE', '', '', ''),

-- 权限管理
('p', 'admin', '/api/v1/admin/rbac*', 'GET', '', '', ''),
('p', 'admin', '/api/v1/admin/rbac*', 'POST', '', '', ''),
('p', 'admin', '/api/v1/admin/rbac*', 'PUT', '', '', ''),
('p', 'admin', '/api/v1/admin/rbac*', 'DELETE', '', '', ''),

-- 示例模块权限
('p', 'admin', '/api/v1/admin/samples*', 'GET', '', '', ''),
('p', 'admin', '/api/v1/admin/samples*', 'POST', '', '', ''),
('p', 'admin', '/api/v1/admin/samples*', 'PUT', '', '', ''),
('p', 'admin', '/api/v1/admin/samples*', 'DELETE', '', '', ''),

-- 账号管理权限
('p', 'admin', '/api/v1/account*', 'GET', '', '', ''),
('p', 'admin', '/api/v1/account*', 'POST', '', '', ''),
('p', 'admin', '/api/v1/account*', 'PUT', '', '', ''),
('p', 'admin', '/api/v1/account*', 'DELETE', '', '', ''),

-- Token刷新权限
('p', 'admin', '/api/v1/auth/refreshToken', 'GET', '', '', '');

-- 普通用户权限：只有查看权限
INSERT INTO `casbin_rule` (`ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES 
-- 长安5G终端查看权限
('p', 'user', '/api/v1/admin/terminals*', 'GET', '', '', ''),

-- 长安5G终端指标查看权限
('p', 'user', '/api/v1/admin/terminal/metrics*', 'GET', '', '', ''),

-- 长安5G告警查看权限
('p', 'user', '/api/v1/admin/alerts*', 'GET', '', '', ''),

-- 长安5G健康检查权限
('p', 'user', '/api/v1/admin/changan5g*', 'GET', '', '', ''),

-- 示例模块查看权限
('p', 'user', '/api/v1/admin/samples*', 'GET', '', '', ''),

-- 用户查看权限（只能查看用户列表，不能增删改）
('p', 'user', '/api/v1/admin/users*', 'GET', '', '', ''),

-- 权限查看权限
('p', 'user', '/api/v1/admin/rbac*', 'GET', '', '', ''),

-- 账号管理权限（可以修改自己的信息）
('p', 'user', '/api/v1/account*', 'GET', '', '', ''),
('p', 'user', '/api/v1/account*', 'PUT', '', '', ''),

-- Token刷新权限
('p', 'user', '/api/v1/auth/refreshToken', 'GET', '', '', '');

-- 5. 创建用户角色映射表（可选，用于更复杂的角色管理）
CREATE TABLE IF NOT EXISTS `user_role` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `user_id` bigint(20) NOT NULL COMMENT '用户ID',
    `role_name` varchar(50) NOT NULL COMMENT '角色名称',
    `created_by` varchar(50) DEFAULT NULL COMMENT '创建人',
    `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_by` varchar(50) DEFAULT NULL COMMENT '更新人',
    `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at` bigint(20) DEFAULT 0 COMMENT '删除时间戳',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_user_role` (`user_id`, `role_name`, `deleted_at`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_role_name` (`role_name`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户角色关系表';

-- 6. 插入用户角色关系数据
INSERT INTO `user_role` (`user_id`, `role_name`, `created_by`) 
SELECT u.id, 'admin', 'system' FROM `user` u WHERE u.username = 'admin' AND u.deleted_at = 0
ON DUPLICATE KEY UPDATE `updated_at` = NOW();

INSERT INTO `user_role` (`user_id`, `role_name`, `created_by`) 
SELECT u.id, 'user', 'system' FROM `user` u WHERE u.username = 'user' AND u.deleted_at = 0
ON DUPLICATE KEY UPDATE `updated_at` = NOW();

COMMIT;
