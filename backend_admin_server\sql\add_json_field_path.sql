-- 为指标阈值配置表添加JSON字段路径字段
-- 执行时间：2024-07-25

-- 添加JSON字段路径字段
ALTER TABLE `metric_threshold_config` 
ADD COLUMN `json_field_path` varchar(200) DEFAULT NULL COMMENT 'JSON字段路径，支持嵌套路径，如：cpu_temp, memory_usage.percent, disk_usage[0].percent' 
AFTER `metric_description`;

-- 为现有配置添加对应的JSON字段路径
UPDATE `metric_threshold_config` SET `json_field_path` = 'cpu_temp' WHERE `metric_type` = 'CPU_TEMPERATURE';
UPDATE `metric_threshold_config` SET `json_field_path` = 'memory_percent' WHERE `metric_type` = 'MEMORY_USAGE';
UPDATE `metric_threshold_config` SET `json_field_path` = 'disk_usage[*].percent' WHERE `metric_type` = 'DISK_USAGE';
UPDATE `metric_threshold_config` SET `json_field_path` = 'disk_data_percent' WHERE `metric_type` = 'DISK_DATA_USAGE';
UPDATE `metric_threshold_config` SET `json_field_path` = 'disk_system_percent' WHERE `metric_type` = 'DISK_SYSTEM_USAGE';
UPDATE `metric_threshold_config` SET `json_field_path` = 'expired_date' WHERE `metric_type` = 'LICENSE_EXPIRY';

-- 将指标类型从英文枚举改为中文描述
UPDATE `metric_threshold_config` SET `metric_type` = 'CPU告警' WHERE `metric_type` = 'CPU_TEMPERATURE';
UPDATE `metric_threshold_config` SET `metric_type` = '内存告警' WHERE `metric_type` = 'MEMORY_USAGE';
UPDATE `metric_threshold_config` SET `metric_type` = '磁盘告警' WHERE `metric_type` = 'DISK_USAGE';
UPDATE `metric_threshold_config` SET `metric_type` = '数据磁盘告警' WHERE `metric_type` = 'DISK_DATA_USAGE';
UPDATE `metric_threshold_config` SET `metric_type` = '系统磁盘告警' WHERE `metric_type` = 'DISK_SYSTEM_USAGE';
UPDATE `metric_threshold_config` SET `metric_type` = '授权过期告警' WHERE `metric_type` = 'LICENSE_EXPIRY';

-- 添加索引以提高查询性能
CREATE INDEX `idx_json_field_path` ON `metric_threshold_config` (`json_field_path`);

-- 移除指标类型的唯一索引，因为现在允许相同指标类型的多个配置
DROP INDEX `uk_metric_type` ON `metric_threshold_config`;

-- 添加普通索引以提高查询性能
CREATE INDEX `idx_metric_type` ON `metric_threshold_config` (`metric_type`);

COMMIT;
