<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unnet.jmanul.system.mapper.UserMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.unnet.jmanul.system.entity.User">
        <id column="id" property="id" />
        <result column="username" property="username" />
        <result column="name" property="name" />
        <result column="password" property="password" />
        <result column="account_source" property="accountSource" />
        <result column="enable" property="enable" />
        <result column="locked" property="locked" />
        <result column="account_expire_date" property="accountExpireDate" />
        <result column="credential_expire_date" property="credentialExpireDate" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, username, name, password, account_source, enable, locked, account_expire_date, credential_expire_date
    </sql>

</mapper>
