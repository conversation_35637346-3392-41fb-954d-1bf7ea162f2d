package com.unnet.jmanul.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.unnet.jmanul.business.entity.TerminalAlertInfo;
import com.unnet.jmanul.business.entity.dto.AlertListRequest;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 终端告警信息Mapper接口
 */
@Mapper
public interface TerminalAlertInfoMapper extends BaseMapper<TerminalAlertInfo> {

    /**
     * 分页查询告警列表
     */
    @Select("<script>" +
            "SELECT id, alert_id, identity_mac, alert_type, alert_level, alert_details, metric_name, threshold, " +
            "current_value, alert_time, alert_status, resolved_time, acknowledged_time, " +
            "acknowledged_by, resolve_comment, notification_sent, notification_time, " +
            "create_time, update_time " +
            "FROM terminal_alert_info " +
            "<where>" +
            "<if test='request.alertId != null and request.alertId != \"\"'>" +
            "AND alert_id LIKE CONCAT('%', #{request.alertId}, '%') " +
            "</if>" +
            "<if test='request.identityMac != null and request.identityMac != \"\"'>" +
            "AND identity_mac LIKE CONCAT('%', #{request.identityMac}, '%') " +
            "</if>" +
            "<if test='request.alertType != null and request.alertType != \"\"'>" +
            "AND alert_type = #{request.alertType} " +
            "</if>" +
            "<if test='request.alertLevel != null and request.alertLevel != \"\"'>" +
            "AND alert_level = #{request.alertLevel} " +
            "</if>" +
            "<if test='request.alertStatus != null and request.alertStatus != \"\"'>" +
            "AND alert_status = #{request.alertStatus} " +
            "</if>" +
            "<if test='request.startTime != null and request.startTime != \"\"'>" +
            "AND alert_time >= #{request.startTime} " +
            "</if>" +
            "<if test='request.endTime != null and request.endTime != \"\"'>" +
            "AND alert_time &lt;= #{request.endTime} " +
            "</if>" +
            "</where>" +
            "ORDER BY alert_time DESC" +
            "</script>")
    IPage<TerminalAlertInfo> selectAlertPage(Page<TerminalAlertInfo> page, @Param("request") AlertListRequest request);

    /**
     * 根据设备MAC地址查询活跃告警数量
     */
    @Select("SELECT COUNT(*) FROM terminal_alert_info WHERE identity_mac = #{identityMac} AND alert_status = 'ACTIVE'")
    int countActiveAlertsByIdentityMac(@Param("identityMac") String identityMac);

    /**
     * 根据告警类型查询活跃告警数量
     */
    @Select("SELECT COUNT(*) FROM terminal_alert_info WHERE alert_type = #{alertType} AND alert_status = 'ACTIVE'")
    int countActiveAlertsByType(@Param("alertType") String alertType);

    /**
     * 查询所有活跃告警数量
     */
    @Select("SELECT COUNT(*) FROM terminal_alert_info WHERE alert_status = 'ACTIVE'")
    int countActiveAlerts();

    /**
     * 查询已解决告警数量
     */
    @Select("SELECT COUNT(*) FROM terminal_alert_info WHERE alert_status = 'RESOLVED'")
    int countResolvedAlerts();

    /**
     * 查询已确认告警数量
     */
    @Select("SELECT COUNT(*) FROM terminal_alert_info WHERE alert_status = 'ACKNOWLEDGED'")
    int countAcknowledgedAlerts();

    /**
     * 确认告警
     */
    @Update("UPDATE terminal_alert_info SET alert_status = 'ACKNOWLEDGED', " +
            "acknowledged_time = #{acknowledgedTime}, acknowledged_by = #{acknowledgedBy}, " +
            "update_time = #{updateTime} WHERE alert_id = #{alertId}")
    int acknowledgeAlert(@Param("alertId") String alertId, 
                        @Param("acknowledgedTime") LocalDateTime acknowledgedTime,
                        @Param("acknowledgedBy") String acknowledgedBy,
                        @Param("updateTime") LocalDateTime updateTime);

    /**
     * 解决告警
     */
    @Update("UPDATE terminal_alert_info SET alert_status = 'RESOLVED', " +
            "resolved_time = #{resolvedTime}, resolve_comment = #{resolveComment}, " +
            "update_time = #{updateTime} WHERE alert_id = #{alertId}")
    int resolveAlert(@Param("alertId") String alertId,
                    @Param("resolvedTime") LocalDateTime resolvedTime,
                    @Param("resolveComment") String resolveComment,
                    @Param("updateTime") LocalDateTime updateTime);

    /**
     * 更新通知状态
     */
    @Update("UPDATE terminal_alert_info SET notification_sent = #{sent}, " +
            "notification_time = #{notificationTime}, update_time = #{updateTime} " +
            "WHERE alert_id = #{alertId}")
    int updateNotificationStatus(@Param("alertId") String alertId,
                                @Param("sent") Boolean sent,
                                @Param("notificationTime") LocalDateTime notificationTime,
                                @Param("updateTime") LocalDateTime updateTime);

    /**
     * 根据设备MAC地址和告警类型查询最新的活跃告警
     */
    @Select("SELECT * FROM terminal_alert_info WHERE identity_mac = #{identityMac} AND alert_type = #{alertType} " +
            "AND alert_status = 'ACTIVE' ORDER BY alert_time DESC LIMIT 1")
    TerminalAlertInfo selectLatestActiveAlert(@Param("identityMac") String identityMac, @Param("alertType") String alertType);

    /**
     * 删除历史告警数据（保留最近N天的数据）
     */
    @Update("DELETE FROM terminal_alert_info WHERE create_time < #{beforeTime}")
    int deleteHistoryAlerts(@Param("beforeTime") LocalDateTime beforeTime);
}
