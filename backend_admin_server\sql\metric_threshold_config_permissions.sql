-- 指标阈值配置权限配置
-- 创建时间：2024-07-21
-- 说明：为指标阈值配置管理功能添加权限规则

-- 开始事务
START TRANSACTION;

-- 为admin角色添加指标阈值配置管理权限（完整的CRUD权限）
INSERT IGNORE INTO `casbin_rule` (`ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES 
('p', 'admin', '/api/v1/admin/metric-threshold-config*', 'GET', '', '', ''),
('p', 'admin', '/api/v1/admin/metric-threshold-config*', 'POST', '', '', ''),
('p', 'admin', '/api/v1/admin/metric-threshold-config*', 'PUT', '', '', ''),
('p', 'admin', '/api/v1/admin/metric-threshold-config*', 'DELETE', '', '', '');

-- 为auditor角色添加指标阈值配置查看权限（只读权限）
INSERT IGNORE INTO `casbin_rule` (`ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES 
('p', 'auditor', '/api/v1/admin/metric-threshold-config*', 'GET', '', '', '');

-- 为user角色添加指标阈值配置查看权限（只读权限）
INSERT IGNORE INTO `casbin_rule` (`ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES 
('p', 'user', '/api/v1/admin/metric-threshold-config*', 'GET', '', '', '');

-- 提交事务
COMMIT;

-- 验证权限是否添加成功
SELECT 
    ptype,
    v0 as role,
    v1 as resource,
    v2 as action
FROM casbin_rule 
WHERE v1 LIKE '%metric-threshold-config%' 
ORDER BY v0, v2;
