package com.unnet.jmanul.common.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.convert.converter.Converter;
import org.springframework.data.elasticsearch.client.ClientConfiguration;
import org.springframework.data.elasticsearch.client.RestClients;
import org.springframework.data.elasticsearch.core.ElasticsearchOperations;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.convert.ElasticsearchCustomConversions;
import org.springframework.data.elasticsearch.repository.config.EnableElasticsearchRepositories;
import org.elasticsearch.client.RestHighLevelClient;

import java.net.URI;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * Elasticsearch配置类 - 长安5G业务模块
 *
 * <AUTHOR>
 * @date 2024-07-16
 */
@Configuration
@EnableElasticsearchRepositories(basePackages = "com.unnet.jmanul.business.mapper")
public class ElasticsearchConfig {

    @Value("${spring.elasticsearch.uris:}")
    private String[] elasticsearchUrls;

    @Value("${spring.elasticsearch.username:}")
    private String username;

    @Value("${spring.elasticsearch.password:}")
    private String password;

    @Bean
    public RestHighLevelClient client() {
        ClientConfiguration clientConfiguration = ClientConfiguration.builder()
                .connectedTo(elasticsearchUrls)
                .withBasicAuth(username, password)
                .build();

        return RestClients.create(clientConfiguration).rest();
    }

    @Bean
    public ElasticsearchOperations elasticsearchTemplate() {
        return new ElasticsearchRestTemplate(client());
    }

    @Bean
    public ElasticsearchCustomConversions elasticsearchCustomConversions() {
        List<Converter<?, ?>> converters = new ArrayList<>();
        converters.add(new StringToLocalDateTimeConverter());
        converters.add(new LocalDateTimeToStringConverter());
        converters.add(new DateToLocalDateTimeConverter());
        converters.add(new LocalDateTimeToDateConverter());
        converters.add(new LongToLocalDateTimeConverter());
        return new ElasticsearchCustomConversions(converters);
    }

    /**
     * 字符串转LocalDateTime转换器
     */
    public static class StringToLocalDateTimeConverter implements Converter<String, LocalDateTime> {
        private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        
        @Override
        public LocalDateTime convert(String source) {
            if (source == null || source.isEmpty()) {
                return null;
            }
            
            try {
                return LocalDateTime.parse(source, DATE_TIME_FORMATTER);
            } catch (Exception e) {
                try {
                    return LocalDateTime.parse(source + " 00:00:00", DATE_TIME_FORMATTER);
                } catch (Exception ex) {
                    throw new IllegalArgumentException("无法将字符串 '" + source + "' 转换为LocalDateTime", ex);
                }
            }
        }
    }
    
    /**
     * LocalDateTime转字符串转换器
     */
    public static class LocalDateTimeToStringConverter implements Converter<LocalDateTime, String> {
        private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        
        @Override
        public String convert(LocalDateTime source) {
            return source == null ? null : source.format(DATE_TIME_FORMATTER);
        }
    }
    
    /**
     * Date转LocalDateTime转换器
     */
    public static class DateToLocalDateTimeConverter implements Converter<Date, LocalDateTime> {
        @Override
        public LocalDateTime convert(Date source) {
            return source == null ? null : 
                LocalDateTime.ofInstant(source.toInstant(), ZoneId.systemDefault());
        }
    }
    
    /**
     * LocalDateTime转Date转换器
     */
    public static class LocalDateTimeToDateConverter implements Converter<LocalDateTime, Date> {
        @Override
        public Date convert(LocalDateTime source) {
            return source == null ? null : 
                Date.from(source.atZone(ZoneId.systemDefault()).toInstant());
        }
    }
    
    /**
     * Long转LocalDateTime转换器
     */
    public static class LongToLocalDateTimeConverter implements Converter<Long, LocalDateTime> {
        @Override
        public LocalDateTime convert(Long source) {
            return source == null ? null : 
                LocalDateTime.ofInstant(new Date(source).toInstant(), ZoneId.systemDefault());
        }
    }
}
