package com.unnet.jmanul.service;

import com.unnet.jmanul.config.MinioDevConfig;
import com.unnet.jmanul.config.MinioProdConfig;
import io.minio.*;
import io.minio.http.Method;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.InputStream;
import java.util.concurrent.TimeUnit;

/**
 * MinIO文件服务类
 * 
 * <AUTHOR>
 * @since 2025-07-28
 */
@Slf4j
@Service
public class MinioService {

    @Autowired
    private MinioClient minioClient;

    @Autowired(required = false)
    private MinioDevConfig minioDevConfig;

    @Autowired(required = false)
    private MinioProdConfig minioProdConfig;

    @Autowired
    private MinioClusterService minioClusterService;

    /**
     * 获取存储桶名称
     */
    private String getBucket() {
        if (minioProdConfig != null) {
            return minioProdConfig.getBucket();
        } else if (minioDevConfig != null) {
            return minioDevConfig.getBucket();
        }
        throw new RuntimeException("未找到MinIO配置");
    }

    /**
     * 是否启用集群模式
     */
    private boolean isClusterEnabled() {
        if (minioProdConfig != null) {
            return minioProdConfig.getCluster().getEnabled();
        }
        return false; // 开发环境默认不启用集群
    }

    /**
     * 检查存储桶是否存在，不存在则创建
     * 支持集群模式的重试机制
     */
    public void ensureBucketExists() {
        try {
            String bucketName = getBucket();

            if (isClusterEnabled()) {
                // 集群模式：使用重试机制
                minioClusterService.executeWithRetry(client -> {
                    boolean exists = client.bucketExists(BucketExistsArgs.builder()
                            .bucket(bucketName)
                            .build());

                    if (!exists) {
                        client.makeBucket(MakeBucketArgs.builder()
                                .bucket(bucketName)
                                .build());
                        log.info("创建MinIO存储桶成功: {}", bucketName);
                    }
                    return null;
                });
            } else {
                // 单节点模式：使用默认客户端
                boolean exists = minioClient.bucketExists(BucketExistsArgs.builder()
                        .bucket(bucketName)
                        .build());

                if (!exists) {
                    minioClient.makeBucket(MakeBucketArgs.builder()
                            .bucket(bucketName)
                            .build());
                    log.info("创建MinIO存储桶成功: {}", bucketName);
                }
            }
        } catch (Exception e) {
            log.error("检查或创建MinIO存储桶失败", e);
            throw new RuntimeException("MinIO存储桶操作失败", e);
        }
    }

    /**
     * 获取文件下载URL
     * 支持集群模式的重试机制
     *
     * @param objectName 对象名称
     * @param expiry 过期时间（秒）
     * @return 下载URL
     */
    public String getDownloadUrl(String objectName, int expiry) {
        try {
            String bucketName = getBucket();

            if (isClusterEnabled()) {
                // 集群模式：使用重试机制
                return minioClusterService.executeWithRetry(client ->
                    client.getPresignedObjectUrl(GetPresignedObjectUrlArgs.builder()
                            .method(Method.GET)
                            .bucket(bucketName)
                            .object(objectName)
                            .expiry(expiry, TimeUnit.SECONDS)
                            .build())
                );
            } else {
                // 单节点模式：使用默认客户端
                return minioClient.getPresignedObjectUrl(GetPresignedObjectUrlArgs.builder()
                        .method(Method.GET)
                        .bucket(bucketName)
                        .object(objectName)
                        .expiry(expiry, TimeUnit.SECONDS)
                        .build());
            }
        } catch (Exception e) {
            log.error("获取文件下载URL失败: {}", objectName, e);
            throw new RuntimeException("获取文件下载URL失败", e);
        }
    }

    /**
     * 获取文件输入流
     * 支持集群模式的重试机制
     *
     * @param objectName 对象名称
     * @return 文件输入流
     */
    public InputStream getFileStream(String objectName) {
        try {
            String bucketName = getBucket();

            if (isClusterEnabled()) {
                // 集群模式：使用重试机制
                return minioClusterService.executeWithRetry(client ->
                    client.getObject(GetObjectArgs.builder()
                            .bucket(bucketName)
                            .object(objectName)
                            .build())
                );
            } else {
                // 单节点模式：使用默认客户端
                return minioClient.getObject(GetObjectArgs.builder()
                        .bucket(bucketName)
                        .object(objectName)
                        .build());
            }
        } catch (Exception e) {
            log.error("获取文件流失败: {}", objectName, e);
            throw new RuntimeException("获取文件流失败", e);
        }
    }

    /**
     * 删除文件
     * 支持集群模式的重试机制
     *
     * @param objectName 对象名称
     * @return 删除结果
     */
    public boolean deleteFile(String objectName) {
        try {
            String bucketName = getBucket();

            if (isClusterEnabled()) {
                // 集群模式：使用重试机制
                minioClusterService.executeWithRetry(client -> {
                    client.removeObject(RemoveObjectArgs.builder()
                            .bucket(bucketName)
                            .object(objectName)
                            .build());
                    return true;
                });
            } else {
                // 单节点模式：使用默认客户端
                minioClient.removeObject(RemoveObjectArgs.builder()
                        .bucket(bucketName)
                        .object(objectName)
                        .build());
            }

            log.info("文件删除成功: {}", objectName);
            return true;
        } catch (Exception e) {
            log.error("文件删除失败: {}", objectName, e);
            return false;
        }
    }

    /**
     * 检查文件是否存在
     * 支持集群模式的重试机制
     *
     * @param objectName 对象名称
     * @return 是否存在
     */
    public boolean isFileExists(String objectName) {
        try {
            String bucketName = getBucket();

            if (isClusterEnabled()) {
                // 集群模式：使用重试机制
                return minioClusterService.executeWithRetry(client -> {
                    client.statObject(StatObjectArgs.builder()
                            .bucket(bucketName)
                            .object(objectName)
                            .build());
                    return true;
                });
            } else {
                // 单节点模式：使用默认客户端
                minioClient.statObject(StatObjectArgs.builder()
                        .bucket(bucketName)
                        .object(objectName)
                        .build());
                return true;
            }
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 获取集群状态信息
     *
     * @return 集群状态
     */
    public Object getClusterStatus() {
        return minioClusterService.getClusterStatus();
    }
}
