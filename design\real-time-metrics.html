<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>实时指标页面</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            color: #333;
            padding: 20px;
        }

        .breadcrumb {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            font-size: 14px;
        }

        .breadcrumb a {
            color: #667eea;
            text-decoration: none;
            cursor: pointer;
        }

        .breadcrumb a:hover {
            text-decoration: underline;
        }

        .breadcrumb span {
            margin: 0 8px;
            color: #999;
        }

        .card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            overflow: hidden;
        }

        .card-header {
            padding: 20px;
            border-bottom: 1px solid #eee;
            background: #fafafa;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .card-header h3 {
            font-size: 18px;
            font-weight: 600;
            color: #333;
        }

        .card-body {
            padding: 20px;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s;
            text-decoration: none;
            display: inline-block;
        }

        .btn-primary {
            background: #667eea;
            color: white;
        }

        .btn-primary:hover {
            background: #5a6fd8;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
            border-left: 4px solid #667eea;
        }

        .stat-number {
            font-size: 32px;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #666;
            font-size: 14px;
        }

        .refresh-info {
            background: #e3f2fd;
            border: 1px solid #bbdefb;
            border-radius: 4px;
            padding: 10px;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .refresh-info i {
            color: #1976d2;
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .metric-section {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
        }

        .metric-section h5 {
            color: #495057;
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 15px;
            padding-bottom: 8px;
            border-bottom: 2px solid #667eea;
        }

        .metric-section .metric-row {
            margin-bottom: 12px;
            padding: 8px 0;
            border-bottom: 1px solid #dee2e6;
        }

        .metric-section .metric-row:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }

        .metric-section .metric-label {
            font-size: 13px;
            color: #6c757d;
            margin-bottom: 5px;
            font-weight: 500;
        }

        .metric-section .metric-value {
            font-size: 14px;
            color: #212529;
            font-weight: 600;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #eee;
            border-radius: 4px;
            overflow: hidden;
            margin-top: 5px;
        }

        .progress-fill {
            height: 100%;
            background: #667eea;
            transition: width 0.3s ease;
        }

        .progress-fill.warning {
            background: #ffc107;
        }

        .progress-fill.danger {
            background: #dc3545;
        }

        .time-info {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 12px;
            margin-bottom: 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 13px;
        }

        .time-info .time-label {
            color: #6c757d;
            font-weight: 500;
        }

        .time-info .time-value {
            color: #495057;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="breadcrumb">
        <a onclick="goBack()">终端管理</a>
        <span>></span>
        <span>实时指标</span>
    </div>
    
    <div class="card">
        <div class="card-header">
            <h3>终端实时指标 - <span id="currentDeviceId"></span></h3>
            <button class="btn btn-primary" onclick="refreshRealTimeMetrics()">
                <i>🔄</i> 刷新数据
            </button>
        </div>
        <div class="card-body">
            <div class="refresh-info">
                <i>ℹ️</i>
                <span>点击刷新按钮获取最新的终端指标数据，数据更新时间：<span id="lastUpdateTime">2024-07-14 18:30:45</span></span>
            </div>

            <!-- 时间信息 -->
            <div class="time-info">
                <div>
                    <span class="time-label">信息采集时间：</span>
                    <span class="time-value" id="metricTime">2024-07-14 18:30:45</span>
                </div>
                <div>
                    <span class="time-label">数据接收时间：</span>
                    <span class="time-value" id="receiveTime">2024-07-14 18:30:45</span>
                </div>
            </div>

            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number" id="cpuTemp">89.9°C</div>
                    <div class="stat-label">CPU温度</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="cpuUsage">40.3%</div>
                    <div class="stat-label">CPU使用率</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="memoryUsage">48.2%</div>
                    <div class="stat-label">内存使用率</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="diskUsage">54.9%</div>
                    <div class="stat-label">磁盘使用率</div>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <h4>详细指标信息</h4>
                </div>
                <div class="card-body">
                    <div class="metrics-grid">
                        <div class="metric-section">
                            <h5>系统信息</h5>
                            <div class="metric-row">
                                <div class="metric-label">系统运行时长</div>
                                <div class="metric-value" id="uptime">98分钟36秒</div>
                            </div>
                            <div class="metric-row">
                                <div class="metric-label">设备温度</div>
                                <div class="metric-value">
                                    SOC温度: 58.9°C<br>
                                    GPU温度: 57.8°C
                                </div>
                            </div>
                        </div>
                        
                        <div class="metric-section">
                            <h5>CPU信息</h5>
                            <div class="metric-row">
                                <div class="metric-label">CPU核心</div>
                                <div class="metric-value">4核4线程</div>
                            </div>
                            <div class="metric-row">
                                <div class="metric-label">CPU使用率</div>
                                <div class="metric-value">
                                    用户态: 46.3% | 系统态: 9.7% | 空闲: 43.7%
                                    <div class="progress-bar">
                                        <div class="progress-fill" id="cpuProgressBar" style="width: 40.3%"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="metric-section">
                            <h5>内存信息</h5>
                            <div class="metric-row">
                                <div class="metric-label">内存容量</div>
                                <div class="metric-value">总内存: 3.8GB</div>
                            </div>
                            <div class="metric-row">
                                <div class="metric-label">内存使用</div>
                                <div class="metric-value">
                                    已用: 1.9GB | 可用: 1.9GB<br>
                                    缓存: 601MB | 缓冲区: 54MB
                                    <div class="progress-bar">
                                        <div class="progress-fill" id="memoryProgressBar" style="width: 48.2%"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="metric-section">
                            <h5>存储信息</h5>
                            <div class="metric-row">
                                <div class="metric-label">磁盘设备</div>
                                <div class="metric-value">/dev/mmcblk0p3 (ext4)</div>
                            </div>
                            <div class="metric-row">
                                <div class="metric-label">磁盘使用</div>
                                <div class="metric-value">
                                    总空间: 28.5GB | 已用: 14.8GB | 可用: 12.5GB
                                    <div class="progress-bar">
                                        <div class="progress-fill warning" id="diskProgressBar" style="width: 54.9%"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="metric-section">
                            <h5>数据文件</h5>
                            <div class="metric-row">
                                <div class="metric-label">采集数据</div>
                                <div class="metric-value">1个文件，总大小: 3.4GB</div>
                            </div>
                            <div class="metric-row">
                                <div class="metric-label">压缩数据</div>
                                <div class="metric-value">0个文件，总大小: 0GB</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 从URL参数获取设备ID
        function getDeviceIdFromUrl() {
            const urlParams = new URLSearchParams(window.location.search);
            return urlParams.get('deviceId') || '_0fd94938951f4a64bb11c6817a81f7e7';
        }

        // 初始化页面
        function initPage() {
            const deviceId = getDeviceIdFromUrl();
            document.getElementById('currentDeviceId').textContent = deviceId;
            
            // 检查是否有历史数据时间参数
            const urlParams = new URLSearchParams(window.location.search);
            const historyTime = urlParams.get('historyTime');
            if (historyTime) {
                // 如果是历史数据查看，更新时间显示
                document.getElementById('lastUpdateTime').textContent = historyTime;
                document.getElementById('metricTime').textContent = historyTime;
                document.getElementById('receiveTime').textContent = historyTime;
                
                // 更新页面标题
                document.querySelector('.card-header h3').innerHTML = `终端历史指标 - <span id="currentDeviceId">${deviceId}</span>`;
                
                // 更新面包屑
                document.querySelector('.breadcrumb').innerHTML = `
                    <a onclick="goBack()">终端管理</a>
                    <span>></span>
                    <a onclick="goToHistory()">历史指标</a>
                    <span>></span>
                    <span>指标详情</span>
                `;
            }
        }

        // 返回上一页
        function goBack() {
            // 在实际Vue应用中，这里会使用路由返回
            window.history.back();
        }

        // 返回历史指标列表
        function goToHistory() {
            const deviceId = getDeviceIdFromUrl();
            window.location.href = `history-metrics.html?deviceId=${deviceId}`;
        }

        // 刷新实时指标数据
        function refreshRealTimeMetrics() {
            // 模拟数据刷新
            const now = new Date();
            const timeString = now.getFullYear() + '-' + 
                             String(now.getMonth() + 1).padStart(2, '0') + '-' + 
                             String(now.getDate()).padStart(2, '0') + ' ' + 
                             String(now.getHours()).padStart(2, '0') + ':' + 
                             String(now.getMinutes()).padStart(2, '0') + ':' + 
                             String(now.getSeconds()).padStart(2, '0');
            
            document.getElementById('lastUpdateTime').textContent = timeString;
            document.getElementById('metricTime').textContent = timeString;
            document.getElementById('receiveTime').textContent = timeString;
            
            // 模拟数据更新
            const newCpuTemp = (85 + Math.random() * 5).toFixed(1);
            const newCpuUsage = (40 + Math.random() * 10).toFixed(1);
            const newMemoryUsage = (48 + Math.random() * 8).toFixed(1);
            const newDiskUsage = (54 + Math.random() * 2).toFixed(1);
            
            document.getElementById('cpuTemp').textContent = newCpuTemp + '°C';
            document.getElementById('cpuUsage').textContent = newCpuUsage + '%';
            document.getElementById('memoryUsage').textContent = newMemoryUsage + '%';
            document.getElementById('diskUsage').textContent = newDiskUsage + '%';
            
            // 更新进度条
            document.getElementById('cpuProgressBar').style.width = newCpuUsage + '%';
            document.getElementById('memoryProgressBar').style.width = newMemoryUsage + '%';
            document.getElementById('diskProgressBar').style.width = newDiskUsage + '%';
            
            // 更新运行时长
            const currentUptimeText = document.getElementById('uptime').textContent;
            const currentMinutes = parseInt(currentUptimeText.match(/(\d+)分钟/)?.[1] || 0);
            const currentHours = parseInt(currentUptimeText.match(/(\d+)小时/)?.[1] || 0);
            const totalMinutes = currentHours * 60 + currentMinutes + Math.floor(Math.random() * 5) + 1;
            const hours = Math.floor(totalMinutes / 60);
            const minutes = totalMinutes % 60;
            document.getElementById('uptime').textContent = `${hours}小时${minutes}分钟`;
            
            alert('数据已刷新！');
        }

        // 页面加载时初始化
        window.onload = function() {
            initPage();
        };
    </script>
</body>
</html> 