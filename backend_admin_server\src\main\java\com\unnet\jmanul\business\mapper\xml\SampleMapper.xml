<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unnet.jmanul.business.mapper.SampleMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.unnet.jmanul.business.entity.Sample">
        <id column="id" property="id" />
        <result column="title" property="title" />
        <result column="description" property="description" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, title, description
    </sql>

</mapper>
