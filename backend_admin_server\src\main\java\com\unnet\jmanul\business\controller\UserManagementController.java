package com.unnet.jmanul.business.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.unnet.jmanul.business.entity.UserRole;
import com.unnet.jmanul.business.service.IUserRoleService;
import com.unnet.jmanul.common.rest.ApiDef;
import com.unnet.jmanul.system.entity.User;
import com.unnet.jmanul.system.service.IUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.casbin.jcasbin.main.Enforcer;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 用户管理Controller - 长安5G业务模块
 * 
 * <AUTHOR>
 * @date 2024-07-16
 */
@RestController
@RequestMapping(ApiDef.V1_ADMIN + "/user-management")
@Slf4j
@RequiredArgsConstructor
@Api(tags = {"长安5G - 用户管理"})
public class UserManagementController {

    private final IUserService userService;
    private final IUserRoleService userRoleService;
    private final Enforcer enforcer;

    /**
     * 获取用户列表（带角色信息）
     */
    @GetMapping("/users")
    @ApiOperation(value = "获取用户列表", notes = "分页查询用户列表，包含角色信息")
    public ResponseEntity<Map<String, Object>> getUserList(
            @ApiParam(value = "页码，从1开始", defaultValue = "1") @RequestParam(defaultValue = "1") int page,
            @ApiParam(value = "每页大小", defaultValue = "10") @RequestParam(value = "size", defaultValue = "10") int pageSize,
            @ApiParam(value = "用户名搜索") @RequestParam(required = false) String username,
            @ApiParam(value = "姓名搜索") @RequestParam(required = false) String name,
            @ApiParam(value = "角色过滤") @RequestParam(required = false) String role,
            @ApiParam(value = "状态过滤") @RequestParam(required = false) String status) {

        try {
            log.info("查询用户列表 - 页码: {}, 每页大小: {}, 用户名: {}, 姓名: {}, 角色: {}, 状态: {}",
                    page, pageSize, username, name, role, status);

            // 确保admin用户有admin角色
            User adminUser = userService.getOne(new QueryWrapper<User>().eq("username", "admin"));
            if (adminUser != null && !userRoleService.hasRole(adminUser.getId(), "admin")) {
                log.info("为admin用户分配admin角色");
                userRoleService.assignRole(adminUser.getId(), "admin");
            }

            // 确保user用户有user角色
            User normalUser = userService.getOne(new QueryWrapper<User>().eq("username", "user"));
            if (normalUser != null && !userRoleService.hasRole(normalUser.getId(), "user")) {
                log.info("为user用户分配user角色");
                userRoleService.assignRole(normalUser.getId(), "user");
            }

            // 参数验证
            if (page < 1) page = 1;
            if (pageSize < 1 || pageSize > 100) pageSize = 10;

            // 构建查询条件
            QueryWrapper<User> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("deleted_at", 0);

            if (username != null && !username.trim().isEmpty()) {
                queryWrapper.like("username", username.trim());
            }

            if (name != null && !name.trim().isEmpty()) {
                queryWrapper.like("name", name.trim());
            }

            if (status != null && !status.trim().isEmpty()) {
                if ("1".equals(status)) {
                    queryWrapper.eq("enable", true);
                } else if ("0".equals(status)) {
                    queryWrapper.eq("enable", false);
                }
            }

            // 按创建时间倒序排列
            queryWrapper.orderByDesc("created_at");

            // 分页查询
            Page<User> pageRequest = new Page<>(page, pageSize);
            IPage<User> pageResult = userService.page(pageRequest, queryWrapper);

            // 为每个用户添加角色信息并进行角色过滤
            List<Map<String, Object>> allUserList = pageResult.getRecords().stream()
                    .map(user -> {
                        Map<String, Object> userMap = new HashMap<>();
                        userMap.put("id", user.getId());
                        userMap.put("username", user.getUsername());
                        userMap.put("name", user.getName());
                        userMap.put("enable", user.getEnable());
                        userMap.put("locked", user.getLocked());
                        userMap.put("accountSource", user.getAccountSource());
                        userMap.put("createdAt", user.getCreatedAt());
                        userMap.put("updatedAt", user.getUpdatedAt());
                        userMap.put("accountExpireDate", user.getAccountExpireDate());
                        userMap.put("credentialExpireDate", user.getCredentialExpireDate());

                        // 获取用户角色
                        try {
                            List<UserRole> roles = userRoleService.getRolesByUserId(user.getId());
                            List<String> roleNames = roles.stream()
                                    .map(UserRole::getRoleName)
                                    .collect(Collectors.toList());
                            userMap.put("roles", roleNames);

                            String primaryRole = userRoleService.getPrimaryRole(user.getId());
                            userMap.put("primaryRole", primaryRole);

                            log.debug("用户 {} 的角色信息 - roles: {}, primaryRole: {}",
                                    user.getUsername(), roleNames, primaryRole);
                        } catch (Exception e) {
                            log.warn("获取用户 {} 的角色信息失败: {}", user.getUsername(), e.getMessage());
                            userMap.put("roles", new ArrayList<>());
                            userMap.put("primaryRole", "user"); // 默认为普通用户
                        }

                        return userMap;
                    })
                    .collect(Collectors.toList());

            // 角色过滤
            List<Map<String, Object>> filteredUserList = allUserList;
            if (role != null && !role.trim().isEmpty()) {
                filteredUserList = allUserList.stream()
                        .filter(userMap -> {
                            String primaryRole = (String) userMap.get("primaryRole");
                            return role.equals(primaryRole);
                        })
                        .collect(Collectors.toList());
            }

            Map<String, Object> response = new HashMap<>();
            response.put("records", filteredUserList);
            response.put("total", filteredUserList.size());
            response.put("current", page);
            response.put("size", pageSize);

            log.info("查询用户列表成功 - 原始总数: {}, 过滤后总数: {}, 当前页数据量: {}",
                    pageResult.getTotal(), filteredUserList.size(), filteredUserList.size());
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("查询用户列表失败 - 错误: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 获取用户角色信息
     */
    @GetMapping("/users/{userId}/roles")
    @ApiOperation(value = "获取用户角色", notes = "获取指定用户的角色列表")
    public ResponseEntity<List<UserRole>> getUserRoles(
            @ApiParam(value = "用户ID", required = true) @PathVariable Long userId) {

        try {
            log.info("查询用户角色 - 用户ID: {}", userId);

            List<UserRole> roles = userRoleService.getRolesByUserId(userId);

            log.info("查询用户角色成功 - 用户ID: {}, 角色数量: {}", userId, roles.size());
            return ResponseEntity.ok(roles);

        } catch (Exception e) {
            log.error("查询用户角色失败 - 用户ID: {}, 错误: {}", userId, e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 为用户分配角色
     */
    @PostMapping("/users/{userId}/roles")
    @ApiOperation(value = "分配用户角色", notes = "为指定用户分配角色")
    public ResponseEntity<String> assignUserRole(
            @ApiParam(value = "用户ID", required = true) @PathVariable Long userId,
            @ApiParam(value = "角色名称", required = true) @RequestParam String roleName) {

        try {
            log.info("分配用户角色 - 用户ID: {}, 角色: {}", userId, roleName);

            // 验证角色名称
            if (!"admin".equals(roleName) && !"user".equals(roleName)) {
                return ResponseEntity.badRequest().body("无效的角色名称，只支持 admin 和 user");
            }

            boolean result = userRoleService.assignRole(userId, roleName);
            if (result) {
                log.info("分配用户角色成功 - 用户ID: {}, 角色: {}", userId, roleName);
                return ResponseEntity.ok("角色分配成功");
            } else {
                return ResponseEntity.internalServerError().body("角色分配失败");
            }

        } catch (Exception e) {
            log.error("分配用户角色失败 - 用户ID: {}, 角色: {}, 错误: {}", userId, roleName, e.getMessage(), e);
            return ResponseEntity.internalServerError().body("角色分配失败：" + e.getMessage());
        }
    }

    /**
     * 移除用户角色
     */
    @DeleteMapping("/users/{userId}/roles")
    @ApiOperation(value = "移除用户角色", notes = "移除指定用户的角色")
    public ResponseEntity<String> removeUserRole(
            @ApiParam(value = "用户ID", required = true) @PathVariable Long userId,
            @ApiParam(value = "角色名称", required = true) @RequestParam String roleName) {

        try {
            log.info("移除用户角色 - 用户ID: {}, 角色: {}", userId, roleName);

            boolean result = userRoleService.removeRole(userId, roleName);
            if (result) {
                log.info("移除用户角色成功 - 用户ID: {}, 角色: {}", userId, roleName);
                return ResponseEntity.ok("角色移除成功");
            } else {
                return ResponseEntity.internalServerError().body("角色移除失败");
            }

        } catch (Exception e) {
            log.error("移除用户角色失败 - 用户ID: {}, 角色: {}, 错误: {}", userId, roleName, e.getMessage(), e);
            return ResponseEntity.internalServerError().body("角色移除失败：" + e.getMessage());
        }
    }

    /**
     * 获取所有可用角色
     */
    @GetMapping("/roles")
    @ApiOperation(value = "获取可用角色", notes = "获取系统中所有可用的角色列表")
    public ResponseEntity<List<Map<String, Object>>> getAvailableRoles() {
        try {
            List<Map<String, Object>> roles = new ArrayList<>();

            Map<String, Object> admin = new HashMap<>();
            admin.put("name", "admin");
            admin.put("description", "管理员");
            admin.put("permissions", "拥有所有权限");

            Map<String, Object> user = new HashMap<>();
            user.put("name", "user");
            user.put("description", "普通用户");
            user.put("permissions", "只有查看权限");

            roles.add(admin);
            roles.add(user);


            return ResponseEntity.ok(roles);
        } catch (Exception e) {
            log.error("获取可用角色失败 - 错误: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 调试接口：检查用户的Casbin状态
     */
    @GetMapping("/users/{userId}/casbin-status")
    @ApiOperation(value = "检查用户Casbin状态", notes = "调试用接口，检查用户在Casbin中的角色和权限状态")
    public ResponseEntity<Map<String, Object>> checkUserCasbinStatus(
            @ApiParam(value = "用户ID", required = true) @PathVariable Long userId) {

        try {
            log.info("检查用户Casbin状态 - 用户ID: {}", userId);

            User user = userService.getById(userId);
            if (user == null) {
                return ResponseEntity.notFound().build();
            }

            Map<String, Object> status = new HashMap<>();
            status.put("userId", userId);
            status.put("username", user.getUsername());

            // 检查数据库中的角色
            List<UserRole> dbRoles = userRoleService.getRolesByUserId(userId);
            List<String> dbRoleNames = dbRoles.stream()
                    .map(UserRole::getRoleName)
                    .collect(Collectors.toList());
            status.put("databaseRoles", dbRoleNames);

            // 检查Casbin中的角色
            List<String> casbinRoles = enforcer.getRolesForUser(user.getUsername());
            status.put("casbinRoles", casbinRoles);

            // 检查具体的权限
            boolean canLogin = enforcer.enforce(user.getUsername(), "/api/v1/auth/login", "POST");
            boolean canViewTerminals = enforcer.enforce(user.getUsername(), "/api/v1/admin/terminals", "GET");
            boolean canManageUsers = enforcer.enforce(user.getUsername(), "/api/v1/admin/users", "POST");

            Map<String, Boolean> permissions = new HashMap<>();
            permissions.put("canLogin", canLogin);
            permissions.put("canViewTerminals", canViewTerminals);
            permissions.put("canManageUsers", canManageUsers);
            status.put("permissions", permissions);

            // 检查是否有Casbin策略
            boolean hasGroupingPolicy = false;
            for (String roleName : dbRoleNames) {
                if (enforcer.hasGroupingPolicy(user.getUsername(), roleName)) {
                    hasGroupingPolicy = true;
                    break;
                }
            }
            status.put("hasGroupingPolicy", hasGroupingPolicy);

            log.info("用户Casbin状态检查完成 - 用户: {}, 数据库角色: {}, Casbin角色: {}",
                    user.getUsername(), dbRoleNames, casbinRoles);

            return ResponseEntity.ok(status);

        } catch (Exception e) {
            log.error("检查用户Casbin状态失败 - 用户ID: {}, 错误: {}", userId, e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }
}
