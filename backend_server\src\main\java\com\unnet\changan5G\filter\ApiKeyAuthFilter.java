package com.unnet.changan5G.filter;

import com.unnet.changan5G.config.AppProperties;
import com.fasterxml.jackson.databind.ObjectMapper;
import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

@Component
@Order(1)
@RequiredArgsConstructor
@Slf4j
public class ApiKeyAuthFilter extends OncePerRequestFilter {

    private static final String API_KEY_HEADER = "X-API-KEY";
    private static final String CLIENT_ID_ATTRIBUTE = "clientId";
    
    private final AppProperties appProperties;
    private final ObjectMapper objectMapper;
    private final AntPathMatcher pathMatcher = new AntPathMatcher();
    
    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
            throws ServletException, IOException {

        String requestUri = request.getRequestURI();
        String method = request.getMethod();

        // 对于OPTIONS预检请求，直接放行（CORS预检请求）
        if ("OPTIONS".equalsIgnoreCase(method)) {
            filterChain.doFilter(request, response);
            return;
        }

        // 如果路径不需要验证API Key，则直接放行
        if (!requiresAuthentication(requestUri)) {
            log.debug("路径 {} 跳过API Key验证", requestUri);
            filterChain.doFilter(request, response);
            return;
        }

        log.debug("路径 {} 需要API Key验证", requestUri);
        
        String apiKey = request.getHeader(API_KEY_HEADER);
        
        // 如果请求中没有API Key，则返回401错误
        if (apiKey == null || apiKey.isEmpty()) {
            handleUnauthorized(response, "缺少API Key");
            return;
        }
        
        // 验证API Key是否有效
        Optional<AppProperties.Api.ApiKey> validApiKey = appProperties.getApi().getKeys().stream()
                .filter(k -> k.getKey().equals(apiKey))
                .findFirst();
        
        if (validApiKey.isPresent()) {
            // API Key有效，将客户端ID存入请求属性，供后续业务逻辑使用
            request.setAttribute(CLIENT_ID_ATTRIBUTE, validApiKey.get().getClientId());
            filterChain.doFilter(request, response);
        } else {
            // API Key无效，返回401错误
            handleUnauthorized(response, "无效的API Key");
        }
    }
    
    /**
     * 判断请求路径是否需要API Key认证
     * 修改策略：只有指标上传接口需要API Key认证，其他接口都不需要
     */
    private boolean requiresAuthentication(String requestUri) {
        log.debug("检查路径是否需要认证: {}", requestUri);

        // 只有以下路径需要API Key认证
        String[] authRequiredPaths = {
            "/api/agent/report",      // 指标上传接口
            "/api/agent/upload-log"   // 日志文件上传接口
        };

        for (String path : authRequiredPaths) {
            boolean matches = pathMatcher.match(path, requestUri);
            log.debug("路径模式 '{}' 匹配 '{}': {}", path, requestUri, matches);
            if (matches) {
                log.debug("路径 {} 需要API Key认证", requestUri);
                return true;
            }
        }

        log.debug("路径 {} 不需要API Key认证，直接放行", requestUri);
        return false;
    }
    
    /**
     * 处理未授权的请求
     */
    private void handleUnauthorized(HttpServletResponse response, String message) throws IOException {
        response.setStatus(HttpStatus.UNAUTHORIZED.value());
        response.setContentType(MediaType.APPLICATION_JSON_VALUE);
        response.setCharacterEncoding(StandardCharsets.UTF_8.name());
        
        Map<String, Object> errorDetails = new HashMap<>();
        errorDetails.put("timestamp", LocalDateTime.now().toString());
        errorDetails.put("status", HttpStatus.UNAUTHORIZED.value());
        errorDetails.put("error", "Unauthorized");
        errorDetails.put("message", message);
        
        response.getWriter().write(objectMapper.writeValueAsString(errorDetails));
    }
} 