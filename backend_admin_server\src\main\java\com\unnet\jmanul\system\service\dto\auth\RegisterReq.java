package com.unnet.jmanul.system.service.dto.auth;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RegisterReq {

    @NotNull
    @Size(min = 3, max = 32)
    private String username;

    @NotNull
    @Size(min = 5, max = 32)
    private String password;

    @NotNull
    private String matchingPassword;

    // captcha
    @NotNull
    private String seq;
    @NotNull
    private String code;

}
