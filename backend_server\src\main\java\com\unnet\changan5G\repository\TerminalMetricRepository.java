package com.unnet.changan5G.repository;

import com.unnet.changan5G.document.TerminalMetricDocument;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.elasticsearch.annotations.Query;
import org.springframework.data.elasticsearch.repository.ElasticsearchRepository;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.Optional;

/**
 * 终端指标信息Elasticsearch Repository
 * 
 * <AUTHOR>
 * @date 2024-07-14
 */
@Repository
public interface TerminalMetricRepository extends ElasticsearchRepository<TerminalMetricDocument, String> {



    /**
     * 删除指定时间之前的历史数据
     */
    void deleteByMetricTimeBefore(LocalDateTime beforeTime);

    /**
     * 根据MAC地址分页查询指标数据（按采集时间倒序）
     */
    Page<TerminalMetricDocument> findByIdentityMacOrderByMetricTimeDesc(String identityMac, Pageable pageable);

    /**
     * 根据MAC地址和时间范围分页查询指标数据
     */
    Page<TerminalMetricDocument> findByIdentityMacAndMetricTimeBetween(String identityMac,
                                                                      LocalDateTime startTime,
                                                                      LocalDateTime endTime,
                                                                      Pageable pageable);

    /**
     * 根据MAC地址查询最新一条记录
     */
    Optional<TerminalMetricDocument> findFirstByIdentityMacOrderByMetricTimeDesc(String identityMac);

    /**
     * 根据MAC地址查询数据条数
     */
    long countByIdentityMac(String identityMac);

    /**
     * 根据MAC地址和时间范围查询数据条数
     */
    long countByIdentityMacAndMetricTimeBetween(String identityMac, LocalDateTime startTime, LocalDateTime endTime);
}
