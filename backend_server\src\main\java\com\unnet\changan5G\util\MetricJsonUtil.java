package com.unnet.changan5G.util;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.unnet.changan5G.dto.metric.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 指标JSON解析工具类
 * 
 * <AUTHOR>
 * @date 2024-07-14
 */
@Component
@Slf4j
public class MetricJsonUtil {

    private final ObjectMapper objectMapper;

    public MetricJsonUtil(ObjectMapper objectMapper) {
        this.objectMapper = objectMapper;
    }

    /**
     * 解析温度信息JSON
     */
    public TemperatureInfo parseTemperatureInfo(Object obj) {
        try {
            if (obj == null) return null;
            if (obj instanceof String) {
                return objectMapper.readValue((String) obj, TemperatureInfo.class);
            } else {
                return objectMapper.convertValue(obj, TemperatureInfo.class);
            }
        } catch (Exception e) {
            log.error("解析温度信息失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 解析CPU使用率信息
     */
    public CpuUsageInfo parseCpuUsageInfo(Object obj) {
        try {
            if (obj == null) return null;
            if (obj instanceof String) {
                return objectMapper.readValue((String) obj, CpuUsageInfo.class);
            } else {
                return objectMapper.convertValue(obj, CpuUsageInfo.class);
            }
        } catch (Exception e) {
            log.error("解析CPU使用率信息失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 解析内存使用信息
     */
    public MemoryUsageInfo parseMemoryUsageInfo(Object obj) {
        try {
            if (obj == null) return null;
            if (obj instanceof String) {
                return objectMapper.readValue((String) obj, MemoryUsageInfo.class);
            } else {
                return objectMapper.convertValue(obj, MemoryUsageInfo.class);
            }
        } catch (Exception e) {
            log.error("解析内存使用信息失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 解析磁盘使用信息数组
     */
    public List<DiskUsageInfo> parseDiskUsageInfo(Object obj) {
        try {
            if (obj == null) return null;
            if (obj instanceof String) {
                return objectMapper.readValue((String) obj, new TypeReference<List<DiskUsageInfo>>() {});
            } else {
                return objectMapper.convertValue(obj, new TypeReference<List<DiskUsageInfo>>() {});
            }
        } catch (Exception e) {
            log.error("解析磁盘使用信息失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 解析文件数据信息
     */
    public FileDataInfo parseFileDataInfo(Object obj) {
        try {
            if (obj == null) return null;
            if (obj instanceof String) {
                return objectMapper.readValue((String) obj, FileDataInfo.class);
            } else {
                return objectMapper.convertValue(obj, FileDataInfo.class);
            }
        } catch (Exception e) {
            log.error("解析文件数据信息失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 解析CPE集合使用信息
     */
    public GroupUsageInfo parseGroupUsageInfo(Object obj) {
        try {
            if (obj == null) return null;
            if (obj instanceof String) {
                return objectMapper.readValue((String) obj, GroupUsageInfo.class);
            } else {
                return objectMapper.convertValue(obj, GroupUsageInfo.class);
            }
        } catch (Exception e) {
            log.error("解析CPE集合使用信息失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 解析应用版本信息
     */
    public AppVersionInfo parseAppVersionInfo(Object obj) {
        try {
            if (obj == null) return null;
            if (obj instanceof String) {
                return objectMapper.readValue((String) obj, AppVersionInfo.class);
            } else {
                return objectMapper.convertValue(obj, AppVersionInfo.class);
            }
        } catch (Exception e) {
            log.error("解析应用版本信息失败: {}", e.getMessage());
            return null;
        }
    }
}
