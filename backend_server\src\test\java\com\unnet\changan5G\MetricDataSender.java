package com.unnet.changan5G;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.http.*;
import org.springframework.web.client.RestTemplate;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 指标数据发送器 - 用于模拟终端设备发送指标数据
 * 每15秒发送一次指标数据，模拟真实设备行为
 */
public class MetricDataSender {

    private static final String BASE_URL = "http://localhost:8081/api/agent/report";
    private static final RestTemplate restTemplate = new RestTemplate();
    private static final ObjectMapper objectMapper = new ObjectMapper();
    private static final Random random = new Random();
    
    // 基础数据
    private double baseUptime = 0.0;
    private double baseCpuTemp = 46.23;
    private long baseGroupBps = 5385836L;
    private double baseCpuPercent = 28.2;
    
    private int sendCount = 0;

    public static void main(String[] args) {
        MetricDataSender sender = new MetricDataSender();
        sender.startSending();
    }

    public void startSending() {
        ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);
        
        System.out.println("=== 指标数据发送器启动 ===");
        System.out.println("目标地址: " + BASE_URL);
        System.out.println("发送间隔: 15秒");
        System.out.println("按 Ctrl+C 停止发送");
        System.out.println("========================");
        
        scheduler.scheduleAtFixedRate(() -> {
            try {
                sendMetricData();
            } catch (Exception e) {
                System.err.println("发送指标数据失败: " + e.getMessage());
                e.printStackTrace();
            }
        }, 0, 15, TimeUnit.SECONDS);
        
        // 添加关闭钩子，优雅停止
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            System.out.println("\n正在停止发送器...");
            scheduler.shutdown();
            try {
                if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                    scheduler.shutdownNow();
                }
            } catch (InterruptedException e) {
                scheduler.shutdownNow();
            }
            System.out.println("发送器已停止，总共发送了 " + sendCount + " 条数据");
        }));
        
        // 保持主线程运行
        try {
            Thread.currentThread().join();
        } catch (InterruptedException e) {
            System.out.println("程序被中断");
        }
    }

    private void sendMetricData() throws Exception {
        sendCount++;
        
        // 更新模拟数据
        updateSimulatedValues();
        
        // 构建指标数据
        Map<String, Object> metricData = buildMetricData();
        
        // 转换为JSON字符串
        String jsonData = objectMapper.writeValueAsString(metricData);
        
        // 发送HTTP请求
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("X-API-Key", "api-SvardHNxjL87TMHbgS2JFDpUsyi1iq9e");
        
        HttpEntity<String> request = new HttpEntity<>(jsonData, headers);
        
        try {
            long startTime = System.currentTimeMillis();
            ResponseEntity<String> response = restTemplate.postForEntity(BASE_URL, request, String.class);
            long endTime = System.currentTimeMillis();
            
            System.out.printf("[%d] 发送成功 - 状态码: %s, 耗时: %dms, uptime: %.1fs, cpu_temp: %.2f°C, group_bps: %d, cpu_percent: %.1f%%\n",
                    sendCount, response.getStatusCode(), (endTime - startTime), 
                    baseUptime, baseCpuTemp, baseGroupBps, baseCpuPercent);
        } catch (Exception e) {
            System.err.printf("[%d] 发送失败: %s\n", sendCount, e.getMessage());
        }
    }

    private void updateSimulatedValues() {
        // uptime每次增加15秒
        baseUptime += 15.0;
        
        // cpu_temp变化范围不超过10度，使用更平滑的变化
        double tempChange = (random.nextGaussian() * 2); // 正态分布，标准差为2
        baseCpuTemp += tempChange;
        baseCpuTemp = Math.max(36.0, Math.min(56.0, baseCpuTemp)); // 限制在36-56度之间
        
        // group_bps变化范围不超过1000，使用更平滑的变化
        long bpsChange = (long)(random.nextGaussian() * 500); // 正态分布，标准差为500
        baseGroupBps += bpsChange;
        baseGroupBps = Math.max(4385836L, Math.min(6385836L, baseGroupBps)); // 限制范围
        
        // cpu_percent变化范围不超过10，使用更平滑的变化
        double percentChange = random.nextGaussian() * 3; // 正态分布，标准差为3
        baseCpuPercent += percentChange;
        baseCpuPercent = Math.max(18.2, Math.min(38.2, baseCpuPercent)); // 限制范围
    }

    private Map<String, Object> buildMetricData() {
        Map<String, Object> data = new HashMap<>();
        
        data.put("hostname", "ARM3588_16034");
        data.put("device_id", "cbca3e75aded4d659f794ad7b367e14f");
        data.put("identity_mac", "12e5c3d1f6e5");
        data.put("uptime", round(baseUptime, 2));
        data.put("cpu_temp", round(baseCpuTemp, 2));
        
        // temperatures - 基于cpu_temp生成相关温度
        Map<String, Double> temperatures = new HashMap<>();
        double tempVariation = random.nextGaussian() * 0.5; // 小幅变化
        temperatures.put("bigcore1-thermal", round(baseCpuTemp + tempVariation, 2));
        temperatures.put("soc-thermal", round(baseCpuTemp + tempVariation, 2));
        temperatures.put("gpu-thermal", round(baseCpuTemp - 0.923 + tempVariation, 3));
        temperatures.put("littlecore-thermal", round(baseCpuTemp + tempVariation, 2));
        temperatures.put("bigcore0-thermal", round(baseCpuTemp - 0.923 + tempVariation, 3));
        temperatures.put("npu-thermal", round(baseCpuTemp - 0.923 + tempVariation, 3));
        temperatures.put("center-thermal", round(baseCpuTemp + tempVariation, 2));
        data.put("temperatures", temperatures);
        
        // cpu_usage - 基于cpu_percent生成相关数据
        Map<String, Object> cpuUsage = new HashMap<>();
        cpuUsage.put("num", "8");
        cpuUsage.put("core", "8");
        cpuUsage.put("thread", "8");
        double userPercent = baseCpuPercent * 0.6 + random.nextGaussian() * 2;
        double sysPercent = baseCpuPercent * 0.2 + random.nextGaussian() * 1;
        double idlePercent = 100 - baseCpuPercent + random.nextGaussian() * 2;
        cpuUsage.put("user", String.valueOf(round(Math.max(0, userPercent), 1)));
        cpuUsage.put("sys", String.valueOf(round(Math.max(0, sysPercent), 1)));
        cpuUsage.put("idle", String.valueOf(round(Math.max(0, idlePercent), 1)));
        data.put("cpu_usage", cpuUsage);
        
        data.put("cpu_percent", round(baseCpuPercent, 1));
        
        // memory_percent - 添加小幅变化
        double memoryPercent = 35.7 + random.nextGaussian() * 2;
        memoryPercent = Math.max(20.0, Math.min(80.0, memoryPercent));
        data.put("memory_percent", round(memoryPercent, 1));
        
        // memory_usage - 基于memory_percent计算
        Map<String, Object> memoryUsage = new HashMap<>();
        long totalMemory = 16719081472L;
        long usedMemory = (long)(totalMemory * memoryPercent / 100);
        long availableMemory = totalMemory - usedMemory;
        
        memoryUsage.put("total", totalMemory);
        memoryUsage.put("available", availableMemory);
        memoryUsage.put("percent", round(memoryPercent, 1));
        memoryUsage.put("used", usedMemory);
        memoryUsage.put("free", availableMemory / 2);
        memoryUsage.put("active", usedMemory + random.nextInt(1000000000));
        memoryUsage.put("inactive", availableMemory + random.nextInt(1000000000));
        memoryUsage.put("buffers", 33615872L + random.nextInt(10000000));
        memoryUsage.put("cached", availableMemory / 2);
        memoryUsage.put("shared", 158978048L + random.nextInt(50000000));
        memoryUsage.put("slab", 271261696L + random.nextInt(100000000));
        data.put("memory_usage", memoryUsage);
        
        // disk_usage - 添加小幅变化
        List<Map<String, Object>> diskUsage = new ArrayList<>();
        
        // 系统磁盘
        Map<String, Object> disk1 = new HashMap<>();
        disk1.put("device", "/dev/mmcblk0p6");
        disk1.put("mountpoint", "/");
        disk1.put("fstype", "ext4");
        disk1.put("total", 245941342208L);
        long disk1Used = 11803267072L + random.nextInt(1000000000);
        disk1.put("used", disk1Used);
        disk1.put("free", 245941342208L - disk1Used);
        double disk1Percent = (double)disk1Used / 245941342208L * 100;
        disk1.put("percent", round(disk1Percent, 1));
        disk1.put("smartmon", new HashMap<>());
        
        Map<String, Object> ioRate1 = new HashMap<>();
        ioRate1.put("MBr/s", round(random.nextDouble() * 10, 1));
        ioRate1.put("MBw/s", round(random.nextDouble() * 5, 1));
        ioRate1.put("read/s", random.nextInt(100));
        ioRate1.put("write/s", random.nextInt(50));
        disk1.put("io_rate", ioRate1);
        
        diskUsage.add(disk1);
        
        // 数据磁盘
        Map<String, Object> disk2 = new HashMap<>();
        disk2.put("device", "/dev/nvme0n1p1");
        disk2.put("mountpoint", "/mnt/nvme0n1p1");
        disk2.put("fstype", "ext4");
        disk2.put("total", 983350091776L);
        long disk2Used = 15583633408L + random.nextInt(1000000000);
        disk2.put("used", disk2Used);
        disk2.put("free", 983350091776L - disk2Used);
        double disk2Percent = (double)disk2Used / 983350091776L * 100;
        disk2.put("percent", round(disk2Percent, 1));
        
        // smartmon数据 - 添加动态变化
        Map<String, Object> smartmon = new HashMap<>();
        smartmon.put("critical_warning", 0);
        smartmon.put("temperature", 49 + random.nextInt(10) - 5); // 44-54度
        smartmon.put("available_spare", 100);
        smartmon.put("available_spare_threshold", 10);
        smartmon.put("percentage_used", 9 + random.nextInt(3)); // 9-11%
        smartmon.put("data_units_read", 113016600L + random.nextInt(1000000));
        smartmon.put("data_units_written", 143996158L + random.nextInt(1000000));
        smartmon.put("host_reads", 388949179L + random.nextInt(10000));
        smartmon.put("host_writes", 175354762L + random.nextInt(10000));
        smartmon.put("controller_busy_time", 7964L + random.nextInt(100));
        smartmon.put("power_cycles", 120);
        smartmon.put("power_on_hours", 1080 + (int)(baseUptime / 3600)); // 基于uptime计算
        smartmon.put("unsafe_shutdowns", 80);
        smartmon.put("media_errors", 0);
        smartmon.put("num_err_log_entries", 0);
        smartmon.put("warning_temp_time", 1754);
        smartmon.put("critical_comp_time", 772);
        smartmon.put("temperature_sensors", Arrays.asList(57 + random.nextInt(6) - 3, 49 + random.nextInt(6) - 3));
        smartmon.put("power_on_time", 1080 + (int)(baseUptime / 3600));
        disk2.put("smartmon", smartmon);
        
        Map<String, Object> ioRate2 = new HashMap<>();
        ioRate2.put("MBr/s", round(random.nextDouble() * 20, 1));
        ioRate2.put("MBw/s", round(random.nextDouble() * 15, 1));
        ioRate2.put("read/s", random.nextInt(200));
        ioRate2.put("write/s", random.nextInt(100));
        disk2.put("io_rate", ioRate2);
        
        diskUsage.add(disk2);
        
        data.put("disk_usage", diskUsage);
        data.put("disk_data_percent", round(disk2Percent, 1));
        data.put("disk_system_percent", round(disk1Percent, 1));
        
        // cdata和zdata - 添加动态变化
        Map<String, Object> cdata = new HashMap<>();
        cdata.put("count", 1 + random.nextInt(3));
        cdata.put("size", 697724846L + random.nextInt(100000000));
        data.put("cdata", cdata);
        
        Map<String, Object> zdata = new HashMap<>();
        zdata.put("count", 9 + random.nextInt(5));
        zdata.put("size", 13811916993L + random.nextInt(1000000000));
        data.put("zdata", zdata);
        
        // group_usage - 基于baseGroupBps生成
        Map<String, Object> groupUsage = new HashMap<>();
        groupUsage.put("group_id", 105);
        double totalPps = 1994.0 + random.nextGaussian() * 100;
        groupUsage.put("pps_total", round(Math.max(1000, totalPps), 1));
        groupUsage.put("bps_total", (double)baseGroupBps);
        
        // 生成buckets数据
        List<Map<String, Object>> buckets = new ArrayList<>();
        double[] ppsDistribution = {0.26, 0.004, 0.496, 0.24}; // 分配比例
        double[] bpsDistribution = {0.257, 0.002, 0.5, 0.241}; // 分配比例
        
        for (int i = 0; i < 4; i++) {
            Map<String, Object> bucket = new HashMap<>();
            bucket.put("bucket", i);
            bucket.put("conn", 9);
            bucket.put("pps", round(totalPps * ppsDistribution[i], 1));
            bucket.put("bps", round(baseGroupBps * bpsDistribution[i], 1));
            buckets.add(bucket);
        }
        
        groupUsage.put("buckets", buckets);
        data.put("group_usage", groupUsage);
        data.put("group_bps", baseGroupBps);
        
        // app_version - 固定数据
        Map<String, String> appVersion = new HashMap<>();
        appVersion.put("controller", "1.0.48");
        appVersion.put("detector", "1.0.48");
        appVersion.put("web", "1.0.55");
        appVersion.put("dist", "1.0.40");
        appVersion.put("extension", "1.0.383");
        appVersion.put("site-packages", "1.0.2");
        appVersion.put("edge-file-uploader", "2.3.1");
        data.put("app_version", appVersion);
        
        data.put("expired_date", "2026-06-15 10:47:08");
        
        return data;
    }
    
    private double round(double value, int places) {
        if (places < 0) throw new IllegalArgumentException();
        
        BigDecimal bd = BigDecimal.valueOf(value);
        bd = bd.setScale(places, RoundingMode.HALF_UP);
        return bd.doubleValue();
    }
}