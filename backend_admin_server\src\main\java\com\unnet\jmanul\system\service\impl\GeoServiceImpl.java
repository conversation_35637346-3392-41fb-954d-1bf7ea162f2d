package com.unnet.jmanul.system.service.impl;

import com.maxmind.geoip2.DatabaseReader;
import com.maxmind.geoip2.model.CityResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.net.InetAddress;

@Slf4j
@Service
@RequiredArgsConstructor
public class GeoServiceImpl {

    private final DatabaseReader reader;

    public CityResponse city(String ip) {
        try {
            return reader.city(InetAddress.getByName(ip));
        } catch (Exception e) {
            log.error(e.getMessage());
            return null;
        }
    }
}
