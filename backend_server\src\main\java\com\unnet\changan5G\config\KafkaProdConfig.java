package com.unnet.changan5G.config;

import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.apache.kafka.common.serialization.StringSerializer;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.kafka.annotation.EnableKafka;
import org.springframework.kafka.config.ConcurrentKafkaListenerContainerFactory;
import org.springframework.kafka.core.*;
import org.springframework.kafka.listener.ContainerProperties;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Kafka生产环境配置类 - 支持集群和SASL认证
 * 
 * <AUTHOR>
 * @date 2024-07-14
 */
@Configuration
@EnableKafka
@Profile("prod")
@Slf4j
public class KafkaProdConfig {

    @Value("${spring.kafka.bootstrap-servers}")
    private List<String> bootstrapServers;

    @Value("${spring.kafka.security.protocol:}")
    private String securityProtocol;

    @Value("${spring.kafka.properties.sasl.mechanism:}")
    private String saslMechanism;

    @Value("${spring.kafka.properties.sasl.jaas.config:}")
    private String saslJaasConfig;

    // 生产者配置
    @Value("${spring.kafka.producer.acks:1}")
    private String producerAcks;

    @Value("${spring.kafka.producer.retries:3}")
    private int producerRetries;

    @Value("${spring.kafka.producer.batch-size:16384}")
    private int producerBatchSize;

    @Value("${spring.kafka.producer.linger-ms:5}")
    private int producerLingerMs;

    @Value("${spring.kafka.producer.buffer-memory:33554432}")
    private long producerBufferMemory;

    @Value("${spring.kafka.producer.compression-type:snappy}")
    private String producerCompressionType;

    // 消费者配置
    @Value("${spring.kafka.consumer.group-id:changan5g-data-consumer}")
    private String consumerGroupId;

    @Value("${spring.kafka.consumer.auto-offset-reset:earliest}")
    private String consumerAutoOffsetReset;

    @Value("${spring.kafka.consumer.enable-auto-commit:false}")
    private boolean consumerEnableAutoCommit;

    @Value("${spring.kafka.consumer.max-poll-records:500}")
    private int consumerMaxPollRecords;

    @Value("${spring.kafka.consumer.fetch-min-size:1}")
    private int consumerFetchMinSize;

    @Value("${spring.kafka.consumer.fetch-max-wait:500ms}")
    private String consumerFetchMaxWait;

    @Value("${spring.kafka.consumer.session-timeout-ms:30000}")
    private int consumerSessionTimeout;

    @Value("${spring.kafka.consumer.heartbeat-interval-ms:3000}")
    private int consumerHeartbeatInterval;

    // 监听器配置
    @Value("${spring.kafka.listener.concurrency:3}")
    private int listenerConcurrency;

    /**
     * Kafka生产者配置 - 生产环境（集群+认证）
     */
    @Bean
    public ProducerFactory<String, String> producerFactory() {
        Map<String, Object> configProps = new HashMap<>();
        
        // 基本配置
        configProps.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapServers);
        configProps.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        configProps.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        
        // 可靠性配置
        configProps.put(ProducerConfig.ACKS_CONFIG, producerAcks);
        configProps.put(ProducerConfig.RETRIES_CONFIG, producerRetries);
        configProps.put(ProducerConfig.RETRY_BACKOFF_MS_CONFIG, 1000);
        
        // 性能配置
        configProps.put(ProducerConfig.BATCH_SIZE_CONFIG, producerBatchSize);
        configProps.put(ProducerConfig.LINGER_MS_CONFIG, producerLingerMs);
        configProps.put(ProducerConfig.BUFFER_MEMORY_CONFIG, producerBufferMemory);
        
        // 压缩配置
        configProps.put(ProducerConfig.COMPRESSION_TYPE_CONFIG, producerCompressionType);
        
        // 安全认证配置
        if (!securityProtocol.isEmpty()) {
            configProps.put("security.protocol", securityProtocol);
            log.info("生产环境Kafka安全协议: {}", securityProtocol);
        }
        if (!saslMechanism.isEmpty()) {
            configProps.put("sasl.mechanism", saslMechanism);
            log.info("生产环境Kafka SASL机制: {}", saslMechanism);
        }
        if (!saslJaasConfig.isEmpty()) {
            configProps.put("sasl.jaas.config", saslJaasConfig);
            log.info("生产环境Kafka SASL认证配置已加载");
        }
        
        log.info("生产环境Kafka生产者配置完成 - Bootstrap Servers: {}, Acks: {}, 集群节点数: {}", 
                bootstrapServers, producerAcks, bootstrapServers.size());
        
        return new DefaultKafkaProducerFactory<>(configProps);
    }

    /**
     * Kafka模板 - 生产环境
     */
    @Bean
    public KafkaTemplate<String, String> kafkaTemplate() {
        KafkaTemplate<String, String> template = new KafkaTemplate<>(producerFactory());
        
        log.info("生产环境KafkaTemplate配置完成");
        
        return template;
    }

    /**
     * Kafka消费者配置 - 生产环境（集群+认证）
     */
    @Bean
    public ConsumerFactory<String, String> consumerFactory() {
        Map<String, Object> configProps = new HashMap<>();
        
        // 基本配置
        configProps.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapServers);
        configProps.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        configProps.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        
        // 消费者组配置
        configProps.put(ConsumerConfig.GROUP_ID_CONFIG, consumerGroupId);
        configProps.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, consumerAutoOffsetReset);
        configProps.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, consumerEnableAutoCommit);
        
        // 性能配置
        configProps.put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, consumerMaxPollRecords);
        configProps.put(ConsumerConfig.MAX_POLL_INTERVAL_MS_CONFIG, 300000);
        configProps.put(ConsumerConfig.SESSION_TIMEOUT_MS_CONFIG, consumerSessionTimeout);
        configProps.put(ConsumerConfig.HEARTBEAT_INTERVAL_MS_CONFIG, consumerHeartbeatInterval);
        
        // 获取数据配置
        configProps.put(ConsumerConfig.FETCH_MIN_BYTES_CONFIG, consumerFetchMinSize);
        configProps.put(ConsumerConfig.FETCH_MAX_WAIT_MS_CONFIG, 500);
        
        // 安全认证配置
        if (!securityProtocol.isEmpty()) {
            configProps.put("security.protocol", securityProtocol);
        }
        if (!saslMechanism.isEmpty()) {
            configProps.put("sasl.mechanism", saslMechanism);
        }
        if (!saslJaasConfig.isEmpty()) {
            configProps.put("sasl.jaas.config", saslJaasConfig);
        }
        
        log.info("生产环境Kafka消费者配置完成 - Bootstrap Servers: {}, Group ID: {}, 集群节点数: {}", 
                bootstrapServers, consumerGroupId, bootstrapServers.size());
        
        return new DefaultKafkaConsumerFactory<>(configProps);
    }

    /**
     * Kafka监听器容器工厂 - 生产环境
     */
    @Bean
    public ConcurrentKafkaListenerContainerFactory<String, String> kafkaListenerContainerFactory() {
        ConcurrentKafkaListenerContainerFactory<String, String> factory = 
                new ConcurrentKafkaListenerContainerFactory<>();
        
        factory.setConsumerFactory(consumerFactory());
        
        // 设置并发级别（消费者线程数）
        factory.setConcurrency(listenerConcurrency);
        
        // 设置确认模式为手动立即确认
        factory.getContainerProperties().setAckMode(ContainerProperties.AckMode.MANUAL_IMMEDIATE);
        
        // 生产环境错误处理配置
        // factory.setErrorHandler(new SeekToCurrentErrorHandler());
        
        log.info("生产环境Kafka监听器容器工厂配置完成 - 并发级别: {}, 确认模式: MANUAL_IMMEDIATE", listenerConcurrency);
        
        return factory;
    }
}
