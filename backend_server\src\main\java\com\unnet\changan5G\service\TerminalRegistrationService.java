package com.unnet.changan5G.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.unnet.changan5G.dto.MetricRequestBody;
import com.unnet.changan5G.dto.terminal.TerminalBasicInfo;
import com.unnet.changan5G.entity.TerminalBasicInfoEntity;
import com.unnet.changan5G.service.TerminalBasicInfoService;
import com.unnet.changan5G.service.TerminalCacheService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 终端注册和状态管理服务
 * 
 * <AUTHOR>
 * @date 2024-07-14
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class TerminalRegistrationService {

    private final ObjectMapper objectMapper;
    private final TerminalBasicInfoService terminalBasicInfoService;
    private final TerminalCacheService terminalCacheService;
    
    // 内存缓存，存储终端最后上报时间（生产环境建议使用Redis）
    private final ConcurrentHashMap<String, LocalDateTime> terminalLastReportTime = new ConcurrentHashMap<>();
    
    // 内存缓存，存储终端基本信息（生产环境建议使用Redis）
    private final ConcurrentHashMap<String, TerminalBasicInfo> terminalBasicInfoCache = new ConcurrentHashMap<>();
    
    // 设备离线判断阈值（30秒）
    private static final int OFFLINE_THRESHOLD_SECONDS = 30;

    /**
     * 处理终端注册和状态更新
     *
     * @param metricData 指标数据
     * @param formattedMac 格式化的MAC地址
     * @param receiveTime 接收时间
     * @return 终端注册结果
     */
    public TerminalRegistrationResult processTerminalRegistration(MetricRequestBody metricData, String formattedMac, LocalDateTime receiveTime) {
        String identityMac = formattedMac;
        
        try {
            // 1. 检查终端是否需要注册
            boolean needsRegistration = checkIfNeedsRegistration(identityMac);

            // 2. 检查设备状态变化
            DeviceStatusChange statusChange = checkDeviceStatusChange(identityMac, receiveTime);

            // 3. 更新终端基本信息
            TerminalBasicInfo basicInfo = updateTerminalBasicInfo(metricData, formattedMac, receiveTime, needsRegistration);

            // 4. 更新最后上报时间
            updateLastReportTime(identityMac, receiveTime);

            log.info("终端注册处理完成 - 设备MAC: {}, 需要注册: {}, 状态变化: {}",
                    identityMac, needsRegistration, statusChange);

            return new TerminalRegistrationResult(needsRegistration, statusChange, basicInfo);

        } catch (Exception e) {
            log.error("处理终端注册时发生错误 - 设备MAC: {}, 错误: {}", identityMac, e.getMessage(), e);
            throw new RuntimeException("终端注册处理失败", e);
        }
    }

    /**
     * 检查终端是否需要注册
     */
    private boolean checkIfNeedsRegistration(String identityMac) {
        // 1. 首先检查Redis缓存中的注册标记
        if (terminalCacheService.isDeviceRegistered(identityMac)) {
            log.debug("设备已在Redis缓存中注册 - 设备MAC: {}", identityMac);
            return false;
        }

        // 2. 检查Redis缓存中是否有基本信息
        if (terminalCacheService.isDeviceExistsInCache(identityMac)) {
            log.debug("设备基本信息存在于Redis缓存中 - 设备MAC: {}", identityMac);
            // 标记为已注册
            terminalCacheService.markDeviceAsRegistered(identityMac);
            return false;
        }

        // 3. 检查内存缓存（保留原有逻辑）
        boolean needsRegistration = !terminalBasicInfoCache.containsKey(identityMac);

        // 4. 最后检查数据库（减少数据库查询）
        if (!needsRegistration) {
            needsRegistration = !terminalBasicInfoService.existsByIdentityMac(identityMac);
            if (!needsRegistration) {
                log.info("设备存在于数据库中，标记为已注册 - 设备MAC: {}", identityMac);
                terminalCacheService.markDeviceAsRegistered(identityMac);
            }
        }

        if (needsRegistration) {
            log.info("检测到新终端，需要注册 - 设备MAC: {}", identityMac);
        }

        return needsRegistration;
    }

    /**
     * 检查设备状态变化
     */
    private DeviceStatusChange checkDeviceStatusChange(String identityMac, LocalDateTime currentTime) {
        LocalDateTime lastReportTime = terminalLastReportTime.get(identityMac);

        // 1. 检查内存中的最后上报时间
        if (lastReportTime == null) {
            // 检查Redis缓存中的最后上报时间
            LocalDateTime cachedLastReportTime = terminalCacheService.getCachedLastReportTime(identityMac);
            if (cachedLastReportTime != null) {
                lastReportTime = cachedLastReportTime;
                // 同步到内存缓存
                terminalLastReportTime.put(identityMac, lastReportTime);
                log.debug("从Redis恢复最后上报时间 - 设备MAC: {}, 时间: {}", identityMac, lastReportTime);
            }
        }

        // 2. 检查当前设备状态（从缓存或数据库）
        Integer currentStatus = terminalCacheService.getCachedDeviceStatus(identityMac);
        if (currentStatus == null) {
            // 从数据库获取状态
            try {
                TerminalBasicInfoEntity entity = terminalBasicInfoService.getByIdentityMac(identityMac);
                if (entity != null) {
                    currentStatus = entity.getStatus();
                    log.debug("从数据库获取设备状态 - 设备MAC: {}, 状态: {}", identityMac, currentStatus);
                }
            } catch (Exception e) {
                log.warn("获取设备状态失败 - 设备MAC: {}, 错误: {}", identityMac, e.getMessage());
            }
        }

        // 3. 判断状态变化
        if (lastReportTime == null) {
            // 首次上报，设备上线
            log.info("设备首次上报，状态变为在线 - 设备MAC: {}", identityMac);
            return DeviceStatusChange.COME_ONLINE;
        }

        // 计算距离上次上报的时间间隔
        long secondsSinceLastReport = java.time.Duration.between(lastReportTime, currentTime).getSeconds();

        // 4. 如果设备当前是离线状态，且现在有数据上报，则为重新上线
        if (currentStatus != null && currentStatus == 0) {
            log.info("设备从离线状态恢复上线 - 设备MAC: {}, 离线时长: {}秒", identityMac, secondsSinceLastReport);
            return DeviceStatusChange.BACK_ONLINE;
        }

        // 5. 如果超过阈值时间，设备从离线恢复在线
        if (secondsSinceLastReport > OFFLINE_THRESHOLD_SECONDS) {
            log.info("设备从长时间无响应恢复在线 - 设备MAC: {}, 离线时长: {}秒", identityMac, secondsSinceLastReport);
            return DeviceStatusChange.BACK_ONLINE;
        }

        // 正常上报，状态无变化
        log.debug("设备正常上报，状态无变化 - 设备MAC: {}, 距离上次: {}秒", identityMac, secondsSinceLastReport);
        return DeviceStatusChange.NO_CHANGE;
    }

    /**
     * 更新终端基本信息（优化版本）
     */
    private TerminalBasicInfo updateTerminalBasicInfo(MetricRequestBody metricData, String formattedMac, LocalDateTime receiveTime, boolean isNewRegistration) {
        String identityMac = formattedMac;

        // 1. 首先尝试从Redis缓存获取
        TerminalBasicInfo basicInfo = terminalCacheService.getCachedTerminalBasicInfo(identityMac);

        // 2. 如果Redis缓存中没有，尝试从内存缓存获取
        if (basicInfo == null) {
            basicInfo = terminalBasicInfoCache.get(identityMac);
        }

        // 3. 如果都没有或者是新注册，创建新的基本信息
        if (basicInfo == null || isNewRegistration) {
            basicInfo = new TerminalBasicInfo();
            basicInfo.setDeviceId(metricData.getDeviceId()); // 保留原始deviceId用于兼容
            basicInfo.setIdentityMac(formattedMac); // 使用格式化的MAC地址作为主键
            basicInfo.setFirstRegisterTime(receiveTime);
            log.info("创建新的终端基本信息 - 设备MAC: {}", formattedMac);
        }

        // 4. 更新基本信息
        basicInfo.setHostname(metricData.getHostname());
        basicInfo.setIdentityMac(identityMac); // 确保使用格式化的MAC地址
        basicInfo.setDeviceId(metricData.getDeviceId());
        basicInfo.setAppVersion(metricData.getAppVersion());
        basicInfo.setExpiredDate(metricData.getExpiredDate());
        basicInfo.setReceiveTime(receiveTime);
        basicInfo.setLastUpdateTime(receiveTime);
        basicInfo.setStatus(1); // 设置为在线状态

        // 5. 同时更新内存缓存和Redis缓存（使用MAC地址作为key）
        terminalBasicInfoCache.put(identityMac, basicInfo);
        terminalCacheService.cacheTerminalBasicInfo(identityMac, basicInfo);

        // 6. 缓存最后上报时间和设备状态
        terminalCacheService.cacheLastReportTime(identityMac, receiveTime);
        terminalCacheService.cacheDeviceStatus(identityMac, 1);

        log.debug("更新终端基本信息完成 - 设备MAC: {}, 主机名: {}", identityMac, basicInfo.getHostname());

        return basicInfo;
    }

    /**
     * 更新最后上报时间
     */
    private void updateLastReportTime(String identityMac, LocalDateTime reportTime) {
        terminalLastReportTime.put(identityMac, reportTime);
        log.debug("更新终端最后上报时间 - 设备MAC: {}, 时间: {}", identityMac, reportTime);
    }

    /**
     * 检查离线设备（定时任务调用）
     */
    public void checkOfflineDevices() {
        LocalDateTime now = LocalDateTime.now();

        terminalLastReportTime.forEach((deviceId, lastReportTime) -> {
            long secondsSinceLastReport = java.time.Duration.between(lastReportTime, now).getSeconds();

            if (secondsSinceLastReport > OFFLINE_THRESHOLD_SECONDS) {
                // 设备离线
                TerminalBasicInfo basicInfo = terminalBasicInfoCache.get(deviceId);
                if (basicInfo != null && basicInfo.getStatus() == 1) {
                    basicInfo.setStatus(0); // 设置为离线状态
                    basicInfo.setLastUpdateTime(now);

                    log.warn("检测到设备离线 - 设备: {}, 最后上报时间: {}, 离线时长: {}秒",
                            deviceId, lastReportTime, secondsSinceLastReport);

                    // TODO: 这里可以添加离线通知逻辑
                    // notificationService.sendOfflineAlert(deviceId, secondsSinceLastReport);
                }
            }
        });
    }

    /**
     * 检查离线设备并返回新发现的离线设备列表（改进版本）
     *
     * @return 新发现的离线设备列表
     */
    public List<TerminalBasicInfo> checkAndGetOfflineDevices() {
        LocalDateTime now = LocalDateTime.now();
        List<TerminalBasicInfo> newOfflineDevices = new ArrayList<>();

        terminalLastReportTime.forEach((deviceId, lastReportTime) -> {
            long secondsSinceLastReport = java.time.Duration.between(lastReportTime, now).getSeconds();

            if (secondsSinceLastReport > OFFLINE_THRESHOLD_SECONDS) {
                // 设备离线
                TerminalBasicInfo basicInfo = terminalBasicInfoCache.get(deviceId);
                if (basicInfo != null && basicInfo.getStatus() == 1) {
                    // 只有状态从在线变为离线的设备才加入列表
                    basicInfo.setStatus(0); // 设置为离线状态
                    basicInfo.setLastUpdateTime(now);

                    // 添加到新离线设备列表
                    newOfflineDevices.add(basicInfo);

                    log.warn("检测到设备离线 - 设备: {}, 主机名: {}, 最后上报时间: {}, 离线时长: {}秒",
                            deviceId, basicInfo.getHostname(), lastReportTime, secondsSinceLastReport);
                }
            }
        });

        return newOfflineDevices;
    }

    /**
     * 获取终端基本信息
     */
    public TerminalBasicInfo getTerminalBasicInfo(String deviceId) {
        return terminalBasicInfoCache.get(deviceId);
    }

    /**
     * 获取所有在线设备数量
     */
    public long getOnlineDeviceCount() {
        // 优先从数据库获取准确数据
        return terminalBasicInfoService.getOnlineDeviceCount();
    }

    /**
     * 获取所有离线设备数量
     */
    public long getOfflineDeviceCount() {
        // 优先从数据库获取准确数据
        return terminalBasicInfoService.getOfflineDeviceCount();
    }

    /**
     * 终端注册结果
     */
    public static class TerminalRegistrationResult {
        private final boolean needsRegistration;
        private final DeviceStatusChange statusChange;
        private final TerminalBasicInfo basicInfo;

        public TerminalRegistrationResult(boolean needsRegistration, DeviceStatusChange statusChange, TerminalBasicInfo basicInfo) {
            this.needsRegistration = needsRegistration;
            this.statusChange = statusChange;
            this.basicInfo = basicInfo;
        }

        public boolean isNeedsRegistration() {
            return needsRegistration;
        }

        public DeviceStatusChange getStatusChange() {
            return statusChange;
        }

        public TerminalBasicInfo getBasicInfo() {
            return basicInfo;
        }
    }

    /**
     * 设备状态变化枚举
     */
    public enum DeviceStatusChange {
        NO_CHANGE("无变化"),
        COME_ONLINE("上线"),
        BACK_ONLINE("重新上线"),
        GO_OFFLINE("离线");

        private final String description;

        DeviceStatusChange(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }
}
