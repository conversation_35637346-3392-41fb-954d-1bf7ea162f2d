//package com.unnet.jmanul.system.websocket;
//
//import com.unnet.jmanul.common.enums.EnumType;
//
//public enum ServerMessageType implements EnumType<String> {
//
//    CONNECT("连接", "connect"),
//    CLOSE("关闭", "close"),
//    INFO("信息", "info"),
//    ERROR("错误", "error"),
//    ;
//
//    private final String name;
//    private final String code;
//
//    ServerMessageType(final String name, final String code) {
//        this.name = name;
//        this.code = code;
//    }
//
//    public static ServerMessageType getEnum(String code) {
//        if (code == null) {
//            return null;
//        }
//        for (ServerMessageType e : values()) {
//            if (e.code.equals(code.toUpperCase())) {
//                return e;
//            }
//        }
//        return null;
//    }
//
//    @Override
//    public String getName() {
//        return name;
//    }
//
//    @Override
//    public String getCode() {
//        return code;
//    }
//}
