# 开发环境配置
NODE_ENV=development

# 管理服务API地址 - backend_admin_server (8080端口)
VITE_ADMIN_API_BASE_URL=http://localhost:8080

# 数据服务API地址 - backend_server (8081端口)
VITE_DATA_API_BASE_URL=http://localhost:8081

# SSE服务地址 - backend_server (8081端口)
VITE_SSE_BASE_URL=http://localhost:8081

# API密钥
VITE_API_KEY=api-SvardHNxjL87TMHbgS2JFDpUsyi1iq9e

# 是否启用Mock数据
VITE_USE_MOCK=false

# 应用标题
VITE_APP_TITLE=长安5G管理平台 - 测试环境

# 调试模式
VITE_DEBUG=true
