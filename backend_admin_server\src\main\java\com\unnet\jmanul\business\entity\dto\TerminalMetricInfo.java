package com.unnet.jmanul.business.entity.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 终端指标信息DTO
 * 
 * <AUTHOR>
 * @date 2024-07-16
 */
@Data
@Schema(description = "终端指标信息")
public class TerminalMetricInfo {

    @Schema(description = "设备唯一标识符")
    private String deviceId;

    @Schema(description = "系统运行时长（单位：秒）")
    private Double uptime;

    @Schema(description = "CPU温度（单位：摄氏度）")
    private Double cpuTemp;

    @Schema(description = "温度信息")
    private Object temperatures;

    @Schema(description = "CPU使用率信息")
    private Object cpuUsage;

    @Schema(description = "CPU总使用率（%）")
    private Double cpuPercent;

    @Schema(description = "内存使用率（%）")
    private Double memoryPercent;

    @Schema(description = "内存详细信息")
    private Object memoryUsage;

    @Schema(description = "磁盘使用情况数组")
    private Object diskUsage;

    @Schema(description = "采集数据磁盘分区使用率")
    private Double diskDataPercent;

    @Schema(description = "系统盘使用率")
    private Double diskSystemPercent;

    @Schema(description = "长安程序落盘文件信息")
    private Object cdata;

    @Schema(description = "压缩文件信息")
    private Object zdata;

    @Schema(description = "CPE集合信息")
    private Object groupUsage;

    @Schema(description = "所有CPE的带宽占用（单位：Byte/s）")
    private Long groupBps;

    @Schema(description = "指标采集时间")
    private LocalDateTime metricTime;

    @Schema(description = "数据接收时间")
    private LocalDateTime receiveTime;
}
