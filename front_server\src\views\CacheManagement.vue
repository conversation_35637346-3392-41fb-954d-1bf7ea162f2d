<template>
  <div class="cache-management-page">
    <!-- 主卡片 -->
    <div class="card">
      <div class="card-header">
        <h3>缓存管理</h3>
        <button class="btn btn-info" @click="loadCacheStats">
          <i>🔄</i> 刷新统计
        </button>
      </div>
      <div class="card-body">
        <!-- 缓存统计信息 -->
        <div class="cache-stats" v-if="cacheStats">
          <div class="stats-grid">
            <div class="stat-item">
              <div class="stat-label">总缓存数量</div>
              <div class="stat-value">{{ cacheStats.totalCaches || 0 }}</div>
            </div>
            <div class="stat-item">
              <div class="stat-label">缓存名称</div>
              <div class="stat-value">
                <span v-for="name in cacheStats.cacheNames" :key="name" class="cache-name">
                  {{ name }}
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- 缓存操作按钮 -->
        <div class="cache-actions">
          <h4>缓存操作</h4>
          <div class="action-buttons">
            <button 
              class="btn btn-primary" 
              @click="refreshThresholdConfig"
              :disabled="loading"
            >
              {{ loading ? '刷新中...' : '刷新指标阈值配置缓存' }}
            </button>
            
            <button 
              class="btn btn-warning" 
              @click="evictCache('metricThresholdConfig')"
              :disabled="loading"
            >
              清空指标阈值配置缓存
            </button>
            
            <button 
              class="btn btn-danger" 
              @click="evictAllCaches"
              :disabled="loading"
            >
              清空所有缓存
            </button>
          </div>
        </div>

        <!-- 缓存详细信息 -->
        <div class="cache-details" v-if="cacheStats && cacheStats.redisInfo">
          <h4>Redis缓存详情</h4>
          <div class="cache-keys">
            <div 
              v-for="(count, cacheName) in cacheStats.redisInfo.cacheKeyCounts" 
              :key="cacheName"
              class="cache-key-item"
            >
              <span class="cache-name">{{ cacheName }}</span>
              <span class="key-count">{{ count }} 个键</span>
              <button 
                class="btn btn-sm btn-warning" 
                @click="evictCache(cacheName)"
                :disabled="loading"
              >
                清空
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { cacheApi } from '@/api/services'

export default {
  name: 'CacheManagement',
  setup() {
    // 响应式数据
    const loading = ref(false)
    const cacheStats = ref(null)

    // 加载缓存统计信息
    const loadCacheStats = async () => {
      try {
        loading.value = true
        const response = await cacheApi.getCacheStats()
        cacheStats.value = response.data || response
        console.log('缓存统计信息:', cacheStats.value)
      } catch (error) {
        console.error('获取缓存统计失败:', error)
        ElMessage.error('获取缓存统计失败: ' + error.message)
      } finally {
        loading.value = false
      }
    }

    // 刷新指标阈值配置缓存
    const refreshThresholdConfig = async () => {
      try {
        loading.value = true
        const response = await cacheApi.refreshThresholdConfigCache()
        ElMessage.success(response.message || '指标阈值配置缓存刷新成功')
        await loadCacheStats() // 重新加载统计信息
      } catch (error) {
        console.error('刷新缓存失败:', error)
        ElMessage.error('刷新缓存失败: ' + error.message)
      } finally {
        loading.value = false
      }
    }

    // 清空指定缓存
    const evictCache = async (cacheName) => {
      try {
        await ElMessageBox.confirm(
          `确定要清空"${cacheName}"缓存吗？`,
          '确认清空缓存',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          }
        )

        loading.value = true
        const response = await cacheApi.evictCache(cacheName)
        ElMessage.success(response.message || `缓存"${cacheName}"清空成功`)
        await loadCacheStats() // 重新加载统计信息
      } catch (error) {
        if (error === 'cancel') {
          return
        }
        console.error('清空缓存失败:', error)
        ElMessage.error('清空缓存失败: ' + error.message)
      } finally {
        loading.value = false
      }
    }

    // 清空所有缓存
    const evictAllCaches = async () => {
      try {
        await ElMessageBox.confirm(
          '确定要清空所有缓存吗？这将影响系统性能！',
          '确认清空所有缓存',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'error',
          }
        )

        loading.value = true
        const response = await cacheApi.evictAllCaches()
        ElMessage.success(response.message || '所有缓存清空成功')
        await loadCacheStats() // 重新加载统计信息
      } catch (error) {
        if (error === 'cancel') {
          return
        }
        console.error('清空所有缓存失败:', error)
        ElMessage.error('清空所有缓存失败: ' + error.message)
      } finally {
        loading.value = false
      }
    }

    // 组件挂载时加载数据
    onMounted(() => {
      loadCacheStats()
    })

    return {
      loading,
      cacheStats,
      loadCacheStats,
      refreshThresholdConfig,
      evictCache,
      evictAllCaches
    }
  }
}
</script>

<style scoped>
.cache-management-page {
  padding: 20px;
}

.card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #eee;
}

.card-header h3 {
  margin: 0;
  color: #333;
}

.card-body {
  padding: 20px;
}

.cache-stats {
  margin-bottom: 30px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.stat-item {
  padding: 15px;
  background: #f8f9fa;
  border-radius: 6px;
  text-align: center;
}

.stat-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 5px;
}

.stat-value {
  font-size: 18px;
  font-weight: bold;
  color: #333;
}

.cache-name {
  display: inline-block;
  background: #e3f2fd;
  color: #1976d2;
  padding: 2px 8px;
  border-radius: 4px;
  margin: 2px;
  font-size: 12px;
}

.cache-actions {
  margin-bottom: 30px;
}

.cache-actions h4 {
  margin-bottom: 15px;
  color: #333;
}

.action-buttons {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.cache-details h4 {
  margin-bottom: 15px;
  color: #333;
}

.cache-keys {
  display: grid;
  gap: 10px;
}

.cache-key-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px;
  background: #f8f9fa;
  border-radius: 6px;
}

.key-count {
  color: #666;
  font-size: 14px;
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background: #007bff;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #0056b3;
}

.btn-info {
  background: #17a2b8;
  color: white;
}

.btn-info:hover:not(:disabled) {
  background: #138496;
}

.btn-warning {
  background: #ffc107;
  color: #212529;
}

.btn-warning:hover:not(:disabled) {
  background: #e0a800;
}

.btn-danger {
  background: #dc3545;
  color: white;
}

.btn-danger:hover:not(:disabled) {
  background: #c82333;
}

.btn-sm {
  padding: 4px 8px;
  font-size: 12px;
}
</style>
