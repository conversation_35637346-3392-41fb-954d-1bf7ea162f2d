// package com.unnet.changan5G.config;

// import lombok.extern.slf4j.Slf4j;
// import org.apache.kafka.clients.consumer.ConsumerConfig;
// import org.apache.kafka.clients.producer.ProducerConfig;
// import org.apache.kafka.common.serialization.StringDeserializer;
// import org.apache.kafka.common.serialization.StringSerializer;
// import org.springframework.beans.factory.annotation.Value;
// import org.springframework.context.annotation.Bean;
// import org.springframework.context.annotation.Configuration;
// import org.springframework.kafka.annotation.EnableKafka;
// import org.springframework.kafka.config.ConcurrentKafkaListenerContainerFactory;
// import org.springframework.kafka.core.*;
// import org.springframework.kafka.listener.ContainerProperties;

// import java.util.HashMap;
// import java.util.Map;

// /**
//  * Kafka配置类
//  * 
//  * <AUTHOR>
//  * @date 2024-07-14
//  */
// @Configuration
// @EnableKafka
// @Slf4j
// public class KafkaConfig {

//     @Value("${spring.kafka.bootstrap-servers}")
//     private String bootstrapServers;

//     @Value("${spring.kafka.producer.acks:1}")
//     private String producerAcks;

//     @Value("${spring.kafka.consumer.auto-offset-reset:earliest}")
//     private String consumerAutoOffsetReset;

//     @Value("${spring.kafka.consumer.enable-auto-commit:false}")
//     private boolean consumerEnableAutoCommit;

//     /**
//      * Kafka生产者配置
//      */
//     @Bean
//     public ProducerFactory<String, String> producerFactory() {
//         Map<String, Object> configProps = new HashMap<>();
        
//         // 基本配置
//         configProps.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapServers);
//         configProps.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
//         configProps.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        
//         // 可靠性配置
//         configProps.put(ProducerConfig.ACKS_CONFIG, producerAcks);
//         configProps.put(ProducerConfig.RETRIES_CONFIG, 3);
//         configProps.put(ProducerConfig.RETRY_BACKOFF_MS_CONFIG, 1000);
        
//         // 性能配置
//         configProps.put(ProducerConfig.BATCH_SIZE_CONFIG, 16384);
//         configProps.put(ProducerConfig.LINGER_MS_CONFIG, 5);
//         configProps.put(ProducerConfig.BUFFER_MEMORY_CONFIG, 33554432);
        
//         // 压缩配置
//         configProps.put(ProducerConfig.COMPRESSION_TYPE_CONFIG, "snappy");
        
//         log.info("Kafka生产者配置完成 - Bootstrap Servers: {}, Acks: {}", bootstrapServers, producerAcks);
        
//         return new DefaultKafkaProducerFactory<>(configProps);
//     }

//     /**
//      * Kafka模板
//      */
//     @Bean
//     public KafkaTemplate<String, String> kafkaTemplate() {
//         KafkaTemplate<String, String> template = new KafkaTemplate<>(producerFactory());
        
//         // 设置默认主题（可选）
//         // template.setDefaultTopic("5g_metric");
        
//         log.info("KafkaTemplate配置完成");
        
//         return template;
//     }

//     /**
//      * Kafka消费者配置
//      */
//     @Bean
//     public ConsumerFactory<String, String> consumerFactory() {
//         Map<String, Object> configProps = new HashMap<>();
        
//         // 基本配置
//         configProps.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapServers);
//         configProps.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
//         configProps.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        
//         // 消费者组配置
//         configProps.put(ConsumerConfig.GROUP_ID_CONFIG, "metric-consumer-group");
//         configProps.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, consumerAutoOffsetReset);
//         configProps.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, consumerEnableAutoCommit);
        
//         // 性能配置
//         configProps.put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, 100);
//         configProps.put(ConsumerConfig.MAX_POLL_INTERVAL_MS_CONFIG, 300000);
//         configProps.put(ConsumerConfig.SESSION_TIMEOUT_MS_CONFIG, 30000);
//         configProps.put(ConsumerConfig.HEARTBEAT_INTERVAL_MS_CONFIG, 10000);
        
//         // 获取数据配置
//         configProps.put(ConsumerConfig.FETCH_MIN_BYTES_CONFIG, 1);
//         configProps.put(ConsumerConfig.FETCH_MAX_WAIT_MS_CONFIG, 500);
        
//         log.info("Kafka消费者配置完成 - Bootstrap Servers: {}, Group ID: metric-consumer-group, Auto Offset Reset: {}", 
//                 bootstrapServers, consumerAutoOffsetReset);
        
//         return new DefaultKafkaConsumerFactory<>(configProps);
//     }

//     /**
//      * Kafka监听器容器工厂
//      */
//     @Bean
//     public ConcurrentKafkaListenerContainerFactory<String, String> kafkaListenerContainerFactory() {
//         ConcurrentKafkaListenerContainerFactory<String, String> factory = 
//                 new ConcurrentKafkaListenerContainerFactory<>();
        
//         factory.setConsumerFactory(consumerFactory());
        
//         // 设置并发级别（消费者线程数）
//         factory.setConcurrency(3);
        
//         // 设置确认模式为手动立即确认
//         factory.getContainerProperties().setAckMode(ContainerProperties.AckMode.MANUAL_IMMEDIATE);
        
//         // 设置错误处理器（可选）
//         // factory.setErrorHandler(new SeekToCurrentErrorHandler());
        
//         log.info("Kafka监听器容器工厂配置完成 - 并发级别: 3, 确认模式: MANUAL_IMMEDIATE");
        
//         return factory;
//     }
// }
