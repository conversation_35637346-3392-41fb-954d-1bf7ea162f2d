<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>终端运维管理平台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            color: #333;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 0 20px;
            height: 60px;
            display: flex;
            align-items: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .header h1 {
            font-size: 20px;
            font-weight: 600;
        }

        .container {
            display: flex;
            height: calc(100vh - 60px);
        }

        .sidebar {
            width: 250px;
            background: white;
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
            padding: 20px 0;
        }

        .nav-menu {
            list-style: none;
        }

        .nav-menu li {
            margin-bottom: 5px;
        }

        .nav-menu a {
            display: block;
            padding: 15px 20px;
            color: #333;
            text-decoration: none;
            transition: all 0.3s;
            border-left: 3px solid transparent;
        }

        .nav-menu a:hover,
        .nav-menu a.active {
            background: #f0f7ff;
            border-left-color: #667eea;
            color: #667eea;
        }

        .nav-menu a i {
            margin-right: 10px;
            width: 20px;
            text-align: center;
        }

        .main-content {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }

        .page {
            display: none;
        }

        .page.active {
            display: block;
        }

        .card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            overflow: hidden;
        }

        .card-header {
            padding: 20px;
            border-bottom: 1px solid #eee;
            background: #fafafa;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .card-header h3 {
            font-size: 18px;
            font-weight: 600;
            color: #333;
        }

        .card-body {
            padding: 20px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
            border-left: 4px solid #667eea;
        }

        .stat-number {
            font-size: 32px;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #666;
            font-size: 14px;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s;
            text-decoration: none;
            display: inline-block;
        }

        .btn-primary {
            background: #667eea;
            color: white;
        }

        .btn-primary:hover {
            background: #5a6fd8;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-info {
            background: #17a2b8;
            color: white;
        }

        .btn-warning {
            background: #ffc107;
            color: #212529;
        }

        .btn-danger {
            background: #dc3545;
            color: white;
        }

        .btn-success {
            background: #28a745;
            color: white;
        }

        .btn-sm {
            padding: 5px 10px;
            font-size: 12px;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }

        .table th,
        .table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }

        .table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #495057;
        }

        .table tr:hover {
            background: #f8f9fa;
        }

        .search-filters {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
            flex-wrap: wrap;
            align-items: center;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            margin-bottom: 5px;
            font-weight: 500;
            color: #555;
        }

        .form-control {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        .form-control:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
        }

        .status-online {
            color: #28a745;
            font-weight: bold;
        }

        .status-offline {
            color: #dc3545;
            font-weight: bold;
        }

        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 4px;
            border-left: 4px solid;
        }

        .alert-success {
            background: #d4edda;
            border-color: #28a745;
            color: #155724;
        }

        .alert-warning {
            background: #fff3cd;
            border-color: #ffc107;
            color: #856404;
        }

        .alert-danger {
            background: #f8d7da;
            border-color: #dc3545;
            color: #721c24;
        }

        .alert-info {
            background: #d1ecf1;
            border-color: #17a2b8;
            color: #0c5460;
        }

        .badge {
            display: inline-block;
            padding: 4px 8px;
            font-size: 12px;
            font-weight: 500;
            border-radius: 12px;
            text-align: center;
            white-space: nowrap;
        }

        .badge-success {
            background: #28a745;
            color: white;
        }

        .badge-danger {
            background: #dc3545;
            color: white;
        }

        .badge-warning {
            background: #ffc107;
            color: #212529;
        }

        .badge-info {
            background: #17a2b8;
            color: white;
        }

        .scroll-container {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #eee;
            border-radius: 4px;
            padding: 10px;
        }

        .content-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }

        .log-item {
            padding: 12px;
            margin-bottom: 8px;
            border-radius: 4px;
            font-size: 14px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: opacity 0.3s ease;
        }

        .log-item.online {
            background: #d4edda;
            color: #155724;
            border-left: 4px solid #28a745;
        }

        .log-item.offline {
            background: #f8d7da;
            color: #721c24;
            border-left: 4px solid #dc3545;
        }

        .log-item.fade-out {
            opacity: 0.5;
        }

        .log-content {
            flex: 1;
        }

        .log-actions {
            margin-left: 10px;
        }

        .list-item {
            padding: 12px;
            margin-bottom: 8px;
            border-radius: 4px;
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .list-item:hover {
            background: #e9ecef;
        }

        .item-info {
            flex: 1;
        }

        .item-title {
            font-weight: 600;
            color: #333;
            margin-bottom: 4px;
        }

        .item-subtitle {
            font-size: 12px;
            color: #666;
        }

        .item-actions {
            margin-left: 10px;
        }

        .status-badge {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
            margin-right: 8px;
        }

        .status-offline {
            background: #f8d7da;
            color: #721c24;
        }

        .status-alert {
            background: #fff3cd;
            color: #856404;
        }

        .empty-message {
            text-align: center;
            color: #999;
            font-style: italic;
            padding: 20px;
        }

        .alert-level {
            display: inline-block;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 10px;
            font-weight: 500;
            margin-left: 8px;
        }

        .alert-level.high {
            background: #dc3545;
            color: white;
        }

        .alert-level.medium {
            background: #ffc107;
            color: #212529;
        }

        .alert-level.low {
            background: #6c757d;
            color: white;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 20px;
            border-radius: 8px;
            width: 80%;
            max-width: 800px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }

        .close {
            font-size: 24px;
            font-weight: bold;
            cursor: pointer;
            color: #999;
        }

        .close:hover {
            color: #333;
        }

        .tag-input {
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            min-height: 40px;
        }

        .tag {
            background: #667eea;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .tag-remove {
            cursor: pointer;
            font-weight: bold;
        }

        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 10px;
            margin-top: 20px;
        }

        .pagination button {
            padding: 8px 12px;
            border: 1px solid #ddd;
            background: white;
            cursor: pointer;
            border-radius: 4px;
        }

        .pagination button:hover {
            background: #f8f9fa;
        }

        .pagination button.active {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }

        .breadcrumb {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            font-size: 14px;
        }

        .breadcrumb a {
            color: #667eea;
            text-decoration: none;
            cursor: pointer;
        }

        .breadcrumb a:hover {
            text-decoration: underline;
        }

        .breadcrumb span {
            margin: 0 8px;
            color: #999;
        }

        .metric-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }

        .metric-row:last-child {
            border-bottom: none;
        }

        .metric-label {
            font-weight: 500;
            color: #555;
        }

        .metric-value {
            font-weight: 600;
            color: #333;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #eee;
            border-radius: 4px;
            overflow: hidden;
            margin-top: 5px;
        }

        .progress-fill {
            height: 100%;
            background: #667eea;
            transition: width 0.3s ease;
        }

        .progress-fill.warning {
            background: #ffc107;
        }

        .progress-fill.danger {
            background: #dc3545;
        }

        .json-display {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }

        .refresh-info {
            background: #e3f2fd;
            border: 1px solid #bbdefb;
            border-radius: 4px;
            padding: 10px;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .refresh-info i {
            color: #1976d2;
        }

        .action-buttons {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .history-filters {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
            align-items: end;
        }

        .datetime-input {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        .detail-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }

        .detail-item {
            display: flex;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .detail-item:last-child {
            border-bottom: none;
        }

        .detail-label {
            font-weight: 600;
            color: #666;
            min-width: 120px;
            margin-right: 10px;
        }

        .detail-value {
            color: #333;
            flex: 1;
        }

        .topology-container {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            position: relative;
        }

        .topology-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 20px;
            color: #495057;
            text-align: center;
        }

        .topology-graph {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 30px;
        }

        .main-terminal {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 25px;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
            text-align: center;
            min-width: 200px;
        }

        .main-terminal-title {
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .main-terminal-id {
            font-size: 12px;
            opacity: 0.9;
        }

        .connection-line {
            width: 2px;
            height: 40px;
            background: linear-gradient(to bottom, #667eea, #ddd);
            position: relative;
        }

        .connection-line::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 8px;
            height: 8px;
            background: #667eea;
            border-radius: 50%;
        }

        .cpe-container {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            justify-content: center;
            max-width: 600px;
        }

        .cpe-item {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 12px;
            min-width: 120px;
            text-align: center;
            transition: all 0.3s;
            position: relative;
        }

        .cpe-item:hover {
            border-color: #667eea;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .cpe-item.active {
            border-color: #28a745;
            background: #f8fff9;
        }

        .cpe-item.inactive {
            border-color: #dc3545;
            background: #fff8f8;
        }

        .cpe-title {
            font-size: 12px;
            font-weight: 600;
            color: #666;
            margin-bottom: 5px;
        }

        .cpe-bucket {
            font-size: 14px;
            font-weight: bold;
            color: #333;
            margin-bottom: 3px;
        }

        .cpe-stats {
            font-size: 10px;
            color: #999;
            line-height: 1.2;
        }

        .cpe-status {
            position: absolute;
            top: -5px;
            right: -5px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            border: 2px solid white;
        }

        .cpe-status.online {
            background: #28a745;
        }

        .cpe-status.offline {
            background: #dc3545;
        }

        .topology-summary {
            background: #e3f2fd;
            border: 1px solid #bbdefb;
            border-radius: 6px;
            padding: 15px;
            margin-top: 20px;
        }

        .summary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
        }

        .summary-item {
            text-align: center;
        }

        .summary-value {
            font-size: 18px;
            font-weight: bold;
            color: #1976d2;
            margin-bottom: 5px;
        }

        .summary-label {
            font-size: 12px;
            color: #666;
        }

        .no-cpe-message {
            text-align: center;
            color: #999;
            font-style: italic;
            padding: 20px;
        }
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .metric-section {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
        }

        .metric-section h5 {
            color: #495057;
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 15px;
            padding-bottom: 8px;
            border-bottom: 2px solid #667eea;
        }

        .metric-section .metric-row {
            margin-bottom: 12px;
            padding: 8px 0;
            border-bottom: 1px solid #dee2e6;
        }

        .metric-section .metric-row:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }

        .metric-section .metric-label {
            font-size: 13px;
            color: #6c757d;
            margin-bottom: 5px;
        }

        .metric-section .metric-value {
            font-size: 14px;
            color: #212529;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>终端运维管理平台</h1>
    </div>

    <div class="container">
        <div class="sidebar">
            <ul class="nav-menu">
                <li><a href="#" class="nav-item active" data-page="overview">
                    <i>📊</i>概览
                </a></li>
                <li><a href="#" class="nav-item" data-page="terminals">
                    <i>💻</i>终端管理
                </a></li>
                <li><a href="#" class="nav-item" data-page="alerts">
                    <i>🚨</i>站内告警
                </a></li>
            </ul>
        </div>

        <div class="main-content">
            <!-- 概览页面 -->
            <div id="overview" class="page active">
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number" id="totalTerminals">1,234</div>
                        <div class="stat-label">终端总数</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="onlineTerminals">1,156</div>
                        <div class="stat-label">在线终端</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="offlineTerminals">78</div>
                        <div class="stat-label">离线终端</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="alertCount">12</div>
                        <div class="stat-label">告警数量</div>
                    </div>
                </div>

                <!-- 主要内容区域 -->
                <div class="content-grid">
                    <!-- 上下线通知 -->
                    <div class="card">
                        <div class="card-header">
                            <h3>终端上下线实时动态</h3>
                        </div>
                        <div class="card-body">
                            <div class="scroll-container" id="logContainer">
                                <div class="empty-message" id="emptyLogMessage">暂无上下线记录</div>
                            </div>
                        </div>
                    </div>

                    <!-- 离线终端列表 -->
                    <div class="card">
                        <div class="card-header">
                            <h3>离线终端列表</h3>
                        </div>
                        <div class="card-body">
                            <div class="scroll-container" id="offlineContainer">
                                <div class="list-item">
                                    <div class="item-info">
                                        <div class="item-title">
                                            <span class="status-badge status-offline">离线</span>
                                            T002
                                        </div>
                                        <div class="item-subtitle">主机名002 - 离线时间: 2024-07-14 18:28:15</div>
                                    </div>
                                    <div class="item-actions">
                                        <button class="btn btn-info btn-sm" onclick="viewTerminalDetailFromOverview('T002')">查看详情</button>
                                    </div>
                                </div>
                                <div class="list-item">
                                    <div class="item-info">
                                        <div class="item-title">
                                            <span class="status-badge status-offline">离线</span>
                                            T005
                                        </div>
                                        <div class="item-subtitle">主机名005 - 离线时间: 2024-07-14 18:20:55</div>
                                    </div>
                                    <div class="item-actions">
                                        <button class="btn btn-info btn-sm" onclick="viewTerminalDetailFromOverview('T005')">查看详情</button>
                                    </div>
                                </div>
                                <div class="list-item">
                                    <div class="item-info">
                                        <div class="item-title">
                                            <span class="status-badge status-offline">离线</span>
                                            T008
                                        </div>
                                        <div class="item-subtitle">主机名008 - 离线时间: 2024-07-14 18:15:20</div>
                                    </div>
                                    <div class="item-actions">
                                        <button class="btn btn-info btn-sm" onclick="viewTerminalDetailFromOverview('T008')">查看详情</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 告警终端列表 -->
                <div class="card">
                    <div class="card-header">
                        <h3>告警终端列表</h3>
                    </div>
                    <div class="card-body">
                        <div class="scroll-container" id="alertContainer">
                            <div class="list-item">
                                <div class="item-info">
                                    <div class="item-title">
                                        <span class="status-badge status-alert">告警</span>
                                        _0fd94938951f4a64bb11c6817a81f7e7
                                        <span class="alert-level high">高</span>
                                    </div>
                                    <div class="item-subtitle">CPU温度过高 - 告警时间: 2024-07-14 18:25:30</div>
                                </div>
                                <div class="item-actions">
                                    <button class="btn btn-danger btn-sm" onclick="viewAlertDetailFromOverview('A001')">查看告警</button>
                                    <button class="btn btn-warning btn-sm" onclick="viewTerminalMetricsFromOverview('_0fd94938951f4a64bb11c6817a81f7e7')">实时指标</button>
                                </div>
                            </div>
                            <div class="list-item">
                                <div class="item-info">
                                    <div class="item-title">
                                        <span class="status-badge status-alert">告警</span>
                                        T003
                                        <span class="alert-level medium">中</span>
                                    </div>
                                    <div class="item-subtitle">系统盘使用率过高 - 告警时间: 2024-07-14 18:18:45</div>
                                </div>
                                <div class="item-actions">
                                    <button class="btn btn-danger btn-sm" onclick="viewAlertDetailFromOverview('A003')">查看告警</button>
                                    <button class="btn btn-warning btn-sm" onclick="viewTerminalMetricsFromOverview('T003')">实时指标</button>
                                </div>
                            </div>
                            <div class="list-item">
                                <div class="item-info">
                                    <div class="item-title">
                                        <span class="status-badge status-alert">告警</span>
                                        _0fd94938951f4a64bb11c6817a81f7e7
                                        <span class="alert-level medium">中</span>
                                    </div>
                                    <div class="item-subtitle">软件许可证即将过期 - 告警时间: 2024-07-14 18:15:20</div>
                                </div>
                                <div class="item-actions">
                                    <button class="btn btn-danger btn-sm" onclick="viewAlertDetailFromOverview('A004')">查看告警</button>
                                    <button class="btn btn-warning btn-sm" onclick="viewTerminalMetricsFromOverview('_0fd94938951f4a64bb11c6817a81f7e7')">实时指标</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 终端管理页面 -->
            <div id="terminals" class="page">
                <div class="card">
                    <div class="card-header">
                        <h3>终端列表</h3>
                    </div>
                    <div class="card-body">
                        <div class="search-filters">
                            <div class="form-group">
                                <label>设备ID</label>
                                <input type="text" class="form-control" placeholder="输入设备ID">
                            </div>
                            <div class="form-group">
                                <label>主机名</label>
                                <input type="text" class="form-control" placeholder="输入主机名">
                            </div>
                            <div class="form-group">
                                <label>状态</label>
                                <select class="form-control">
                                    <option value="">全部</option>
                                    <option value="1">在线</option>
                                    <option value="0">离线</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>标签</label>
                                <input type="text" class="form-control" placeholder="输入标签">
                            </div>
                            <button class="btn btn-primary" style="margin-top: 25px;">搜索</button>
                            <button class="btn btn-secondary" style="margin-top: 25px;">重置</button>
                        </div>

                        <table class="table">
                            <thead>
                                <tr>
                                    <th>设备ID</th>
                                    <th>主机名</th>
                                    <th>MAC地址</th>
                                    <th>状态</th>
                                    <th>标签</th>
                                    <th>最后更新</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- 数据将通过JavaScript动态生成 -->
                            </tbody>
                        </table>

                        <div class="pagination">
                            <button>上一页</button>
                            <button class="active">1</button>
                            <button>2</button>
                            <button>3</button>
                            <button>下一页</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 实时指标页面 -->
            <div id="realTimeMetrics" class="page">
                <div class="breadcrumb">
                    <a onclick="showPage('terminals')">终端管理</a>
                    <span>></span>
                    <span>实时指标</span>
                </div>
                
                <div class="card">
                    <div class="card-header">
                        <h3>终端实时指标 - <span id="currentDeviceId">_0fd94938951f4a64bb11c6817a81f7e7</span></h3>
                        <button class="btn btn-primary" onclick="refreshRealTimeMetrics()">
                            <i>🔄</i> 刷新数据
                        </button>
                    </div>
                    <div class="card-body">
                        <div class="refresh-info">
                            <i>ℹ️</i>
                            <span>点击刷新按钮获取最新的终端指标数据，数据更新时间：<span id="lastUpdateTime">2024-07-14 18:30:45</span></span>
                        </div>

                        <div class="stats-grid">
                            <div class="stat-card">
                                <div class="stat-number" id="cpuTemp">85.9°C</div>
                                <div class="stat-label">CPU温度</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number" id="cpuUsage">43.2%</div>
                                <div class="stat-label">CPU使用率</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number" id="memoryUsage">50.9%</div>
                                <div class="stat-label">内存使用率</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number" id="diskUsage">54.3%</div>
                                <div class="stat-label">磁盘使用率</div>
                            </div>
                        </div>

                        <div class="card">
                            <div class="card-header">
                                <h4>详细指标信息</h4>
                            </div>
                            <div class="card-body">
                                <div class="metrics-grid">
                                    <div class="metric-section">
                                        <h5>系统信息</h5>
                                        <div class="metric-row">
                                            <div class="metric-label">系统运行时长</div>
                                            <div class="metric-value" id="uptime">98分钟36秒</div>
                                        </div>
                                        <div class="metric-row">
                                            <div class="metric-label">设备温度</div>
                                            <div class="metric-value">
                                                <div>SOC温度: 58.9°C</div>
                                                <div>GPU温度: 57.8°C</div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="metric-section">
                                        <h5>CPU信息</h5>
                                        <div class="metric-row">
                                            <div class="metric-label">CPU核心</div>
                                            <div class="metric-value">4核4线程</div>
                                        </div>
                                        <div class="metric-row">
                                            <div class="metric-label">CPU使用率</div>
                                            <div class="metric-value">
                                                <div>用户态: 46.3% | 系统态: 9.7% | 空闲: 43.7%</div>
                                                <div class="progress-bar">
                                                    <div class="progress-fill" id="cpuProgressBar" style="width: 43.2%"></div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="metric-section">
                                        <h5>内存信息</h5>
                                        <div class="metric-row">
                                            <div class="metric-label">内存容量</div>
                                            <div class="metric-value">总内存: 3.8GB</div>
                                        </div>
                                        <div class="metric-row">
                                            <div class="metric-label">内存使用</div>
                                            <div class="metric-value">
                                                <div>已用: 1.9GB | 可用: 1.9GB</div>
                                                <div>缓存: 601MB | 缓冲区: 54MB</div>
                                                <div class="progress-bar">
                                                    <div class="progress-fill" id="memoryProgressBar" style="width: 50.9%"></div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="metric-section">
                                        <h5>存储信息</h5>
                                        <div class="metric-row">
                                            <div class="metric-label">磁盘设备</div>
                                            <div class="metric-value">/dev/mmcblk0p3 (ext4)</div>
                                        </div>
                                        <div class="metric-row">
                                            <div class="metric-label">磁盘使用</div>
                                            <div class="metric-value">
                                                <div>总空间: 28.5GB | 已用: 14.8GB | 可用: 12.5GB</div>
                                                <div class="progress-bar">
                                                    <div class="progress-fill warning" id="diskProgressBar" style="width: 54.3%"></div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="metric-section">
                                        <h5>数据文件</h5>
                                        <div class="metric-row">
                                            <div class="metric-label">采集数据</div>
                                            <div class="metric-value">1个文件，总大小: 3.4GB</div>
                                        </div>
                                        <div class="metric-row">
                                            <div class="metric-label">压缩数据</div>
                                            <div class="metric-value">0个文件，总大小: 0GB</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 历史指标页面 -->
            <div id="historyMetrics" class="page">
                <div class="breadcrumb">
                    <a onclick="showPage('terminals')">终端管理</a>
                    <span>></span>
                    <span>历史指标</span>
                </div>
                
                <div class="card">
                    <div class="card-header">
                        <h3>终端历史指标 - <span id="historyDeviceId">_0fd94938951f4a64bb11c6817a81f7e7</span></h3>
                    </div>
                    <div class="card-body">
                        <div class="history-filters">
                            <div class="form-group">
                                <label>开始时间</label>
                                <input type="datetime-local" class="datetime-input" id="startTime" value="2024-07-14T10:00">
                            </div>
                            <div class="form-group">
                                <label>结束时间</label>
                                <input type="datetime-local" class="datetime-input" id="endTime" value="2024-07-14T20:00">
                            </div>
                            <button class="btn btn-primary" onclick="searchHistoryMetrics()">查询</button>
                            <button class="btn btn-secondary" onclick="resetHistoryFilters()">重置</button>
                        </div>

                        <table class="table">
                            <thead>
                                <tr>
                                    <th>采集时间</th>
                                    <th>CPU温度</th>
                                    <th>CPU使用率</th>
                                    <th>内存使用率</th>
                                    <th>磁盘使用率</th>
                                    <th>运行时长</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="historyTableBody">
                                <tr>
                                    <td>2024-07-14 18:30:45</td>
                                    <td>85.9°C</td>
                                    <td>43.2%</td>
                                    <td>50.9%</td>
                                    <td>54.3%</td>
                                    <td>98分钟36秒</td>
                                    <td>
                                        <button class="btn btn-info btn-sm" onclick="viewHistoryDetail('2024-07-14 18:30:45')">查看详情</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>2024-07-14 18:25:45</td>
                                    <td>84.2°C</td>
                                    <td>41.8%</td>
                                    <td>48.5%</td>
                                    <td>54.1%</td>
                                    <td>93分钟36秒</td>
                                    <td>
                                        <button class="btn btn-info btn-sm" onclick="viewHistoryDetail('2024-07-14 18:25:45')">查看详情</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>2024-07-14 18:20:45</td>
                                    <td>83.5°C</td>
                                    <td>39.6%</td>
                                    <td>46.2%</td>
                                    <td>53.8%</td>
                                    <td>88分钟36秒</td>
                                    <td>
                                        <button class="btn btn-info btn-sm" onclick="viewHistoryDetail('2024-07-14 18:20:45')">查看详情</button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>

                        <div class="pagination">
                            <button>上一页</button>
                            <button class="active">1</button>
                            <button>2</button>
                            <button>3</button>
                            <button>下一页</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 站内告警页面 -->
            <div id="alerts" class="page">
                <div class="card">
                    <div class="card-header">
                        <h3>告警列表</h3>
                    </div>
                    <div class="card-body">
                        <div class="search-filters">
                            <div class="form-group">
                                <label>设备ID</label>
                                <input type="text" class="form-control" placeholder="输入设备ID">
                            </div>
                            <div class="form-group">
                                <label>告警类型</label>
                                <select class="form-control">
                                    <option value="">全部</option>
                                    <option value="CPU_TEMPERATURE">CPU温度</option>
                                    <option value="MEMORY_USAGE">内存使用率</option>
                                    <option value="DISK_USAGE">磁盘使用率</option>
                                    <option value="LICENSE_EXPIRY">许可证过期</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>告警状态</label>
                                <select class="form-control">
                                    <option value="">全部</option>
                                    <option value="ACTIVE">活跃</option>
                                    <option value="RESOLVED">已解决</option>
                                    <option value="ACKNOWLEDGED">已确认</option>
                                </select>
                            </div>
                            <button class="btn btn-primary" style="margin-top: 25px;">搜索</button>
                            <button class="btn btn-secondary" style="margin-top: 25px;">重置</button>
                        </div>

                        <table class="table">
                            <thead>
                                <tr>
                                    <th>告警ID</th>
                                    <th>设备ID</th>
                                    <th>告警类型</th>
                                    <th>告警详情</th>
                                    <th>当前值</th>
                                    <th>阈值</th>
                                    <th>状态</th>
                                    <th>告警时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- 数据将通过JavaScript动态生成 -->
                            </tbody>
                        </table>

                        <div class="pagination">
                            <button>上一页</button>
                            <button class="active">1</button>
                            <button>2</button>
                            <button>3</button>
                            <button>下一页</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 模态框 -->
    <!-- 终端详情模态框 -->
    <div id="terminalDetailModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>终端详情</h3>
                <span class="close" onclick="closeModal('terminalDetailModal')">&times;</span>
            </div>
            <div class="modal-body">
                <!-- 基本信息 -->
                <div class="card">
                    <div class="card-header">
                        <h4>基本信息</h4>
                    </div>
                    <div class="card-body">
                        <div class="detail-grid">
                            <div>
                                <div class="detail-item">
                                    <div class="detail-label">设备ID:</div>
                                    <div class="detail-value">_0fd94938951f4a64bb11c6817a81f7e7</div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">主机名:</div>
                                    <div class="detail-value">ec_3568_25030031</div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">MAC地址:</div>
                                    <div class="detail-value">de:07:65:52:3e:60</div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">状态:</div>
                                    <div class="detail-value"><span class="status-online">在线</span></div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">数据源:</div>
                                    <div class="detail-value">kafka</div>
                                </div>
                            </div>
                            <div>
                                <div class="detail-item">
                                    <div class="detail-label">首次注册:</div>
                                    <div class="detail-value">2024-07-14 18:30:45</div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">最后更新:</div>
                                    <div class="detail-value">2024-07-14 18:30:45</div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">授权到期:</div>
                                    <div class="detail-value">2035-03-09 22:47:20</div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">接收时间:</div>
                                    <div class="detail-value">2024-07-14 18:30:45</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 终端拓扑关系 -->
                <div class="card">
                    <div class="card-header">
                        <h4>终端拓扑关系</h4>
                    </div>
                    <div class="card-body">
                        <div class="topology-container">
                            <div class="topology-title">终端系统架构图</div>
                            <div class="topology-graph">
                                <!-- 主终端 -->
                                <div class="main-terminal">
                                    <div class="main-terminal-title">主终端服务</div>
                                    <div class="main-terminal-id">ec_3568_25030031</div>
                                </div>
                                
                                <!-- 连接线 -->
                                <div class="connection-line"></div>
                                
                                <!-- CPE集合 -->
                                <div class="cpe-container" id="cpeContainer">
                                    <div class="cpe-item active">
                                        <div class="cpe-status online"></div>
                                        <div class="cpe-title">CPE</div>
                                        <div class="cpe-bucket">Bucket 0</div>
                                        <div class="cpe-stats">
                                            连接: 0<br>
                                            PPS: 0.0<br>
                                            BPS: 0.0
                                        </div>
                                    </div>
                                    <div class="cpe-item active">
                                        <div class="cpe-status online"></div>
                                        <div class="cpe-title">CPE</div>
                                        <div class="cpe-bucket">Bucket 1</div>
                                        <div class="cpe-stats">
                                            连接: 0<br>
                                            PPS: 0.0<br>
                                            BPS: 0.0
                                        </div>
                                    </div>
                                    <div class="cpe-item active">
                                        <div class="cpe-status online"></div>
                                        <div class="cpe-title">CPE</div>
                                        <div class="cpe-bucket">Bucket 2</div>
                                        <div class="cpe-stats">
                                            连接: 0<br>
                                            PPS: 0.0<br>
                                            BPS: 0.0
                                        </div>
                                    </div>
                                    <div class="cpe-item active">
                                        <div class="cpe-status online"></div>
                                        <div class="cpe-title">CPE</div>
                                        <div class="cpe-bucket">Bucket 3</div>
                                        <div class="cpe-stats">
                                            连接: 0<br>
                                            PPS: 0.0<br>
                                            BPS: 0.0
                                        </div>
                                    </div>
                                    <div class="cpe-item active">
                                        <div class="cpe-status online"></div>
                                        <div class="cpe-title">CPE</div>
                                        <div class="cpe-bucket">Bucket 4</div>
                                        <div class="cpe-stats">
                                            连接: 0<br>
                                            PPS: 0.0<br>
                                            BPS: 0.0
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 拓扑统计信息 -->
                            <div class="topology-summary">
                                <div class="summary-grid">
                                    <div class="summary-item">
                                        <div class="summary-value">105</div>
                                        <div class="summary-label">组ID</div>
                                    </div>
                                    <div class="summary-item">
                                        <div class="summary-value">5</div>
                                        <div class="summary-label">CPE数量</div>
                                    </div>
                                    <div class="summary-item">
                                        <div class="summary-value">0.0</div>
                                        <div class="summary-label">总PPS</div>
                                    </div>
                                    <div class="summary-item">
                                        <div class="summary-value">0 B/s</div>
                                        <div class="summary-label">总带宽</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 版本信息 -->
                <div class="card">
                    <div class="card-header">
                        <h4>版本信息</h4>
                    </div>
                    <div class="card-body">
                        <div class="detail-grid">
                            <div>
                                <div class="detail-item">
                                    <div class="detail-label">Controller:</div>
                                    <div class="detail-value">1.0.1</div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">Detector:</div>
                                    <div class="detail-value">1.0.48</div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">Web:</div>
                                    <div class="detail-value">1.0.55</div>
                                </div>
                            </div>
                            <div>
                                <div class="detail-item">
                                    <div class="detail-label">Agent:</div>
                                    <div class="detail-value">1.0.0</div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">Front:</div>
                                    <div class="detail-value">1.0.40</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 标签信息 -->
                <div class="card">
                    <div class="card-header">
                        <h4>标签信息</h4>
                    </div>
                    <div class="card-body">
                        <div class="detail-grid">
                            <div>
                                <div class="detail-item">
                                    <div class="detail-label">位置:</div>
                                    <div class="detail-value">未知</div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">部门:</div>
                                    <div class="detail-value">运维部</div>
                                </div>
                            </div>
                            <div>
                                <div class="detail-item">
                                    <div class="detail-label">环境:</div>
                                    <div class="detail-value">生产</div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">优先级:</div>
                                    <div class="detail-value">高</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 自定义字段 -->
                <div class="card">
                    <div class="card-header">
                        <h4>自定义字段</h4>
                    </div>
                    <div class="card-body">
                        <div class="detail-grid">
                            <div>
                                <div class="detail-item">
                                    <div class="detail-label">车辆型号:</div>
                                    <div class="detail-value">长安车型</div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">安装日期:</div>
                                    <div class="detail-value">2024-07-14</div>
                                </div>
                            </div>
                            <div>
                                <div class="detail-item">
                                    <div class="detail-label">维护联系人:</div>
                                    <div class="detail-value">技术支持</div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">备注:</div>
                                    <div class="detail-value">生产环境设备</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 编辑终端模态框 -->
    <div id="editTerminalModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>编辑终端</h3>
                <span class="close" onclick="closeModal('editTerminalModal')">&times;</span>
            </div>
            <div class="modal-body">
                <div class="card">
                    <div class="card-header">
                        <h4>基本信息</h4>
                    </div>
                    <div class="card-body">
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                            <div class="form-group">
                                <label>设备ID</label>
                                <input type="text" class="form-control" value="_0fd94938951f4a64bb11c6817a81f7e7" readonly>
                            </div>
                            <div class="form-group">
                                <label>主机名</label>
                                <input type="text" class="form-control" value="ec_3568_25030031">
                            </div>
                            <div class="form-group">
                                <label>MAC地址</label>
                                <input type="text" class="form-control" value="de:07:65:52:3e:60">
                            </div>
                            <div class="form-group">
                                <label>状态</label>
                                <select class="form-control">
                                    <option value="1" selected>在线</option>
                                    <option value="0">离线</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h4>标签管理</h4>
                    </div>
                    <div class="card-body">
                        <div class="form-group">
                            <label>标签</label>
                            <div class="tag-input" id="tagInput">
                                <div class="tag">
                                    生产 <span class="tag-remove" onclick="removeTag(this)">×</span>
                                </div>
                                <div class="tag">
                                    高 <span class="tag-remove" onclick="removeTag(this)">×</span>
                                </div>
                                <div class="tag">
                                    运维部 <span class="tag-remove" onclick="removeTag(this)">×</span>
                                </div>
                                <input type="text" style="border: none; outline: none; flex: 1;" placeholder="输入标签后按回车" onkeypress="addTag(event)">
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h4>自定义字段</h4>
                    </div>
                    <div class="card-body">
                        <div id="customFields">
                            <div style="display: flex; gap: 10px; margin-bottom: 10px; align-items: center;">
                                <input type="text" class="form-control" placeholder="字段名" value="车辆型号">
                                <input type="text" class="form-control" placeholder="字段值" value="长安车型">
                                <button class="btn btn-danger btn-sm" onclick="removeCustomField(this)">删除</button>
                            </div>
                            <div style="display: flex; gap: 10px; margin-bottom: 10px; align-items: center;">
                                <input type="text" class="form-control" placeholder="字段名" value="安装日期">
                                <input type="text" class="form-control" placeholder="字段值" value="2024-07-14">
                                <button class="btn btn-danger btn-sm" onclick="removeCustomField(this)">删除</button>
                            </div>
                            <div style="display: flex; gap: 10px; margin-bottom: 10px; align-items: center;">
                                <input type="text" class="form-control" placeholder="字段名" value="维护联系人">
                                <input type="text" class="form-control" placeholder="字段值" value="技术支持">
                                <button class="btn btn-danger btn-sm" onclick="removeCustomField(this)">删除</button>
                            </div>
                        </div>
                        <button class="btn btn-secondary" onclick="addCustomField()">添加字段</button>
                    </div>
                </div>

                <div style="text-align: center; margin-top: 20px;">
                    <button class="btn btn-primary">保存</button>
                    <button class="btn btn-secondary" onclick="closeModal('editTerminalModal')">取消</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 告警详情模态框 -->
    <div id="alertDetailModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>告警详情</h3>
                <span class="close" onclick="closeModal('alertDetailModal')">&times;</span>
            </div>
            <div class="modal-body">
                <div class="card">
                    <div class="card-body">
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                            <div>
                                <strong>告警ID:</strong> <span id="alertId">A001</span><br>
                                <strong>设备ID:</strong> <span id="deviceId">_0fd94938951f4a64bb11c6817a81f7e7</span><br>
                                <strong>告警类型:</strong> <span id="alertType">CPU温度</span><br>
                                <strong>告警详情:</strong> <span id="alertDetails">CPU温度过高</span><br>
                                <strong>当前值:</strong> <span id="currentValue">85.9°C</span><br>
                                <strong>阈值:</strong> <span id="threshold">80°C</span>
                            </div>
                            <div>
                                <strong>告警时间:</strong> <span id="alertTime">2024-07-14 18:25:30</span><br>
                                <strong>状态:</strong> <span id="alertStatus" class="badge badge-danger">活跃</span><br>
                                <strong>通知状态:</strong> <span id="notificationStatus">已发送</span><br>
                                <strong>通知时间:</strong> <span id="notificationTime">2024-07-14 18:25:35</span><br>
                                <strong>确认人:</strong> <span id="acknowledgedBy">未确认</span><br>
                                <strong>解决时间:</strong> <span id="resolvedTime">未解决</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div style="text-align: center; margin-top: 20px;">
                    <button class="btn btn-warning">确认告警</button>
                    <button class="btn btn-success">标记已解决</button>
                    <button class="btn btn-secondary" onclick="closeModal('alertDetailModal')">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 删除确认模态框 -->
    <div id="deleteConfirmModal" class="modal">
        <div class="modal-content" style="max-width: 400px;">
            <div class="modal-header">
                <h3>确认删除</h3>
                <span class="close" onclick="closeModal('deleteConfirmModal')">&times;</span>
            </div>
            <div class="modal-body">
                <div class="alert alert-warning">
                    <strong>警告!</strong> 您确定要删除这个离线终端吗？此操作不可恢复。
                </div>
                <div style="text-align: center; margin-top: 20px;">
                    <button class="btn btn-danger" onclick="confirmDelete()">确认删除</button>
                    <button class="btn btn-secondary" onclick="closeModal('deleteConfirmModal')">取消</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentDeleteDeviceId = null;

        // 页面切换
        document.querySelectorAll('.nav-item').forEach(item => {
            item.addEventListener('click', function(e) {
                e.preventDefault();
                showPage(this.dataset.page);
            });
        });

        function showPage(pageId) {
            // 移除所有活跃状态
            document.querySelectorAll('.nav-item').forEach(nav => nav.classList.remove('active'));
            document.querySelectorAll('.page').forEach(page => page.classList.remove('active'));
            
            // 添加活跃状态
            if (pageId === 'terminals' || pageId === 'overview' || pageId === 'alerts') {
                document.querySelector(`[data-page="${pageId}"]`).classList.add('active');
            }
            document.getElementById(pageId).classList.add('active');
        }

        // 模态框函数
        function openModal(modalId) {
            document.getElementById(modalId).style.display = 'block';
        }

        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }

        // 点击模态框外部关闭
        window.onclick = function(event) {
            if (event.target.classList.contains('modal')) {
                event.target.style.display = 'none';
            }
        }

        // 终端管理相关函数
        function viewRealTimeMetrics(deviceId) {
            document.getElementById('currentDeviceId').textContent = deviceId;
            showPage('realTimeMetrics');
        }

        function viewRealTimeMetricsFromAlert(deviceId) {
            document.getElementById('currentDeviceId').textContent = deviceId;
            showPage('realTimeMetrics');
        }

        function viewHistoryMetrics(deviceId) {
            document.getElementById('historyDeviceId').textContent = deviceId;
            showPage('historyMetrics');
        }

        function viewDetails(deviceId) {
            // 模拟获取group_usage数据
            const mockGroupUsage = {
                group_id: 105,
                pps_total: 0.0,
                bps_total: 0.0,
                buckets: [
                    { bucket: 0, conn: 0, pps: 0.0, bps: 0.0 },
                    { bucket: 1, conn: 0, pps: 0.0, bps: 0.0 },
                    { bucket: 2, conn: 0, pps: 0.0, bps: 0.0 },
                    { bucket: 3, conn: 0, pps: 0.0, bps: 0.0 },
                    { bucket: 4, conn: 0, pps: 0.0, bps: 0.0 }
                ]
            };
            
            // 渲染CPE拓扑图
            renderCPETopology(mockGroupUsage);
            
            openModal('terminalDetailModal');
        }

        function editTerminal(deviceId) {
            openModal('editTerminalModal');
        }

        function deleteTerminal(deviceId) {
            currentDeleteDeviceId = deviceId;
            openModal('deleteConfirmModal');
        }

        function confirmDelete() {
            if (currentDeleteDeviceId) {
                alert('终端 ' + currentDeleteDeviceId + ' 已删除');
                closeModal('deleteConfirmModal');
                currentDeleteDeviceId = null;
                // 这里可以添加实际的删除逻辑
            }
        }

        // 模拟终端数据
        const mockTerminalData = [
            {
                deviceId: '_0fd94938951f4a64bb11c6817a81f7e7',
                hostname: 'ec_3568_25030031',
                mac: 'de:07:65:52:3e:60',
                status: 1,
                tags: ['生产', '高', '运维部'],
                lastUpdate: '2024-07-14 18:30:45'
            },
            {
                deviceId: 'T002',
                hostname: '主机名002',
                mac: 'AA:BB:CC:DD:EE:02',
                status: 0,
                tags: ['测试'],
                lastUpdate: '2024-07-14 18:28:15'
            },
            {
                deviceId: 'T003',
                hostname: '主机名003',
                mac: 'AA:BB:CC:DD:EE:03',
                status: 1,
                tags: ['生产'],
                lastUpdate: '2024-07-14 18:25:42'
            },
            {
                deviceId: 'T004',
                hostname: '主机名004',
                mac: 'AA:BB:CC:DD:EE:04',
                status: 1,
                tags: ['生产', '中', '开发部'],
                lastUpdate: '2024-07-14 18:22:30'
            },
            {
                deviceId: 'T005',
                hostname: '主机名005',
                mac: 'AA:BB:CC:DD:EE:05',
                status: 0,
                tags: ['测试', '低'],
                lastUpdate: '2024-07-14 18:20:15'
            }
        ];

        // 模拟告警数据
        const mockAlertData = [
            {
                alertId: 'A001',
                deviceId: '_0fd94938951f4a64bb11c6817a81f7e7',
                alertType: 'CPU_TEMPERATURE',
                alertTypeDisplay: 'CPU温度',
                alertDetails: 'CPU温度过高',
                currentValue: '85.9°C',
                threshold: '80°C',
                alertStatus: 'ACTIVE',
                alertStatusDisplay: '活跃',
                alertTime: '2024-07-14 18:25:30',
                notificationStatus: '已发送',
                notificationTime: '2024-07-14 18:25:35',
                acknowledgedBy: '未确认',
                resolvedTime: '未解决'
            },
            {
                alertId: 'A002',
                deviceId: 'T002',
                alertType: 'MEMORY_USAGE',
                alertTypeDisplay: '内存使用率',
                alertDetails: '内存使用率过高',
                currentValue: '92%',
                threshold: '90%',
                alertStatus: 'RESOLVED',
                alertStatusDisplay: '已解决',
                alertTime: '2024-07-14 18:20:15',
                notificationStatus: '已发送',
                notificationTime: '2024-07-14 18:20:20',
                acknowledgedBy: '运维人员',
                resolvedTime: '2024-07-14 18:35:10'
            },
            {
                alertId: 'A003',
                deviceId: 'T003',
                alertType: 'DISK_USAGE',
                alertTypeDisplay: '磁盘使用率',
                alertDetails: '系统盘使用率过高',
                currentValue: '88%',
                threshold: '85%',
                alertStatus: 'ACKNOWLEDGED',
                alertStatusDisplay: '已确认',
                alertTime: '2024-07-14 18:18:45',
                notificationStatus: '已发送',
                notificationTime: '2024-07-14 18:18:50',
                acknowledgedBy: '运维人员',
                resolvedTime: '未解决'
            },
            {
                alertId: 'A004',
                deviceId: '_0fd94938951f4a64bb11c6817a81f7e7',
                alertType: 'LICENSE_EXPIRY',
                alertTypeDisplay: '许可证过期',
                alertDetails: '软件许可证即将过期',
                currentValue: '30天',
                threshold: '90天',
                alertStatus: 'ACKNOWLEDGED',
                alertStatusDisplay: '已确认',
                alertTime: '2024-07-14 18:15:20',
                notificationStatus: '已发送',
                notificationTime: '2024-07-14 18:15:25',
                acknowledgedBy: '运维人员',
                resolvedTime: '未解决'
            },
            {
                alertId: 'A005',
                deviceId: 'T005',
                alertType: 'CPU_TEMPERATURE',
                alertTypeDisplay: 'CPU温度',
                alertDetails: 'CPU温度异常',
                currentValue: '78.5°C',
                threshold: '80°C',
                alertStatus: 'RESOLVED',
                alertStatusDisplay: '已解决',
                alertTime: '2024-07-14 18:10:30',
                notificationStatus: '已发送',
                notificationTime: '2024-07-14 18:10:35',
                acknowledgedBy: '运维人员',
                resolvedTime: '2024-07-14 18:25:10'
            }
        ];

        // 渲染终端列表
        function renderTerminalList(data = mockTerminalData) {
            const tbody = document.querySelector('#terminals .table tbody');
            tbody.innerHTML = '';
            
            data.forEach(terminal => {
                const row = document.createElement('tr');
                
                // 状态显示
                const statusClass = terminal.status === 1 ? 'status-online' : 'status-offline';
                const statusText = terminal.status === 1 ? '在线' : '离线';
                
                // 标签显示
                const tagBadges = terminal.tags.map(tag => {
                    let badgeClass = 'badge-info';
                    if (tag === '生产') badgeClass = 'badge-success';
                    if (tag === '高') badgeClass = 'badge-danger';
                    if (tag === '中') badgeClass = 'badge-warning';
                    if (tag === '测试') badgeClass = 'badge-success';
                    return `<span class="badge ${badgeClass}">${tag}</span>`;
                }).join(' ');
                
                // 操作按钮
                let actionButtons = `
                    <button class="btn btn-success btn-sm" onclick="viewRealTimeMetrics('${terminal.deviceId}')">实时指标</button>
                    <button class="btn btn-info btn-sm" onclick="viewHistoryMetrics('${terminal.deviceId}')">历史指标</button>
                    <button class="btn btn-primary btn-sm" onclick="viewDetails('${terminal.deviceId}')">详情</button>
                    <button class="btn btn-warning btn-sm" onclick="editTerminal('${terminal.deviceId}')">编辑</button>
                `;
                
                // 离线设备添加删除按钮
                if (terminal.status === 0) {
                    actionButtons += `<button class="btn btn-danger btn-sm" onclick="deleteTerminal('${terminal.deviceId}')">删除</button>`;
                }
                
                row.innerHTML = `
                    <td>${terminal.deviceId}</td>
                    <td>${terminal.hostname}</td>
                    <td>${terminal.mac}</td>
                    <td><span class="${statusClass}">${statusText}</span></td>
                    <td>${tagBadges}</td>
                    <td>${terminal.lastUpdate}</td>
                    <td>
                        <div class="action-buttons">
                            ${actionButtons}
                        </div>
                    </td>
                `;
                
                tbody.appendChild(row);
            });
        }

        // 筛选终端数据
        function filterTerminals() {
            const deviceIdFilter = document.querySelector('#terminals .search-filters input[placeholder="输入设备ID"]').value.toLowerCase();
            const hostnameFilter = document.querySelector('#terminals .search-filters input[placeholder="输入主机名"]').value.toLowerCase();
            const statusFilter = document.querySelector('#terminals .search-filters select').value;
            const tagFilter = document.querySelector('#terminals .search-filters input[placeholder="输入标签"]').value.toLowerCase();
            
            const filteredData = mockTerminalData.filter(terminal => {
                // 设备ID筛选
                if (deviceIdFilter && !terminal.deviceId.toLowerCase().includes(deviceIdFilter)) {
                    return false;
                }
                
                // 主机名筛选
                if (hostnameFilter && !terminal.hostname.toLowerCase().includes(hostnameFilter)) {
                    return false;
                }
                
                // 状态筛选
                if (statusFilter && terminal.status.toString() !== statusFilter) {
                    return false;
                }
                
                // 标签筛选
                if (tagFilter && !terminal.tags.some(tag => tag.toLowerCase().includes(tagFilter))) {
                    return false;
                }
                
                return true;
            });
            
            renderTerminalList(filteredData);
        }

        // 重置筛选
        function resetTerminalFilters() {
            document.querySelector('#terminals .search-filters input[placeholder="输入设备ID"]').value = '';
            document.querySelector('#terminals .search-filters input[placeholder="输入主机名"]').value = '';
            document.querySelector('#terminals .search-filters select').value = '';
            document.querySelector('#terminals .search-filters input[placeholder="输入标签"]').value = '';
            renderTerminalList();
        }

        // 绑定筛选事件
        function bindTerminalFilterEvents() {
            const searchButton = document.querySelector('#terminals .search-filters .btn-primary');
            const resetButton = document.querySelector('#terminals .search-filters .btn-secondary');
            
            if (searchButton) {
                searchButton.onclick = filterTerminals;
            }
            
            if (resetButton) {
                resetButton.onclick = resetTerminalFilters;
            }
            
            // 绑定回车键搜索
            const inputs = document.querySelectorAll('#terminals .search-filters input');
            inputs.forEach(input => {
                input.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        filterTerminals();
                    }
                });
            });
        }

        // 实时指标相关函数
        function refreshRealTimeMetrics() {
            // 模拟数据刷新
            const now = new Date();
            const timeString = now.getFullYear() + '-' + 
                             String(now.getMonth() + 1).padStart(2, '0') + '-' + 
                             String(now.getDate()).padStart(2, '0') + ' ' + 
                             String(now.getHours()).padStart(2, '0') + ':' + 
                             String(now.getMinutes()).padStart(2, '0') + ':' + 
                             String(now.getSeconds()).padStart(2, '0');
            
            document.getElementById('lastUpdateTime').textContent = timeString;
            
            // 模拟数据更新
            const newCpuTemp = (85 + Math.random() * 5).toFixed(1);
            const newCpuUsage = (40 + Math.random() * 10).toFixed(1);
            const newMemoryUsage = (48 + Math.random() * 8).toFixed(1);
            const newDiskUsage = (54 + Math.random() * 2).toFixed(1);
            
            document.getElementById('cpuTemp').textContent = newCpuTemp + '°C';
            document.getElementById('cpuUsage').textContent = newCpuUsage + '%';
            document.getElementById('memoryUsage').textContent = newMemoryUsage + '%';
            document.getElementById('diskUsage').textContent = newDiskUsage + '%';
            
            // 更新进度条
            const progressBars = document.querySelectorAll('.progress-fill');
            progressBars[0].style.width = newCpuUsage + '%';
            progressBars[1].style.width = newMemoryUsage + '%';
            progressBars[2].style.width = newDiskUsage + '%';
            
            alert('数据已刷新！');
        }

        // 历史指标相关函数
        function searchHistoryMetrics() {
            const startTime = document.getElementById('startTime').value;
            const endTime = document.getElementById('endTime').value;
            
            if (!startTime || !endTime) {
                alert('请选择开始时间和结束时间');
                return;
            }
            
            if (new Date(startTime) > new Date(endTime)) {
                alert('开始时间不能大于结束时间');
                return;
            }
            
            alert('查询时间范围: ' + startTime + ' 至 ' + endTime);
            // 这里可以添加实际的查询逻辑
        }

        function resetHistoryFilters() {
            document.getElementById('startTime').value = '2024-07-14T10:00';
            document.getElementById('endTime').value = '2024-07-14T20:00';
        }

        function viewHistoryDetail(time) {
            // 复用实时指标页面显示历史详情
            document.getElementById('currentDeviceId').textContent = document.getElementById('historyDeviceId').textContent;
            document.getElementById('lastUpdateTime').textContent = time;
            showPage('realTimeMetrics');
        }

        // 告警相关函数
        function viewAlertDetails(alertId) {
            // 从模拟数据中获取详细信息
            const alertData = mockAlertData.find(alert => alert.alertId === alertId);
            
            if (alertData) {
                // 填充模态框数据
                document.getElementById('alertId').textContent = alertData.alertId;
                document.getElementById('deviceId').textContent = alertData.deviceId;
                document.getElementById('alertType').textContent = alertData.alertTypeDisplay;
                document.getElementById('alertDetails').textContent = alertData.alertDetails;
                document.getElementById('currentValue').textContent = alertData.currentValue;
                document.getElementById('threshold').textContent = alertData.threshold;
                document.getElementById('alertTime').textContent = alertData.alertTime;
                
                // 状态徽章
                let badgeClass = 'badge-info';
                if (alertData.alertStatus === 'ACTIVE') badgeClass = 'badge-danger';
                if (alertData.alertStatus === 'RESOLVED') badgeClass = 'badge-success';
                if (alertData.alertStatus === 'ACKNOWLEDGED') badgeClass = 'badge-warning';
                
                document.getElementById('alertStatus').innerHTML = `<span class="badge ${badgeClass}">${alertData.alertStatusDisplay}</span>`;
                document.getElementById('notificationStatus').textContent = alertData.notificationStatus;
                document.getElementById('notificationTime').textContent = alertData.notificationTime;
                document.getElementById('acknowledgedBy').textContent = alertData.acknowledgedBy;
                document.getElementById('resolvedTime').textContent = alertData.resolvedTime;
            }
            
            openModal('alertDetailModal');
        }

        function sendDingTalkAlert(alertId) {
            alert('钉钉提醒已发送！告警ID: ' + alertId);
        }

        // 渲染告警列表
        function renderAlertList(data = mockAlertData) {
            const tbody = document.querySelector('#alerts .table tbody');
            if (!tbody) return;
            
            tbody.innerHTML = '';
            
            data.forEach(alert => {
                const row = document.createElement('tr');
                
                // 状态徽章样式
                let badgeClass = 'badge-info';
                if (alert.alertStatus === 'ACTIVE') badgeClass = 'badge-danger';
                if (alert.alertStatus === 'RESOLVED') badgeClass = 'badge-success';
                if (alert.alertStatus === 'ACKNOWLEDGED') badgeClass = 'badge-warning';
                
                row.innerHTML = `
                    <td>${alert.alertId}</td>
                    <td>${alert.deviceId}</td>
                    <td>${alert.alertTypeDisplay}</td>
                    <td>${alert.alertDetails}</td>
                    <td>${alert.currentValue}</td>
                    <td>${alert.threshold}</td>
                    <td><span class="badge ${badgeClass}">${alert.alertStatusDisplay}</span></td>
                    <td>${alert.alertTime}</td>
                    <td>
                        <button class="btn btn-info btn-sm" onclick="viewAlertDetails('${alert.alertId}')">详情</button>
                        <button class="btn btn-warning btn-sm" onclick="viewRealTimeMetricsFromAlert('${alert.deviceId}')">实时指标</button>
                        <button class="btn btn-primary btn-sm" onclick="sendDingTalkAlert('${alert.alertId}')">钉钉提醒</button>
                    </td>
                `;
                
                tbody.appendChild(row);
            });
        }

        // 筛选告警数据
        function filterAlerts() {
            const deviceIdFilter = document.querySelector('#alerts .search-filters input[placeholder="输入设备ID"]').value.toLowerCase();
            const alertTypeFilter = document.querySelector('#alerts .search-filters select').value;
            const alertStatusFilter = document.querySelectorAll('#alerts .search-filters select')[1].value;
            
            const filteredData = mockAlertData.filter(alert => {
                // 设备ID筛选
                if (deviceIdFilter && !alert.deviceId.toLowerCase().includes(deviceIdFilter)) {
                    return false;
                }
                
                // 告警类型筛选
                if (alertTypeFilter && alert.alertType !== alertTypeFilter) {
                    return false;
                }
                
                // 告警状态筛选
                if (alertStatusFilter && alert.alertStatus !== alertStatusFilter) {
                    return false;
                }
                
                return true;
            });
            
            renderAlertList(filteredData);
        }

        // 重置告警筛选
        function resetAlertFilters() {
            document.querySelector('#alerts .search-filters input[placeholder="输入设备ID"]').value = '';
            document.querySelector('#alerts .search-filters select').value = '';
            document.querySelectorAll('#alerts .search-filters select')[1].value = '';
            renderAlertList();
        }

        // 绑定告警筛选事件
        function bindAlertFilterEvents() {
            const searchButton = document.querySelector('#alerts .search-filters .btn-primary');
            const resetButton = document.querySelector('#alerts .search-filters .btn-secondary');
            
            if (searchButton) {
                searchButton.onclick = filterAlerts;
            }
            
            if (resetButton) {
                resetButton.onclick = resetAlertFilters;
            }
            
            // 绑定回车键搜索
            const inputs = document.querySelectorAll('#alerts .search-filters input');
            inputs.forEach(input => {
                input.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        filterAlerts();
                    }
                });
            });
        }

        // 标签管理
        function addTag(event) {
            if (event.key === 'Enter' && event.target.value.trim()) {
                const tagInput = document.getElementById('tagInput');
                const newTag = document.createElement('div');
                newTag.className = 'tag';
                newTag.innerHTML = `${event.target.value.trim()} <span class="tag-remove" onclick="removeTag(this)">×</span>`;
                tagInput.insertBefore(newTag, event.target);
                event.target.value = '';
            }
        }

        function removeTag(element) {
            element.parentElement.remove();
        }

        // 自定义字段管理
        function addCustomField() {
            const customFields = document.getElementById('customFields');
            const newField = document.createElement('div');
            newField.style.cssText = 'display: flex; gap: 10px; margin-bottom: 10px; align-items: center;';
            newField.innerHTML = `
                <input type="text" class="form-control" placeholder="字段名">
                <input type="text" class="form-control" placeholder="字段值">
                <button class="btn btn-danger btn-sm" onclick="removeCustomField(this)">删除</button>
            `;
            customFields.appendChild(newField);
        }

        function removeCustomField(element) {
            element.parentElement.remove();
        }

        // 存储日志项目的定时器
        const logTimers = new Map();
        
        // 模拟终端状态变化和上下线通知
        function simulateTerminalStatusChange() {
            const terminals = [
                { id: 'T006', hostname: '主机名006', type: 'online' },
                { id: 'T007', hostname: '主机名007', type: 'offline' },
                { id: 'T008', hostname: '主机名008', type: 'online' },
                { id: '_0fd94938951f4a64bb11c6817a81f7e7', hostname: 'ec_3568_25030031', type: 'online' },
                { id: 'T010', hostname: '主机名010', type: 'offline' }
            ];
            
            const randomTerminal = terminals[Math.floor(Math.random() * terminals.length)];
            addLogItem(randomTerminal.id, randomTerminal.hostname, randomTerminal.type);
        }

        // 添加日志项目
        function addLogItem(deviceId, hostname, type) {
            const logContainer = document.getElementById('logContainer');
            const emptyMessage = document.getElementById('emptyLogMessage');
            
            // 隐藏空消息
            if (emptyMessage) {
                emptyMessage.style.display = 'none';
            }
            
            const now = new Date();
            const timeString = now.getFullYear() + '-' + 
                             String(now.getMonth() + 1).padStart(2, '0') + '-' + 
                             String(now.getDate()).padStart(2, '0') + ' ' + 
                             String(now.getHours()).padStart(2, '0') + ':' + 
                             String(now.getMinutes()).padStart(2, '0') + ':' + 
                             String(now.getSeconds()).padStart(2, '0');
            
            const logItem = document.createElement('div');
            const logId = 'log_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
            logItem.id = logId;
            logItem.className = `log-item ${type}`;
            
            const statusText = type === 'online' ? '上线' : '离线';
            
            logItem.innerHTML = `
                <div class="log-content">
                    <strong>${timeString}</strong> - 终端 [${deviceId}-${hostname}] ${statusText}
                </div>
                <div class="log-actions">
                    <button class="btn btn-primary btn-sm" onclick="viewTerminalDetailFromOverview('${deviceId}')">查看详情</button>
                </div>
            `;
            
            logContainer.insertBefore(logItem, logContainer.firstChild);
            
            // 设置10秒后开始淡出效果
            const fadeTimer = setTimeout(() => {
                logItem.classList.add('fade-out');
            }, 8000);
            
            // 设置10秒后删除
            const removeTimer = setTimeout(() => {
                if (logItem.parentNode) {
                    logItem.parentNode.removeChild(logItem);
                }
                logTimers.delete(logId);
                
                // 如果没有日志项目了，显示空消息
                if (logContainer.children.length === 0 || 
                    (logContainer.children.length === 1 && logContainer.children[0].id === 'emptyLogMessage')) {
                    if (emptyMessage) {
                        emptyMessage.style.display = 'block';
                    }
                }
            }, 10000);
            
            // 存储定时器引用
            logTimers.set(logId, { fadeTimer, removeTimer });
            
            // 保持最多10条日志
            const logItems = logContainer.querySelectorAll('.log-item');
            if (logItems.length > 10) {
                const oldestItem = logItems[logItems.length - 1];
                const oldestId = oldestItem.id;
                
                // 清除旧的定时器
                if (logTimers.has(oldestId)) {
                    const timers = logTimers.get(oldestId);
                    clearTimeout(timers.fadeTimer);
                    clearTimeout(timers.removeTimer);
                    logTimers.delete(oldestId);
                }
                
                oldestItem.remove();
            }
        }

        // 概览页面专用的页面跳转函数
        function viewTerminalDetailFromOverview(deviceId) {
            // 在集成页面中，直接切换到终端管理页面并搜索
            showPage('terminals');
            // 设置搜索参数并执行搜索
            setTimeout(() => {
                const deviceIdInput = document.querySelector('#terminals .search-filters input[placeholder="输入设备ID"]');
                if (deviceIdInput) {
                    deviceIdInput.value = deviceId;
                    filterTerminals();
                    // 高亮显示搜索结果
                    highlightTerminalSearchResult(deviceId);
                }
            }, 100);
        }

        function viewAlertDetailFromOverview(alertId) {
            // 在集成页面中，直接切换到告警页面并显示详情
            showPage('alerts');
            setTimeout(() => {
                viewAlertDetails(alertId);
            }, 100);
        }

        function viewTerminalMetricsFromOverview(deviceId) {
            // 在集成页面中，直接切换到实时指标页面
            document.getElementById('currentDeviceId').textContent = deviceId;
            showPage('realTimeMetrics');
        }

        // 高亮显示终端搜索结果
        function highlightTerminalSearchResult(searchTerm) {
            setTimeout(() => {
                const rows = document.querySelectorAll('#terminals .table tbody tr');
                rows.forEach(row => {
                    const deviceIdCell = row.cells[0];
                    if (deviceIdCell && deviceIdCell.textContent.includes(searchTerm)) {
                        row.style.backgroundColor = '#fff3cd';
                        row.style.border = '2px solid #ffc107';
                        // 滚动到该行
                        row.scrollIntoView({ behavior: 'smooth', block: 'center' });
                        // 3秒后移除高亮
                        setTimeout(() => {
                            row.style.backgroundColor = '';
                            row.style.border = '';
                        }, 3000);
                    }
                });
            }, 200);
        }

        // 更新统计数据
        function updateStats() {
            // 模拟统计数据更新
            const totalChange = Math.floor(Math.random() * 5) - 2; // -2 到 2 的变化
            const onlineChange = Math.floor(Math.random() * 10) - 5; // -5 到 5 的变化
            const alertChange = Math.floor(Math.random() * 3) - 1; // -1 到 1 的变化
            
            const totalElement = document.getElementById('totalTerminals');
            const onlineElement = document.getElementById('onlineTerminals');
            const offlineElement = document.getElementById('offlineTerminals');
            const alertElement = document.getElementById('alertCount');
            
            let total = parseInt(totalElement.textContent.replace(',', '')) + totalChange;
            let online = parseInt(onlineElement.textContent.replace(',', '')) + onlineChange;
            let alerts = parseInt(alertElement.textContent) + alertChange;
            
            // 确保数据合理性
            total = Math.max(1000, total);
            online = Math.max(900, Math.min(total, online));
            alerts = Math.max(0, alerts);
            
            const offline = total - online;
            
            totalElement.textContent = total.toLocaleString();
            onlineElement.textContent = online.toLocaleString();
            offlineElement.textContent = offline.toLocaleString();
            alertElement.textContent = alerts.toString();
        }

        // 初始化概览页面
        function initOverviewPage() {
            // 每15秒模拟一次终端状态变化
            setInterval(simulateTerminalStatusChange, 15000);
            
            // 每30秒更新一次统计数据
            setInterval(updateStats, 30000);
            
            // 初始化时显示空消息
            const logContainer = document.getElementById('logContainer');
            const emptyMessage = document.getElementById('emptyLogMessage');
            if (logContainer && emptyMessage && logContainer.children.length === 1) {
                emptyMessage.style.display = 'block';
            }
        }

        // 页面卸载时清理定时器
        function cleanupOverviewTimers() {
            logTimers.forEach((timers) => {
                clearTimeout(timers.fadeTimer);
                clearTimeout(timers.removeTimer);
            });
            logTimers.clear();
        }

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化终端列表
            renderTerminalList();
            // 绑定筛选事件
            bindTerminalFilterEvents();
            // 初始化告警列表
            renderAlertList();
            // 绑定告警筛选事件
            bindAlertFilterEvents();
            // 初始化概览页面
            initOverviewPage();
        });

        // 页面卸载时清理资源
        window.addEventListener('beforeunload', function() {
            cleanupOverviewTimers();
        });

        // 渲染CPE拓扑图
        function renderCPETopology(groupUsage) {
            const container = document.getElementById('cpeContainer');
            if (!groupUsage || !groupUsage.buckets || groupUsage.buckets.length === 0) {
                container.innerHTML = '<div class="no-cpe-message">暂无CPE设备信息</div>';
                return;
            }

            container.innerHTML = '';
            
            groupUsage.buckets.forEach((bucket, index) => {
                const cpeItem = document.createElement('div');
                const isActive = bucket.conn > 0 || bucket.pps > 0 || bucket.bps > 0;
                
                cpeItem.className = `cpe-item ${isActive ? 'active' : 'inactive'}`;
                cpeItem.innerHTML = `
                    <div class="cpe-status ${isActive ? 'online' : 'offline'}"></div>
                    <div class="cpe-title">CPE</div>
                    <div class="cpe-bucket">Bucket ${bucket.bucket}</div>
                    <div class="cpe-stats">
                        连接: ${bucket.conn}<br>
                        PPS: ${bucket.pps.toFixed(1)}<br>
                        BPS: ${bucket.bps.toFixed(1)}
                    </div>
                `;
                
                container.appendChild(cpeItem);
            });
            
            // 更新统计信息
            updateTopologySummary(groupUsage);
        }

        // 更新拓扑统计信息
        function updateTopologySummary(groupUsage) {
            const summaryItems = document.querySelectorAll('.summary-item .summary-value');
            if (summaryItems.length >= 4) {
                summaryItems[0].textContent = groupUsage.group_id || 'N/A';
                summaryItems[1].textContent = groupUsage.buckets ? groupUsage.buckets.length : 0;
                summaryItems[2].textContent = groupUsage.pps_total ? groupUsage.pps_total.toFixed(1) : '0.0';
                summaryItems[3].textContent = groupUsage.bps_total ? formatBytes(groupUsage.bps_total) + '/s' : '0 B/s';
            }
        }

        // 格式化字节数
        function formatBytes(bytes) {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
        }
    </script>
</body>
</html> 