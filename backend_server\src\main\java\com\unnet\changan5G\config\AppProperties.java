package com.unnet.changan5G.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@ConfigurationProperties(prefix = "app")
@Data
public class AppProperties {

    private Api api = new Api();
    private Security security = new Security();

    @Data
    public static class Api {
        private List<ApiKey> keys;

        @Data
        public static class ApiKey {
            private String key;
            private String clientId;
        }
    }
    
    @Data
    public static class Security {
        private ReplayProtection replayProtection = new ReplayProtection();
        
        @Data
        public static class ReplayProtection {
            private boolean enabled = true; // 默认启用防重放保护
            private int timestampToleranceSeconds = 300; // 默认5分钟
            private int nonceCacheMinutes = 10; // 默认10分钟
        }
    }
} 