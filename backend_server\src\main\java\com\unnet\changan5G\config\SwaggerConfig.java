package com.unnet.changan5G.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.Components;
import io.swagger.v3.oas.models.security.SecurityScheme;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import io.swagger.v3.oas.models.parameters.Parameter;
import org.springdoc.core.GroupedOpenApi;
import org.springdoc.core.customizers.OperationCustomizer;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Swagger文档配置
 * 通过springdoc.api-docs.enabled和springdoc.swagger-ui.enabled控制开关
 */
@Configuration
@ConditionalOnProperty(name = "springdoc.api-docs.enabled", havingValue = "true", matchIfMissing = true)
public class SwaggerConfig {
    
    @Value("${spring.application.name}")
    private String applicationName;
    
    @Value("${app.security.replay-protection.enabled:true}")
    private boolean replayProtectionEnabled;
    
    @Bean
    public OpenAPI customOpenAPI() {
        final String securitySchemeName = "ApiKey";
        final String nonceHeaderName = "X-Request-Nonce";
        final String timestampHeaderName = "X-Request-Timestamp";
        
        OpenAPI openAPI = new OpenAPI()
                .info(new Info()
                        .title(applicationName + " API文档")
                        .description("API接口文档")
                        .version("v1.0.0")
                        .contact(new Contact()
                                .name("开发团队")
                                .email("<EMAIL>")
                                .url("https://www.example.com"))
                        .license(new License()
                                .name("Apache 2.0")
                                .url("https://www.apache.org/licenses/LICENSE-2.0.html")));
        
        SecurityRequirement securityRequirement = new SecurityRequirement().addList(securitySchemeName);
        Components components = new Components()
                .addSecuritySchemes(securitySchemeName, 
                        new SecurityScheme()
                            .name("X-API-Key")
                            .type(SecurityScheme.Type.APIKEY)
                            .in(SecurityScheme.In.HEADER)
                            .description("API密钥认证"));
        
        // 如果启用防重放保护，添加相关请求头参数
        if (replayProtectionEnabled) {
            securityRequirement.addList(nonceHeaderName).addList(timestampHeaderName);
            
            components.addSecuritySchemes(nonceHeaderName,
                    new SecurityScheme()
                        .name(nonceHeaderName)
                        .type(SecurityScheme.Type.APIKEY)
                        .in(SecurityScheme.In.HEADER)
                        .description("防重放攻击随机数"))
                    .addSecuritySchemes(timestampHeaderName,
                    new SecurityScheme()
                        .name(timestampHeaderName)
                        .type(SecurityScheme.Type.APIKEY)
                        .in(SecurityScheme.In.HEADER)
                        .description("请求时间戳"));
        }
        
        return openAPI.addSecurityItem(securityRequirement).components(components);
    }
    
    @Bean
    public GroupedOpenApi allApi() {
        return GroupedOpenApi.builder()
                .group("all-apis")
                .pathsToMatch("/**")
                .build();
    }
    
    @Bean
    public GroupedOpenApi publicApi() {
        return GroupedOpenApi.builder()
                .group("public-api")
                .pathsToMatch("/api/**")
                .build();
    }
    
    @Bean
    public GroupedOpenApi adminApi() {
        return GroupedOpenApi.builder()
                .group("admin-api")
                .pathsToMatch("/api/admin/**")
                .build();
    }
    
    @Bean
    public GroupedOpenApi demoApi() {
        return GroupedOpenApi.builder()
                .group("demo-api")
                .pathsToMatch("/demo/**", "/api/demo/**")
                .build();
    }
} 