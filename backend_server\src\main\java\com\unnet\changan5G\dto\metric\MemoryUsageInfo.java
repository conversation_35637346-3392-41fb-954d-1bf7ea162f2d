package com.unnet.changan5G.dto.metric;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 内存使用信息
 * 
 * <AUTHOR>
 * @date 2024-07-14
 */
@Data
@Schema(description = "内存使用信息")
public class MemoryUsageInfo {

    @Schema(description = "总内存容量（字节）", example = "4085002240")
    private Long total;

    @Schema(description = "可用内存（字节）", example = "2006622208")
    private Long available;

    @Schema(description = "内存使用率（%）", example = "50.9")
    private Double percent;

    @Schema(description = "实际使用内存（字节，不含缓存）", example = "2024525824")
    private Long used;

    @Schema(description = "空闲内存（字节，未分配）", example = "1373265920")
    private Long free;

    @Schema(description = "活跃内存区域（字节）", example = "229298176")
    private Long active;

    @Schema(description = "非活跃内存区域（字节，可能可释放）", example = "2192863232")
    private Long inactive;

    @Schema(description = "内核缓冲区（字节）", example = "56995840")
    private Long buffers;

    @Schema(description = "文件系统缓存（字节）", example = "630214656")
    private Long cached;

    @Schema(description = "共享内存区域（字节）", example = "1032192")
    private Long shared;

    @Schema(description = "内核数据结构缓存（字节）", example = "136617984")
    private Long slab;
}
