package com.unnet.jmanul.system.service.dto.user;

import com.unnet.jmanul.common.entity.BaseEntity;
import com.unnet.jmanul.system.entity.User;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.BeanUtils;

import java.time.LocalDateTime;

@Getter
@Setter
public class UserGetResp extends BaseEntity {

    private Long id;

    private String username;

    private String name;

    private String accountSource;

    private Boolean enable;

    private Boolean locked;

    private LocalDateTime accountExpireDate;

    private LocalDateTime credentialExpireDate;

    public static UserGetResp fromUser(User user) {
        UserGetResp resp = new UserGetResp();
        BeanUtils.copyProperties(user, resp);
        return resp;
    }

}
