package com.unnet.jmanul.business.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 指标阈值配置实体类
 * 
 * <AUTHOR>
 * @since 2024-07-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("metric_threshold_config")
public class MetricThresholdConfig {

    /**
     * 配置ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 指标类型：CPU_TEMPERATURE, MEMORY_USAGE, DISK_USAGE, DISK_DATA_USAGE, DISK_SYSTEM_USAGE, LICENSE_EXPIRY
     */
    @TableField("metric_type")
    private String metricType;

    /**
     * 指标名称
     */
    @TableField("metric_name")
    private String metricName;

    /**
     * 指标描述
     */
    @TableField("metric_description")
    private String metricDescription;

    /**
     * JSON字段路径：支持嵌套路径，如：cpu_temp, memory_usage.percent, disk_usage[0].percent
     */
    @TableField("json_field_path")
    private String jsonFieldPath;

    /**
     * 阈值
     */
    @TableField("threshold_value")
    private BigDecimal thresholdValue;

    /**
     * 阈值单位：%, °C, days等
     */
    @TableField("threshold_unit")
    private String thresholdUnit;

    /**
     * 比较操作符：>=, >, <=, <, =
     */
    @TableField("comparison_operator")
    private String comparisonOperator;

    /**
     * 告警级别：LOW, MEDIUM, HIGH, CRITICAL
     */
    @TableField("alert_level")
    private String alertLevel;

    /**
     * 告警消息模板
     */
    @TableField("alert_message")
    private String alertMessage;

    /**
     * 是否启用：0-禁用，1-启用
     */
    @TableField("is_enabled")
    private Boolean isEnabled;

    /**
     * 排序
     */
    @TableField("sort_order")
    private Integer sortOrder;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    @TableField("create_by")
    private String createBy;

    /**
     * 更新人
     */
    @TableField("update_by")
    private String updateBy;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    // 移除指标类型枚举，改为直接使用数据库中的中文描述

    /**
     * 告警级别枚举
     */
    public enum AlertLevel {
        LOW("LOW", "低"),
        MEDIUM("MEDIUM", "中"),
        HIGH("HIGH", "高"),
        CRITICAL("CRITICAL", "严重");

        private final String code;
        private final String description;

        AlertLevel(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }
    }
}
