import { defineStore } from 'pinia'
import { terminalApi, alertApi, statsApi } from '@/api/services'

// 终端管理状态
export const useTerminalStore = defineStore('terminal', {
  state: () => ({
    terminals: [],
    currentTerminal: null,
    loading: false,
    total: 0,
    pagination: {
      page: 1,
      pageSize: 10
    },
    filters: {
      identityMac: '',
      hostname: '',
      status: '',
      tag: ''
    }
  }),

  actions: {
    // 获取终端列表
    async fetchTerminals(params = {}) {
      this.loading = true
      try {
        const response = await terminalApi.getTerminals({
          ...this.filters,
          ...this.pagination,
          ...params
        })

        // 处理终端列表中的JSON字符串字段
        // 兼容Mock数据和真实API的不同响应结构
        console.log('API Response:', response)

        let terminalList = []
        let total = 0

        // 检查响应数据结构
        if (response.data && response.data.list) {
          // Mock数据结构: { code, data: { list, total }, message }
          terminalList = response.data.list || []
          total = response.data.total || 0
        } else if (response.list) {
          // 真实API结构: { list, total, page, pageSize }
          terminalList = response.list || []
          total = response.total || 0
        } else {
          console.error('Invalid response structure:', response)
          this.terminals = []
          this.total = 0
          return
        }

        console.log('Terminal List:', terminalList)
        console.log('Total:', total)

        this.terminals = terminalList.map(terminal => {
          const processedTerminal = { ...terminal }

          // 解析tags字段
          if (typeof processedTerminal.tags === 'string') {
            try {
              processedTerminal.tags = JSON.parse(processedTerminal.tags)
            } catch (e) {
              console.warn('解析终端tags失败:', e)
              processedTerminal.tags = {}
            }
          }

          // 解析customFields字段
          if (typeof processedTerminal.customFields === 'string') {
            try {
              processedTerminal.customFields = JSON.parse(processedTerminal.customFields)
            } catch (e) {
              console.warn('解析终端customFields失败:', e)
              processedTerminal.customFields = {}
            }
          }

          // 解析appVersion字段
          if (typeof processedTerminal.appVersion === 'string') {
            try {
              processedTerminal.appVersion = JSON.parse(processedTerminal.appVersion)
            } catch (e) {
              console.warn('解析终端appVersion失败:', e)
              processedTerminal.appVersion = {}
            }
          }

          return processedTerminal
        })

        this.total = total
      } catch (error) {
        console.error('获取终端列表失败:', error)
        throw error
      } finally {
        this.loading = false
      }
    },

    // 获取终端详情
    async fetchTerminalDetail(identityMac) {
      this.loading = true
      try {
        const response = await terminalApi.getTerminalDetail(identityMac)

        // 处理JSON字符串字段
        const processedData = { ...response }

        // 解析tags字段
        if (typeof processedData.tags === 'string') {
          try {
            processedData.tags = JSON.parse(processedData.tags)
          } catch (e) {
            console.warn('解析终端详情tags失败:', e)
            processedData.tags = {}
          }
        }

        // 解析customFields字段
        if (typeof processedData.customFields === 'string') {
          try {
            processedData.customFields = JSON.parse(processedData.customFields)
          } catch (e) {
            console.warn('解析终端详情customFields失败:', e)
            processedData.customFields = {}
          }
        }

        // 解析appVersion字段
        if (typeof processedData.appVersion === 'string') {
          try {
            processedData.appVersion = JSON.parse(processedData.appVersion)
          } catch (e) {
            console.warn('解析终端详情appVersion失败:', e)
            processedData.appVersion = {}
          }
        }

        this.currentTerminal = processedData
        return processedData
      } catch (error) {
        console.error('获取终端详情失败:', error)
        throw error
      } finally {
        this.loading = false
      }
    },

    // 更新终端
    async updateTerminal(identityMac, data) {
      try {
        const response = await terminalApi.updateTerminal(identityMac, data)
        // 更新本地状态
        const index = this.terminals.findIndex(t => t.identityMac === identityMac)
        if (index !== -1) {
          this.terminals[index] = response
        }
        if (this.currentTerminal?.identityMac === identityMac) {
          this.currentTerminal = response
        }
        return response
      } catch (error) {
        console.error('更新终端失败:', error)
        throw error
      }
    },

    // 删除终端
    async deleteTerminal(identityMac) {
      try {
        await terminalApi.deleteTerminal(identityMac)
        // 从本地状态中移除
        this.terminals = this.terminals.filter(t => t.identityMac !== identityMac)
        this.total -= 1
      } catch (error) {
        console.error('删除终端失败:', error)
        throw error
      }
    },

    // 设置过滤条件
    setFilters(filters) {
      this.filters = { ...this.filters, ...filters }
    },

    // 设置分页
    setPagination(pagination) {
      this.pagination = { ...this.pagination, ...pagination }
    },

    // 重置过滤条件
    resetFilters() {
      this.filters = {
        identityMac: '',
        hostname: '',
        status: '',
        tag: ''
      }
    }
  }
})

// 告警管理状态
export const useAlertStore = defineStore('alert', {
  state: () => ({
    alerts: [],
    currentAlert: null,
    loading: false,
    total: 0,
    pagination: {
      page: 1,
      pageSize: 10
    },
    filters: {
      identityMac: '',
      alertType: '',
      alertStatus: '',
      alertLevel: ''
    }
  }),

  actions: {
    // 获取告警列表
    async fetchAlerts(params = {}) {
      this.loading = true
      try {
        const response = await alertApi.getAlerts({
          ...this.filters,
          ...this.pagination,
          ...params
        })
        // 兼容Mock数据和真实API的不同响应结构
        if (response.data && response.data.list) {
          // Mock数据结构
          this.alerts = response.data.list || []
          this.total = response.data.total || 0
        } else if (response.list) {
          // 真实API结构
          this.alerts = response.list || []
          this.total = response.total || 0
        } else {
          this.alerts = []
          this.total = 0
        }
      } catch (error) {
        console.error('获取告警列表失败:', error)
        throw error
      } finally {
        this.loading = false
      }
    },

    // 获取告警详情
    async fetchAlertDetail(id) {
      this.loading = true
      try {
        const response = await alertApi.getAlertDetail(id)
        this.currentAlert = response
        return response
      } catch (error) {
        console.error('获取告警详情失败:', error)
        throw error
      } finally {
        this.loading = false
      }
    },

    // 确认告警
    async acknowledgeAlert(id, acknowledgedBy) {
      try {
        const response = await alertApi.acknowledgeAlert(id, acknowledgedBy)
        // 更新本地状态
        const index = this.alerts.findIndex(a => a.id === id)
        if (index !== -1) {
          this.alerts[index] = response
        }
        if (this.currentAlert?.id === id) {
          this.currentAlert = response
        }
        return response
      } catch (error) {
        console.error('确认告警失败:', error)
        throw error
      }
    },

    // 解决告警
    async resolveAlert(id, resolvedBy) {
      try {
        const response = await alertApi.resolveAlert(id, resolvedBy)
        // 更新本地状态
        const index = this.alerts.findIndex(a => a.id === id)
        if (index !== -1) {
          this.alerts[index] = response
        }
        if (this.currentAlert?.id === id) {
          this.currentAlert = response
        }
        return response
      } catch (error) {
        console.error('解决告警失败:', error)
        throw error
      }
    },

    // 发送钉钉通知
    async sendDingTalkNotification(id) {
      try {
        await alertApi.sendDingTalkNotification(id)
        // 更新本地状态
        const index = this.alerts.findIndex(a => a.id === id)
        if (index !== -1) {
          this.alerts[index].notificationSent = true
        }
      } catch (error) {
        console.error('发送钉钉通知失败:', error)
        throw error
      }
    },

    // 设置过滤条件
    setFilters(filters) {
      this.filters = { ...this.filters, ...filters }
    },

    // 设置分页
    setPagination(pagination) {
      this.pagination = { ...this.pagination, ...pagination }
    },

    // 重置过滤条件
    resetFilters() {
      this.filters = {
        identityMac: '',
        alertType: '',
        alertStatus: '',
        alertLevel: ''
      }
    }
  }
})

// 概览统计状态
export const useStatsStore = defineStore('stats', {
  state: () => ({
    stats: {
      totalTerminals: 0,
      onlineTerminals: 0,
      offlineTerminals: 0,
      totalAlerts: 0,
      criticalAlerts: 0,
      warningAlerts: 0,
      resolvedAlerts: 0
    },
    logs: [],
    loading: false
  }),

  actions: {
    // 获取概览统计
    async fetchStats() {
      this.loading = true
      try {
        const response = await statsApi.getOverviewStats()
        this.stats = response
      } catch (error) {
        console.error('获取统计数据失败:', error)
        throw error
      } finally {
        this.loading = false
      }
    },

    // 获取概览初始化数据
    async fetchInitData(terminalLimit = 10, alertLimit = 10) {
      this.loading = true
      try {
        const response = await statsApi.getOverviewInitData(terminalLimit, alertLimit)
        return response
      } catch (error) {
        console.error('获取概览初始化数据失败:', error)
        throw error
      } finally {
        this.loading = false
      }
    },

    // 获取实时动态
    async fetchLogs() {
      try {
        const response = await statsApi.getRealTimeLogs()
        this.logs = response
      } catch (error) {
        console.error('获取实时动态失败:', error)
        throw error
      }
    }
  }
}) 