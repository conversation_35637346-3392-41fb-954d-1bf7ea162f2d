# ========================================
# 长安5G管理平台 - 数据收集服务开发环境配置
# ========================================

spring:
  # 数据库配置 - 开发环境（MySQL）
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      driver-class-name: com.mysql.cj.jdbc.Driver
      url: *************************************************************************************************************************************************************************************
      username: root
      password: ABcd12#$
      initial-size: 5                                       # 初始化大小
      min-idle: 10                                          # 最小连接数
      max-active: 20                                        # 最大连接数
      max-wait: 60000                                       # 获取连接时的最大等待时间
      min-evictable-idle-time-millis: 300000   # 配置一个连接在池中最小生存的时间
      validation-query: SELECT 1                 # 验证连接是否有效的查询语句
      test-while-idle: true                      # 打开空闲连接检测
      test-on-borrow: false                      # 申请连接时执行validationQuery检测连接是否有效
      test-on-return: false                      # 归还连接时执行validationQuery检测连接是否有效

  # Redis配置 - 开发环境
  redis:
    host: *************
    port: 30379
    database: 0
    password: password4redis
    timeout: 10s
    lettuce:
      pool:
        min-idle: 0
        max-idle: 8
        max-active: 8
        max-wait: -1ms

  # Kafka配置 - 开发环境（无认证）
  kafka:
    bootstrap-servers: *************:9092
    listener:
      ack-mode: manual_immediate
      missing-topics-fatal: false
    producer:
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.apache.kafka.common.serialization.StringSerializer
      acks: 1
      retries: 3
    consumer:
      group-id: changan5g-data-dev-consumer
      auto-offset-reset: earliest
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      enable-auto-commit: false

  # MinIO配置 - 开发环境
  minio:
    host: http://*************:9000
    url: ${spring.minio.host}/${spring.minio.bucket}/
    access-key: minioadmin
    secret-key: minioadmin
    bucket: log-files

  # 文件上传配置
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB
      file-size-threshold: 2KB
      location: ${java.io.tmpdir}
      resolve-lazily: false

  # Elasticsearch配置 - 开发环境
  elasticsearch:
    username: elastic
    password: 123456
    uris: *************:9200
    connection-timeout: 10s
    socket-timeout: 30s


# 开发环境配置
server:
  port: 8081
  servlet:
    context-path: /api
    encoding:
      charset: UTF-8
      force: true
      enabled: true
  # 支持HTTP和HTTPS
  forward-headers-strategy: native
  # Tomcat配置
  tomcat:
    uri-encoding: UTF-8
    max-connections: 8192
    threads:
      max: 200
      min-spare: 10

# 开发环境启用Swagger
springdoc:
  api-docs:
    enabled: true  # 开启API文档
    path: /v3/api-docs  # API文档路径
  swagger-ui:
    enabled: true  # 开启Swagger UI界面
    path: /swagger-ui.html  # Swagger UI路径
  packages-to-scan: com.unnet.changan5G.controller  # 要扫描的包
  paths-to-match: /**, /demo/**, /api/demo/**  # 要匹配的路径
  cache:
    disabled: true

# 日志配置 - 开发环境
logging:
  level:
    root: INFO
    com.unnet.changan5G: DEBUG  # 启用应用调试日志
    org.springframework.kafka: DEBUG
    org.elasticsearch: INFO
    org.springframework.data.redis: INFO
    org.springframework.web: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"

# 监控配置 - 开发环境
management:
  endpoints:
    web:
      exposure:
        include: "*"  # 开发环境暴露所有端点
  endpoint:
    health:
      show-details: always  # 显示详细健康信息

# 应用自定义配置 - 开发环境
app:
  # API密钥配置
  api:
    keys:
      - key: "api-SvardHNxjL87TMHbgS2JFDpUsyi1iq9e"
        clientId: "haiyun-ai-live"
      - key: "api-key-client2"
        clientId: "client2"
  security:
    # 防重放攻击配置
    replay-protection:
      enabled: false  # 开发环境禁用防重放保护
      timestamp-tolerance-seconds: 300
      nonce-cache-minutes: 10