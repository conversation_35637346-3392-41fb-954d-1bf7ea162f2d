package com.unnet.changan5G.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Web配置类 - 配置CORS等
 * 
 * <AUTHOR>
 * @date 2024-07-15
 */
@Configuration
public class WebConfig implements WebMvcConfigurer {

    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**")
                .allowedOriginPatterns("*")
                .allowedMethods("GET", "POST", "PUT", "DELETE", "OPTIONS")
                .allowedHeaders("*")
                .allowCredentials(false) // 修复：设置为false以避免CORS错误
                .exposedHeaders("*")
                .maxAge(3600);

        // 特别为SSE端点配置CORS（过滤器接收到的URI包含context-path）
        registry.addMapping("/api/device/notifications/sse")
                .allowedOriginPatterns("*")
                .allowedMethods("GET", "OPTIONS")
                .allowedHeaders("*")
                .allowCredentials(false) // SSE不需要凭据
                .exposedHeaders("*")
                .maxAge(3600);
    }
}
