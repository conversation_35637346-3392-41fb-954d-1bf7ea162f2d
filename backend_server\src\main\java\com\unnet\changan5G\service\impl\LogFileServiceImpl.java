package com.unnet.changan5G.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.unnet.changan5G.entity.LogFile;
import com.unnet.changan5G.mapper.LogFileMapper;
import com.unnet.changan5G.service.LogFileService;
import com.unnet.changan5G.service.MinioService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.UUID;

/**
 * 日志文件记录 服务实现类
 * 
 * <AUTHOR>
 * @since 2025-07-28
 */
@Slf4j
@Service
public class LogFileServiceImpl extends ServiceImpl<LogFileMapper, LogFile> implements LogFileService {

    @Autowired
    private LogFileMapper logFileMapper;

    @Autowired
    private MinioService minioService;

    @Override
    public LogFile uploadLogFile(String identityMac, MultipartFile file, String description) {
        try {
            // 验证文件
            if (file.isEmpty()) {
                throw new RuntimeException("上传文件不能为空");
            }

            // 获取文件名称
            String originalFilename = file.getOriginalFilename();
            
            // 生成文件扩展名（在外部定义，供后续使用）
            String fileExtension = "";
            if (StringUtils.hasText(originalFilename) && originalFilename.contains(".")) {
                fileExtension = originalFilename.substring(originalFilename.lastIndexOf("."));
            }

            // 判断文件名称是否存在
            LogFile existingFile = logFileMapper.selectByOriginalFilename(identityMac,originalFilename);
            boolean flag = true;
            String storagePath;
            String storedFilename;

            if (existingFile != null) {
                log.warn("文件已存在，覆盖前面上传的文件: identityMac={}, filename={}", identityMac, file.getOriginalFilename());
                flag = false;
                // 使用已存在文件的存储路径，确保覆盖到正确的位置
                storagePath = existingFile.getStoragePath();
                storedFilename = existingFile.getStoredFilename();
                log.info("使用已存在的存储路径进行覆盖: {}", storagePath);
            } else {
                // 生成存储文件名
                String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
                storedFilename = String.format("%s_%s_%s%s", identityMac,originalFilename, timestamp, fileExtension);

                // 构建存储路径
                storagePath = String.format("logs/%s/%s", identityMac, storedFilename);
                log.info("生成新的存储路径: {}", storagePath);
            }

            // 如果是第一次上传
            if(flag) {
                // 上传到MinIO
                boolean uploadSuccess = minioService.uploadFile(file, storagePath);
                if (!uploadSuccess) {
                    throw new RuntimeException("文件上传到MinIO失败");
                }

                // 保存文件记录到数据库
                existingFile = new LogFile();
                existingFile.setIdentityMac(identityMac);
                existingFile.setOriginalFilename(originalFilename);
                existingFile.setStoredFilename(storedFilename);
                existingFile.setFileSize(file.getSize());
                existingFile.setFileType(fileExtension);
                existingFile.setStoragePath(storagePath);
                existingFile.setUploadTime(LocalDateTime.now());
                existingFile.setDescription(description);
                existingFile.setStatus(0);

                boolean saveSuccess = this.save(existingFile);
                if (!saveSuccess) {
                    // 如果数据库保存失败，删除已上传的文件
                    minioService.deleteFile(storagePath);
                    throw new RuntimeException("保存文件记录到数据库失败");
                }
            }else {
                // 若是已经创建了则直接增量覆盖
                // 上传到MinIO 增量覆盖
                boolean uploadSuccess = minioService.uploadFile(file, storagePath);
                if (!uploadSuccess) {
                    throw new RuntimeException("文件上传到MinIO失败");
                }
                // 更新文件信息：上传时间、文件大小等
                existingFile.setUploadTime(LocalDateTime.now());
                existingFile.setFileSize(file.getSize()); // 更新文件大小，因为覆盖的文件大小可能不同
                boolean updateSuccess = this.updateById(existingFile);
                if (!updateSuccess) {
                    // 如果数据库保存失败，删除已上传的文件
                    minioService.deleteFile(storagePath);
                    throw new RuntimeException("更新文件记录到数据库失败");
                }
            }
            log.info("日志文件上传成功: identityMac={}, filename={}, storagePath={}",
                    identityMac, originalFilename, storagePath);
            return existingFile;

        } catch (Exception e) {
            log.error("上传日志文件失败: identityMac={}, filename={}", identityMac, file.getOriginalFilename(), e);
            throw new RuntimeException("上传日志文件失败: " + e.getMessage(), e);
        }
    }

    @Override
    public boolean isFileExists(String identityMac, String fileMd5) {
        LogFile existingFile = logFileMapper.selectByIdentityMacAndMd5(identityMac, fileMd5);
        return existingFile != null;
    }
}
