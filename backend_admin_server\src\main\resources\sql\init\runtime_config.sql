-- ----------------------------
-- Table structure for runtime_config
-- ----------------------------
DROP TABLE IF EXISTS `runtime_config`;
CREATE TABLE `runtime_config`  (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增ID',
    `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '配置名称（app.auth.keypair.publicKey）',
    `value` longtext COMMENT '配置值（0xffffabcd）',
    `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '描述（认证密钥对公钥）',
    `created_at` datetime(3) NULL DEFAULT NULL,
    `updated_at` datetime(3) NULL DEFAULT NULL,
    `deleted_at` bigint(20) UNSIGNED NULL DEFAULT 0,
    `created_by` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
    `updated_by` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `idx_runtime_config_name` (`name`)
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;
