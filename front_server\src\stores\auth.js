import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import api from '@/api'
import { authApi } from '@/api/services'
import { createRSAUtil } from '@/utils/crypto'

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const token = ref(localStorage.getItem('token') || '')
  const userInfo = ref(JSON.parse(localStorage.getItem('userInfo') || 'null'))
  const isLoggedIn = computed(() => !!token.value && !!userInfo.value)

  // 设置token
  const setToken = (newToken) => {
    token.value = newToken
    if (newToken) {
      localStorage.setItem('token', newToken)
      // 设置axios默认请求头
      api.defaults.headers.common['Authorization'] = `Bearer ${newToken}`
    } else {
      localStorage.removeItem('token')
      delete api.defaults.headers.common['Authorization']
    }
  }

  // 设置用户信息
  const setUserInfo = (info) => {
    userInfo.value = info
    if (info) {
      localStorage.setItem('userInfo', JSON.stringify(info))
    } else {
      localStorage.removeItem('userInfo')
    }
  }

  // 获取验证码
  const getCaptcha = async () => {
    try {
      const response = await authApi.getCaptcha()
      return response
    } catch (error) {
      console.error('获取验证码失败:', error)
      throw error
    }
  }

  // 获取公钥
  const getPublicKey = async () => {
    try {
      const response = await authApi.getPublicKey()
      return response
    } catch (error) {
      console.error('获取公钥失败:', error)
      throw error
    }
  }

  // 加密密码
  const encryptPassword = async (password) => {
    try {
      // 获取公钥
      const publicKey = await getPublicKey()
      if (!publicKey) {
        throw new Error('无法获取公钥')
      }

      // 创建RSA加密实例
      const rsaUtil = createRSAUtil()
      rsaUtil.setPublicKey(publicKey)

      // 加密密码
      const encryptedPassword = rsaUtil.encryptText(password)
      if (!encryptedPassword) {
        throw new Error('密码加密失败')
      }

      return encryptedPassword
    } catch (error) {
      console.error('密码加密失败:', error)
      throw error
    }
  }

  // 登录
  const login = async (credentials) => {
    try {
      // 加密密码
      const encryptedPassword = await encryptPassword(credentials.password)

      const response = await authApi.login({
        username: credentials.username,
        credential: encryptedPassword,
        captchaCode: credentials.captchaCode,
        captchaId: credentials.captchaId
      })

      if (response && response.token) {
        // 设置token
        setToken(response.token)

        // 获取用户信息
        await getUserInfo()

        return true
      }
      return false
    } catch (error) {
      console.error('登录失败:', error)
      console.log('错误响应:', error.response)
      console.log('响应头:', error.response?.headers)

      // 检查是否有特定的认证错误信息
      if (error.response && error.response.headers) {
        const authError = error.response.headers['x-auth-error']
        const authMessage = error.response.headers['x-auth-message']

        console.log('认证错误码:', authError)
        console.log('认证错误消息:', authMessage)

        if (authError && authMessage) {
          // 解码URL编码的中文消息
          let decodedMessage = authMessage
          try {
            decodedMessage = decodeURIComponent(authMessage)
            console.log('解码后的错误消息:', decodedMessage)
          } catch (e) {
            console.warn('Failed to decode auth message:', authMessage)
          }

          // 创建包含具体错误信息的错误对象
          const specificError = new Error(decodedMessage)
          specificError.code = authError
          throw specificError
        }
      }

      // 处理403错误（可能是CasbinFilter拦截）
      if (error.response && error.response.status === 403) {
        // 403错误通常表示认证失败，显示通用错误信息
        const forbiddenError = new Error('登录失败，请检查用户名和密码')
        forbiddenError.code = 'FORBIDDEN'
        throw forbiddenError
      }

      throw error
    }
  }

  // 获取用户信息
  const getUserInfo = async () => {
    try {
      const response = await authApi.getUserInfo()
      if (response) {
        setUserInfo(response)
        return response
      }
    } catch (error) {
      console.error('获取用户信息失败:', error)
      // 如果获取用户信息失败，清除token
      logout()
      throw error
    }
  }

  // 刷新token
  const refreshToken = async () => {
    try {
      const response = await authApi.refreshToken()
      if (response && response.token) {
        setToken(response.token)
        return true
      }
      return false
    } catch (error) {
      console.error('刷新token失败:', error)
      logout()
      return false
    }
  }

  // 登出
  const logout = async () => {
    try {
      // 调用后端登出接口
      await authApi.logout()
    } catch (error) {
      console.error('登出接口调用失败:', error)
    } finally {
      // 清除本地存储
      setToken('')
      setUserInfo(null)
    }
  }

  // 检查用户权限
  const hasPermission = (permission) => {
    if (!userInfo.value) return false
    
    // 管理员拥有所有权限
    if (userInfo.value.username === 'admin') {
      return true
    }
    
    // 这里可以根据实际的权限结构来判断
    // 目前简化处理：admin用户拥有所有权限，其他用户只有查看权限
    const readOnlyPermissions = ['read', 'view', 'get']
    const writePermissions = ['create', 'update', 'delete', 'post', 'put']
    
    if (readOnlyPermissions.some(p => permission.toLowerCase().includes(p))) {
      return true
    }
    
    if (writePermissions.some(p => permission.toLowerCase().includes(p))) {
      return userInfo.value.username === 'admin'
    }
    
    return false
  }

  // 检查是否为管理员
  const isAdmin = computed(() => {
    return userInfo.value && userInfo.value.username === 'cqcadevice'
  })

  // 初始化认证状态
  const initAuth = async () => {
    if (token.value) {
      // 设置axios请求头
      api.defaults.headers.common['Authorization'] = `Bearer ${token.value}`
      
      try {
        // 验证token有效性并获取用户信息
        await getUserInfo()
      } catch (error) {
        // token无效，清除认证信息
        logout()
      }
    }
  }

  return {
    // 状态
    token,
    userInfo,
    isLoggedIn,
    isAdmin,

    // 方法
    setToken,
    setUserInfo,
    getCaptcha,
    getPublicKey,
    encryptPassword,
    login,
    logout,
    getUserInfo,
    refreshToken,
    hasPermission,
    initAuth
  }
})
