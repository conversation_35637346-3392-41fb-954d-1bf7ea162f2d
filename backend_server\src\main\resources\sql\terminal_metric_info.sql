-- 终端指标信息表
CREATE TABLE `terminal_metric_info` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `device_id` varchar(100) NOT NULL COMMENT '设备唯一标识符',
  `uptime` decimal(10,2) DEFAULT NULL COMMENT '系统运行时长（单位：秒）',
  `cpu_temp` decimal(5,2) DEFAULT NULL COMMENT 'CPU温度（单位：摄氏度）',
  `temperatures` json DEFAULT NULL COMMENT '温度信息(JSON格式)',
  `cpu_usage` json DEFAULT NULL COMMENT 'CPU使用率信息(JSON格式)',
  `cpu_percent` decimal(5,2) DEFAULT NULL COMMENT 'CPU总使用率（%）',
  `memory_percent` decimal(5,2) DEFAULT NULL COMMENT '内存使用率（%）',
  `memory_usage` json DEFAULT NULL COMMENT '内存详细信息(JSON格式)',
  `disk_usage` json DEFAULT NULL COMMENT '磁盘使用情况数组(JSON格式)',
  `disk_data_percent` decimal(5,2) DEFAULT NULL COMMENT '采集数据磁盘分区使用率',
  `disk_system_percent` decimal(5,2) DEFAULT NULL COMMENT '系统盘使用率',
  `cdata` json DEFAULT NULL COMMENT '长安程序落盘文件信息(JSON格式)',
  `zdata` json DEFAULT NULL COMMENT '压缩文件信息(JSON格式)',
  `group_usage` json DEFAULT NULL COMMENT 'CPE集合信息(JSON格式)',
  `group_bps` bigint(20) DEFAULT NULL COMMENT '所有CPE的带宽占用（单位：Byte/s）',
  `metric_time` datetime DEFAULT NULL COMMENT '指标采集时间',
  `receive_time` datetime DEFAULT NULL COMMENT '数据接收时间',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_device_id` (`device_id`),
  KEY `idx_metric_time` (`metric_time`),
  KEY `idx_receive_time` (`receive_time`),
  KEY `idx_cpu_temp` (`cpu_temp`),
  KEY `idx_cpu_percent` (`cpu_percent`),
  KEY `idx_memory_percent` (`memory_percent`),
  KEY `idx_disk_data_percent` (`disk_data_percent`),
  KEY `idx_disk_system_percent` (`disk_system_percent`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='终端指标信息表';

-- 添加复合索引优化查询性能
CREATE INDEX `idx_device_metric_time` ON `terminal_metric_info` (`device_id`, `metric_time`);
CREATE INDEX `idx_device_create_time` ON `terminal_metric_info` (`device_id`, `create_time`);
