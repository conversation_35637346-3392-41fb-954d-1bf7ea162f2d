-- ----------------------------
-- Table structure for casbin_rule
-- ----------------------------
DROP TABLE IF EXISTS `casbin_rule`;
CREATE TABLE `casbin_rule`  (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `ptype` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
    `v0` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
    `v1` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
    `v2` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
    `v3` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
    `v4` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
    `v5` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 32 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of casbin_rule
-- ----------------------------
INSERT INTO `casbin_rule` VALUES (1, 'p', 'user', '/api/v1/account*', 'GET', '', '', '');
INSERT INTO `casbin_rule` VALUES (2, 'p', 'user', '/api/v1/account*', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (3, 'p', 'user', '/api/v1/account*', 'PUT', '', '', '');
INSERT INTO `casbin_rule` VALUES (4, 'p', 'user', '/api/v1/account*', 'DELETE', '', '', '');
INSERT INTO `casbin_rule` VALUES (5, 'p', 'admin', '/api/v1/account*', 'GET', NULL, NULL, NULL);
INSERT INTO `casbin_rule` VALUES (6, 'p', 'admin', '/api/v1/account*', 'POST', NULL, NULL, NULL);
INSERT INTO `casbin_rule` VALUES (7, 'p', 'admin', '/api/v1/account*', 'DELETE', NULL, NULL, NULL);
INSERT INTO `casbin_rule` VALUES (8, 'p', 'admin', '/api/v1/account*', 'PUT', NULL, NULL, NULL);
INSERT INTO `casbin_rule` VALUES (9, 'p', 'admin', '/api/v1/account*', 'DELETE', NULL, NULL, NULL);
INSERT INTO `casbin_rule` VALUES (10, 'p', 'admin', '/api/v1/account*', 'PUT', NULL, NULL, NULL);
INSERT INTO `casbin_rule` VALUES (11, 'p', 'admin', '/api/v1/admin/rbac*', 'GET', '', '', '');
INSERT INTO `casbin_rule` VALUES (12, 'p', 'admin', '/api/v1/admin/rbac*', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (13, 'p', 'admin', '/api/v1/admin/rbac*', 'PUT', '', '', '');
INSERT INTO `casbin_rule` VALUES (14, 'p', 'admin', '/api/v1/admin/rbac*', 'DELETE', '', '', '');
INSERT INTO `casbin_rule` VALUES (15, 'p', 'auditor', '/api/v1/admin/rbac*', 'GET', '', '', '');
INSERT INTO `casbin_rule` VALUES (16, 'p', 'admin', '/api/v1/admin/samples*', 'GET', '', '', '');
INSERT INTO `casbin_rule` VALUES (17, 'p', 'admin', '/api/v1/admin/samples*', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (18, 'p', 'admin', '/api/v1/admin/samples*', 'PUT', '', '', '');
INSERT INTO `casbin_rule` VALUES (19, 'p', 'admin', '/api/v1/admin/samples*', 'DELETE', '', '', '');
INSERT INTO `casbin_rule` VALUES (20, 'p', 'auditor', '/api/v1/admin/samples*', 'GET', '', '', '');
INSERT INTO `casbin_rule` VALUES (21, 'p', 'auditor', '/api/v1/admin/samples*', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (22, 'p', 'auditor', '/api/v1/admin/samples*', 'PUT', '', '', '');
INSERT INTO `casbin_rule` VALUES (23, 'p', 'auditor', '/api/v1/admin/samples*', 'DELETE', '', '', '');
INSERT INTO `casbin_rule` VALUES (24, 'p', 'admin', '/api/v1/admin/users*', 'GET', '', '', '');
INSERT INTO `casbin_rule` VALUES (25, 'p', 'admin', '/api/v1/admin/users*', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (26, 'p', 'admin', '/api/v1/admin/users*', 'PUT', '', '', '');
INSERT INTO `casbin_rule` VALUES (27, 'p', 'admin', '/api/v1/admin/users*', 'DELETE', '', '', '');
INSERT INTO `casbin_rule` VALUES (28, 'p', 'auditor', '/api/v1/admin/users*', 'GET', '', '', '');
INSERT INTO `casbin_rule` VALUES (29, 'p', 'auditor', '/api/v1/auth/refreshToken', 'GET', NULL, NULL, NULL);
INSERT INTO `casbin_rule` VALUES (30, 'p', 'admin', '/api/v1/auth/refreshToken', 'GET', NULL, NULL, NULL);
INSERT INTO `casbin_rule` VALUES (31, 'p', 'user', '/api/v1/auth/refreshToken', 'GET', NULL, NULL, NULL);
