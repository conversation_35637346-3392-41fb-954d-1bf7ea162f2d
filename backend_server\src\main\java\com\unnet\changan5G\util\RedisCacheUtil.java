package com.unnet.changan5G.util;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.data.redis.cache.RedisCache;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * Redis缓存工具类 - 提供缓存统计和管理功能
 * 
 * <AUTHOR>
 * @date 2024-07-24
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class RedisCacheUtil {

    private final CacheManager cacheManager;
    private final RedisTemplate<String, Object> redisTemplate;

    /**
     * 获取所有缓存名称
     */
    public Collection<String> getCacheNames() {
        return cacheManager.getCacheNames();
    }

    /**
     * 获取指定缓存
     */
    public Cache getCache(String cacheName) {
        return cacheManager.getCache(cacheName);
    }

    /**
     * 清空指定缓存
     */
    public void evictCache(String cacheName) {
        Cache cache = cacheManager.getCache(cacheName);
        if (cache != null) {
            cache.clear();
            log.info("已清空缓存: {}", cacheName);
        }
    }

    /**
     * 清空所有缓存
     */
    public void evictAllCaches() {
        cacheManager.getCacheNames().forEach(this::evictCache);
        log.info("已清空所有缓存");
    }

    /**
     * 获取缓存统计信息
     */
    public Map<String, Object> getCacheStatistics() {
        Map<String, Object> stats = new HashMap<>();
        
        try {
            // 获取所有缓存名称
            Collection<String> cacheNames = getCacheNames();
            stats.put("totalCaches", cacheNames.size());
            stats.put("cacheNames", cacheNames);
            
            // 获取Redis连接信息
            Map<String, Object> redisInfo = new HashMap<>();
            
            // 统计各个缓存的key数量
            Map<String, Long> cacheKeyCounts = new HashMap<>();
            for (String cacheName : cacheNames) {
                try {
                    // Redis缓存的key模式通常是 cacheName::*
                    Set<String> keys = redisTemplate.keys(cacheName + "::*");
                    cacheKeyCounts.put(cacheName, keys != null ? (long) keys.size() : 0L);
                } catch (Exception e) {
                    log.warn("获取缓存{}的key数量失败: {}", cacheName, e.getMessage());
                    cacheKeyCounts.put(cacheName, -1L);
                }
            }
            
            redisInfo.put("cacheKeyCounts", cacheKeyCounts);
            stats.put("redisInfo", redisInfo);
            
        } catch (Exception e) {
            log.error("获取缓存统计信息失败: {}", e.getMessage(), e);
            stats.put("error", e.getMessage());
        }
        
        return stats;
    }

    /**
     * 检查缓存是否存在指定key
     */
    public boolean hasKey(String cacheName, String key) {
        Cache cache = getCache(cacheName);
        if (cache != null) {
            return cache.get(key) != null;
        }
        return false;
    }

    /**
     * 获取缓存中的值
     */
    public Object getCacheValue(String cacheName, String key) {
        Cache cache = getCache(cacheName);
        if (cache != null) {
            Cache.ValueWrapper wrapper = cache.get(key);
            return wrapper != null ? wrapper.get() : null;
        }
        return null;
    }

    /**
     * 手动设置缓存值
     */
    public void putCacheValue(String cacheName, String key, Object value) {
        Cache cache = getCache(cacheName);
        if (cache != null) {
            cache.put(key, value);
            log.debug("手动设置缓存值 - 缓存: {}, Key: {}", cacheName, key);
        }
    }

    /**
     * 删除缓存中的指定key
     */
    public void evictCacheKey(String cacheName, String key) {
        Cache cache = getCache(cacheName);
        if (cache != null) {
            cache.evict(key);
            log.debug("删除缓存key - 缓存: {}, Key: {}", cacheName, key);
        }
    }

    /**
     * 获取Redis缓存的原生操作对象
     */
    public RedisCache getRedisCache(String cacheName) {
        Cache cache = getCache(cacheName);
        if (cache instanceof RedisCache) {
            return (RedisCache) cache;
        }
        return null;
    }
}