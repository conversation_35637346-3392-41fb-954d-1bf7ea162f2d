package com.unnet.jmanul.business.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.unnet.jmanul.business.entity.LogFile;

import java.util.List;

/**
 * 日志文件记录 服务类
 * 
 * <AUTHOR>
 * @since 2025-07-28
 */
public interface LogFileService extends IService<LogFile> {

    /**
     * 根据终端MAC地址分页查询日志文件
     * 
     * @param page 分页参数
     * @param identityMac 终端MAC地址
     * @return 分页结果
     */
    IPage<LogFile> getLogFilesByIdentityMac(Page<LogFile> page, String identityMac);

    /**
     * 根据终端MAC地址查询日志文件列表
     * 
     * @param identityMac 终端MAC地址
     * @return 日志文件列表
     */
    List<LogFile> getLogFilesByIdentityMac(String identityMac);

    /**
     * 下载日志文件
     * 
     * @param fileId 文件ID
     * @return 文件下载URL
     */
    String getDownloadUrl(Long fileId);

    /**
     * 删除日志文件
     * 
     * @param fileId 文件ID
     * @return 删除结果
     */
    boolean deleteLogFile(Long fileId);

    /**
     * 检查文件是否已存在（基于MD5）
     * 
     * @param identityMac 终端MAC地址
     * @param fileMd5 文件MD5值
     * @return 是否存在
     */
    boolean isFileExists(String identityMac, String fileMd5);
}
