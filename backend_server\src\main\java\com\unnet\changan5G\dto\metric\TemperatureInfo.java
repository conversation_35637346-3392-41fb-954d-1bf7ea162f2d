package com.unnet.changan5G.dto.metric;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 温度信息
 * 
 * <AUTHOR>
 * @date 2024-07-14
 */
@Data
@Schema(description = "温度信息")
public class TemperatureInfo {

    @Schema(description = "SoC芯片温度（摄氏度）", example = "58.888")
    @JsonProperty("soc-thermal")
    private Double socThermal;

    @Schema(description = "GPU温度（摄氏度）", example = "57.777")
    @JsonProperty("gpu-thermal")
    private Double gpuThermal;
}
