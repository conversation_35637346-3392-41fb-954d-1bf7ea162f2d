package com.unnet.changan5G.controller;

import com.unnet.changan5G.dto.ApiResp;
import com.unnet.changan5G.dto.UserRequest;
import com.unnet.changan5G.exception.BusinessException;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;

import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/demo")
@Slf4j
@Tag(name = "示例接口", description = "用于演示基本API功能的接口")
public class DemoController {

    /**
     * 简单的GET请求示例
     */
    @GetMapping("/hello")
    @Operation(summary = "问候接口", description = "返回一个简单的问候信息")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "操作成功")
    })
    public ApiResp<String> hello() {
        return ApiResp.success("Hello, Jmanul API!");
    }
    
    /**
     * 带路径参数的GET请求示例
     */
    @GetMapping("/hello/{name}")
    @Operation(summary = "带名称的问候接口", description = "根据路径参数返回个性化问候信息")
    public ApiResp<String> helloWithName(
            @Parameter(name = "name", description = "用户名称", required = true) 
            @PathVariable String name) {
        return ApiResp.success("Hello, " + name + "!");
    }
    
    /**
     * 带查询参数的GET请求示例
     */
    @GetMapping("/greeting")
    @Operation(summary = "带参数的问候接口", description = "使用查询参数返回个性化问候信息")
    public ApiResp<Map<String, Object>> greeting(
            @Parameter(name = "name", description = "用户名称") 
            @RequestParam(defaultValue = "Guest") String name,
            @Parameter(name = "times", description = "访问次数") 
            @RequestParam(defaultValue = "0") Integer times) {
        
        Map<String, Object> result = new HashMap<>();
        result.put("message", "Welcome, " + name + "!");
        result.put("visitTimes", times);
        
        return ApiResp.success(result);
    }
    
    /**
     * 带请求体和参数校验的POST请求示例
     */
    @PostMapping("/users")
    @Operation(summary = "创建用户", description = "接收用户信息并进行创建")
    public ApiResp<UserRequest> createUser(
            @Parameter(name = "userRequest", description = "用户信息", required = true) 
            @Valid @RequestBody UserRequest userRequest) {
        // 模拟业务逻辑处理
        log.info("创建用户: {}", userRequest);
        
        // 返回创建的用户信息
        return ApiResp.success("用户创建成功", userRequest);
    }
    
    /**
     * 演示业务异常处理的示例
     */
    @GetMapping("/error-demo")
    @Operation(summary = "异常处理演示", description = "演示不同类型异常的处理方式")
    public ApiResp<Object> errorDemo(
            @Parameter(name = "type", description = "异常类型：0-无异常，1-业务异常，2-404异常，3-运行时异常") 
            @RequestParam(defaultValue = "0") Integer type) {
        switch (type) {
            case 1:
                // 抛出默认的业务异常 (400 Bad Request)
                throw new BusinessException("业务异常示例");
            case 2:
                // 抛出指定状态码的业务异常
                throw new BusinessException(HttpStatus.NOT_FOUND, "资源未找到异常示例");
            case 3:
                // 抛出运行时异常 (全局异常处理将捕获并处理)
                throw new RuntimeException("未处理的运行时异常示例");
            default:
                // 正常返回
                return ApiResp.success("没有错误发生");
        }
    }
    
    /**
     * 访问客户端信息示例 (API Key认证后的clientId)
     */
    @GetMapping("/client-info")
    @Operation(summary = "获取客户端信息", description = "返回当前请求的客户端相关信息")
    public ApiResp<Map<String, Object>> getClientInfo(HttpServletRequest request) {
        String clientId = (String) request.getAttribute("clientId");
        
        Map<String, Object> clientInfo = new HashMap<>();
        clientInfo.put("clientId", clientId);
        clientInfo.put("remoteAddr", request.getRemoteAddr());
        clientInfo.put("userAgent", request.getHeader("User-Agent"));
        
        return ApiResp.success(clientInfo);
    }
} 