<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CORS测试页面</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .result { margin: 10px 0; padding: 10px; border: 1px solid #ccc; background: #f9f9f9; }
        .success { border-color: #4CAF50; background: #e8f5e9; }
        .error { border-color: #f44336; background: #ffebee; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; }
        textarea { width: 100%; height: 200px; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>CORS跨域测试页面</h1>
        
        <div>
            <label for="apiUrl">API地址:</label>
            <input type="text" id="apiUrl" value="http://changan5g.un-net.com:8888/api/agent/report" style="width: 500px;">
        </div>
        
        <div>
            <label for="apiKey">API Key:</label>
            <input type="text" id="apiKey" value="api-SvardHNxjL87TMHbgS2JFDpUsyi1iq9e" style="width: 400px;">
        </div>
        
        <div>
            <button onclick="testCors()">测试CORS请求</button>
            <button onclick="clearResults()">清空结果</button>
        </div>
        
        <div>
            <h3>请求数据:</h3>
            <textarea id="requestData">{
  "hostname": "ec_3568_25030031",
  "device_id": "_0fd94938951f4a64bb11c6817a81f7e7",
  "identity_mac": "de0765523e60",
  "uptime": 5916.29,
  "cpu_temp": 58.888,
  "temperatures": {
    "soc-thermal": 58.888,
    "gpu-thermal": 57.777
  },
  "cpu_usage": {
    "num": "4",
    "core": "4",
    "thread": "4",
    "user": "46.3",
    "sys": "9.7",
    "idle": "43.7"
  },
  "cpu_percent": 43.2,
  "memory_percent": 50.9,
  "memory_usage": {
    "total": 4085002240,
    "available": 2006622208,
    "percent": 50.9,
    "used": 2024525824,
    "free": 1373265920,
    "active": 229298176,
    "inactive": 2192863232,
    "buffers": 56995840,
    "cached": 630214656,
    "shared": 1032192,
    "slab": 136617984
  },
  "disk_usage": [
    {
      "device": "/dev/mmcblk0p3",
      "mountpoint": "/",
      "fstype": "ext4",
      "total": 30602608640,
      "used": 15916904448,
      "free": 13409980416,
      "percent": 54.3
    }
  ],
  "disk_data_percent": 0,
  "disk_system_percent": 54.3,
  "cdata": {
    "count": 1,
    "size": 3627078580
  },
  "zdata": {
    "count": 0,
    "size": 0
  },
  "group_usage": {},
  "group_bps": 0,
  "app_version": {
    "controller": "1.0.1",
    "detector": "1.0.48",
    "web": "1.0.55",
    "agent": "1.0.0",
    "front": "1.0.40"
  },
  "expired_date": "2035-03-09 22:47:20"
}</textarea>
        </div>
        
        <div id="results"></div>
    </div>

    <script>
        function addResult(message, isSuccess = true) {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = 'result ' + (isSuccess ? 'success' : 'error');
            div.innerHTML = '<strong>' + new Date().toLocaleTimeString() + '</strong>: ' + message;
            results.appendChild(div);
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        async function testCors() {
            const apiUrl = document.getElementById('apiUrl').value;
            const apiKey = document.getElementById('apiKey').value;
            const requestData = document.getElementById('requestData').value;

            addResult('开始测试CORS请求...');

            try {
                const response = await fetch(apiUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-API-Key': apiKey,
                        'Accept': '*/*'
                    },
                    body: requestData
                });

                if (response.ok) {
                    const result = await response.text();
                    addResult('请求成功! 状态码: ' + response.status + ', 响应: ' + result, true);
                } else {
                    addResult('请求失败! 状态码: ' + response.status + ', 状态文本: ' + response.statusText, false);
                }
            } catch (error) {
                addResult('请求异常: ' + error.message, false);
                console.error('CORS测试错误:', error);
            }
        }
    </script>
</body>
</html>
