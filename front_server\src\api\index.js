import axios from 'axios'
import { ElMessage } from 'element-plus'

// 创建管理服务API实例 - backend_admin_server (8080端口)
const adminApi = axios.create({
  baseURL: import.meta.env.VITE_ADMIN_API_BASE_URL || 'http://localhost:8080',
  timeout: 30000, // 增加到30秒
  headers: {
    'Content-Type': 'application/json'
  }
})

// 创建数据服务API实例 - backend_server (8081端口)
const dataApi = axios.create({
  baseURL: import.meta.env.VITE_DATA_API_BASE_URL || 'http://localhost:8081',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 管理服务请求拦截器 - 用于认证授权、终端管理等
adminApi.interceptors.request.use(
  config => {
    // 从localStorage获取token
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }

    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// 数据服务请求拦截器 - 用于SSE推送、实时数据等
dataApi.interceptors.request.use(
  config => {
    // 对于指标上传接口，添加API Key
    if (config.url && config.url.includes('/agent/report')) {
      config.headers['X-API-KEY'] = import.meta.env.VITE_API_KEY || 'api-SvardHNxjL87TMHbgS2JFDpUsyi1iq9e'
    }

    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// 通用响应处理函数
const handleResponse = (response) => {
  return response.data
}

const handleError = (error, serviceName) => {
  console.error(`${serviceName} API Error:`, error)

  // 处理认证错误（只对管理服务处理认证）
  if (error.response) {
    const { status, data, headers } = error.response

    switch (status) {
      case 401:
        // 未授权，检查是否有具体的认证错误信息
        if (serviceName === '管理服务') {
          const authError = headers['x-auth-error']
          const authMessage = headers['x-auth-message']

          if (authError && authMessage) {
            // 如果是登录相关的认证错误，不清除token，只显示错误信息
            if (authError === 'USER_NOT_FOUND' || authError === 'PASSWORD_INCORRECT' || authError === 'CAPTCHA_INVALID') {
              // 不做任何处理，让上层处理具体的错误信息
              break
            }
          }

          // 其他401错误，清除token并跳转到登录页
          localStorage.removeItem('token')
          localStorage.removeItem('userInfo')
          if (window.location.pathname !== '/login') {
            ElMessage.error('登录已过期，请重新登录')
            window.location.href = '/login'
          }
        } else {
          ElMessage.error('数据服务认证失败')
        }
        break
      case 403:
        // 403错误可能是登录失败，检查是否在登录页面
        if (serviceName === '管理服务' && window.location.pathname === '/login') {
          // 在登录页面的403错误，不显示通用错误信息，让上层处理
          break
        } else {
          ElMessage.error('没有权限访问该资源')
        }
        break
      case 500:
        ElMessage.error(`${serviceName}内部错误`)
        break
      default:
        ElMessage.error(data?.message || `${serviceName}请求失败`)
    }
  } else {
    ElMessage.error('网络错误，请检查网络连接')
  }

  return Promise.reject(error)
}

// 管理服务响应拦截器
adminApi.interceptors.response.use(
  response => {
    console.log('管理服务响应成功:', response)
    return handleResponse(response)
  },
  error => {
    console.log('管理服务响应错误:', error)
    console.log('错误响应头:', error.response?.headers)
    return handleError(error, '管理服务')
  }
)

// 数据服务响应拦截器
dataApi.interceptors.response.use(
  handleResponse,
  error => handleError(error, '数据服务')
)

// 导出两个API实例
export { adminApi, dataApi }
export default adminApi // 默认导出管理服务API