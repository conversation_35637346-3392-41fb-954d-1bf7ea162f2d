# ========================================
# 长安5G管理平台 - 生产环境配置
# ========================================

# 生产环境禁用Swagger文档
springfox:
  documentation:
    enabled: false
    auto-startup: false
    swagger-ui:
      enabled: false

springdoc:
  api-docs:
    enabled: false
  swagger-ui:
    enabled: false

# 生产环境配置
server:
  port: 8080

spring:
  # 数据库配置 - 支持环境变量覆盖
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: jdbc:${DB_TYPE:mysql}://${DB_HOST:********}:${DB_PORT:3306}/${DB_NAME:changan_5g}?useUnicode=true&characterEncoding=utf-8&useSSL=false&serverTimezone=Asia/Shanghai&allowPublicKeyRetrieval=true
    username: ${DB_USERNAME:root}
    password: ${DB_PASSWORD:ABcd12#$}
    hikari:
      maximum-pool-size: ${DB_POOL_MAX_SIZE:20}
      minimum-idle: ${DB_POOL_MIN_IDLE:5}
      connection-timeout: ${DB_CONNECTION_TIMEOUT:30000}
      idle-timeout: ${DB_IDLE_TIMEOUT:600000}
      max-lifetime: ${DB_MAX_LIFETIME:1800000}
      leak-detection-threshold: ${DB_LEAK_DETECTION:60000}

  # Redis集群配置 - 主从模式
  redis:
    host: ${REDIS_HOST:********}
    port: ${REDIS_PORT:7000}
    database: ${REDIS_DB:0}
    password: ${REDIS_PASSWORD:Changan5g.redis}
    timeout: ${REDIS_TIMEOUT:5000ms}

    # 连接池配置
    lettuce:
      pool:
        max-active: ${REDIS_POOL_MAX_ACTIVE:20}
        max-idle: ${REDIS_POOL_MAX_IDLE:10}
        min-idle: ${REDIS_POOL_MIN_IDLE:5}
        max-wait: ${REDIS_POOL_MAX_WAIT:5000ms}
      cluster:
        refresh:
          adaptive: true
          period: 30s

    # Redis集群节点配置（如果使用集群模式，取消注释）
    cluster:
      nodes:
        - ********:7000
        - ********:7001
        - ********:7000
        - ********:7001
        - ********:7000
        - ********:7001
      max-redirects: 3

  # Elasticsearch集群配置
  elasticsearch:
    uris:
      - ********:9200
      - ********:9200
      - ********:9200
    username: ${ES_USERNAME:elastic}
    password: ${ES_PASSWORD:Changan5g.es}
    connection-timeout: ${ES_CONNECTION_TIMEOUT:10s}
    read-timeout: ${ES_SOCKET_TIMEOUT:30s}
    # 集群相关配置
    cluster-name: ${ES_CLUSTER_NAME:changan5g-cluster}

  # MinIO集群配置 - 生产环境
  minio:
    # MinIO集群节点配置（负载均衡）
    hosts:
      - http://********:9000
      - http://********:9000
      - http://********:9000
    # 主节点配置（用于兼容单节点配置的代码）
    host: ${MINIO_HOST:http://********:9000}
    url: ${MINIO_URL:${spring.minio.host}/${spring.minio.bucket}/}
    # 认证配置
    access-key: ${MINIO_ACCESS_KEY:admin}
    secret-key: ${MINIO_SECRET_KEY:Changan5g.minio}
    # 存储桶配置
    bucket: ${MINIO_BUCKET:log-files}
    # 集群配置
    cluster:
      enabled: ${MINIO_CLUSTER_ENABLED:true}
      retry-attempts: ${MINIO_CLUSTER_RETRY_ATTEMPTS:3}
      retry-delay-ms: ${MINIO_CLUSTER_RETRY_DELAY:1000}
      health-check-interval-seconds: ${MINIO_CLUSTER_HEALTH_CHECK_INTERVAL:30}


# 应用自定义配置
app:
  development: ${APP_DEVELOPMENT:false}
  jwt-secret: ${APP_JWT_SECRET:changan5g-prod-secret-2024}
  jwt-expire-seconds: ${APP_JWT_EXPIRE_SECONDS:86400}
  jwt-max-refresh-window-seconds: ${APP_JWT_MAX_REFRESH_WINDOW_SECONDS:86400}
  websocket-enabled: ${APP_WEBSOCKET_ENABLED:true}

# 日志配置
logging:
  level:
    root: ${LOG_LEVEL_ROOT:INFO}
    com.unnet.jmanul: ${LOG_LEVEL_APP:INFO}
    org.springframework.kafka: ${LOG_LEVEL_KAFKA:WARN}
    org.elasticsearch: ${LOG_LEVEL_ES:WARN}
    org.springframework.data.redis: ${LOG_LEVEL_REDIS:WARN}
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: ${LOG_FILE_PATH:./logs/changan5g-admin.log}
    max-size: ${LOG_FILE_MAX_SIZE:100MB}
    max-history: ${LOG_FILE_MAX_HISTORY:30}

# 监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: when-authorized
  metrics:
    export:
      prometheus:
        enabled: true
