package com.unnet.changan5G.dto.terminal;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 终端列表查询请求
 * 
 * <AUTHOR>
 * @date 2024-07-16
 */
@Data
@Schema(description = "终端列表查询请求")
public class TerminalListRequest {

    @Schema(description = "页码，从1开始", example = "1")
    private Integer page = 1;

    @Schema(description = "每页大小", example = "10")
    private Integer pageSize = 10;

    @Schema(description = "设备ID", example = "_0fd94938951f4a64bb11c6817a81f7e7")
    private String deviceId;

    @Schema(description = "主机名", example = "ec_3568_25030031")
    private String hostname;

    @Schema(description = "状态：0-离线，1-在线", example = "1")
    private Integer status;

    @Schema(description = "标签搜索", example = "北京")
    private String tag;
}
