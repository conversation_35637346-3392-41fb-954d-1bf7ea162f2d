# ========================================
# 长安5G管理平台 - 开发环境配置
# ========================================

# 开发环境启用Swagger文档
springfox:
  documentation:
    enabled: true
    auto-startup: true
    swagger-ui:
      enabled: true

springdoc:
  api-docs:
    enabled: true
  swagger-ui:
    enabled: true
    path: /swagger-ui.html

spring:
  # 数据库配置 - 开发环境
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    # 开发环境数据库配置
    url: *************************************************************************************************************************************************************************************
    username: root
    password: ABcd12#$
    hikari:
      maximum-pool-size: 10
      minimum-idle: 2
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000

  # Redis配置 - 开发环境
  redis:
    host: *************
    port: 30379
    database: 0
    password: password4redis
    timeout: 10s
    lettuce:
      pool:
        min-idle: 0
        max-idle: 8
        max-active: 8
        max-wait: -1ms

  # MinIO配置 - 开发环境
  minio:
    host: http://*************:9000
    url: ${spring.minio.host}/${spring.minio.bucket}/
    access-key: minioadmin
    secret-key: minioadmin
    bucket: log-files

  # 长安5G - Elasticsearch配置- 开发环境
  elasticsearch:
    username: elastic
    password: 123456
    uris: *************:9200

# 日志配置 - 开发环境
logging:
  level:
    root: INFO
    com.unnet.jmanul: DEBUG
    com.unnet.jmanul.business.mapper: DEBUG
    com.unnet.jmanul.system.mapper: DEBUG
    org.springframework.kafka: DEBUG
    org.elasticsearch: INFO
    org.springframework.data.redis: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"

# 应用配置 - 开发环境
app:
  development: false  # 修改为false，启用验证码校验
  jwt-secret: changan5g-dev-secret
  jwt-expire-seconds: 86400
  jwt-max-refresh-window-seconds: 86400
  websocket-enabled: true

# 监控配置 - 开发环境
management:
  endpoints:
    web:
      exposure:
        include: "*"
  endpoint:
    health:
      show-details: always
