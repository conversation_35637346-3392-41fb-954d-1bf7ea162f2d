-- ----------------------------
-- Table structure for user
-- ----------------------------
DROP TABLE IF EXISTS `user`;
CREATE TABLE `user`  (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `created_at` datetime(3) NULL DEFAULT NULL,
    `updated_at` datetime(3) NULL DEFAULT NULL,
    `deleted_at` bigint(20) UNSIGNED NULL DEFAULT 0,
    `username` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
    `name` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL,
    `password` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL,
    `account_source` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
    `enable` tinyint(1) NULL DEFAULT NULL,
    `locked` tinyint(1) NULL DEFAULT NULL,
    `account_expire_date` datetime(3) NULL DEFAULT NULL,
    `credential_expire_date` datetime(3) NULL DEFAULT NULL,
    `created_by` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
    `updated_by` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `idx_username_account_source_del`(`deleted_at`, `username`, `account_source`) USING BTREE,
    UNIQUE INDEX `idx_user_username`(`username`) USING BTREE,
    INDEX `idx_users_created_by`(`created_by`) USING BTREE,
    INDEX `idx_users_updated_by`(`updated_by`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;
