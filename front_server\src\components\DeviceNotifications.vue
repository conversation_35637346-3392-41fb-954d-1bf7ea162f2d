<template>
  <div class="device-notifications">

    <!-- 实时通知列表 -->
    <div class="notifications-container">
      <h3 class="notifications-title">终端上下线实时动态</h3>
      
      <div class="notifications-list" v-if="notifications.length > 0">
        <transition-group name="notification" tag="div">
          <div 
            v-for="notification in notifications" 
            :key="notification.id"
            class="notification-item"
            :class="notification.type.toLowerCase().replace('_', '-')"
          >
            <div class="notification-icon">
              <i :class="getNotificationIcon(notification.type)"></i>
            </div>
            <div class="notification-content">
              <div class="notification-message">{{ notification.message }}</div>

              <!-- 告警通知的额外信息 -->
              <div v-if="notification.type === 'DEVICE_ALERT'" class="alert-details">
                <div class="alert-level" :class="'level-' + (notification.alertLevel || 'medium').toLowerCase()">
                  {{ getAlertLevelText(notification.alertLevel) }}
                </div>
                <div class="alert-values" v-if="notification.currentValue && notification.threshold">
                  <span class="current-value">当前值: {{ notification.currentValue }}</span>
                  <span class="threshold">阈值: {{ notification.threshold }}</span>
                </div>
                <div class="alert-type" v-if="notification.alertType">
                  {{ getAlertTypeText(notification.alertType) }}
                </div>
              </div>

              <div class="notification-details">
                <span class="device-id" :title="'设备MAC地址: ' + notification.deviceId">{{ notification.deviceId }}</span>
                <span class="hostname" v-if="notification.hostname">{{ notification.hostname }}</span>
                <span class="timestamp">{{ notification.timestamp }}</span>
              </div>
            </div>
            <div class="notification-close" @click="removeNotification(notification.id)">
              <i class="el-icon-close"></i>
            </div>
          </div>
        </transition-group>
      </div>

      <div class="no-notifications" v-else>
        <i class="el-icon-info"></i>
        <span>暂无实时通知</span>
      </div>
    </div>
  </div>
</template>

<script>
import sseService from '@/services/sseService'

export default {
  name: 'DeviceNotifications',
  props: {
    showAlerts: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      isConnected: false,
      notifications: [],
      notificationIdCounter: 0,
      processedEvents: new Set(), // 用于去重的事件ID集合
      lastEventTime: 0 // 最后事件时间戳，用于去重
    }
  },
  computed: {
    connectionStatusText() {
      return this.isConnected ? '实时连接已建立' : '连接已断开'
    },
    sseEndpoint() {
      const baseUrl = import.meta.env.VITE_SSE_BASE_URL || ''
      if (baseUrl.startsWith('http')) {
        // 开发环境：使用完整URL
        return `${baseUrl}/api/device/notifications/sse`
      } else if (baseUrl) {
        // 生产环境：使用相对路径，通过nginx代理
        return `${baseUrl}/api/device/notifications/sse`
      } else {
        // 默认相对路径
        return '/api/device/notifications/sse'
      }
    }
  },
  mounted() {
    this.initSSE()
  },
  beforeUnmount() {
    this.cleanupSSE()
  },
  methods: {
    /**
     * 初始化SSE连接
     */
    initSSE() {
      console.log('=== 初始化SSE连接 ===')

      // 先清理可能存在的旧监听器
      this.cleanupSSE()

      // 监听连接状态
      sseService.on('connected', this.handleConnected)
      sseService.on('connection-opened', this.handleConnectionOpened)
      sseService.on('max-reconnect-reached', this.handleMaxReconnectReached)

      // 监听设备事件
      sseService.on('device-registered', this.handleDeviceRegistered)
      sseService.on('device-online', this.handleDeviceOnline)
      sseService.on('device-offline', this.handleDeviceOffline)

      // 只有在showAlerts为true时才监听告警事件
      if (this.showAlerts) {
        sseService.on('device-alert', this.handleDeviceAlert)
      }

      // 监听统计更新
      sseService.on('statistics-update', this.handleStatisticsUpdate)

      // 连接SSE
      console.log('开始连接SSE服务...')
      sseService.connect().then(() => {
        console.log('SSE连接Promise resolved')
      }).catch((error) => {
        console.error('SSE连接Promise rejected:', error)
        // 不在这里重连，让SSE服务自己处理重连逻辑
        console.log('初始连接失败，SSE服务将自动重连...')
      })
    },

    /**
     * 清理SSE连接
     */
    cleanupSSE() {
      console.log('=== 清理SSE事件监听器 ===')

      // 移除所有事件监听器
      sseService.off('connected', this.handleConnected)
      sseService.off('connection-opened', this.handleConnectionOpened)
      sseService.off('max-reconnect-reached', this.handleMaxReconnectReached)
      sseService.off('device-registered', this.handleDeviceRegistered)
      sseService.off('device-online', this.handleDeviceOnline)
      sseService.off('device-offline', this.handleDeviceOffline)

      // 只有在showAlerts为true时才移除告警事件监听器
      if (this.showAlerts) {
        sseService.off('device-alert', this.handleDeviceAlert)
      }

      sseService.off('statistics-update', this.handleStatisticsUpdate)

      // 断开SSE连接（引用计数管理）
      sseService.disconnect()
    },

    /**
     * 处理连接成功
     */
    handleConnected(data) {
      this.isConnected = true
      console.log('=== SSE连接成功事件 ===')
      console.log('连接数据:', data)
      console.log('连接地址:', this.sseEndpoint)
    },

    /**
     * 处理连接打开
     */
    handleConnectionOpened(data) {
      console.log('=== SSE连接打开事件 ===')
      console.log('连接信息:', data)
      this.isConnected = true
    },

    /**
     * 处理最大重连次数达到
     */
    handleMaxReconnectReached() {
      console.error('=== SSE达到最大重连次数 ===')
      this.isConnected = false
      // 可以在这里显示用户提示，或者尝试手动重连
      console.log('10秒后尝试重新初始化SSE连接...')
      setTimeout(() => {
        console.log('重新初始化SSE连接...')
        this.initSSE()
      }, 10000)
    },

    /**
     * 处理设备注册通知
     */
    handleDeviceRegistered(data) {
      this.addNotification({
        type: 'DEVICE_REGISTERED',
        deviceId: data.deviceId,
        hostname: data.hostname,
        message: data.message,
        timestamp: data.timestamp
      })

      // 触发父组件事件
      this.$emit('device-registered', data)
    },

    /**
     * 处理设备上线通知
     */
    handleDeviceOnline(data) {
      console.log('DeviceNotifications: 处理设备上线通知', data)

      // 生成事件唯一标识
      const eventId = `${data.type}_${data.deviceId}_${data.timestamp}`

      // 检查是否已处理过此事件
      if (this.processedEvents.has(eventId)) {
        console.log('DeviceNotifications: 重复事件已忽略', eventId)
        return
      }

      // 记录已处理的事件
      this.processedEvents.add(eventId)

      // 清理过期的事件ID（保留最近100个）
      if (this.processedEvents.size > 100) {
        const eventsArray = Array.from(this.processedEvents)
        this.processedEvents = new Set(eventsArray.slice(-50))
      }

      this.addNotification({
        type: 'DEVICE_ONLINE',
        deviceId: data.deviceId,
        hostname: data.hostname,
        message: data.message,
        timestamp: data.timestamp
      })

      // 触发父组件事件
      console.log('DeviceNotifications: 触发device-online事件', data)
      this.$emit('device-online', data)
    },

    /**
     * 处理设备离线通知
     */
    handleDeviceOffline(data) {
      console.log('DeviceNotifications: 处理设备离线通知', data)

      // 生成事件唯一标识
      const eventId = `${data.type}_${data.deviceId}_${data.timestamp}`

      // 检查是否已处理过此事件
      if (this.processedEvents.has(eventId)) {
        console.log('DeviceNotifications: 重复事件已忽略', eventId)
        return
      }

      // 记录已处理的事件
      this.processedEvents.add(eventId)

      // 清理过期的事件ID（保留最近100个）
      if (this.processedEvents.size > 100) {
        const eventsArray = Array.from(this.processedEvents)
        this.processedEvents = new Set(eventsArray.slice(-50))
      }

      this.addNotification({
        type: 'DEVICE_OFFLINE',
        deviceId: data.deviceId,
        hostname: data.hostname,
        message: data.message,
        timestamp: data.timestamp
      })

      // 触发父组件事件
      console.log('DeviceNotifications: 触发device-offline事件', data)
      this.$emit('device-offline', data)
    },

    /**
     * 处理设备告警通知
     */
    handleDeviceAlert(data) {
      console.log('DeviceNotifications: 处理设备告警通知', data)

      // 如果showAlerts为false，直接返回，不处理告警事件
      if (!this.showAlerts) {
        console.log('DeviceNotifications: showAlerts为false，跳过告警处理')
        return
      }

      // 生成事件唯一标识
      const eventId = `${data.type}_${data.deviceId}_${data.timestamp}`

      // 检查是否已处理过此事件
      if (this.processedEvents.has(eventId)) {
        console.log('DeviceNotifications: 重复事件已忽略', eventId)
        return
      }

      // 记录已处理的事件
      this.processedEvents.add(eventId)

      // 清理过期的事件ID（保留最近100个）
      if (this.processedEvents.size > 100) {
        const eventsArray = Array.from(this.processedEvents)
        this.processedEvents = new Set(eventsArray.slice(-50))
      }

      // 添加告警通知到显示列表
      this.addNotification({
        type: 'DEVICE_ALERT',
        deviceId: data.deviceId,
        hostname: data.hostname,
        alertType: data.alertType,
        alertLevel: data.alertLevel,
        message: data.message,
        details: data.details,
        currentValue: data.currentValue,
        threshold: data.threshold,
        timestamp: data.timestamp
      })

      // 触发父组件事件
      console.log('DeviceNotifications: 触发device-alert事件', data)
      this.$emit('device-alert', data)
    },

    /**
     * 处理统计信息更新
     */
    handleStatisticsUpdate(data) {
      // 触发父组件更新统计信息
      this.$emit('statistics-updated', {
        onlineCount: data.onlineCount,
        offlineCount: data.offlineCount,
        alertCount: data.alertCount,
        totalCount: data.totalCount
      })
    },

    /**
     * 添加通知
     */
    addNotification(notification) {
      const id = ++this.notificationIdCounter
      const notificationWithId = {
        ...notification,
        id
      }
      
      // 添加到列表开头
      this.notifications.unshift(notificationWithId)
      
      // 限制通知数量（最多显示10条）
      if (this.notifications.length > 10) {
        this.notifications = this.notifications.slice(0, 10)
      }
      
      // 10秒后自动移除
      setTimeout(() => {
        this.removeNotification(id)
      }, 10000)
    },

    /**
     * 移除通知
     */
    removeNotification(id) {
      const index = this.notifications.findIndex(n => n.id === id)
      if (index > -1) {
        this.notifications.splice(index, 1)
      }
    },

    /**
     * 获取通知图标
     */
    getNotificationIcon(type) {
      switch (type) {
        case 'DEVICE_REGISTERED':
          return 'el-icon-plus'
        case 'DEVICE_ONLINE':
          return 'el-icon-success'
        case 'DEVICE_OFFLINE':
          return 'el-icon-warning'
        case 'DEVICE_ALERT':
          return 'el-icon-warning-outline'
        default:
          return 'el-icon-info'
      }
    },

    /**
     * 获取告警级别文本
     */
    getAlertLevelText(level) {
      switch (level) {
        case 'CRITICAL':
          return '严重'
        case 'HIGH':
          return '高'
        case 'MEDIUM':
          return '中'
        case 'LOW':
          return '低'
        default:
          return '中'
      }
    },

    /**
     * 获取告警类型文本
     */
    getAlertTypeText(type) {
      // 如果已经是中文，直接返回
      if (type && (type.includes('告警') || type.includes('过期'))) {
        return type
      }

      // 兼容旧的英文枚举值
      switch (type) {
        case 'CPU_TEMPERATURE':
          return 'CPU温度告警'
        case 'MEMORY_USAGE':
          return '内存使用率告警'
        case 'DISK_USAGE':
          return '磁盘使用率告警'
        case 'DISK_DATA_USAGE':
          return '数据磁盘使用率告警'
        case 'DISK_SYSTEM_USAGE':
          return '系统磁盘使用率告警'
        case 'LICENSE_EXPIRY':
          return '软件授权过期告警'
        default:
          return type || '系统告警'
      }
    }
  }
}
</script>

<style scoped>
.device-notifications {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.connection-status {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 14px;
}

.connection-status.connected {
  background: #f0f9ff;
  color: #1890ff;
}

.connection-status.disconnected {
  background: #fff2f0;
  color: #ff4d4f;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 8px;
}

.connected .status-dot {
  background: #52c41a;
}

.disconnected .status-dot {
  background: #ff4d4f;
}

.connection-url {
  font-size: 12px;
  color: #8c8c8c;
  margin-left: 10px;
  font-family: monospace;
}

.notifications-title {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

.notifications-list {
  max-height: 400px;
  overflow-y: auto;
}

.notification-item {
  display: flex;
  align-items: flex-start;
  padding: 12px;
  margin-bottom: 8px;
  border-radius: 6px;
  border-left: 4px solid;
  background: #fafafa;
  transition: all 0.3s ease;
}

.notification-item:hover {
  background: #f0f0f0;
}

.notification-item.device-registered {
  border-left-color: #1890ff;
}

.notification-item.device-online {
  border-left-color: #52c41a;
}

.notification-item.device-offline {
  border-left-color: #ff4d4f;
}

.notification-item.device-alert {
  border-left-color: #fa8c16;
}

.notification-icon {
  margin-right: 12px;
  font-size: 16px;
}

.device-registered .notification-icon {
  color: #1890ff;
}

.device-online .notification-icon {
  color: #52c41a;
}

.device-offline .notification-icon {
  color: #ff4d4f;
}

.device-alert .notification-icon {
  color: #fa8c16;
}

.notification-content {
  flex: 1;
}

.notification-message {
  font-size: 14px;
  font-weight: 500;
  color: #262626;
  margin-bottom: 4px;
}

.notification-details {
  font-size: 12px;
  color: #8c8c8c;
}

.notification-details span {
  margin-right: 12px;
}

.device-id {
  font-family: monospace;
  background: #f5f5f5;
  padding: 2px 4px;
  border-radius: 2px;
}

.notification-close {
  cursor: pointer;
  color: #bfbfbf;
  font-size: 14px;
  padding: 4px;
}

.notification-close:hover {
  color: #8c8c8c;
}

.no-notifications {
  text-align: center;
  padding: 40px 20px;
  color: #8c8c8c;
}

.no-notifications i {
  font-size: 24px;
  margin-bottom: 8px;
  display: block;
}

/* 动画效果 */
.notification-enter-active,
.notification-leave-active {
  transition: all 0.3s ease;
}

.notification-enter-from {
  opacity: 0;
  transform: translateX(-30px);
}

.notification-leave-to {
  opacity: 0;
  transform: translateX(30px);
}

/* 告警详情样式 */
.alert-details {
  margin: 8px 0;
  padding: 8px;
  background: rgba(250, 140, 22, 0.1);
  border-radius: 4px;
  font-size: 12px;
}

.alert-level {
  display: inline-block;
  padding: 2px 6px;
  border-radius: 3px;
  font-weight: bold;
  margin-bottom: 4px;
}

.alert-level.level-critical {
  background: #ff4d4f;
  color: white;
}

.alert-level.level-high {
  background: #fa8c16;
  color: white;
}

.alert-level.level-medium {
  background: #faad14;
  color: white;
}

.alert-level.level-low {
  background: #52c41a;
  color: white;
}

.alert-values {
  margin: 4px 0;
  display: flex;
  gap: 12px;
}

.current-value, .threshold {
  font-size: 11px;
  color: #666;
}

.alert-type {
  font-size: 11px;
  color: #999;
  margin-top: 4px;
}
</style>
