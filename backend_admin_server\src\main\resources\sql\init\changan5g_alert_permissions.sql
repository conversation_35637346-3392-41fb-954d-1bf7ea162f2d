-- 长安5G告警管理权限配置
-- 执行此脚本为告警管理功能添加权限规则

-- 开始事务
START TRANSACTION;

-- 为admin角色添加告警管理权限（完整的CRUD权限）
INSERT IGNORE INTO `casbin_rule` (`ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES 
('p', 'admin', '/api/v1/admin/alerts*', 'GET', '', '', ''),
('p', 'admin', '/api/v1/admin/alerts*', 'POST', '', '', ''),
('p', 'admin', '/api/v1/admin/alerts*', 'PUT', '', '', ''),
('p', 'admin', '/api/v1/admin/alerts*', 'DELETE', '', '', '');

-- 为auditor角色添加告警查看权限（只读权限）
INSERT IGNORE INTO `casbin_rule` (`ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES 
('p', 'auditor', '/api/v1/admin/alerts*', 'GET', '', '', '');

-- 为user角色添加告警查看权限（只读权限）
INSERT IGNORE INTO `casbin_rule` (`ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES 
('p', 'user', '/api/v1/admin/alerts*', 'GET', '', '', '');

-- 提交事务
COMMIT;

-- 验证权限是否添加成功
SELECT 
    ptype,
    v0 as role,
    v1 as resource,
    v2 as action
FROM casbin_rule 
WHERE v1 LIKE '%alerts%' 
ORDER BY v0, v2;
