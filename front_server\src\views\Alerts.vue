<template>
  <div class="alerts-page">
    <div class="card">
      <div class="card-header">
        <h3>告警列表</h3>
      </div>
      <div class="card-body">
        <div class="search-filters">
          <div class="form-group">
            <label>设备MAC地址</label>
            <input
              type="text"
              class="form-control"
              placeholder="输入设备MAC地址"
              v-model="searchForm.identityMac"
              @keypress="handleEnterSearch"
            >
          </div>
          <div class="form-group">
            <label>告警类型</label>
            <select class="form-control" v-model="searchForm.alertType">
              <option value="">全部</option>
              <option value="CPU告警">CPU告警</option>
              <option value="内存告警">内存告警</option>
              <option value="磁盘告警">磁盘告警</option>
              <option value="数据磁盘告警">数据磁盘告警</option>
              <option value="系统磁盘告警">系统磁盘告警</option>
              <option value="授权过期告警">授权过期告警</option>
              <!-- 兼容旧的英文类型 -->
              <option value="CPU_TEMPERATURE">CPU温度告警(旧)</option>
              <option value="MEMORY_USAGE">内存使用率告警(旧)</option>
              <option value="DISK_USAGE">磁盘使用率告警(旧)</option>
              <option value="DISK_DATA_USAGE">数据磁盘使用率告警(旧)</option>
              <option value="DISK_SYSTEM_USAGE">系统磁盘使用率告警(旧)</option>
              <option value="LICENSE_EXPIRY">许可证过期告警(旧)</option>
            </select>
          </div>

          <div class="form-group">
            <label>告警级别</label>
            <select class="form-control" v-model="searchForm.alertLevel">
              <option value="">全部</option>
              <option value="CRITICAL">严重</option>
              <option value="HIGH">高</option>
              <option value="MEDIUM">中</option>
              <option value="LOW">低</option>
            </select>
          </div>

          <div class="form-group">
            <label>告警状态</label>
            <select class="form-control" v-model="searchForm.alertStatus">
              <option value="">全部</option>
              <option value="ACTIVE">激活</option>
              <option value="ACKNOWLEDGED">已确认</option>
              <option value="RESOLVED">已解决</option>
            </select>
          </div>
          <div class="form-group button-group">
            <label>&nbsp;</label> <!-- 占位标签，保持高度一致 -->
            <div class="button-container">
              <button class="btn btn-primary" @click="handleSearch">搜索</button>
              <button class="btn btn-secondary" @click="handleReset">重置</button>
            </div>
          </div>
        </div>

        <!-- Loading 状态显示 -->
        <div v-if="loading" style="text-align: center; padding: 40px;">
          <div style="font-size: 16px; color: #666;">加载中...</div>
        </div>

        <table class="table" v-else style="display: table; width: 100%; border-collapse: collapse;">
          <thead>
            <tr>
              <th>设备MAC地址</th>
              <th>告警类型</th>
              <th>告警级别</th>
              <th>告警详情</th>
              <th>当前值</th>
              <th>阈值</th>
              <th>状态</th>
              <th>告警时间</th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody>
            <tr v-if="!loading && alerts.length === 0">
              <td colspan="9" style="text-align: center; padding: 40px; color: #999;">
                暂无告警数据
              </td>
            </tr>
            <tr v-for="alert in alerts" :key="alert.alertId">
              <td>{{ alert.identityMac }}</td>
              <td>
                <span :class="getAlertTypeClass(alert.alertType)">
                  {{ getAlertTypeText(alert.alertType) }}
                </span>
              </td>
              <td>
                <span class="alert-level" :class="getAlertLevelClass(alert.alertLevel)">
                  {{ getAlertLevelText(alert.alertLevel) }}
                </span>
              </td>
              <td>{{ alert.alertDetails || '-' }}</td>
              <td>{{ alert.currentValue || '-' }}</td>
              <td>{{ alert.threshold || '-' }}</td>
              <td>
                <span :class="getAlertStatusClass(alert.alertStatus)">
                  {{ getAlertStatusText(alert.alertStatus) }}
                </span>
              </td>
              <td>{{ formatDateTime(alert.alertTime) }}</td>
              <td>
                <div class="action-buttons">
                  <button class="btn btn-info btn-sm" @click="viewAlertDetails(alert)">详情</button>
                  <button class="btn btn-warning btn-sm" @click="viewRealTimeMetrics(alert.identityMac)">实时指标</button>
                  <button
                    v-if="alert.alertStatus === 'ACTIVE'"
                    class="btn btn-success btn-sm"
                    @click="acknowledgeAlert(alert)"
                  >
                    确认
                  </button>
                  <button
                    v-if="alert.alertStatus !== 'RESOLVED'"
                    class="btn btn-danger btn-sm"
                    @click="resolveAlert(alert)"
                  >
                    解决
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>

        <!-- 分页 -->
        <div class="pagination">
          <button @click="prevPage" :disabled="pagination.page <= 1">上一页</button>
          <button 
            v-for="page in visiblePages" 
            :key="page"
            :class="{ active: page === pagination.page }"
            @click="goToPage(page)"
          >
            {{ page }}
          </button>
          <button @click="nextPage" :disabled="pagination.page >= totalPages">下一页</button>
        </div>
      </div>
    </div>

    <!-- 告警详情模态框 -->
    <div v-if="detailDialogVisible" class="modal" @click="handleDetailClose">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>告警详情</h3>
          <span class="close" @click="handleDetailClose">&times;</span>
        </div>
        <div class="modal-body" v-if="currentAlert">
          <div class="alert-detail-container">
            <div class="detail-row">
              <div class="detail-item">
                <span class="detail-label">告警ID:</span>
                <span class="detail-value">{{ currentAlert.alertId }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">告警时间:</span>
                <span class="detail-value">{{ formatDateTime(currentAlert.alertTime) }}</span>
              </div>
            </div>

            <div class="detail-row">
              <div class="detail-item">
                <span class="detail-label">设备ID:</span>
                <span class="detail-value">{{ currentAlert.deviceId }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">创建时间:</span>
                <span class="detail-value">{{ formatDateTime(currentAlert.createTime) }}</span>
              </div>
            </div>

            <div class="detail-row">
              <div class="detail-item">
                <span class="detail-label">指标名称:</span>
                <span class="detail-value">{{ currentAlert.metricName || '-' }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">更新时间:</span>
                <span class="detail-value">{{ formatDateTime(currentAlert.updateTime) }}</span>
              </div>
            </div>

            <div class="detail-row">
              <div class="detail-item">
                <span class="detail-label">告警类型:</span>
                <span class="detail-value">{{ getAlertTypeText(currentAlert.alertType) }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">告警级别:</span>
                <span class="detail-value">
                  <span class="alert-level" :class="getAlertLevelClass(currentAlert.alertLevel)">
                    {{ getAlertLevelText(currentAlert.alertLevel) }}
                  </span>
                </span>
              </div>
            </div>

            <div class="detail-row">
              <div class="detail-item">
                <span class="detail-label">状态:</span>
                <span class="detail-value">{{ getAlertStatusText(currentAlert.alertStatus) }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">通知状态:</span>
                <span class="detail-value">{{ currentAlert.notificationSent ? '已发送' : '未发送' }}</span>
              </div>
            </div>

            <div class="detail-row">
              <div class="detail-item">
                <span class="detail-label">告警详情:</span>
                <span class="detail-value">{{ currentAlert.alertDetails || '-' }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">通知时间:</span>
                <span class="detail-value">{{ formatDateTime(currentAlert.notificationTime) || '-' }}</span>
              </div>
            </div>

            <div class="detail-row">
              <div class="detail-item">
                <span class="detail-label">当前值:</span>
                <span class="detail-value">{{ currentAlert.currentValue || '-' }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">阈值:</span>
                <span class="detail-value">{{ currentAlert.threshold || '-' }}</span>
              </div>
            </div>

            <div class="detail-row">
              <div class="detail-item">
                <span class="detail-label">确认人:</span>
                <span class="detail-value">{{ currentAlert.acknowledgedBy || '未确认' }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">确认时间:</span>
                <span class="detail-value">{{ formatDateTime(currentAlert.acknowledgedTime) || '-' }}</span>
              </div>
            </div>

            <div class="detail-row">
              <div class="detail-item">
                <span class="detail-label">解决时间:</span>
                <span class="detail-value">{{ formatDateTime(currentAlert.resolvedTime) || '-' }}</span>
              </div>
            </div>

            <div class="detail-row" v-if="currentAlert.resolveComment">
              <div class="detail-item full-width">
                <span class="detail-label">解决备注:</span>
                <span class="detail-value">{{ currentAlert.resolveComment }}</span>
              </div>
            </div>
          </div>
          <div class="modal-footer">
            <button
              v-if="currentAlert.alertStatus === 'ACTIVE'"
              class="btn btn-warning"
              @click="acknowledgeAlert(currentAlert)"
            >
              确认告警
            </button>
            <button
              v-if="currentAlert.alertStatus !== 'RESOLVED'"
              class="btn btn-success"
              @click="resolveAlert(currentAlert)"
            >
              标记已解决
            </button>
            <button class="btn btn-secondary" @click="handleDetailClose">关闭</button>
          </div>
        </div>
      </div>
    </div>

    <!-- 确认告警模态框 -->
    <div v-if="acknowledgeDialogVisible" class="modal" @click="acknowledgeDialogVisible = false">
      <div class="modal-content acknowledge-modal" @click.stop>
        <div class="modal-header">
          <h3><i class="icon-check-circle"></i>确认告警</h3>
          <span class="close" @click="acknowledgeDialogVisible = false">&times;</span>
        </div>
        <div class="modal-body">
          <div class="alert-info-section" v-if="currentAlert">
            <div class="alert-summary">
              <div class="alert-type">
                <span :class="getAlertTypeClass(currentAlert.alertType)">
                  {{ getAlertTypeText(currentAlert.alertType) }}
                </span>
              </div>
              <div class="alert-device">设备: {{ currentAlert.deviceId }}</div>
              <div class="alert-time">时间: {{ formatDateTime(currentAlert.alertTime) }}</div>
            </div>
          </div>

          <div class="form-section">
            <div class="form-group">
              <label><i class="icon-user"></i>确认人</label>
              <input
                type="text"
                class="form-control"
                placeholder="请输入确认人姓名"
                v-model="acknowledgeForm.acknowledgedBy"
                @keypress="handleAcknowledgeEnter"
              >
            </div>
            <div class="form-tip">
              <i class="icon-info"></i>
              确认告警表示您已知晓此告警，告警状态将变更为"已确认"
            </div>
          </div>

          <div class="modal-actions">
            <button class="btn btn-primary" @click="confirmAcknowledge">
              <i class="icon-check"></i>确认告警
            </button>
            <button class="btn btn-secondary" @click="acknowledgeDialogVisible = false">
              <i class="icon-close"></i>取消
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 解决告警模态框 -->
    <div v-if="resolveDialogVisible" class="modal" @click="resolveDialogVisible = false">
      <div class="modal-content resolve-modal" @click.stop>
        <div class="modal-header">
          <h3><i class="icon-check-square"></i>解决告警</h3>
          <span class="close" @click="resolveDialogVisible = false">&times;</span>
        </div>
        <div class="modal-body">
          <div class="alert-info-section" v-if="currentAlert">
            <div class="alert-summary">
              <div class="alert-type">
                <span :class="getAlertTypeClass(currentAlert.alertType)">
                  {{ getAlertTypeText(currentAlert.alertType) }}
                </span>
              </div>
              <div class="alert-device">设备: {{ currentAlert.deviceId }}</div>
              <div class="alert-time">时间: {{ formatDateTime(currentAlert.alertTime) }}</div>
              <div class="alert-details" v-if="currentAlert.alertDetails">
                详情: {{ currentAlert.alertDetails }}
              </div>
            </div>
          </div>

          <div class="form-section">
            <div class="form-group">
              <label><i class="icon-user"></i>解决人</label>
              <input
                type="text"
                class="form-control"
                placeholder="请输入解决人姓名"
                v-model="resolveForm.resolvedBy"
                @keypress="handleResolveEnter"
              >
            </div>
            <div class="form-group">
              <label><i class="icon-edit"></i>解决备注</label>
              <textarea
                class="form-control"
                rows="3"
                placeholder="请描述解决方案或处理过程（可选）"
                v-model="resolveForm.resolveComment"
              ></textarea>
            </div>
            <div class="form-tip">
              <i class="icon-info"></i>
              解决告警表示问题已处理完成，告警状态将变更为"已解决"
            </div>
          </div>

          <div class="modal-actions">
            <button class="btn btn-success" @click="confirmResolve">
              <i class="icon-check"></i>标记已解决
            </button>
            <button class="btn btn-secondary" @click="resolveDialogVisible = false">
              <i class="icon-close"></i>取消
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { alertApi } from '@/api/services'

const router = useRouter()
const route = useRoute()

// 响应式数据
const loading = ref(false)
const alerts = ref([])
const total = ref(0)

// 搜索表单
const searchForm = reactive({
  identityMac: '',
  alertType: '',
  alertLevel: '',
  alertStatus: ''
})

// 分页
const pagination = reactive({
  page: 1,
  pageSize: 10
})

// 对话框状态
const detailDialogVisible = ref(false)
const acknowledgeDialogVisible = ref(false)
const resolveDialogVisible = ref(false)
const currentAlert = ref(null)

// 确认和解决表单
const acknowledgeForm = reactive({
  acknowledgedBy: ''
})

const resolveForm = reactive({
  resolvedBy: '',
  resolveComment: ''
})

// 计算属性 - 使用后端分页，不需要本地过滤

const totalPages = computed(() => {
  return Math.ceil(total.value / pagination.pageSize)
})

const visiblePages = computed(() => {
  const current = pagination.page
  const total = totalPages.value
  const pages = []
  
  let start = Math.max(1, current - 2)
  let end = Math.min(total, current + 2)
  
  for (let i = start; i <= end; i++) {
    pages.push(i)
  }
  
  return pages
})

// 查看告警详情
const viewAlertDetails = (alert) => {
  currentAlert.value = alert
  detailDialogVisible.value = true
  // 阻止body滚动
  document.body.style.overflow = 'hidden'
}

// 查看实时指标
const viewRealTimeMetrics = (identityMac) => {
  if (!identityMac) {
    console.error('设备MAC地址不能为空')
    return
  }

  console.log('跳转到实时指标页面，设备MAC:', identityMac)

  router.push({
    name: 'RealTimeMetrics',
    params: { identityMac },
    query: { from: 'alerts' }  // 添加来源信息
  }).then(() => {
    console.log('成功跳转到实时指标页面')
  }).catch(error => {
    console.error('跳转失败:', error)
  })
}

// 发送钉钉通知（简化为前端提醒）
const sendDingTalkNotification = async (alert) => {
  // 简化为前端提醒，不实际发送钉钉通知
  ElMessage({
    message: '钉钉通知功能暂未开放，请联系管理员处理此告警',
    type: 'info',
    duration: 3000,
    showClose: true
  })
}

// 确认告警
const acknowledgeAlert = (alert) => {
  currentAlert.value = alert
  acknowledgeForm.acknowledgedBy = ''
  acknowledgeDialogVisible.value = true
  // 如果详情弹框开着，先关闭
  if (detailDialogVisible.value) {
    detailDialogVisible.value = false
  }
}

// 确认告警确认
const confirmAcknowledge = async () => {
  if (!acknowledgeForm.acknowledgedBy.trim()) {
    ElMessage({
      message: '请输入确认人姓名',
      type: 'warning',
      duration: 3000
    })
    return
  }

  try {
    console.log('Acknowledging alert:', currentAlert.value.alertId, acknowledgeForm.acknowledgedBy)

    await alertApi.acknowledgeAlert(currentAlert.value.alertId, acknowledgeForm.acknowledgedBy)

    // 更新本地状态
    currentAlert.value.alertStatus = 'ACKNOWLEDGED'
    currentAlert.value.acknowledgedBy = acknowledgeForm.acknowledgedBy
    currentAlert.value.acknowledgedTime = new Date().toLocaleString('zh-CN')

    // 更新列表中的数据
    const alertIndex = alerts.value.findIndex(a => a.alertId === currentAlert.value.alertId)
    if (alertIndex !== -1) {
      alerts.value[alertIndex] = { ...currentAlert.value }
    }

    acknowledgeDialogVisible.value = false
    // 重新打开详情弹框显示更新后的状态
    setTimeout(() => {
      detailDialogVisible.value = true
    }, 100)

    ElMessage({
      message: '告警已确认',
      type: 'success',
      duration: 3000
    })

  } catch (error) {
    console.error('确认告警失败:', error)
    ElMessage({
      message: '确认告警失败，请重试',
      type: 'error',
      duration: 3000
    })
  }
}

// 处理确认弹框回车键
const handleAcknowledgeEnter = (event) => {
  if (event.key === 'Enter') {
    confirmAcknowledge()
  }
}

// 解决告警
const resolveAlert = (alert) => {
  currentAlert.value = alert
  resolveForm.resolvedBy = ''
  resolveForm.resolveComment = ''
  resolveDialogVisible.value = true
  // 如果详情弹框开着，先关闭
  if (detailDialogVisible.value) {
    detailDialogVisible.value = false
  }
}

// 确认解决告警
const confirmResolve = async () => {
  if (!resolveForm.resolvedBy.trim()) {
    ElMessage({
      message: '请输入解决人姓名',
      type: 'warning',
      duration: 3000
    })
    return
  }

  try {
    console.log('Resolving alert:', currentAlert.value.alertId, resolveForm.resolvedBy, resolveForm.resolveComment)

    await alertApi.resolveAlert(currentAlert.value.alertId, resolveForm.resolvedBy, resolveForm.resolveComment)

    // 更新本地状态
    currentAlert.value.alertStatus = 'RESOLVED'
    currentAlert.value.resolvedBy = resolveForm.resolvedBy
    currentAlert.value.resolvedTime = new Date().toLocaleString('zh-CN')
    currentAlert.value.resolveComment = resolveForm.resolveComment
    if (!currentAlert.value.acknowledgedBy) {
      currentAlert.value.acknowledgedBy = resolveForm.resolvedBy
      currentAlert.value.acknowledgedTime = new Date().toLocaleString('zh-CN')
    }

    // 更新列表中的数据
    const alertIndex = alerts.value.findIndex(a => a.alertId === currentAlert.value.alertId)
    if (alertIndex !== -1) {
      alerts.value[alertIndex] = { ...currentAlert.value }
    }

    resolveDialogVisible.value = false
    // 重新打开详情弹框显示更新后的状态
    setTimeout(() => {
      detailDialogVisible.value = true
    }, 100)

    ElMessage({
      message: '告警已解决',
      type: 'success',
      duration: 3000
    })

  } catch (error) {
    console.error('解决告警失败:', error)
    ElMessage({
      message: '解决告警失败，请重试',
      type: 'error',
      duration: 3000
    })
  }
}

// 处理解决弹框回车键
const handleResolveEnter = (event) => {
  if (event.key === 'Enter' && event.target.tagName !== 'TEXTAREA') {
    confirmResolve()
  }
}

// 对话框关闭处理
const handleDetailClose = () => {
  detailDialogVisible.value = false
  currentAlert.value = null
  // 恢复body滚动
  document.body.style.overflow = 'auto'
}

// 高亮告警
const highlightAlert = (alertId) => {
  setTimeout(() => {
    // 兼容不同的ID字段：alertId（字符串）或 id（数字）
    const alert = alerts.value.find(a =>
      a.alertId === alertId ||
      a.id === alertId ||
      a.id === parseInt(alertId) ||
      a.alertId === String(alertId)
    )
    if (alert) {
      viewAlertDetails(alert)
    }
  }, 500)
}

// 监听ESC键关闭模态框
const handleKeydown = (event) => {
  if (event.key === 'Escape') {
    if (detailDialogVisible.value) {
      handleDetailClose()
    } else if (acknowledgeDialogVisible.value) {
      acknowledgeDialogVisible.value = false
    } else if (resolveDialogVisible.value) {
      resolveDialogVisible.value = false
    }
  }
}

// 组件挂载时添加键盘监听
onMounted(async () => {
  // 检查URL参数并自动填充搜索表单
  if (route.query.alertId) {
    console.log('从概览页面跳转，告警ID:', route.query.alertId)
    searchForm.alertId = route.query.alertId
  }

  if (route.query.identityMac) {
    console.log('从概览页面跳转，设备MAC:', route.query.identityMac)
    searchForm.identityMac = route.query.identityMac
  }

  // 如果有autoSearch参数，自动执行搜索
  if (route.query.autoSearch === 'true') {
    console.log('自动执行搜索，不自动打开弹框')
    await handleSearch()

    // 不自动高亮显示，只是过滤数据
    console.log('搜索完成，显示过滤后的告警列表')
  } else {
    await loadAlerts()
  }

  // 添加键盘事件监听
  document.addEventListener('keydown', handleKeydown)
})

// 组件卸载时移除键盘监听
onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
  // 确保恢复body滚动
  document.body.style.overflow = 'auto'
})

// 加载告警列表
const loadAlerts = async () => {
  console.log('=== loadAlerts 开始 ===')
  console.log('设置 loading = true')
  loading.value = true
  try {
    console.log('Loading alerts with params:', {
      alertId: searchForm.alertId,
      deviceId: searchForm.deviceId,
      alertType: searchForm.alertType,
      alertLevel: searchForm.alertLevel,
      alertStatus: searchForm.alertStatus,
      page: pagination.page,
      pageSize: pagination.pageSize
    })

    const params = {
      alertId: searchForm.alertId || undefined,
      deviceId: searchForm.deviceId || undefined,
      alertType: searchForm.alertType || undefined,
      alertLevel: searchForm.alertLevel || undefined,
      alertStatus: searchForm.alertStatus || undefined,
      page: pagination.page,
      pageSize: pagination.pageSize
    }

    console.log('开始调用 alertApi.getAlerts...')
    const response = await alertApi.getAlerts(params)
    console.log('API 调用完成!')
    console.log('API Response:', response)

    // 简化数据处理逻辑
    let records = []
    let totalCount = 0

    try {
      // 检查不同的响应结构
      if (response && response.data && response.data.records) {
        // axios包装的Spring Boot分页: { data: { records: [...], total: number } }
        records = response.data.records
        totalCount = response.data.total
        console.log('使用 response.data 结构')
      } else if (response && response.records) {
        // 直接的Spring Boot分页: { records: [...], total: number }
        records = response.records
        totalCount = response.total
        console.log('使用直接分页结构')
      } else if (response && response.data && response.data.list) {
        // Mock数据: { data: { list: [...], total: number } }
        records = response.data.list
        totalCount = response.data.total
        console.log('使用 Mock 数据结构')
      } else if (response && Array.isArray(response)) {
        // 直接数组
        records = response
        totalCount = response.length
        console.log('使用直接数组结构')
      } else {
        console.error('未知的响应结构:', response)
        records = []
        totalCount = 0
      }

      // 确保records是数组
      if (!Array.isArray(records)) {
        console.error('records不是数组:', records)
        records = []
      }

      alerts.value = records
      total.value = totalCount

      console.log('最终设置的告警数据:', alerts.value)
      console.log('最终设置的总数:', total.value)
      console.log('alerts.value.length:', alerts.value.length)

    } catch (dataError) {
      console.error('数据处理错误:', dataError)
      alerts.value = []
      total.value = 0
    }

  } catch (error) {
    console.error('加载告警列表失败:', error)
    console.error('错误详情:', error.stack)
    alerts.value = []
    total.value = 0
  } finally {
    console.log('设置 loading = false')
    loading.value = false
    console.log('当前 loading 状态:', loading.value)
    console.log('当前 alerts 长度:', alerts.value.length)
    console.log('=== loadAlerts 结束 ===')

    // 使用nextTick确保响应式更新
    nextTick(() => {
      console.log('nextTick检查 - loading:', loading.value, 'alerts:', alerts.value.length)
      if (loading.value) {
        console.warn('警告：loading状态仍然为true，强制设置为false')
        loading.value = false
      }
    })
  }
}



// 搜索
const handleSearch = async () => {
  pagination.page = 1
  await loadAlerts()
}

// 重置搜索
const handleReset = async () => {
  Object.assign(searchForm, {
    alertId: '',
    identityMac: '',
    alertType: '',
    alertLevel: '',
    alertStatus: ''
  })
  pagination.page = 1
  await loadAlerts()
}

// 回车搜索
const handleEnterSearch = (event) => {
  if (event.key === 'Enter') {
    handleSearch()
  }
}

// 分页处理
const prevPage = async () => {
  if (pagination.page > 1) {
    pagination.page--
    await loadAlerts()
  }
}

const nextPage = async () => {
  if (pagination.page < totalPages.value) {
    pagination.page++
    await loadAlerts()
  }
}

const goToPage = async (page) => {
  pagination.page = page
  await loadAlerts()
}

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return '-'

  try {
    const date = new Date(dateTime)
    if (isNaN(date.getTime())) return '-'

    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    })
  } catch (error) {
    console.error('日期格式化错误:', error)
    return '-'
  }
}

// 获取告警类型样式类
const getAlertTypeClass = (type) => {
  const typeMap = {
    'CPU_TEMPERATURE': 'badge badge-danger',
    'MEMORY_USAGE': 'badge badge-warning',
    'DISK_USAGE': 'badge badge-warning',
    'DISK_DATA_USAGE': 'badge badge-warning',
    'DISK_SYSTEM_USAGE': 'badge badge-warning',
    'LICENSE_EXPIRY': 'badge badge-info'
  }
  return typeMap[type] || 'badge badge-info'
}

// 获取告警类型文本
const getAlertTypeText = (type) => {
  // 如果已经是中文，直接返回
  if (type && (type.includes('告警') || type.includes('过期'))) {
    return type
  }

  // 兼容旧的英文枚举值
  const typeMap = {
    'CPU_TEMPERATURE': 'CPU温度告警',
    'MEMORY_USAGE': '内存使用率告警',
    'DISK_USAGE': '磁盘使用率告警',
    'DISK_DATA_USAGE': '数据磁盘使用率告警',
    'DISK_SYSTEM_USAGE': '系统磁盘使用率告警',
    'LICENSE_EXPIRY': '许可证过期告警'
  }
  return typeMap[type] || type
}

// 获取告警级别样式类
const getAlertLevelClass = (level) => {
  const classMap = {
    'CRITICAL': 'critical',
    'HIGH': 'high',
    'MEDIUM': 'medium',
    'LOW': 'low'
  }
  return classMap[level] || 'medium'
}

// 获取告警级别文本
const getAlertLevelText = (level) => {
  const levelMap = {
    'CRITICAL': '严重',
    'HIGH': '高',
    'MEDIUM': '中',
    'LOW': '低'
  }
  return levelMap[level] || level
}



// 获取告警状态样式类
const getAlertStatusClass = (status) => {
  const statusMap = {
    'ACTIVE': 'badge badge-danger',
    'ACKNOWLEDGED': 'badge badge-warning',
    'RESOLVED': 'badge badge-success'
  }
  return statusMap[status] || 'badge badge-info'
}

// 获取告警状态文本
const getAlertStatusText = (status) => {
  const statusMap = {
    'ACTIVE': '激活',
    'ACKNOWLEDGED': '已确认',
    'RESOLVED': '已解决'
  }
  return statusMap[status] || status
}
</script>

<style scoped>
.alerts-page {
  /* 继承通用样式 */
}

/* 告警详情容器 */
.alert-detail-container {
  padding: 20px;
}

.detail-row {
  display: flex;
  margin-bottom: 16px;
  gap: 40px;
}

.detail-item {
  flex: 1;
  display: flex;
  align-items: flex-start;
  min-height: 24px;
}

.detail-item.full-width {
  flex: 1 1 100%;
}

.detail-label {
  font-weight: 600;
  color: #333;
  min-width: 80px;
  margin-right: 12px;
  flex-shrink: 0;
  text-align: right;
}

.detail-value {
  color: #666;
  word-break: break-all;
  line-height: 1.4;
}

/* 模态框样式 */
.modal {
  display: block;
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0,0,0,0.5);
}

.modal-content {
  background-color: white;
  margin: 5% auto;
  padding: 0;
  border-radius: 8px;
  width: 85%;
  max-width: 900px;
  max-height: 85vh;
  overflow-y: auto;
  position: relative;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 20px 15px 20px;
  margin-bottom: 0;
  border-bottom: 1px solid #eee;
}

.modal-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.modal-footer {
  padding: 15px 20px 20px 20px;
  text-align: center;
  border-top: 1px solid #eee;
  margin-top: 20px;
}

.modal-footer .btn {
  margin: 0 5px;
}

.close {
  font-size: 24px;
  font-weight: bold;
  cursor: pointer;
  color: #999;
  line-height: 1;
}

.close:hover {
  color: #333;
}

.modal-body {
  padding: 0;
}

/* 卡片样式 */
.card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  margin-bottom: 20px;
  overflow: hidden;
}

.card-header {
  padding: 20px;
  border-bottom: 1px solid #eee;
  background: #fafafa;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.card-body {
  padding: 20px;
}

/* 搜索过滤器样式 */
.search-filters {
  display: flex;
  gap: 15px;
  margin-bottom: 20px;
  flex-wrap: wrap;
  align-items: end; /* 将所有项目底部对齐 */
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group label {
  margin-bottom: 5px;
  font-weight: 500;
  color: #555;
  min-height: 20px; /* 确保标签有统一高度 */
}

.form-control {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  min-width: 120px;
}

.form-control:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

/* 按钮组样式 */
.button-group {
  align-self: flex-end; /* 确保按钮组在底部 */
}

.button-container {
  display: flex;
  gap: 10px;
}

/* 按钮样式 */
.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s;
  text-decoration: none;
  display: inline-block;
}

.btn-primary {
  background: #667eea;
  color: white;
}

.btn-primary:hover {
  background: #5a6fd8;
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background: #5a6268;
}

.btn-info {
  background: #17a2b8;
  color: white;
}

.btn-info:hover {
  background: #138496;
}

.btn-warning {
  background: #ffc107;
  color: #212529;
}

.btn-warning:hover {
  background: #e0a800;
}

.btn-danger {
  background: #dc3545;
  color: white;
}

.btn-danger:hover {
  background: #c82333;
}

.btn-success {
  background: #28a745;
  color: white;
}

.btn-success:hover {
  background: #218838;
}

.btn-sm {
  padding: 5px 10px;
  font-size: 12px;
}

/* 表格样式 */
.table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 15px;
}

.table th,
.table td {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid #eee;
}

.table th {
  background: #f8f9fa;
  font-weight: 600;
  color: #495057;
}

.table tr:hover {
  background: #f8f9fa;
}

/* 徽章样式 */
.badge {
  display: inline-block;
  padding: 4px 8px;
  font-size: 12px;
  font-weight: 500;
  border-radius: 12px;
  text-align: center;
  white-space: nowrap;
}

.badge-success {
  background: #28a745;
  color: white;
}

.badge-danger {
  background: #dc3545;
  color: white;
}

.badge-warning {
  background: #ffc107;
  color: #212529;
}

.badge-info {
  background: #17a2b8;
  color: white;
}

/* 告警级别样式 */
.alert-level {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: bold;
  color: white;
  margin-left: 4px;
}

.alert-level.critical {
  background: #ff4d4f;
}

.alert-level.high {
  background: #fa8c16;
}

.alert-level.medium {
  background: #faad14;
}

.alert-level.low {
  background: #52c41a;
}

/* 操作按钮组 */
.action-buttons {
  display: flex;
  gap: 5px;
  flex-wrap: wrap;
}

/* 分页样式 */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
  margin-top: 20px;
}

.pagination button {
  padding: 8px 12px;
  border: 1px solid #ddd;
  background: white;
  cursor: pointer;
  border-radius: 4px;
}

.pagination button:hover {
  background: #f8f9fa;
}

.pagination button.active {
  background: #667eea;
  color: white;
  border-color: #667eea;
}

.pagination button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  background: white !important;
  color: #999 !important;
  border-color: #ddd !important;
}

/* 确认和解决告警弹框样式 */
.acknowledge-modal,
.resolve-modal {
  max-width: 500px;
}

.alert-info-section {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 20px;
  border-left: 4px solid #667eea;
}

.alert-summary {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.alert-type {
  display: flex;
  align-items: center;
}

.alert-device,
.alert-time,
.alert-details {
  font-size: 14px;
  color: #666;
  display: flex;
  align-items: center;
}

.alert-details {
  margin-top: 4px;
  font-style: italic;
}

.form-section {
  margin-bottom: 20px;
}

.form-section .form-group {
  margin-bottom: 16px;
}

.form-section label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.form-tip {
  background: #e3f2fd;
  border: 1px solid #bbdefb;
  border-radius: 4px;
  padding: 12px;
  font-size: 13px;
  color: #1976d2;
  display: flex;
  align-items: flex-start;
  gap: 8px;
  margin-top: 12px;
}

.modal-actions {
  display: flex;
  justify-content: center;
  gap: 12px;
  padding-top: 20px;
  border-top: 1px solid #eee;
}

.modal-actions .btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 10px 20px;
  font-weight: 500;
}

/* 图标样式（使用文字符号代替） */
.icon-check-circle::before { content: "✓"; margin-right: 4px; }
.icon-check-square::before { content: "✅"; margin-right: 4px; }
.icon-user::before { content: "👤"; margin-right: 4px; }
.icon-edit::before { content: "📝"; margin-right: 4px; }
.icon-info::before { content: "ℹ️"; margin-right: 4px; }
.icon-check::before { content: "✓"; margin-right: 4px; }
.icon-close::before { content: "✕"; margin-right: 4px; }

/* 响应式设计 */
@media (max-width: 768px) {
  .search-filters {
    flex-direction: column;
    align-items: stretch;
  }
  
  .form-group {
    margin-bottom: 10px;
  }
  
  .action-buttons {
    flex-direction: column;
    gap: 5px;
  }
  
  .action-buttons .btn {
    width: 100%;
  }
  
  .modal-content {
    width: 95%;
    margin: 2vh auto;
  }

  .acknowledge-modal,
  .resolve-modal {
    max-width: 95%;
  }

  .detail-row {
    flex-direction: column;
    gap: 12px;
  }

  .detail-label {
    min-width: 70px;
    text-align: left;
  }

  .table {
    font-size: 12px;
  }

  .table th,
  .table td {
    padding: 8px 4px;
  }

  .modal-actions {
    flex-direction: column;
    gap: 8px;
  }

  .modal-actions .btn {
    width: 100%;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .modal-content {
    width: 98%;
    margin: 1vh auto;
    padding: 15px;
  }
  
  .card-body {
    padding: 15px;
  }
  
  .action-buttons .btn {
    font-size: 11px;
    padding: 4px 8px;
  }
  
  .badge {
    font-size: 10px;
    padding: 2px 6px;
  }
}
</style> 