package com.unnet.changan5G.config;

import com.unnet.changan5G.service.DeviceNotificationService;
import com.unnet.changan5G.service.TerminalBasicInfoService;
import com.unnet.changan5G.service.TerminalCacheService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.connection.Message;
import org.springframework.data.redis.listener.KeyExpirationEventMessageListener;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import org.springframework.stereotype.Component;

/**
 * Redis键过期事件监听器
 * 监听设备在线键的过期事件，自动处理设备离线逻辑
 * 
 * <AUTHOR>
 * @date 2024-07-14
 */
@Component
@Slf4j
public class RedisKeyExpirationListener extends KeyExpirationEventMessageListener {

    private final DeviceNotificationService deviceNotificationService;
    private final TerminalBasicInfoService terminalBasicInfoService;
    private final TerminalCacheService terminalCacheService;

    public RedisKeyExpirationListener(RedisMessageListenerContainer listenerContainer,
                                    DeviceNotificationService deviceNotificationService,
                                    TerminalBasicInfoService terminalBasicInfoService,
                                    TerminalCacheService terminalCacheService) {
        super(listenerContainer);
        this.deviceNotificationService = deviceNotificationService;
        this.terminalBasicInfoService = terminalBasicInfoService;
        this.terminalCacheService = terminalCacheService;
    }

    @Override
    public void onMessage(Message message, byte[] pattern) {
        try {
            String expiredKey = message.toString();
            log.debug("Redis键过期事件 - 键: {}", expiredKey);

            // 只处理设备在线键的过期事件
            if (expiredKey.startsWith("device:online:")) {
                String mac = expiredKey.substring("device:online:".length());
                handleDeviceOffline(mac);
            }
        } catch (Exception e) {
            log.error("处理Redis键过期事件失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 处理设备离线逻辑
     */
    private void handleDeviceOffline(String mac) {
        try {
            log.warn("检测到设备离线 - MAC: {}", mac);

            // 1. 获取设备基本信息
            var deviceInfo = terminalCacheService.getCachedTerminalBasicInfoByMac(mac);
            if (deviceInfo == null) {
                // 从数据库获取设备信息
                var entity = terminalBasicInfoService.getByIdentityMac(mac);
                if (entity == null) {
                    log.warn("未找到设备信息，跳过离线处理 - MAC: {}", mac);
                    return;
                }
                // 将entity转换为TerminalBasicInfo
                deviceInfo = new com.unnet.changan5G.dto.terminal.TerminalBasicInfo();
                org.springframework.beans.BeanUtils.copyProperties(entity, deviceInfo);
            }

            // 2. 检查设备当前状态，避免重复处理
            if (deviceInfo.getStatus() != null && deviceInfo.getStatus() == 0) {
                log.debug("设备已经是离线状态，跳过处理 - MAC: {}", mac);
                return;
            }

            // 3. 更新数据库状态为离线
            boolean updateResult = terminalBasicInfoService.updateDeviceStatusByMac(mac, 0);
            if (!updateResult) {
                log.error("更新设备离线状态失败 - MAC: {}", mac);
                return;
            }

            // 4. 清理相关Redis缓存
            terminalCacheService.removeDeviceOnlineStatus(mac);
            terminalCacheService.cacheDeviceStatusByMac(mac, 0);

            // 5. 发送离线通知到SSE
            String hostname = deviceInfo.getHostname() != null ? deviceInfo.getHostname() : "未知主机";
            log.info("准备发送设备离线SSE通知 - MAC: {}, 主机名: {}", mac, hostname);
            deviceNotificationService.sendDeviceOfflineNotification(mac, hostname);
            log.info("设备离线SSE通知已发送 - MAC: {}", mac);

            // 6. 发送统计信息更新通知
            sendStatisticsUpdateNotification();

            log.warn("设备离线处理完成 - MAC: {}, 主机名: {}", mac, hostname);

        } catch (Exception e) {
            log.error("处理设备离线失败 - MAC: {}, 错误: {}", mac, e.getMessage(), e);
        }
    }

    /**
     * 发送统计信息更新通知
     */
    private void sendStatisticsUpdateNotification() {
        try {
            int onlineCount = terminalBasicInfoService.countOnlineDevices();
            int offlineCount = terminalBasicInfoService.countOfflineDevices();
            int alertCount = 0; // 可根据需要获取告警数量

            deviceNotificationService.sendStatisticsUpdateNotification(
                    onlineCount, offlineCount, alertCount);

            log.debug("发送统计信息更新通知 - 在线: {}, 离线: {}", onlineCount, offlineCount);

        } catch (Exception e) {
            log.error("发送统计信息更新通知失败: {}", e.getMessage(), e);
        }
    }
}