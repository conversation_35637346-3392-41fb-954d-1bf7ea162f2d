package com.unnet.changan5G.service.impl;

import com.unnet.changan5G.entity.MetricThresholdConfigEntity;
import com.unnet.changan5G.mapper.MetricThresholdConfigMapper;
import com.unnet.changan5G.service.MetricThresholdConfigService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 指标阈值配置服务实现类
 * 
 * <AUTHOR>
 * @since 2024-07-21
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MetricThresholdConfigServiceImpl implements MetricThresholdConfigService {

    private final MetricThresholdConfigMapper metricThresholdConfigMapper;

    @Override
    @Cacheable(value = "metricThresholdConfig", key = "#metricType")
    public MetricThresholdConfigEntity getConfigByMetricType(String metricType) {
        try {
            MetricThresholdConfigEntity config = metricThresholdConfigMapper.selectEnabledByMetricType(metricType);
            log.debug("获取指标阈值配置 - 指标类型: {}, 配置: {}", metricType, config != null ? "存在" : "不存在");
            return config;
        } catch (Exception e) {
            log.error("获取指标阈值配置失败 - 指标类型: {}, 错误: {}", metricType, e.getMessage(), e);
            return null;
        }
    }

    @Override
    @Cacheable(value = "metricThresholdConfigs", key = "'all'")
    public List<MetricThresholdConfigEntity> getAllEnabledConfigs() {
        try {
            List<MetricThresholdConfigEntity> configs = metricThresholdConfigMapper.selectAllEnabled();
            log.debug("获取所有启用的指标阈值配置 - 数量: {}", configs.size());
            return configs;
        } catch (Exception e) {
            log.error("获取所有启用的指标阈值配置失败 - 错误: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    @Cacheable(value = "metricThresholdConfigsMap", key = "'all'")
    public Map<String, MetricThresholdConfigEntity> getAllEnabledConfigsMap() {
        try {
            log.info("从数据库加载指标阈值配置映射（将缓存到Redis）");
            List<MetricThresholdConfigEntity> configs = getAllEnabledConfigs();
            Map<String, MetricThresholdConfigEntity> configMap = configs.stream()
                    .collect(Collectors.toMap(
                            MetricThresholdConfigEntity::getMetricType,
                            Function.identity(),
                            (existing, replacement) -> existing
                    ));
            log.info("指标阈值配置映射加载完成并已缓存到Redis - 数量: {}", configMap.size());
            return configMap;
        } catch (Exception e) {
            log.error("获取指标阈值配置映射失败 - 错误: {}", e.getMessage(), e);
            return new HashMap<>();
        }
    }

    @Override
    public boolean isThresholdExceeded(BigDecimal currentValue, MetricThresholdConfigEntity config) {
        if (currentValue == null || config == null || config.getThresholdValue() == null) {
            return false;
        }

        String operator = config.getComparisonOperator();
        BigDecimal thresholdValue = config.getThresholdValue();
        log.info("指标名称：{}，当前指标值{}，指标阈值：{},操作类型{}",config.getMetricName(),currentValue,thresholdValue,operator);
        try {
            switch (operator) {
                case ">=":
                    return currentValue.compareTo(thresholdValue) >= 0;
                case ">":
                    return currentValue.compareTo(thresholdValue) > 0;
                case "<=":
                    return currentValue.compareTo(thresholdValue) <= 0;
                case "<":
                    return currentValue.compareTo(thresholdValue) < 0;
                case "=":
                    return currentValue.compareTo(thresholdValue) == 0;
                default:
                    log.warn("未知的比较操作符: {}", operator);
                    return false;
            }
        } catch (Exception e) {
            log.error("阈值比较失败 - 当前值: {}, 阈值: {}, 操作符: {}, 错误: {}", 
                    currentValue, thresholdValue, operator, e.getMessage());
            return false;
        }
    }

    @Override
    public boolean isThresholdExceeded(Double currentValue, MetricThresholdConfigEntity config) {
        if (currentValue == null) {
            return false;
        }
        return isThresholdExceeded(BigDecimal.valueOf(currentValue), config);
    }

    @Override
    @CacheEvict(value = {"metricThresholdConfig", "metricThresholdConfigs", "metricThresholdConfigsMap"}, allEntries = true)
    public void refreshConfigCache() {
        log.info("刷新指标阈值配置缓存");
    }
}
