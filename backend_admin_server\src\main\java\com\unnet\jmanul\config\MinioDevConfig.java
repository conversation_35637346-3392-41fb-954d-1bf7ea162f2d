package com.unnet.jmanul.config;

import io.minio.MinioClient;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;

import java.util.concurrent.TimeUnit;

/**
 * MinIO开发环境配置类
 * 单节点模式，简化配置
 * 
 * <AUTHOR>
 * @since 2025-07-28
 */
@Slf4j
@Data
@Configuration
@Profile("dev")
@ConfigurationProperties(prefix = "spring.minio")
public class MinioDevConfig {

    /**
     * MinIO服务地址
     */
    private String host;

    /**
     * MinIO访问URL
     */
    private String url;

    /**
     * MinIO访问密钥
     */
    private String accessKey;

    /**
     * MinIO秘密密钥
     */
    private String secretKey;

    /**
     * MinIO存储桶名称
     */
    private String bucket;

    /**
     * 创建MinIO客户端 - 开发环境（单节点）
     */
    @Bean
    public MinioClient minioClient() {
        log.info("创建开发环境MinIO客户端 - 端点: {}", host);

        // 创建自定义的OkHttpClient，禁用缓存
        OkHttpClient httpClient = new OkHttpClient.Builder()
                .connectTimeout(30, TimeUnit.SECONDS)
                .writeTimeout(60, TimeUnit.SECONDS)
                .readTimeout(60, TimeUnit.SECONDS)
                .cache(null) // 禁用HTTP缓存，解决文件覆盖后下载旧文件的问题
                .build();

        MinioClient client = MinioClient.builder()
                .endpoint(host)
                .credentials(accessKey, secretKey)
                .httpClient(httpClient) // 使用自定义的HTTP客户端
                .build();

        log.info("开发环境MinIO客户端创建成功 - 主机: {}, 存储桶: {}, HTTP缓存: 已禁用", host, bucket);

        return client;
    }

    /**
     * 获取MinIO配置信息
     */
    public MinioProperties getMinioProperties() {
        MinioProperties properties = new MinioProperties();
        properties.setHost(host);
        properties.setUrl(url);
        properties.setAccessKey(accessKey);
        properties.setSecretKey(secretKey);
        properties.setBucket(bucket);
        properties.setClusterEnabled(false);
        return properties;
    }

    /**
     * MinIO属性类
     */
    @Data
    public static class MinioProperties {
        private String host;
        private String url;
        private String accessKey;
        private String secretKey;
        private String bucket;
        private Boolean clusterEnabled;
    }
}
