package com.unnet.jmanul.business.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.unnet.jmanul.business.entity.MetricThresholdConfig;
import com.unnet.jmanul.business.entity.dto.MetricThresholdConfigRequest;
import com.unnet.jmanul.business.entity.dto.MetricThresholdConfigResponse;
import com.unnet.jmanul.business.entity.dto.OptionDto;
import com.unnet.jmanul.business.mapper.MetricThresholdConfigMapper;
import com.unnet.jmanul.business.service.IMetricThresholdConfigService;
import com.unnet.jmanul.common.exceptions.BusinessException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 指标阈值配置服务实现类
 * 
 * <AUTHOR>
 * @since 2024-07-21
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MetricThresholdConfigServiceImpl extends ServiceImpl<MetricThresholdConfigMapper, MetricThresholdConfig> 
        implements IMetricThresholdConfigService {

    private final MetricThresholdConfigMapper metricThresholdConfigMapper;

    @Override
    public IPage<MetricThresholdConfigResponse> getConfigPage(Long current, Long size, 
                                                             String metricType, String metricName, 
                                                             Boolean isEnabled, String alertLevel) {
        Page<MetricThresholdConfig> page = new Page<>(current, size);
        IPage<MetricThresholdConfig> configPage = metricThresholdConfigMapper.selectPageWithConditions(
                page, metricType, metricName, isEnabled, alertLevel);
        
        // 转换为响应DTO
        IPage<MetricThresholdConfigResponse> responsePage = new Page<>(current, size, configPage.getTotal());
        List<MetricThresholdConfigResponse> responseList = configPage.getRecords().stream()
                .map(this::convertToResponse)
                .collect(Collectors.toList());
        responsePage.setRecords(responseList);
        
        return responsePage;
    }

    @Override
    public MetricThresholdConfigResponse getConfigById(Long id) {
        MetricThresholdConfig config = getById(id);
        if (config == null) {
            throw new BusinessException("指标配置不存在");
        }
        return convertToResponse(config);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createConfig(MetricThresholdConfigRequest request, String operator) {
        // 允许相同指标类型的多个配置，因为现在指标类型是用户自定义的中文描述
        // 如果需要唯一性检查，可以基于指标类型+指标名称的组合

        MetricThresholdConfig config = new MetricThresholdConfig();
        BeanUtils.copyProperties(request, config);
        config.setCreateBy(operator);
        config.setUpdateBy(operator);
        config.setCreateTime(LocalDateTime.now());
        config.setUpdateTime(LocalDateTime.now());

        boolean result = save(config);
        if (result) {
            log.info("创建指标阈值配置成功 - 指标类型: {}, 操作人: {}", request.getMetricType(), operator);
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateConfig(Long id, MetricThresholdConfigRequest request, String operator) {
        MetricThresholdConfig existingConfig = getById(id);
        if (existingConfig == null) {
            throw new BusinessException("指标配置不存在");
        }

        // 允许相同指标类型的多个配置，因为现在指标类型是用户自定义的中文描述
        // 如果需要唯一性检查，可以基于指标类型+指标名称的组合

        BeanUtils.copyProperties(request, existingConfig);
        existingConfig.setId(id);
        existingConfig.setUpdateBy(operator);
        existingConfig.setUpdateTime(LocalDateTime.now());

        boolean result = updateById(existingConfig);
        if (result) {
            log.info("更新指标阈值配置成功 - ID: {}, 指标类型: {}, 操作人: {}", id, request.getMetricType(), operator);
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteConfig(Long id) {
        boolean result = removeById(id);
        if (result) {
            log.info("删除指标阈值配置成功 - ID: {}", id);
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchDeleteConfig(List<Long> ids) {
        boolean result = removeByIds(ids);
        if (result) {
            log.info("批量删除指标阈值配置成功 - IDs: {}", ids);
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean toggleConfig(Long id, Boolean enabled, String operator) {
        MetricThresholdConfig config = getById(id);
        if (config == null) {
            throw new BusinessException("指标配置不存在");
        }

        config.setIsEnabled(enabled);
        config.setUpdateBy(operator);
        config.setUpdateTime(LocalDateTime.now());

        boolean result = updateById(config);
        if (result) {
            log.info("{}指标阈值配置成功 - ID: {}, 操作人: {}", enabled ? "启用" : "禁用", id, operator);
        }
        return result;
    }

    @Override
    public MetricThresholdConfig getConfigByMetricType(String metricType) {
        return metricThresholdConfigMapper.selectByMetricType(metricType);
    }

    @Override
    public List<MetricThresholdConfig> getAllEnabledConfigs() {
        return metricThresholdConfigMapper.selectAllEnabled();
    }

    @Override
    public List<OptionDto> getMetricTypeOptions() {
        // 从数据库查询所有不重复的指标类型
        List<String> metricTypes = metricThresholdConfigMapper.selectDistinctMetricTypes();
        return metricTypes.stream()
                .map(type -> new OptionDto(type, type)) // 指标类型现在直接是中文描述
                .collect(Collectors.toList());
    }

    @Override
    public List<OptionDto> getAlertLevelOptions() {
        return Arrays.stream(MetricThresholdConfig.AlertLevel.values())
                .map(level -> new OptionDto(level.getCode(), level.getDescription()))
                .collect(Collectors.toList());
    }

    /**
     * 转换为响应DTO
     */
    private MetricThresholdConfigResponse convertToResponse(MetricThresholdConfig config) {
        MetricThresholdConfigResponse response = new MetricThresholdConfigResponse();
        BeanUtils.copyProperties(config, response);

        // 指标类型现在直接是中文描述，不需要转换
        response.setMetricTypeDescription(config.getMetricType());
        
        try {
            MetricThresholdConfig.AlertLevel alertLevel = MetricThresholdConfig.AlertLevel.valueOf(config.getAlertLevel());
            response.setAlertLevelDescription(alertLevel.getDescription());
        } catch (Exception e) {
            response.setAlertLevelDescription(config.getAlertLevel());
        }
        
        return response;
    }
}
