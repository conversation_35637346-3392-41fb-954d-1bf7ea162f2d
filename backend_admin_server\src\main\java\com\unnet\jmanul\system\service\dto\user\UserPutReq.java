package com.unnet.jmanul.system.service.dto.user;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.*;

import java.io.Serializable;
import java.util.Date;

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class UserPutReq implements Serializable {

    private Boolean enable;

    private Boolean locked;

    private Date accountExpireDate;

    private Date credentialExpireDate;

    private String name;

    private String password;

}
