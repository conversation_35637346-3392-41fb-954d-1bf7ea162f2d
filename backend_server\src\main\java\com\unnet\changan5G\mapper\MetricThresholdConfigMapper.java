package com.unnet.changan5G.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.unnet.changan5G.entity.MetricThresholdConfigEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 指标阈值配置Mapper接口
 * 
 * <AUTHOR>
 * @since 2024-07-21
 */
@Mapper
public interface MetricThresholdConfigMapper extends BaseMapper<MetricThresholdConfigEntity> {

    /**
     * 根据指标类型查询启用的配置
     * 
     * @param metricType 指标类型
     * @return 配置信息
     */
    @Select("SELECT * FROM metric_threshold_config WHERE metric_type = #{metricType} AND is_enabled = 1")
    MetricThresholdConfigEntity selectEnabledByMetricType(@Param("metricType") String metricType);

    /**
     * 查询所有启用的配置
     * 
     * @return 配置列表
     */
    @Select("SELECT * FROM metric_threshold_config WHERE is_enabled = 1 ORDER BY sort_order ASC")
    List<MetricThresholdConfigEntity> selectAllEnabled();
}
