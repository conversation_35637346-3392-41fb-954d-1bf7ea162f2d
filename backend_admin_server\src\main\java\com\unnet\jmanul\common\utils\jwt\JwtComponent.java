package com.unnet.jmanul.common.utils.jwt;

import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTVerifier;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.exceptions.JWTVerificationException;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.unnet.jmanul.common.properties.AppProperties;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.time.Instant;
import java.util.Optional;

@Component
@Slf4j
@RequiredArgsConstructor
public class JwtComponent {

    private final AppProperties appProperties;

    private Algorithm algorithm = null;
    private JWTVerifier verifier = null;

    private Algorithm getAlgorithm() {
        if (algorithm == null) {
            algorithm = Algorithm.HMAC256(appProperties.getJwtSecret());
        }
        return algorithm;
    }

    private JWTVerifier getJwtVerifier() {
        if (verifier == null) {
            verifier = JWT.require(getAlgorithm()).build();
        }
        return verifier;
    }

    public IssueResult generate(IssueRequest req) {
        // Create a new JWT token string, with the username embedded in the payload
        Instant now = Instant.now();
        Instant expire = now.plusSeconds(appProperties.getJwtExpireSeconds());
        String token = JWT.create()
                .withIssuedAt(now)
                .withExpiresAt(expire)
                // A "claim" is a single payload value which we can set
                .withClaim("id", req.getId())
                .withClaim("name", req.getName())
                .withClaim("username", req.getUsername())
                .withClaim("accountSource", req.getAccountSource())
                .withClaim("refreshTokenDuration", 24)
                .sign(getAlgorithm());

        return IssueResult.builder()
                .code(200)
                .token(token)
                .expire(expire)
                .build();
    }

    public Optional<String> getToken(HttpServletRequest request) {
        String authorization = request.getHeader("Authorization");
        if (authorization != null && authorization.startsWith("Bearer ")) {
            return Optional.of(authorization.substring(7));
        }

        // Return empty if not found
        return Optional.empty();
    }

    public Optional<DecodedJWT> getValidatedToken(String token) {
        try {
            // If the token is successfully verified, return its value
            return Optional.of(getJwtVerifier().verify(token));
        } catch (JWTVerificationException e) {
            // If the token can't be verified, return an empty value
            return Optional.empty();
        }
    }

    // Gets the expiry timestamp from the request and returns true if it falls
    // within the allowed window, which starts at a given time before expiry
    // in this case, 30s
    public boolean isRefreshable(HttpServletRequest request) {
        Optional<String> token = getToken(request);
        if (token.isEmpty()) {
            return false;
        }
        Instant expiryTime = JWT.decode(token.get()).getExpiresAtAsInstant();
        Instant canBeRefreshedAfter = expiryTime.minusSeconds(appProperties.getJwtMaxRefreshWindowSeconds());
        return Instant.now().isAfter(canBeRefreshedAfter);
    }

    public Optional<String> getUsernameFromSecurityContext() {
        SecurityContext context = SecurityContextHolder.getContext();
        Object principal = context.getAuthentication().getPrincipal();

        if (principal instanceof String) {
            return Optional.of((String) principal);
        }
        return Optional.empty();
    }
}