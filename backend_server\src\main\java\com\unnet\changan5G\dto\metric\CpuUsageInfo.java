package com.unnet.changan5G.dto.metric;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * CPU使用率信息
 * 
 * <AUTHOR>
 * @date 2024-07-14
 */
@Data
@Schema(description = "CPU使用率信息")
public class CpuUsageInfo {

    @Schema(description = "逻辑CPU数量（核×线程）", example = "4")
    private String num;

    @Schema(description = "核心数", example = "4")
    private String core;

    @Schema(description = "每核线程数", example = "4")
    private String thread;

    @Schema(description = "用户态占用率", example = "46.3")
    private String user;

    @Schema(description = "内核态占用率", example = "9.7")
    private String sys;

    @Schema(description = "空闲占比", example = "43.7")
    private String idle;
}
