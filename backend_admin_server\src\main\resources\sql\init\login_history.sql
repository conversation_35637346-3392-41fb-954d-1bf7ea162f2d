-- ----------------------------
-- Table structure for login_history
-- ----------------------------
DROP TABLE IF EXISTS `login_history`;
CREATE TABLE `login_history`  (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `created_at` datetime(3) NULL DEFAULT NULL,
    `updated_at` datetime(3) NULL DEFAULT NULL,
    `deleted_at` bigint(20) UNSIGNED NULL DEFAULT 0,
    `username` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
    `ip_addr` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL,
    `location` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
    `browser` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
    `login_method` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL,
    `success` tinyint(1) NOT NULL,
    `created_by` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
    `updated_by` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
    PRIMARY KEY (`id`) USING BTREE,
    INDEX `idx_login_histories_deleted_at`(`deleted_at`) USING BTREE,
    INDEX `idx_login_histories_username`(`username`) USING BTREE,
    INDEX `idx_login_history_username`(`username`) USING BTREE,
    INDEX `idx_login_history_created_by`(`created_by`) USING BTREE,
    INDEX `idx_login_history_updated_by`(`updated_by`) USING BTREE,
    INDEX `idx_login_history_deleted_at`(`deleted_at`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;
