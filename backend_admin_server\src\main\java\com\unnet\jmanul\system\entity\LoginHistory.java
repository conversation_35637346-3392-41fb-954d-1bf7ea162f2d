package com.unnet.jmanul.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.unnet.jmanul.common.entity.BaseEntity;

import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@TableName("login_history")
@ApiModel(value = "LoginHistory对象", description = "")
public class LoginHistory extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("username")
    private String username;

    @TableField("ip_addr")
    private String ipAddr;

    @TableField("location")
    private String location;

    @TableField("browser")
    private String browser;

    @TableField("login_method")
    private String loginMethod;

    @TableField("success")
    private Boolean success;

}
