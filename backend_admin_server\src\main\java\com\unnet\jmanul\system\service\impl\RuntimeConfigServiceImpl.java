package com.unnet.jmanul.system.service.impl;

import com.unnet.jmanul.system.entity.RuntimeConfig;
import com.unnet.jmanul.system.mapper.RuntimeConfigMapper;
import com.unnet.jmanul.system.service.IRuntimeConfigService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-07
 */
@Service
public class RuntimeConfigServiceImpl extends ServiceImpl<RuntimeConfigMapper, RuntimeConfig> implements IRuntimeConfigService {

}
