# 实时指标页面功能改进

## 概述

根据需求，对实时指标页面进行了以下改进：

1. **默认显示最近1小时数据**：页面加载时默认显示最近1小时的指标数据
2. **使用receiveTime作为横坐标**：优先使用JSON字段中的`receiveTime`，仅显示时分秒
3. **增强的鼠标悬停提示**：移动到图表上的点时，显示详细的指标信息
4. **智能时间范围过滤**：根据选择的时间范围动态过滤数据

## 功能详情

### 1. 默认时间范围

```javascript
// 默认显示最近1小时
const currentTimeRange = ref('最近1小时')
const maxHistoryPoints = ref(60) // 60个数据点
```

**行为**：
- 页面加载时获取今天凌晨0点到现在的所有数据
- 默认过滤并显示最近1小时的数据
- 用户可以通过时间范围选择器切换到其他范围

### 2. 横坐标时间显示

```javascript
// 使用receiveTime，优先级：receiveTime > metricTime > timestamp
const receiveTime = new Date(item.receiveTime || item.metricTime || item.timestamp)

// 横坐标格式：仅显示时分秒
xAxis: {
  data: timePoints.map(time => {
    return time.toLocaleTimeString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    })
  })
}
```

**特点**：
- 优先使用`receiveTime`字段作为时间基准
- 横坐标统一显示为"HH:mm:ss"格式
- 自动调整标签间隔避免重叠

### 3. 增强的Tooltip显示

鼠标悬停时显示的详细信息包括：

#### 基本信息
- **时间**：当前数据点的时间（HH:mm:ss格式）
- **接收时间**：原始的receiveTime字符串
- **当前图表指标**：显示在图表中的所有指标及其数值

#### 详细指标信息
- **CPU信息**：
  - CPU用户态使用率
  - CPU系统态使用率  
  - CPU空闲率
- **温度信息**：
  - SOC温度
  - GPU温度
- **内存信息**：
  - 内存已用（GB）
  - 内存可用（GB）
- **磁盘信息**：
  - 磁盘设备名称
  - 磁盘已用空间（GB）
  - 磁盘可用空间（GB）
- **数据文件信息**：
  - 采集文件数量
  - 压缩文件数量
- **系统信息**：
  - 系统运行时间

### 4. 时间范围过滤

```javascript
const filterDataByTimeRange = (data) => {
  const now = new Date()
  let startTime
  
  switch (currentTimeRange.value) {
    case '最近10分钟':
      startTime = new Date(now.getTime() - 10 * 60 * 1000)
      break
    case '最近1小时':
      startTime = new Date(now.getTime() - 60 * 60 * 1000)
      break
    case '最近6小时':
      startTime = new Date(now.getTime() - 6 * 60 * 60 * 1000)
      break
    case '今日全天':
      startTime = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 0, 0, 0)
      break
    case '最近24小时':
      startTime = new Date(now.getTime() - 24 * 60 * 60 * 1000)
      break
  }
  
  return data.filter(item => item.time >= startTime && item.time <= now)
}
```

**支持的时间范围**：
- 最近10分钟
- 最近1小时（默认）
- 最近6小时
- 今日全天
- 最近24小时

## 数据处理流程

### 1. 初始化流程
```
页面加载 → 获取今日全部历史数据 → 按默认时间范围过滤 → 显示图表 → 启动10秒轮询
```

### 2. 时间范围切换流程
```
用户选择新时间范围 → 重新过滤历史数据 → 更新图表显示
```

### 3. 实时数据更新流程
```
每10秒轮询 → 获取最新数据 → 添加到历史记录 → 按当前时间范围过滤 → 更新图表
```

## API调用

### 获取历史数据
```http
GET /api/v1/admin/terminal/metrics/device/{identityMac}/range
?startTime=2025-07-28 00:00:00
&endTime=2025-07-28 23:59:59
&page=0
&size=1000
```

### 获取最新数据（每10秒）
```http
GET /api/v1/admin/terminal/metrics/device/{identityMac}/latest
```

## 数据结构

### 历史数据点结构
```javascript
{
  time: Date,                    // 用于图表的时间对象
  receiveTime: "2025-07-28 21:56:00", // 原始接收时间字符串
  metricTime: "2025-07-28 21:56:00",  // 原始指标时间字符串
  rawData: { /* 原始ES数据 */ },      // 完整的原始数据
  
  // 图表显示的指标
  cpuUsage: 43.2,
  cpuTemp: 89.69,
  memory: 50.9,
  disk: 77,
  
  // 详细指标（用于tooltip）
  cpuUserUsage: 46.3,
  cpuSystemUsage: 9.7,
  socTemp: 58.888,
  gpuTemp: 57.777,
  memoryUsed: 1.89,
  memoryAvailable: 1.87,
  // ... 其他指标
}
```

## 用户体验改进

### 1. 默认视图优化
- 页面加载时直接显示最近1小时的数据，避免数据过多导致的性能问题
- 横坐标清晰显示时分秒，便于精确查看时间点

### 2. 交互体验增强
- 鼠标悬停显示丰富的详细信息，无需切换页面即可查看完整指标
- 时间范围切换响应迅速，立即过滤和显示对应数据

### 3. 数据展示优化
- 使用receiveTime确保时间的准确性
- 智能的标签间隔避免横坐标拥挤
- 详细的tooltip信息帮助用户理解每个数据点的完整状态

## 测试验证

### 1. 功能测试
使用测试页面验证：
```
http://localhost:5173/test_time_range_filter.html
```

### 2. 手动测试步骤
1. 访问实时指标页面
2. 验证默认显示最近1小时数据
3. 切换不同时间范围，确认数据正确过滤
4. 鼠标悬停在图表上，检查tooltip显示的详细信息
5. 等待10秒，确认新数据自动添加

### 3. 调试工具
```javascript
// 在浏览器控制台执行
debugRealTimeMetrics()

// 检查当前数据状态
console.log('当前时间范围:', currentTimeRange.value)
console.log('历史数据数量:', metricsHistory.value.length)
console.log('数据时间范围:', {
  start: metricsHistory.value[0]?.receiveTime,
  end: metricsHistory.value[metricsHistory.value.length - 1]?.receiveTime
})
```

## 性能优化

### 1. 数据量控制
- 默认显示1小时数据，减少初始渲染压力
- 智能过滤避免处理过多数据点
- 限制历史数据保存数量

### 2. 渲染优化
- 自动调整横坐标标签间隔
- 按需显示滑动条控件
- 优化tooltip渲染性能

### 3. 内存管理
- 及时清理超出范围的历史数据
- 避免重复保存原始数据
- 合理的数据结构设计

## 后续扩展

### 1. 可能的功能增强
- 支持自定义时间范围选择
- 添加数据导出功能
- 支持多设备对比显示

### 2. 性能优化方向
- 实现数据虚拟化显示
- 添加数据缓存机制
- 优化大数据量的处理性能
