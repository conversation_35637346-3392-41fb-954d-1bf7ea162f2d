/* 响应式工具类 */

/* 确保所有容器使用正确的盒模型 */
*, *::before, *::after {
  box-sizing: border-box;
}

/* 防止水平滚动 */
html {
  overflow-x: hidden;
}

body {
  overflow-x: hidden;
  width: 100%;
}

/* Element Plus 组件的全局响应式优化 */
.el-table {
  width: 100% !important;
}

.el-table__body-wrapper {
  overflow-x: auto;
}

/* 表格在小屏幕上的优化 */
@media (max-width: 768px) {
  .el-table {
    font-size: 12px;
  }
  
  .el-table .cell {
    padding: 0 4px;
  }
  
  .el-table th.el-table__cell,
  .el-table td.el-table__cell {
    padding: 8px 4px;
  }
  
  .el-button {
    padding: 6px 10px;
    font-size: 12px;
  }
  
  .el-button--small {
    padding: 4px 8px;
    font-size: 11px;
  }
  
  .el-tag {
    padding: 0 4px;
    font-size: 10px;
  }
  
  .el-tag--small {
    padding: 0 3px;
    font-size: 9px;
  }
}

/* 对话框响应式 */
@media (max-width: 768px) {
  .el-dialog {
    width: 90% !important;
    margin: 5vh auto;
  }
  
  .el-dialog__header {
    padding: 15px 20px 10px;
  }
  
  .el-dialog__body {
    padding: 10px 20px;
  }
  
  .el-dialog__footer {
    padding: 10px 20px 15px;
  }
}

@media (max-width: 480px) {
  .el-dialog {
    width: 95% !important;
    margin: 2vh auto;
  }
  
  .el-dialog__header {
    padding: 12px 15px 8px;
  }
  
  .el-dialog__body {
    padding: 8px 15px;
  }
  
  .el-dialog__footer {
    padding: 8px 15px 12px;
  }
}

/* 表单响应式 */
@media (max-width: 768px) {
  .el-form--inline .el-form-item {
    display: block;
    margin-right: 0;
    margin-bottom: 15px;
  }
  
  .el-form-item__content {
    margin-left: 0 !important;
  }
  
  .el-input {
    width: 100% !important;
  }
  
  .el-select {
    width: 100% !important;
  }
  
  .el-date-editor {
    width: 100% !important;
  }
}

/* 卡片响应式 */
@media (max-width: 768px) {
  .el-card {
    margin-bottom: 15px;
  }
  
  .el-card__header {
    padding: 15px 20px;
  }
  
  .el-card__body {
    padding: 15px 20px;
  }
}

@media (max-width: 480px) {
  .el-card__header {
    padding: 12px 15px;
  }
  
  .el-card__body {
    padding: 12px 15px;
  }
}

/* 分页响应式 */
@media (max-width: 768px) {
  .el-pagination {
    justify-content: center;
    flex-wrap: wrap;
  }
  
  .el-pagination .btn-prev,
  .el-pagination .btn-next,
  .el-pagination .el-pager li {
    margin: 2px;
  }
}

@media (max-width: 480px) {
  .el-pagination .el-pagination__sizes {
    margin-bottom: 10px;
  }
  
  .el-pagination .el-pagination__jump {
    margin-top: 10px;
  }
}

/* 工具类 */
.hidden-xs {
  display: block;
}

.hidden-sm {
  display: block;
}

.hidden-md {
  display: block;
}

@media (max-width: 480px) {
  .hidden-xs {
    display: none !important;
  }
}

@media (max-width: 768px) {
  .hidden-sm {
    display: none !important;
  }
}

@media (max-width: 1024px) {
  .hidden-md {
    display: none !important;
  }
}

/* 布局工具类 */
.w-full {
  width: 100% !important;
}

.flex-wrap {
  flex-wrap: wrap !important;
}

.flex-column {
  flex-direction: column !important;
}

.text-center {
  text-align: center !important;
}

.text-left {
  text-align: left !important;
}

.text-right {
  text-align: right !important;
}

/* 间距工具类 */
.mt-0 { margin-top: 0 !important; }
.mt-1 { margin-top: 0.25rem !important; }
.mt-2 { margin-top: 0.5rem !important; }
.mt-3 { margin-top: 1rem !important; }
.mt-4 { margin-top: 1.5rem !important; }
.mt-5 { margin-top: 3rem !important; }

.mb-0 { margin-bottom: 0 !important; }
.mb-1 { margin-bottom: 0.25rem !important; }
.mb-2 { margin-bottom: 0.5rem !important; }
.mb-3 { margin-bottom: 1rem !important; }
.mb-4 { margin-bottom: 1.5rem !important; }
.mb-5 { margin-bottom: 3rem !important; }

.p-0 { padding: 0 !important; }
.p-1 { padding: 0.25rem !important; }
.p-2 { padding: 0.5rem !important; }
.p-3 { padding: 1rem !important; }
.p-4 { padding: 1.5rem !important; }
.p-5 { padding: 3rem !important; } 