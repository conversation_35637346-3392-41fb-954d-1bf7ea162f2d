package com.unnet.jmanul.business.entity.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 指标阈值配置响应DTO
 * 
 * <AUTHOR>
 * @since 2024-07-21
 */
@Data
@Schema(description = "指标阈值配置响应")
public class MetricThresholdConfigResponse {

    @Schema(description = "配置ID")
    private Long id;

    @Schema(description = "指标类型")
    private String metricType;

    @Schema(description = "指标类型描述")
    private String metricTypeDescription;

    @Schema(description = "指标名称")
    private String metricName;

    @Schema(description = "指标描述")
    private String metricDescription;

    @Schema(description = "JSON字段路径")
    private String jsonFieldPath;

    @Schema(description = "阈值")
    private BigDecimal thresholdValue;

    @Schema(description = "阈值单位")
    private String thresholdUnit;

    @Schema(description = "比较操作符")
    private String comparisonOperator;

    @Schema(description = "告警级别")
    private String alertLevel;

    @Schema(description = "告警级别描述")
    private String alertLevelDescription;

    @Schema(description = "告警消息模板")
    private String alertMessage;

    @Schema(description = "是否启用")
    private Boolean isEnabled;

    @Schema(description = "排序")
    private Integer sortOrder;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "创建人")
    private String createBy;

    @Schema(description = "更新人")
    private String updateBy;

    @Schema(description = "备注")
    private String remark;
}
