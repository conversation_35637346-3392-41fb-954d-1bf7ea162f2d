package com.unnet.changan5G.service.impl;

import com.unnet.changan5G.dto.terminal.TerminalBasicInfo;
import com.unnet.changan5G.entity.TerminalBasicInfoEntity;
import com.unnet.changan5G.service.TerminalBasicInfoService;
import com.unnet.changan5G.service.TerminalCacheService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 终端缓存服务实现
 * 
 * <AUTHOR>
 * @date 2024-07-15
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class TerminalCacheServiceImpl implements TerminalCacheService {

    private final RedisTemplate<String, Object> redisTemplate;
    private final TerminalBasicInfoService terminalBasicInfoService;

    // 缓存键前缀
    private static final String TERMINAL_BASIC_INFO_PREFIX = "terminal:basic:";
    private static final String TERMINAL_LAST_REPORT_PREFIX = "terminal:last_report:";
    private static final String TERMINAL_STATUS_PREFIX = "terminal:status:";
    private static final String TERMINAL_REGISTERED_PREFIX = "terminal:registered:";
    private static final String ONLINE_DEVICES_SET = "terminal:online_devices";
    private static final String OFFLINE_DEVICES_SET = "terminal:offline_devices";
    
    // 新增：基于MAC地址的缓存键前缀
    private static final String DEVICE_ONLINE_PREFIX = "device:online:";
    private static final String DEVICE_INFO_PREFIX = "device:info:";
    private static final String DEVICE_LAST_REPORT_PREFIX = "device:last_report:";
    private static final String DEVICE_STATUS_PREFIX = "device:status:";

    // 缓存过期时间
    private static final long BASIC_INFO_EXPIRE_HOURS = 24; // 基本信息缓存24小时
    private static final long LAST_REPORT_EXPIRE_MINUTES = 60; // 最后上报时间缓存60分钟
    private static final long STATUS_EXPIRE_HOURS = 12; // 状态缓存12小时
    private static final long REGISTERED_EXPIRE_DAYS = 7; // 注册标记缓存7天

    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Override
    public void cacheTerminalBasicInfo(String deviceId, TerminalBasicInfo basicInfo) {
        try {
            if (deviceId == null || basicInfo == null) {
                log.warn("设备ID或基本信息为空，跳过缓存 - 设备: {}", deviceId);
                return;
            }

            String key = TERMINAL_BASIC_INFO_PREFIX + deviceId;
            redisTemplate.opsForValue().set(key, basicInfo, BASIC_INFO_EXPIRE_HOURS, TimeUnit.HOURS);
            log.debug("缓存终端基本信息成功 - 设备: {}", deviceId);
        } catch (Exception e) {
            log.error("缓存终端基本信息失败 - 设备: {}, 错误: {}", deviceId, e.getMessage(), e);
            // 缓存失败不影响主流程，继续执行
        }
    }

    @Override
    public TerminalBasicInfo getCachedTerminalBasicInfo(String deviceId) {
        try {
            String key = TERMINAL_BASIC_INFO_PREFIX + deviceId;
            Object cached = redisTemplate.opsForValue().get(key);
            if (cached instanceof TerminalBasicInfo) {
                log.debug("从缓存获取终端基本信息成功 - 设备: {}", deviceId);
                return (TerminalBasicInfo) cached;
            }
        } catch (Exception e) {
            log.error("从缓存获取终端基本信息失败 - 设备: {}, 错误: {}", deviceId, e.getMessage(), e);
        }
        return null;
    }

    @Override
    public void removeCachedTerminalBasicInfo(String deviceId) {
        try {
            String key = TERMINAL_BASIC_INFO_PREFIX + deviceId;
            redisTemplate.delete(key);
            log.debug("删除终端基本信息缓存成功 - 设备: {}", deviceId);
        } catch (Exception e) {
            log.error("删除终端基本信息缓存失败 - 设备: {}, 错误: {}", deviceId, e.getMessage(), e);
        }
    }

    @Override
    public void cacheLastReportTime(String deviceId, LocalDateTime reportTime) {
        try {
            String key = TERMINAL_LAST_REPORT_PREFIX + deviceId;
            String timeStr = reportTime.format(DATE_TIME_FORMATTER);
            redisTemplate.opsForValue().set(key, timeStr, LAST_REPORT_EXPIRE_MINUTES, TimeUnit.MINUTES);
            log.debug("缓存最后上报时间成功 - 设备: {}, 时间: {}", deviceId, timeStr);
        } catch (Exception e) {
            log.error("缓存最后上报时间失败 - 设备: {}, 错误: {}", deviceId, e.getMessage(), e);
        }
    }

    @Override
    public LocalDateTime getCachedLastReportTime(String deviceId) {
        try {
            String key = TERMINAL_LAST_REPORT_PREFIX + deviceId;
            Object cached = redisTemplate.opsForValue().get(key);
            if (cached != null) {
                String timeStr = cached.toString();
                LocalDateTime reportTime = LocalDateTime.parse(timeStr, DATE_TIME_FORMATTER);
                log.debug("从缓存获取最后上报时间成功 - 设备: {}, 时间: {}", deviceId, timeStr);
                return reportTime;
            }
        } catch (Exception e) {
            log.error("从缓存获取最后上报时间失败 - 设备: {}, 错误: {}", deviceId, e.getMessage(), e);
        }
        return null;
    }

    @Override
    public List<LocalDateTime> batchGetLastReportTime(List<String> deviceIds) {
        List<LocalDateTime> result = new ArrayList<>();
        try {
            List<String> keys = deviceIds.stream()
                    .map(deviceId -> TERMINAL_LAST_REPORT_PREFIX + deviceId)
                    .collect(Collectors.toList());
            
            List<Object> cached = redisTemplate.opsForValue().multiGet(keys);
            if (cached != null) {
                for (Object timeObj : cached) {
                    if (timeObj != null) {
                        try {
                            result.add(LocalDateTime.parse(timeObj.toString(), DATE_TIME_FORMATTER));
                        } catch (Exception e) {
                            result.add(null);
                        }
                    } else {
                        result.add(null);
                    }
                }
            }
            log.debug("批量获取最后上报时间成功 - 设备数量: {}", deviceIds.size());
        } catch (Exception e) {
            log.error("批量获取最后上报时间失败 - 错误: {}", e.getMessage(), e);
        }
        return result;
    }

    @Override
    public void cacheDeviceStatus(String deviceId, Integer status) {
        try {
            if (deviceId == null) {
                log.warn("设备ID为空，跳过缓存设备状态");
                return;
            }

            if (status == null) {
                log.warn("设备状态为空，默认设置为离线状态 - 设备: {}", deviceId);
                status = 0; // 默认设置为离线状态
            }

            String key = TERMINAL_STATUS_PREFIX + deviceId;
            redisTemplate.opsForValue().set(key, status, STATUS_EXPIRE_HOURS, TimeUnit.HOURS);

            // 同时更新在线/离线设备集合
            if (status == 1) {
                redisTemplate.opsForSet().add(ONLINE_DEVICES_SET, deviceId);
                redisTemplate.opsForSet().remove(OFFLINE_DEVICES_SET, deviceId);
            } else {
                redisTemplate.opsForSet().add(OFFLINE_DEVICES_SET, deviceId);
                redisTemplate.opsForSet().remove(ONLINE_DEVICES_SET, deviceId);
            }

            log.debug("缓存设备状态成功 - 设备: {}, 状态: {}", deviceId, status);
        } catch (Exception e) {
            log.error("缓存设备状态失败 - 设备: {}, 错误: {}", deviceId, e.getMessage(), e);
        }
    }

    @Override
    public Integer getCachedDeviceStatus(String deviceId) {
        try {
            String key = TERMINAL_STATUS_PREFIX + deviceId;
            Object cached = redisTemplate.opsForValue().get(key);
            if (cached != null) {
                Integer status = Integer.valueOf(cached.toString());
                log.debug("从缓存获取设备状态成功 - 设备: {}, 状态: {}", deviceId, status);
                return status;
            }
        } catch (Exception e) {
            log.error("从缓存获取设备状态失败 - 设备: {}, 错误: {}", deviceId, e.getMessage(), e);
        }
        return null;
    }

    @Override
    public void batchUpdateDeviceStatus(List<String> deviceIds, Integer status) {
        try {
            // 批量更新状态缓存
            Map<String, Object> statusMap = new HashMap<>();
            for (String deviceId : deviceIds) {
                statusMap.put(TERMINAL_STATUS_PREFIX + deviceId, status);
            }
            redisTemplate.opsForValue().multiSet(statusMap);
            
            // 批量更新在线/离线设备集合
            if (status == 1) {
                redisTemplate.opsForSet().add(ONLINE_DEVICES_SET, deviceIds.toArray());
                redisTemplate.opsForSet().remove(OFFLINE_DEVICES_SET, deviceIds.toArray());
            } else {
                redisTemplate.opsForSet().add(OFFLINE_DEVICES_SET, deviceIds.toArray());
                redisTemplate.opsForSet().remove(ONLINE_DEVICES_SET, deviceIds.toArray());
            }
            
            log.debug("批量更新设备状态成功 - 设备数量: {}, 状态: {}", deviceIds.size(), status);
        } catch (Exception e) {
            log.error("批量更新设备状态失败 - 错误: {}", e.getMessage(), e);
        }
    }

    @Override
    public Set<String> getOnlineDeviceIds() {
        try {
            Set<Object> members = redisTemplate.opsForSet().members(ONLINE_DEVICES_SET);
            if (members != null) {
                return members.stream().map(Object::toString).collect(Collectors.toSet());
            }
        } catch (Exception e) {
            log.error("获取在线设备ID失败 - 错误: {}", e.getMessage(), e);
        }
        return new HashSet<>();
    }

    @Override
    public Set<String> getOfflineDeviceIds() {
        try {
            Set<Object> members = redisTemplate.opsForSet().members(OFFLINE_DEVICES_SET);
            if (members != null) {
                return members.stream().map(Object::toString).collect(Collectors.toSet());
            }
        } catch (Exception e) {
            log.error("获取离线设备ID失败 - 错误: {}", e.getMessage(), e);
        }
        return new HashSet<>();
    }

    @Override
    public boolean isDeviceExistsInCache(String deviceId) {
        try {
            String key = TERMINAL_BASIC_INFO_PREFIX + deviceId;
            return Boolean.TRUE.equals(redisTemplate.hasKey(key));
        } catch (Exception e) {
            log.error("检查设备缓存存在性失败 - 设备: {}, 错误: {}", deviceId, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public void markDeviceAsRegistered(String identityMac) {
        try {
            String key = TERMINAL_REGISTERED_PREFIX + identityMac;
            redisTemplate.opsForValue().set(key, "1", REGISTERED_EXPIRE_DAYS, TimeUnit.DAYS);
            log.debug("标记设备已注册成功 - 设备: {}", identityMac);
        } catch (Exception e) {
            log.error("标记设备已注册失败 - 设备: {}, 错误: {}", identityMac, e.getMessage(), e);
        }
    }

    @Override
    public boolean isDeviceRegistered(String identityMac) {
        try {
            String key = TERMINAL_REGISTERED_PREFIX + identityMac;
            return Boolean.TRUE.equals(redisTemplate.hasKey(key));
        } catch (Exception e) {
            log.error("检查设备注册状态失败 - 设备: {}, 错误: {}", identityMac, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public void cleanExpiredCache() {
        // Redis会自动清理过期的键，这里可以添加一些自定义的清理逻辑
        log.info("执行缓存清理任务");
    }

    @Override
    public Object getCacheStatistics() {
        try {
            long onlineCount = redisTemplate.opsForSet().size(ONLINE_DEVICES_SET);
            long offlineCount = redisTemplate.opsForSet().size(OFFLINE_DEVICES_SET);
            
            return new Object() {
                public final long onlineDevices = onlineCount;
                public final long offlineDevices = offlineCount;
                public final long totalDevices = onlineCount + offlineCount;
                public final String timestamp = LocalDateTime.now().format(DATE_TIME_FORMATTER);
            };
        } catch (Exception e) {
            log.error("获取缓存统计信息失败 - 错误: {}", e.getMessage(), e);
            return null;
        }
    }

    @Override
    public void warmUpCache() {
        try {
            log.info("开始预热终端缓存...");

            // 从数据库加载所有设备的基本信息
            List<TerminalBasicInfoEntity> entities = terminalBasicInfoService.getDeviceLastUpdateTimes();

            if (entities == null || entities.isEmpty()) {
                log.info("数据库中暂无设备数据，跳过缓存预热");
                return;
            }

            int successCount = 0;
            for (TerminalBasicInfoEntity entity : entities) {
                try {
                    if (entity == null || entity.getDeviceId() == null) {
                        log.warn("跳过无效的设备实体");
                        continue;
                    }

                    // 转换为DTO
                    TerminalBasicInfo basicInfo = new TerminalBasicInfo();
                    BeanUtils.copyProperties(entity, basicInfo);

                    // 缓存基本信息
                    cacheTerminalBasicInfo(entity.getDeviceId(), basicInfo);

                    // 缓存状态（处理null值）
                    Integer status = entity.getStatus() != null ? entity.getStatus() : 0;
                    cacheDeviceStatus(entity.getDeviceId(), status);

                    // 标记为已注册
                    markDeviceAsRegistered(entity.getDeviceId());

                    successCount++;
                } catch (Exception e) {
                    log.error("预热设备缓存失败 - 设备: {}, 错误: {}",
                            entity != null ? entity.getDeviceId() : "unknown", e.getMessage());
                }
            }

            log.info("终端缓存预热完成 - 总设备数: {}, 成功预热: {}", entities.size(), successCount);
        } catch (Exception e) {
            log.error("终端缓存预热失败 - 错误: {}", e.getMessage(), e);
        }
    }

    @Override
    public void setDeviceOnlineWithTTL(String mac, long ttlSeconds) {
        try {
            if (mac == null || mac.trim().isEmpty()) {
                log.warn("MAC地址为空，跳过设置设备在线状态");
                return;
            }

            String key = DEVICE_ONLINE_PREFIX + mac;
            redisTemplate.opsForValue().set(key, "1", ttlSeconds, TimeUnit.SECONDS);
            log.debug("设置设备在线状态 - MAC: {}, TTL: {}秒", mac, ttlSeconds);
        } catch (Exception e) {
            log.error("设置设备在线状态失败 - MAC: {}, 错误: {}", mac, e.getMessage(), e);
        }
    }

    @Override
    public void cacheTerminalBasicInfoByMac(String mac, TerminalBasicInfo basicInfo) {
        try {
            if (mac == null || mac.trim().isEmpty() || basicInfo == null) {
                log.warn("MAC地址或基本信息为空，跳过缓存 - MAC: {}", mac);
                return;
            }

            String key = DEVICE_INFO_PREFIX + mac;
            redisTemplate.opsForValue().set(key, basicInfo, BASIC_INFO_EXPIRE_HOURS, TimeUnit.HOURS);
            log.debug("缓存设备基本信息 - MAC: {}", mac);
        } catch (Exception e) {
            log.error("缓存设备基本信息失败 - MAC: {}, 错误: {}", mac, e.getMessage(), e);
        }
    }

    @Override
    public TerminalBasicInfo getCachedTerminalBasicInfoByMac(String mac) {
        try {
            if (mac == null || mac.trim().isEmpty()) {
                log.warn("MAC地址为空，无法获取缓存信息");
                return null;
            }

            String key = DEVICE_INFO_PREFIX + mac;
            Object cached = redisTemplate.opsForValue().get(key);
            if (cached instanceof TerminalBasicInfo) {
                log.debug("从缓存获取设备基本信息成功 - MAC: {}", mac);
                return (TerminalBasicInfo) cached;
            }
        } catch (Exception e) {
            log.error("从缓存获取设备基本信息失败 - MAC: {}, 错误: {}", mac, e.getMessage(), e);
        }
        return null;
    }

    @Override
    public void cacheLastReportTimeByMac(String mac, LocalDateTime reportTime) {
        try {
            if (mac == null || mac.trim().isEmpty() || reportTime == null) {
                log.warn("MAC地址或上报时间为空，跳过缓存 - MAC: {}", mac);
                return;
            }

            String key = DEVICE_LAST_REPORT_PREFIX + mac;
            String timeStr = reportTime.format(DATE_TIME_FORMATTER);
            redisTemplate.opsForValue().set(key, timeStr, LAST_REPORT_EXPIRE_MINUTES, TimeUnit.MINUTES);
            log.debug("缓存设备最后上报时间 - MAC: {}, 时间: {}", mac, timeStr);
        } catch (Exception e) {
            log.error("缓存设备最后上报时间失败 - MAC: {}, 错误: {}", mac, e.getMessage(), e);
        }
    }

    @Override
    public LocalDateTime getCachedLastReportTimeByMac(String mac) {
        try {
            if (mac == null || mac.trim().isEmpty()) {
                log.warn("MAC地址为空，无法获取最后上报时间");
                return null;
            }

            String key = DEVICE_LAST_REPORT_PREFIX + mac;
            Object cached = redisTemplate.opsForValue().get(key);
            if (cached != null) {
                String timeStr = cached.toString();
                LocalDateTime reportTime = LocalDateTime.parse(timeStr, DATE_TIME_FORMATTER);
                log.debug("从缓存获取设备最后上报时间成功 - MAC: {}, 时间: {}", mac, timeStr);
                return reportTime;
            }
        } catch (Exception e) {
            log.error("从缓存获取设备最后上报时间失败 - MAC: {}, 错误: {}", mac, e.getMessage(), e);
        }
        return null;
    }

    @Override
    public void removeDeviceOnlineStatus(String mac) {
        try {
            if (mac == null || mac.trim().isEmpty()) {
                log.warn("MAC地址为空，无法移除设备在线状态");
                return;
            }

            String key = DEVICE_ONLINE_PREFIX + mac;
            redisTemplate.delete(key);
            log.debug("移除设备在线状态 - MAC: {}", mac);
        } catch (Exception e) {
            log.error("移除设备在线状态失败 - MAC: {}, 错误: {}", mac, e.getMessage(), e);
        }
    }

    @Override
    public void cacheDeviceStatusByMac(String mac, int status) {
        try {
            if (mac == null || mac.trim().isEmpty()) {
                log.warn("MAC地址为空，跳过缓存设备状态");
                return;
            }

            String key = DEVICE_STATUS_PREFIX + mac;
            redisTemplate.opsForValue().set(key, status, STATUS_EXPIRE_HOURS, TimeUnit.HOURS);
            log.debug("缓存设备状态 - MAC: {}, 状态: {}", mac, status);
        } catch (Exception e) {
            log.error("缓存设备状态失败 - MAC: {}, 错误: {}", mac, e.getMessage(), e);
        }
    }
}
