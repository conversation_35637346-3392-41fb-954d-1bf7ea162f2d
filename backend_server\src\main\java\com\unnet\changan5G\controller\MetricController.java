package com.unnet.changan5G.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.unnet.changan5G.dto.ApiResp;
import com.unnet.changan5G.dto.MetricRequestBody;
import com.unnet.changan5G.dto.metric.*;
import com.unnet.changan5G.mapper.MetricTestMapper;
import com.unnet.changan5G.model.MetricTest;
import com.unnet.changan5G.service.MetricKafkaProducer;
import com.unnet.changan5G.service.LogFileService;
import com.unnet.changan5G.entity.LogFile;
import com.unnet.changan5G.util.MetricJsonUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/agent")
@Slf4j
@RequiredArgsConstructor
@Tag(name = "指标上送接口", description = "接收终端上送的指标信息")
public class MetricController {

    private final MetricJsonUtil metricJsonUtil;
    private final MetricKafkaProducer metricKafkaProducer;
    private final ObjectMapper objectMapper;

    @Autowired
    private MetricTestMapper metricTestMapper;

    @Autowired
    private LogFileService logFileService;

    /**
     * 上传指标信息
     */
    @PostMapping("/report")
    @Operation(summary = "指标信息上传", description = "接收终端上送的指标信息并发送到Kafka")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "指标信息接收成功"),
        @ApiResponse(responseCode = "400", description = "请求参数错误"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    public ApiResp<String> reportMetrics(
            @Parameter(name = "metricRequestBody", description = "终端指标信息JSON字符串", required = true)
            @RequestBody String metricRequestBody) {

        log.info("接收到指标数据上送请求");
        log.debug("上送数据为：{}", metricRequestBody);

        try {
            // 1. 获取当前时间作为指标采集时间和告警时间的基准
            LocalDateTime metricCollectTime = LocalDateTime.now();
            String timeStr = metricCollectTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            log.info("接收到指标数据 - 上报时间: {}", timeStr);

            // 2. 解析JSON获取MAC地址用作Kafka消息key
            String identityMac = extractIdentityMac(metricRequestBody);
            log.info("上送指标设备MAC地址:{}",identityMac);
            // 3. 创建包含时间戳的消息数据（简化版本）
            Map<String, Object> messageData = new HashMap<>();
            messageData.put("originalData", metricRequestBody);
            messageData.put("metricCollectTime", timeStr);
            messageData.put("receiveTime", timeStr);

            String messageWithTimestamp = objectMapper.writeValueAsString(messageData);

            // 4. 发送数据到Kafka进行异步处理
            metricKafkaProducer.sendRawJsonData(messageWithTimestamp, identityMac);
            log.info("指标数据已发送到Kafka - 设备MAC: {}, 上报时间: {}", identityMac, timeStr);

            return ApiResp.success("指标信息接收成功并已发送到Kafka进行处理");

        } catch (Exception e) {
            log.error("处理指标数据时发生异常: {}", e.getMessage(), e);
            return ApiResp.error(500, "处理指标数据时发生异常: " + e.getMessage());
        }
    }

    /**
     * 从JSON字符串中提取MAC地址并格式化
     */
    private String extractIdentityMac(String jsonData) {
        try {
            MetricRequestBody metricData = objectMapper.readValue(jsonData, MetricRequestBody.class);
            String rawMac = metricData.getIdentityMac();
            if (rawMac != null && !rawMac.isEmpty()) {
                // 格式化MAC地址（每两个字符添加冒号）
                return formatMacAddress(rawMac);
            }
            return "unknown";
        } catch (Exception e) {
            log.warn("提取MAC地址失败，使用默认值: {}", e.getMessage());
            return "unknown";
        }
    }

    /**
     * 格式化MAC地址（每两个字符添加冒号）
     */
    private String formatMacAddress(String rawMac) {
        if (rawMac == null || rawMac.length() != 12) {
            return rawMac;
        }
        StringBuilder formatted = new StringBuilder();
        for (int i = 0; i < rawMac.length(); i += 2) {
            if (i > 0) {
                formatted.append(":");
            }
            formatted.append(rawMac.substring(i, i + 2));
        }
        return formatted.toString();
    }

    /**
     * 上传日志文件
     */
    @PostMapping("/upload-log")
    @Operation(summary = "日志文件上传", description = "接收终端上传的日志文件")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "上传成功"),
            @ApiResponse(responseCode = "400", description = "请求参数错误"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    public ApiResp<String> uploadLogFile(
            @Parameter(description = "终端MAC地址", required = true)
            @RequestParam(value = "identityMac", required = true) String identityMac,
            @Parameter(description = "日志文件", required = true)
            @RequestParam(value = "file", required = true) MultipartFile file) {

        try {
            log.info("接收日志文件上传请求 - 设备MAC: {}, 文件名: {}, 文件大小: {} bytes",
                    identityMac, file.getOriginalFilename(), file.getSize());

            // 验证参数
            if (identityMac == null || identityMac.trim().isEmpty()) {
                return ApiResp.error(400, "终端MAC地址不能为空");
            }

            if (file.isEmpty()) {
                return ApiResp.error(400, "上传文件不能为空");
            }

            // 文件大小限制（100MB）
            long maxFileSize = 100 * 1024 * 1024;
            if (file.getSize() > maxFileSize) {
                return ApiResp.error(400, "文件大小不能超过100MB");
            }

            // 上传文件
            LogFile logFile = logFileService.uploadLogFile(identityMac, file, null);

            log.info("日志文件上传成功 - 设备MAC: {}, 文件ID: {}, 存储路径: {}",
                    identityMac, logFile.getId(), logFile.getStoragePath());

            return ApiResp.success("日志文件上传成功");

        } catch (Exception e) {
            log.error("日志文件上传失败 - 设备MAC: {}, 文件名: {}, 错误: {}",
                    identityMac, file.getOriginalFilename(), e.getMessage(), e);
            return ApiResp.error(500, "日志文件上传失败: " + e.getMessage());
        }
    }

}
