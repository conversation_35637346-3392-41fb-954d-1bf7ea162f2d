package com.unnet.jmanul.config;

import com.unnet.jmanul.service.MinioClusterService;
import com.unnet.jmanul.service.MinioService;
import io.minio.MinioClient;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import static org.junit.jupiter.api.Assertions.*;

/**
 * MinIO配置测试类
 * 
 * <AUTHOR>
 * @since 2025-07-28
 */
public class MinioConfigTest {

    /**
     * 开发环境配置测试
     */
    @SpringBootTest
    @ActiveProfiles("dev")
    static class DevConfigTest {

        @Autowired(required = false)
        private MinioDevConfig minioDevConfig;

        @Autowired(required = false)
        private MinioProdConfig minioProdConfig;

        @Autowired
        private MinioClient minioClient;

        @Autowired
        private MinioService minioService;

        @Autowired
        private MinioClusterService minioClusterService;

        @Test
        void testDevConfigLoaded() {
            // 验证开发环境配置已加载
            assertNotNull(minioDevConfig, "开发环境MinIO配置应该被加载");
            assertNull(minioProdConfig, "生产环境MinIO配置不应该被加载");
            
            // 验证配置属性
            assertNotNull(minioDevConfig.getHost(), "MinIO主机地址不能为空");
            assertNotNull(minioDevConfig.getAccessKey(), "MinIO访问密钥不能为空");
            assertNotNull(minioDevConfig.getBucket(), "MinIO存储桶不能为空");
            
            // 验证MinIO客户端已创建
            assertNotNull(minioClient, "MinIO客户端应该被创建");
        }

        @Test
        void testDevServices() {
            // 验证服务已注入
            assertNotNull(minioService, "MinIO服务应该被注入");
            assertNotNull(minioClusterService, "MinIO集群服务应该被注入");
            
            // 验证集群状态
            var clusterStatus = minioClusterService.getClusterStatus();
            assertNotNull(clusterStatus, "集群状态不能为空");
            assertEquals("development", clusterStatus.get("environment"), "应该是开发环境");
            assertEquals(false, clusterStatus.get("clusterEnabled"), "开发环境不应该启用集群");
        }
    }

    /**
     * 生产环境配置测试
     */
    @SpringBootTest
    @ActiveProfiles("prod")
    static class ProdConfigTest {

        @Autowired(required = false)
        private MinioDevConfig minioDevConfig;

        @Autowired(required = false)
        private MinioProdConfig minioProdConfig;

        @Autowired
        private MinioClient minioClient;

        @Autowired
        private MinioService minioService;

        @Autowired
        private MinioClusterService minioClusterService;

        @Test
        void testProdConfigLoaded() {
            // 验证生产环境配置已加载
            assertNull(minioDevConfig, "开发环境MinIO配置不应该被加载");
            assertNotNull(minioProdConfig, "生产环境MinIO配置应该被加载");
            
            // 验证配置属性
            assertNotNull(minioProdConfig.getHosts(), "MinIO集群节点列表不能为空");
            assertFalse(minioProdConfig.getHosts().isEmpty(), "MinIO集群节点列表不能为空");
            assertEquals(3, minioProdConfig.getHosts().size(), "应该有3个集群节点");
            
            // 验证集群节点地址
            assertTrue(minioProdConfig.getHosts().contains("http://********:9000"), "应该包含节点1");
            assertTrue(minioProdConfig.getHosts().contains("http://********:9000"), "应该包含节点2");
            assertTrue(minioProdConfig.getHosts().contains("http://********:9000"), "应该包含节点3");
            
            // 验证认证信息
            assertEquals("admin", minioProdConfig.getAccessKey(), "访问密钥应该是admin");
            assertEquals("Changan5g.minio", minioProdConfig.getSecretKey(), "秘密密钥应该是Changan5g.minio");
            assertEquals("log-files", minioProdConfig.getBucket(), "存储桶应该是log-files");
            
            // 验证集群配置
            assertNotNull(minioProdConfig.getCluster(), "集群配置不能为空");
            assertTrue(minioProdConfig.getCluster().getEnabled(), "生产环境应该启用集群");
            assertEquals(3, minioProdConfig.getCluster().getRetryAttempts(), "重试次数应该是3");
            
            // 验证MinIO客户端已创建
            assertNotNull(minioClient, "MinIO客户端应该被创建");
        }

        @Test
        void testProdServices() {
            // 验证服务已注入
            assertNotNull(minioService, "MinIO服务应该被注入");
            assertNotNull(minioClusterService, "MinIO集群服务应该被注入");
            
            // 验证集群状态
            var clusterStatus = minioClusterService.getClusterStatus();
            assertNotNull(clusterStatus, "集群状态不能为空");
            assertEquals("production", clusterStatus.get("environment"), "应该是生产环境");
            assertEquals(true, clusterStatus.get("clusterEnabled"), "生产环境应该启用集群");
            assertEquals(3, clusterStatus.get("totalNodes"), "应该有3个节点");
        }
    }

    /**
     * 配置属性测试
     */
    @Test
    void testConfigurationProperties() {
        // 这个测试不依赖于特定的Profile
        // 可以验证配置类的基本结构
        
        // 验证开发环境配置类结构
        MinioDevConfig devConfig = new MinioDevConfig();
        devConfig.setHost("http://localhost:9000");
        devConfig.setAccessKey("test");
        devConfig.setSecretKey("test");
        devConfig.setBucket("test-bucket");
        
        var devProperties = devConfig.getMinioProperties();
        assertNotNull(devProperties, "开发环境配置属性不能为空");
        assertEquals(false, devProperties.getClusterEnabled(), "开发环境不应该启用集群");
        
        // 验证生产环境配置类结构
        MinioProdConfig prodConfig = new MinioProdConfig();
        prodConfig.setHosts(java.util.List.of("http://node1:9000", "http://node2:9000"));
        prodConfig.setAccessKey("admin");
        prodConfig.setSecretKey("password");
        prodConfig.setBucket("prod-bucket");
        
        var prodProperties = prodConfig.getMinioProperties();
        assertNotNull(prodProperties, "生产环境配置属性不能为空");
        assertEquals(true, prodProperties.getClusterEnabled(), "生产环境应该启用集群");
        assertNotNull(prodProperties.getCluster(), "集群配置不能为空");
    }
}
