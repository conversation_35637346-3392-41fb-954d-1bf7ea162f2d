-- 指标阈值配置表
-- 创建时间：2024-07-21
-- 说明：用于配置各种告警指标的阈值，替代硬编码的告警阈值

-- 指标阈值配置表
CREATE TABLE IF NOT EXISTS `metric_threshold_config` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '配置ID',
    `metric_type` varchar(50) NOT NULL COMMENT '指标类型：CPU_TEMPERATURE, MEMORY_USAGE, DISK_USAGE, DISK_DATA_USAGE, DISK_SYSTEM_USAGE, LICENSE_EXPIRY',
    `metric_name` varchar(100) NOT NULL COMMENT '指标名称',
    `metric_description` varchar(200) DEFAULT NULL COMMENT '指标描述',
    `threshold_value` decimal(10,2) NOT NULL COMMENT '阈值',
    `threshold_unit` varchar(20) NOT NULL COMMENT '阈值单位：%, °C, days等',
    `comparison_operator` varchar(10) NOT NULL DEFAULT '>=' COMMENT '比较操作符：>=, >, <=, <, =',
    `alert_level` varchar(20) NOT NULL DEFAULT 'MEDIUM' COMMENT '告警级别：LOW, MEDIUM, HIGH, CRITICAL',
    `alert_message` varchar(500) DEFAULT NULL COMMENT '告警消息模板',
    `is_enabled` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用：0-禁用，1-启用',
    `sort_order` int(11) DEFAULT '0' COMMENT '排序',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
    `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
    `remark` varchar(500) DEFAULT NULL COMMENT '备注',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_metric_type` (`metric_type`),
    KEY `idx_is_enabled` (`is_enabled`),
    KEY `idx_alert_level` (`alert_level`),
    KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='指标阈值配置表';

-- 插入默认的阈值配置数据（基于当前硬编码的值）
INSERT INTO `metric_threshold_config` (`metric_type`, `metric_name`, `metric_description`, `threshold_value`, `threshold_unit`, `comparison_operator`, `alert_level`, `alert_message`, `is_enabled`, `sort_order`, `create_by`, `remark`) VALUES
('CPU_TEMPERATURE', 'CPU温度', 'CPU温度过高告警', 85.00, '°C', '>=', 'HIGH', 'CPU温度过高，当前温度：{current_value}，阈值：{threshold_value}', 1, 1, 'system', '当CPU温度达到或超过85摄氏度时触发告警'),
('MEMORY_USAGE', '内存使用率', '内存使用率过高告警', 90.00, '%', '>=', 'HIGH', '内存使用率过高，当前使用率：{current_value}，阈值：{threshold_value}', 1, 2, 'system', '当内存使用率达到或超过90%时触发告警'),
('DISK_USAGE', '磁盘使用率', '磁盘使用率过高告警', 80.00, '%', '>=', 'MEDIUM', '磁盘使用率过高，当前使用率：{current_value}，阈值：{threshold_value}', 1, 3, 'system', '当磁盘使用率达到或超过80%时触发告警'),
('DISK_DATA_USAGE', '数据磁盘使用率', '数据磁盘使用率过高告警', 80.00, '%', '>=', 'MEDIUM', '数据磁盘使用率过高，当前使用率：{current_value}，阈值：{threshold_value}', 1, 4, 'system', '当数据磁盘使用率达到或超过80%时触发告警'),
('DISK_SYSTEM_USAGE', '系统磁盘使用率', '系统磁盘使用率过高告警', 80.00, '%', '>=', 'MEDIUM', '系统磁盘使用率过高，当前使用率：{current_value}，阈值：{threshold_value}', 1, 5, 'system', '当系统磁盘使用率达到或超过80%时触发告警'),
('LICENSE_EXPIRY', '软件授权过期', '软件授权即将过期告警', 30.00, 'days', '<=', 'CRITICAL', '软件授权即将过期，剩余天数：{current_value}，阈值：{threshold_value}', 1, 6, 'system', '当软件授权剩余天数少于或等于30天时触发告警');

COMMIT;
