-- 创建终端告警信息表（如果不存在）
-- 执行此脚本创建告警表和测试数据

-- 创建告警表
CREATE TABLE IF NOT EXISTS `terminal_alert_info` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `alert_id` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '告警唯一ID（UUID）',
  `device_id` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '告警终端设备ID',
  `alert_type` varchar(30) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '告警类型：CPU_TEMPERATURE,MEMORY_USAGE,DISK_USAGE,DISK_DATA_USAGE,DISK_SYSTEM_USAGE,LICENSE_EXPIRY',
  `alert_details` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '告警详情',
  `metric_name` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '告警指标名称',
  `threshold` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '告警阈值',
  `current_value` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '当前值',
  `alert_time` datetime DEFAULT NULL COMMENT '告警时间',
  `alert_status` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT 'ACTIVE' COMMENT '告警状态：ACTIVE,RESOLVED,ACKNOWLEDGED',
  `resolved_time` datetime DEFAULT NULL COMMENT '告警解决时间',
  `acknowledged_time` datetime DEFAULT NULL COMMENT '告警确认时间',
  `acknowledged_by` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '告警确认人',
  `resolve_comment` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '解决备注',
  `notification_sent` tinyint(1) DEFAULT '0' COMMENT '是否已发送通知：0-未发送，1-已发送',
  `notification_time` datetime DEFAULT NULL COMMENT '通知发送时间',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_alert_id` (`alert_id`),
  KEY `idx_device_id` (`device_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='终端告警信息表';

-- 插入测试数据
INSERT IGNORE INTO `terminal_alert_info` (`alert_id`, `device_id`, `alert_type`, `alert_details`, `metric_name`, `threshold`, `current_value`, `alert_time`, `alert_status`, `notification_sent`) VALUES
('alert-001', '_0fd94938951f4a64bb11c6817a81f7e7', 'CPU_TEMPERATURE', 'CPU温度超过阈值', 'cpu_temperature', '80', '85.9', '2025-07-16 20:25:30', 'ACTIVE', 0),
('alert-002', '_0fd94938951f4a64bb11c6817a81f7e7', 'MEMORY_USAGE', '内存使用率超过阈值', 'memory_usage_percent', '85', '92.5', '2025-07-16 20:28:15', 'ACTIVE', 1),
('alert-003', 'TERM-003', 'DISK_USAGE', '磁盘使用率超过阈值', 'disk_usage_percent', '90', '95.2', '2025-07-16 19:45:00', 'RESOLVED', 1),
('alert-004', '_0fd94938951f4a64bb11c6817a81f7e7', 'LICENSE_EXPIRY', '许可证即将过期', 'license_expiry_days', '30', '15', '2025-07-16 18:25:30', 'ACKNOWLEDGED', 1),
('alert-005', 'TERM-005', 'CPU_TEMPERATURE', 'CPU温度异常', 'cpu_temperature', '75', '82.3', '2025-07-16 17:15:20', 'RESOLVED', 1);

-- 更新已解决告警的解决时间
UPDATE `terminal_alert_info` SET 
  `resolved_time` = '2025-07-16 20:00:00',
  `resolve_comment` = '磁盘清理完成，使用率已降至正常水平'
WHERE `alert_id` = 'alert-003';

UPDATE `terminal_alert_info` SET 
  `resolved_time` = '2025-07-16 17:30:00',
  `resolve_comment` = '更换散热器，温度已恢复正常'
WHERE `alert_id` = 'alert-005';

-- 更新已确认告警的确认信息
UPDATE `terminal_alert_info` SET 
  `acknowledged_time` = '2025-07-16 18:30:00',
  `acknowledged_by` = '技术支持'
WHERE `alert_id` = 'alert-004';

COMMIT;
