package com.unnet.changan5G.dto.metric;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 应用版本信息
 * 
 * <AUTHOR>
 * @date 2024-07-14
 */
@Data
@Schema(description = "应用版本信息")
public class AppVersionInfo {

    @Schema(description = "控制器版本", example = "1.0.1")
    private String controller;

    @Schema(description = "检测器版本", example = "1.0.48")
    private String detector;

    @Schema(description = "Web版本", example = "1.0.55")
    private String web;

    @Schema(description = "代理版本", example = "1.0.0")
    private String agent;

    @Schema(description = "前端版本", example = "1.0.40")
    private String front;
}
