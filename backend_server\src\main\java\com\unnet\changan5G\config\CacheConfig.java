package com.unnet.changan5G.config;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.PropertyAccessor;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.jsontype.impl.LaissezFaireSubTypeValidator;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.CacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.serializer.Jackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.RedisSerializationContext;
import org.springframework.data.redis.serializer.StringRedisSerializer;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;

@Configuration
public class CacheConfig {

    @Autowired
    private AppProperties appProperties;

    @Bean
    public CacheManager cacheManager(RedisConnectionFactory redisConnectionFactory) {
        // 配置Jackson序列化器
        Jackson2JsonRedisSerializer<Object> jackson2JsonRedisSerializer = new Jackson2JsonRedisSerializer<>(Object.class);
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.ANY);
        objectMapper.activateDefaultTyping(LaissezFaireSubTypeValidator.instance, ObjectMapper.DefaultTyping.NON_FINAL);
        objectMapper.registerModule(new JavaTimeModule());
        objectMapper.disable(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
        jackson2JsonRedisSerializer.setObjectMapper(objectMapper);

        // 默认缓存配置
        RedisCacheConfiguration defaultCacheConfig = RedisCacheConfiguration.defaultCacheConfig()
                .entryTtl(Duration.ofMinutes(30))  // 默认30分钟过期
                .serializeKeysWith(RedisSerializationContext.SerializationPair.fromSerializer(new StringRedisSerializer()))
                .serializeValuesWith(RedisSerializationContext.SerializationPair.fromSerializer(jackson2JsonRedisSerializer))
                .disableCachingNullValues();  // 不缓存null值

        // 针对不同缓存设置不同的过期时间
        Map<String, RedisCacheConfiguration> cacheConfigurations = new HashMap<>();
        
        // 防重放攻击缓存 - 使用配置文件中的时间
        cacheConfigurations.put("nonceCache", defaultCacheConfig
                .entryTtl(Duration.ofMinutes(appProperties.getSecurity().getReplayProtection().getNonceCacheMinutes())));
        
        // 指标阈值配置缓存 - 1小时过期（配置变化不频繁）
        cacheConfigurations.put("metricThresholdConfig", defaultCacheConfig
                .entryTtl(Duration.ofHours(1)));
        cacheConfigurations.put("metricThresholdConfigs", defaultCacheConfig
                .entryTtl(Duration.ofHours(1)));
        cacheConfigurations.put("metricThresholdConfigsMap", defaultCacheConfig
                .entryTtl(Duration.ofHours(1)));
        
        // 终端基本信息缓存 - 2小时过期
        cacheConfigurations.put("terminalBasicInfo", defaultCacheConfig
                .entryTtl(Duration.ofHours(2)));
        
        // 设备状态缓存 - 10分钟过期（状态变化较频繁）
        cacheConfigurations.put("deviceStatus", defaultCacheConfig
                .entryTtl(Duration.ofMinutes(10)));
        
        // 最后上报时间缓存 - 5分钟过期（更新频繁）
        cacheConfigurations.put("lastReportTime", defaultCacheConfig
                .entryTtl(Duration.ofMinutes(5)));

        return RedisCacheManager.builder(redisConnectionFactory)
                .cacheDefaults(defaultCacheConfig)
                .withInitialCacheConfigurations(cacheConfigurations)
                .build();
    }
} 