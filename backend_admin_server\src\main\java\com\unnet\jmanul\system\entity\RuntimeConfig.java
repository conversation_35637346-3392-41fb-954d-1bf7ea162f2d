package com.unnet.jmanul.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.unnet.jmanul.common.entity.BaseEntity;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-07
 */
@TableName("runtime_config")
@ApiModel(value = "RuntimeConfig对象", description = "")
public class RuntimeConfig extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("自增ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("配置名称（app.auth.keypair.publicKey）")
    @TableField("name")
    private String name;

    @ApiModelProperty("配置值（0xffffabcd）")
    @TableField("value")
    private String value;

    @ApiModelProperty("描述（认证密钥对公钥）")
    @TableField("description")
    private String description;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    @Override
    public String toString() {
        return "RuntimeConfig{" +
        "id = " + id +
        ", name = " + name +
        ", value = " + value +
        ", description = " + description +
        "}";
    }
}
