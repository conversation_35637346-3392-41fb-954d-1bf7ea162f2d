package com.unnet.changan5G.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 终端告警信息实体类
 * 
 * <AUTHOR>
 * @date 2024-07-14
 */
@Data
@TableName("terminal_alert_info")
public class TerminalAlertInfoEntity {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("alert_id")
    private String alertId;

    /**
     * 告警终端设备ID
     */
    @TableField("identity_mac")
    private String identityMac;

    @TableField("alert_type")
    private String alertType;

    @TableField("alert_level")
    private String alertLevel;

    @TableField("alert_details")
    private String alertDetails;

    @TableField("metric_name")
    private String metricName;

    @TableField("threshold")
    private String threshold;

    @TableField("current_value")
    private String currentValue;

    @TableField("metric_id")
    private String metricId;

    @TableField("alert_time")
    private LocalDateTime alertTime;

    @TableField("alert_status")
    private String alertStatus;

    @TableField("resolved_time")
    private LocalDateTime resolvedTime;

    @TableField("acknowledged_time")
    private LocalDateTime acknowledgedTime;

    @TableField("acknowledged_by")
    private String acknowledgedBy;

    @TableField("resolve_comment")
    private String resolveComment;

    @TableField("notification_sent")
    private Integer notificationSent;

    @TableField("notification_time")
    private LocalDateTime notificationTime;

    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
}
