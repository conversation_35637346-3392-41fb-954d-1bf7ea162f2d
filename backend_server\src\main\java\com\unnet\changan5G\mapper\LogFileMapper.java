package com.unnet.changan5G.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.unnet.changan5G.entity.LogFile;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 日志文件记录 Mapper 接口 - 只负责上传功能
 *
 * <AUTHOR>
 * @since 2025-07-28
 */
@Mapper
public interface LogFileMapper extends BaseMapper<LogFile> {

    /**
     * 根据文件MD5查询是否存在重复文件
     *
     * @param identityMac 终端MAC地址
     * @param fileMd5 文件MD5值
     * @return 日志文件记录
     */
    LogFile selectByIdentityMacAndMd5(@Param("identityMac") String identityMac, @Param("fileMd5") String fileMd5);

    @Select("SELECT * FROM log_file WHERE identity_mac = #{identityMac} and original_filename = #{originalFilename}")
    LogFile selectByOriginalFilename(@Param("identityMac") String identityMac,@Param("originalFilename") String originalFilename);

}
