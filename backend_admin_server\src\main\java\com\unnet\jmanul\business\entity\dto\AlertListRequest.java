package com.unnet.jmanul.business.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 告警列表查询请求DTO
 */
@Data
@ApiModel(description = "告警列表查询请求")
public class AlertListRequest {

    @ApiModelProperty(value = "告警ID", example = "alert_001")
    private String alertId;

    @ApiModelProperty(value = "设备MAC地址", example = "12:e5:c3:d1:f6:e5")
    private String identityMac;

    @ApiModelProperty(value = "告警类型", example = "CPU告警")
    private String alertType;

    @ApiModelProperty(value = "告警级别", example = "HIGH")
    private String alertLevel;

    @ApiModelProperty(value = "告警状态", example = "ACTIVE")
    private String alertStatus;

    @ApiModelProperty(value = "页码，从1开始", example = "1")
    private Integer page = 1;

    @ApiModelProperty(value = "每页大小", example = "10")
    private Integer pageSize = 10;

    @ApiModelProperty(value = "开始时间", example = "2024-01-01 00:00:00")
    private String startTime;

    @ApiModelProperty(value = "结束时间", example = "2024-12-31 23:59:59")
    private String endTime;
}
