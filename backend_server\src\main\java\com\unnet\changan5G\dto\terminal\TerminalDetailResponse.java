package com.unnet.changan5G.dto.terminal;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.unnet.changan5G.document.TerminalMetricDocument;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 终端详情查询响应
 * 
 * <AUTHOR>
 * @date 2024-07-16
 */
@Data
@Schema(description = "终端详情查询响应")
public class TerminalDetailResponse {

    @Schema(description = "设备ID")
    private String deviceId;

    @Schema(description = "主机名")
    private String hostname;

    @Schema(description = "MAC地址")
    private String identityMac;

    @Schema(description = "状态：0-离线，1-在线")
    private Integer status;

    @Schema(description = "标签信息")
    private Object tags;

    @Schema(description = "自定义字段")
    private Object customFields;

    @Schema(description = "版本信息")
    private Object appVersion;

    @Schema(description = "过期时间")
    private String expiredDate;

    @Schema(description = "数据来源")
    private String dataSource;

    @Schema(description = "首次注册时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime firstRegisterTime;

    @Schema(description = "最后更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime lastUpdateTime;

    @Schema(description = "数据接收时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime receiveTime;

    @Schema(description = "最新指标信息")
    private TerminalMetricDocument latestMetric;

    @Schema(description = "CPE集合信息（用于拓扑图显示）")
    private Object groupUsage;

    @Schema(description = "所有CPE的带宽占用（单位：Byte/s）")
    private Long groupBps;
}
