package com.unnet.jmanul.business.controller;

import com.unnet.jmanul.business.document.TerminalMetricDocument;
import com.unnet.jmanul.business.service.ITerminalMetricElasticsearchService;
import com.unnet.jmanul.common.rest.ApiDef;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.Optional;

/**
 * 终端指标查询接口 - 长安5G业务模块
 * 
 * <AUTHOR>
 * @date 2024-07-16
 */
@RestController
@RequestMapping(ApiDef.V1_ADMIN_TERMINAL_METRICS)
@Slf4j
@RequiredArgsConstructor
@Api(tags = {"长安5G - 终端指标查询"})
public class TerminalMetricController {

    private final ITerminalMetricElasticsearchService terminalMetricElasticsearchService;

    /**
     * 根据设备MAC地址分页查询指标数据
     */
    @GetMapping("/device/{identityMac}")
    @ApiOperation(value = "根据设备MAC地址分页查询指标数据", notes = "按采集时间倒序返回指定设备的指标数据，每页10条")
    public ResponseEntity<Page<TerminalMetricDocument>> getMetricsByIdentityMac(
            @ApiParam(value = "设备MAC地址", required = true) @PathVariable String identityMac,
            @ApiParam(value = "页码，从0开始", defaultValue = "0") @RequestParam(defaultValue = "0") int page,
            @ApiParam(value = "每页大小，默认10条", defaultValue = "10") @RequestParam(defaultValue = "10") int size) {

        try {
            log.info("根据设备MAC地址分页查询指标数据 - 设备MAC: {}, 页码: {}, 每页大小: {}", identityMac, page, size);

            // 限制每页最大数量 - 为了支持大时间范围的全量数据获取，增加限制
            if (size > 10000) {
                size = 10000; // 增加到10000条，支持大时间范围的数据获取
            }

            Pageable pageable = PageRequest.of(page, size);
            Page<TerminalMetricDocument> result = terminalMetricElasticsearchService.getMetricsByIdentityMac(identityMac, pageable);
            
            log.info("查询完成 - 设备MAC: {}, 总数: {}, 当前页数据量: {}",
                    identityMac, result.getTotalElements(), result.getContent().size());

            return ResponseEntity.ok(result);

        } catch (Exception e) {
            log.error("根据设备MAC地址分页查询指标数据失败 - 设备MAC: {}, 错误: {}", identityMac, e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 根据设备MAC地址获取最新指标数据
     */
    @GetMapping("/device/{identityMac}/latest")
    @ApiOperation(value = "获取设备最新指标数据", notes = "返回指定设备的最新一条指标数据")
    public ResponseEntity<TerminalMetricDocument> getLatestMetricByIdentityMac(
            @ApiParam(value = "设备MAC地址", required = true) @PathVariable String identityMac) {

        try {
            log.info("获取设备最新指标数据 - 设备MAC: {}", identityMac);

            Optional<TerminalMetricDocument> result = terminalMetricElasticsearchService.getLatestMetricByIdentityMac(identityMac);

            if (result.isPresent()) {
                log.info("获取最新指标数据成功 - 设备MAC: {}, 采集时间: {}，运行时长:{}",
                        identityMac, result.get().getMetricTime(),result.get().getUptime());
                return ResponseEntity.ok(result.get());
            } else {
                log.warn("未找到设备的指标数据 - 设备MAC: {}", identityMac);
                return ResponseEntity.notFound().build();
            }
            
        } catch (Exception e) {
            log.error("获取设备最新指标数据失败 - 设备MAC: {}, 错误: {}", identityMac, e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 根据设备MAC地址和时间范围分页查询指标数据
     */
    @GetMapping("/device/{identityMac}/range")
    @Operation(summary = "根据时间范围查询指标数据", description = "查询指定设备在指定时间范围内的指标数据")
    public ResponseEntity<Page<TerminalMetricDocument>> getMetricsByIdentityMacAndTimeRange(
            @Parameter(name = "identityMac", description = "设备MAC地址", required = true)
            @PathVariable String identityMac,
            @Parameter(name = "startTime", description = "开始时间", required = true, example = "2024-07-14 00:00:00")
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
            @Parameter(name = "endTime", description = "结束时间", required = true, example = "2024-07-14 23:59:59")
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime,
            @Parameter(name = "page", description = "页码，从0开始", required = false)
            @RequestParam(defaultValue = "0") int page,
            @Parameter(name = "size", description = "每页大小，默认10条", required = false)
            @RequestParam(defaultValue = "10") int size) {

        try {
            log.info("根据设备MAC地址和时间范围分页查询指标数据 - 设备MAC: {}, 时间范围: {} - {}, 页码: {}, 每页大小: {}",
                    identityMac, startTime, endTime, page, size);

            // 参数验证
            if (startTime.isAfter(endTime)) {
                return ResponseEntity.badRequest().build();
            }

            // 限制每页最大数量 - 为了支持大时间范围的全量数据获取，增加限制
            if (size > 10000) {
                size = 10000; // 增加到10000条，支持大时间范围的数据获取
            }

            Pageable pageable = PageRequest.of(page, size);
            Page<TerminalMetricDocument> result = terminalMetricElasticsearchService
                    .getMetricsByIdentityMacAndTimeRange(identityMac, startTime, endTime, pageable);

            log.info("时间范围查询完成 - 设备MAC: {}, 总数: {}, 当前页数据量: {}",
                    identityMac, result.getTotalElements(), result.getContent().size());
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            log.error("根据设备MAC地址和时间范围分页查询指标数据失败 - 设备MAC: {}, 时间范围: {} - {}, 错误: {}",
                    identityMac, startTime, endTime, e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 根据设备MAC地址和时间范围获取全量指标数据（不分页）
     */
    @GetMapping("/device/{identityMac}/range/all")
    @Operation(summary = "根据时间范围获取全量指标数据", description = "查询指定设备在指定时间范围内的全量指标数据，不分页，最多返回10000条")
    public ResponseEntity<Object> getAllMetricsByIdentityMacAndTimeRange(
            @Parameter(name = "identityMac", description = "设备MAC地址", required = true)
            @PathVariable String identityMac,
            @Parameter(name = "startTime", description = "开始时间", required = true, example = "2024-07-14 00:00:00")
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
            @Parameter(name = "endTime", description = "结束时间", required = true, example = "2024-07-14 23:59:59")
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime) {

        try {
            log.info("根据设备MAC地址和时间范围获取全量指标数据 - 设备MAC: {}, 时间范围: {} - {}",
                    identityMac, startTime, endTime);

            // 参数验证
            if (startTime.isAfter(endTime)) {
                return ResponseEntity.badRequest().build();
            }

            // 先获取总数量
            long totalCount = terminalMetricElasticsearchService.countByIdentityMacAndTimeRange(identityMac, startTime, endTime);
            log.info("时间范围内总数据量: {}", totalCount);

            if (totalCount == 0) {
                // 返回空结果，但保持与分页接口相同的结构
                final String currentIdentityMac = identityMac;
                Object emptyResult = new Object() {
                    public final java.util.List<TerminalMetricDocument> content = java.util.Collections.emptyList();
                    public final long totalElements = 0;
                    public final boolean hasContent = false;
                };
                return ResponseEntity.ok(emptyResult);
            }

            // 限制最大返回数量
            int maxSize = Math.min((int) totalCount, 10000);
            Pageable pageable = PageRequest.of(0, maxSize, Sort.by(Sort.Direction.DESC, "metricTime"));

            Page<TerminalMetricDocument> result = terminalMetricElasticsearchService
                    .getMetricsByIdentityMacAndTimeRange(identityMac, startTime, endTime, pageable);

            log.info("全量查询完成 - 设备MAC: {}, 总数: {}, 返回数据量: {}",
                    identityMac, result.getTotalElements(), result.getContent().size());

            // 返回与分页接口相同的结构
            final String currentIdentityMac = identityMac;
            final Page<TerminalMetricDocument> finalResult = result;
            Object response = new Object() {
                public final java.util.List<TerminalMetricDocument> content = finalResult.getContent();
                public final long totalElements = finalResult.getTotalElements();
                public final boolean hasContent = finalResult.hasContent();
            };

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("根据设备MAC地址和时间范围获取全量指标数据失败 - 设备MAC: {}, 时间范围: {} - {}, 错误: {}",
                    identityMac, startTime, endTime, e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 获取设备指标统计信息
     */
    @GetMapping("/device/{identityMac}/stats")
    @Operation(summary = "获取设备指标统计信息", description = "获取指定设备的指标数据统计信息")
    public ResponseEntity<Object> getMetricStatsByIdentityMac(
            @Parameter(name = "identityMac", description = "设备MAC地址", required = true)
            @PathVariable String identityMac) {

        try {
            log.info("获取设备指标统计信息 - 设备MAC: {}", identityMac);

            // 获取总数据量
            long totalCount = terminalMetricElasticsearchService.countByIdentityMac(identityMac);

            // 获取最新数据
            Optional<TerminalMetricDocument> latestMetric = terminalMetricElasticsearchService.getLatestMetricByIdentityMac(identityMac);

            // 构建统计信息
            final String currentIdentityMac = identityMac; // 避免自引用
            Object stats = new Object() {
                public final long totalRecords = totalCount;
                public final String identityMac = currentIdentityMac;
                public final LocalDateTime latestMetricTime = latestMetric.map(TerminalMetricDocument::getMetricTime).orElse(null);
                public final LocalDateTime queryTime = LocalDateTime.now();
            };

            return ResponseEntity.ok(stats);

        } catch (Exception e) {
            log.error("获取设备指标统计信息失败 - 设备MAC: {}, 错误: {}", identityMac, e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 根据指标ID获取指标详情
     */
    @GetMapping("/metric/{metricId}")
    @Operation(summary = "根据指标ID获取指标详情", description = "根据指标ID获取完整的指标数据信息，用于告警关联查询")
    public ResponseEntity<TerminalMetricDocument> getMetricById(
            @Parameter(name = "metricId", description = "指标ID（Elasticsearch文档ID）", required = true)
            @PathVariable String metricId) {

        try {
            log.info("根据指标ID获取指标详情 - 指标ID: {}", metricId);

            Optional<TerminalMetricDocument> result = terminalMetricElasticsearchService.getMetricById(metricId);

            if (result.isPresent()) {
                TerminalMetricDocument metric = result.get();
                log.info("获取指标详情成功 - 指标ID: {}, 设备: {}, 采集时间: {}",
                        metricId, metric.getIdentityMac(), metric.getMetricTime());
                return ResponseEntity.ok(metric);
            } else {
                log.warn("未找到指标数据 - 指标ID: {}", metricId);
                return ResponseEntity.notFound().build();
            }

        } catch (Exception e) {
            log.error("根据指标ID获取指标详情失败 - 指标ID: {}, 错误: {}", metricId, e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }
}
