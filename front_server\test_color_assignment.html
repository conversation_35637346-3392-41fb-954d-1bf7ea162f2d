<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>颜色分配测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .color-box {
            display: inline-block;
            width: 20px;
            height: 20px;
            border-radius: 4px;
            margin-right: 10px;
            vertical-align: middle;
        }
        .metric-item {
            padding: 8px;
            margin: 4px 0;
            background: #f9f9f9;
            border-radius: 4px;
            display: flex;
            align-items: center;
        }
        .legend-color-1 { background: #1890ff; }
        .legend-color-2 { background: #ff4d4f; }
        .legend-color-3 { background: #52c41a; }
        .legend-color-4 { background: #faad14; }
        .legend-color-default { background: #999999; }
        
        .tooltip-demo {
            position: relative;
            background: rgba(0, 0, 0, 0.75);
            backdrop-filter: blur(8px);
            border-radius: 8px;
            padding: 12px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.25);
            border: 1px solid rgba(255, 255, 255, 0.15);
            min-width: 200px;
            max-width: 300px;
            opacity: 0.9;
            color: white;
            margin: 20px 0;
        }
        
        .tooltip-header {
            margin-bottom: 8px;
            padding-bottom: 8px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .tooltip-time {
            color: #ffffff;
            font-size: 14px;
            font-weight: bold;
            margin-bottom: 2px;
        }
        
        .tooltip-date {
            color: #cccccc;
            font-size: 12px;
        }
        
        .tooltip-content {
            display: flex;
            flex-direction: column;
            gap: 6px;
        }
        
        .tooltip-metric {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .metric-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            flex-shrink: 0;
        }
        
        .metric-info {
            flex: 1;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .metric-name {
            color: #ffffff;
            font-size: 12px;
        }
        
        .metric-value {
            font-size: 12px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <h1>实时指标页面颜色分配测试</h1>
    
    <div class="test-container">
        <h2>固定的4种颜色配置</h2>
        <div class="metric-item">
            <div class="color-box legend-color-1"></div>
            <span>颜色1 (#1890ff) - 蓝色，优先给上行速率</span>
        </div>
        <div class="metric-item">
            <div class="color-box legend-color-2"></div>
            <span>颜色2 (#ff4d4f) - 红色，温度类指标</span>
        </div>
        <div class="metric-item">
            <div class="color-box legend-color-3"></div>
            <span>颜色3 (#52c41a) - 绿色，使用率类指标</span>
        </div>
        <div class="metric-item">
            <div class="color-box legend-color-4"></div>
            <span>颜色4 (#faad14) - 橙色，其他指标</span>
        </div>
        <div class="metric-item">
            <div class="color-box legend-color-default"></div>
            <span>默认颜色 (#999999) - 灰色，未启用的指标</span>
        </div>
    </div>
    
    <div class="test-container">
        <h2>颜色分配优先级规则</h2>
        <ol>
            <li><strong>上行速率</strong> - 优先使用蓝色 (#1890ff)</li>
            <li><strong>温度类指标</strong> - 优先使用红色 (#ff4d4f)</li>
            <li><strong>使用率类指标</strong> - 优先使用绿色 (#52c41a)</li>
            <li><strong>其他指标</strong> - 按顺序分配剩余颜色</li>
        </ol>
    </div>
    
    <div class="test-container">
        <h2>示例场景测试</h2>
        
        <h3>场景1：默认配置（上行速率 + CPU温度 + CPU使用率）</h3>
        <div class="metric-item">
            <div class="color-box legend-color-1"></div>
            <span>上行速率 - 蓝色（优先分配）</span>
        </div>
        <div class="metric-item">
            <div class="color-box legend-color-2"></div>
            <span>CPU温度 - 红色（温度类优先）</span>
        </div>
        <div class="metric-item">
            <div class="color-box legend-color-3"></div>
            <span>CPU使用率 - 绿色（使用率类优先）</span>
        </div>
        
        <h3>场景2：无上行速率（CPU温度 + CPU使用率 + 内存使用率 + 磁盘使用率）</h3>
        <div class="metric-item">
            <div class="color-box legend-color-2"></div>
            <span>CPU温度 - 红色（温度类优先）</span>
        </div>
        <div class="metric-item">
            <div class="color-box legend-color-3"></div>
            <span>CPU使用率 - 绿色（使用率类优先）</span>
        </div>
        <div class="metric-item">
            <div class="color-box legend-color-4"></div>
            <span>内存使用率 - 橙色（第二个使用率类）</span>
        </div>
        <div class="metric-item">
            <div class="color-box legend-color-1"></div>
            <span>磁盘使用率 - 蓝色（剩余颜色分配）</span>
        </div>
    </div>
    
    <div class="test-container">
        <h2>半透明悬浮框效果演示</h2>
        <div class="tooltip-demo">
            <div class="tooltip-header">
                <div class="tooltip-time">14:32:15</div>
                <div class="tooltip-date">2024-01-15</div>
            </div>
            <div class="tooltip-content">
                <div class="tooltip-metric">
                    <div class="metric-indicator" style="background-color: #1890ff;"></div>
                    <div class="metric-info">
                        <div class="metric-name">上行速率</div>
                        <div class="metric-value" style="color: #1890ff;">25.6 MB/s</div>
                    </div>
                </div>
                <div class="tooltip-metric">
                    <div class="metric-indicator" style="background-color: #ff4d4f;"></div>
                    <div class="metric-info">
                        <div class="metric-name">CPU温度</div>
                        <div class="metric-value" style="color: #ff4d4f;">68.5°C</div>
                    </div>
                </div>
                <div class="tooltip-metric">
                    <div class="metric-indicator" style="background-color: #52c41a;"></div>
                    <div class="metric-info">
                        <div class="metric-name">CPU使用率</div>
                        <div class="metric-value" style="color: #52c41a;">45.2%</div>
                    </div>
                </div>
                <div class="tooltip-metric">
                    <div class="metric-indicator" style="background-color: #faad14;"></div>
                    <div class="metric-info">
                        <div class="metric-name">内存使用率</div>
                        <div class="metric-value" style="color: #faad14;">72.8%</div>
                    </div>
                </div>
            </div>
        </div>
        <p><em>注意：悬浮框现在具有半透明效果（opacity: 0.9）和模糊背景（backdrop-filter: blur(8px)）</em></p>
    </div>
    
    <div class="test-container">
        <h2>修改总结</h2>
        <ul>
            <li>✅ 悬浮框设置为半透明（opacity: 0.9，背景透明度从0.85降至0.75）</li>
            <li>✅ 悬浮框位置智能调整，防止超出图表边界</li>
            <li>✅ 固定4种差别较大的颜色：蓝色、红色、绿色、橙色</li>
            <li>✅ 上行速率优先使用蓝色，其他指标不能使用相同颜色</li>
            <li>✅ 温度类指标优先使用红色</li>
            <li>✅ 使用率类指标优先使用绿色</li>
            <li>✅ 动态颜色分配系统，支持指标启用/禁用时重新分配</li>
        </ul>
    </div>
</body>
</html>