package com.unnet.jmanul.business.entity.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Size;

/**
 * 终端更新请求
 * 
 * <AUTHOR>
 * @date 2024-07-16
 */
@Data
@Schema(description = "终端更新请求")
public class TerminalUpdateRequest {

    @Schema(description = "设备id", example = "ec_3568_25030031")
    @Size(max = 200, message = "设备id长度不能超过200个字符")
    private String deviceId;

    @Schema(description = "主机名", example = "ec_3568_25030031")
    @Size(max = 100, message = "主机名长度不能超过100个字符")
    private String hostname;

    @Schema(description = "过期时间", example = "2035-03-09 22:47:20")
    @Size(max = 30, message = "过期时间长度不能超过30个字符")
    private String expiredDate;

    @Schema(description = "标签信息", example = "{\"location\": \"北京\", \"department\": \"研发部\"}")
    private Object tags;

    @Schema(description = "自定义字段", example = "{\"vehicle_model\": \"长安CS75\", \"maintenance_contact\": \"张工\"}")
    private Object customFields;
}
