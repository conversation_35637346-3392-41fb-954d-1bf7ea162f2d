-- 日志文件记录表
CREATE TABLE `log_file` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `identity_mac` varchar(17) NOT NULL COMMENT '终端MAC地址（唯一标识）',
  `original_filename` varchar(255) NOT NULL COMMENT '原始文件名',
  `stored_filename` varchar(255) NOT NULL COMMENT '存储文件名（MinIO中的文件名）',
  `file_size` bigint(20) NOT NULL COMMENT '文件大小（字节）',
  `file_type` varchar(50) DEFAULT NULL COMMENT '文件类型/扩展名',
  `storage_path` varchar(500) NOT NULL COMMENT 'MinIO存储路径',
  `file_md5` varchar(32) NOT NULL COMMENT '文件MD5值',
  `upload_time` datetime NOT NULL COMMENT '上传时间',
  `description` varchar(500) DEFAULT NULL COMMENT '文件描述',
  `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '文件状态：0-正常，1-已删除',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(4) NOT NULL DEFAULT '0' COMMENT '逻辑删除标记：0-未删除，1-已删除',
  PRIMARY KEY (`id`),
  KEY `idx_identity_mac` (`identity_mac`),
  KEY `idx_upload_time` (`upload_time`),
  KEY `idx_file_md5` (`file_md5`),
  KEY `idx_identity_mac_md5` (`identity_mac`, `file_md5`),
  KEY `idx_status_deleted` (`status`, `deleted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='日志文件记录表';
