package com.unnet.changan5G.service;

import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

/**
 * 设备通知服务接口
 * 
 * <AUTHOR>
 * @date 2024-07-15
 */
public interface DeviceNotificationService {

    /**
     * 订阅设备通知
     *
     * @return SseEmitter 事件发射器
     */
    SseEmitter subscribe();

    /**
     * 订阅设备通知（带客户端IP）
     *
     * @param clientIp 客户端IP地址
     * @return SseEmitter 事件发射器
     */
    SseEmitter subscribe(String clientIp);
    
    /**
     * 发送设备注册通知
     *
     * @param identityMac 设备MAC地址
     * @param hostname 主机名
     */
    void sendDeviceRegisteredNotification(String identityMac, String hostname);

    /**
     * 发送设备上线通知
     *
     * @param identityMac 设备MAC地址
     * @param hostname 主机名
     */
    void sendDeviceOnlineNotification(String identityMac, String hostname);

    /**
     * 发送设备离线通知
     *
     * @param identityMac 设备MAC地址
     * @param hostname 主机名
     */
    void sendDeviceOfflineNotification(String identityMac, String hostname);
    
    /**
     * 发送统计信息更新通知
     *
     * @param onlineCount 在线设备数
     * @param offlineCount 离线设备数
     * @param alertCount 告警设备数
     */
    void sendStatisticsUpdateNotification(int onlineCount, int offlineCount, int alertCount);

    /**
     * 发送告警通知
     *
     * @param identityMac 设备MAC地址
     * @param hostname 主机名
     * @param alertType 告警类型
     * @param alertLevel 告警级别
     * @param message 告警消息
     * @param details 告警详情
     * @param currentValue 当前值
     * @param threshold 阈值
     */
    void sendAlertNotification(String identityMac, String hostname, String alertType,
                              String alertLevel, String message, String details,
                              String currentValue, String threshold);

    /**
     * 获取当前连接统计信息
     *
     * @return 连接统计信息字符串
     */
    String getConnectionStats();
}
