package com.unnet.jmanul.common.init;

import com.unnet.jmanul.common.properties.AppProperties;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

@Order(10)
@Component
@RequiredArgsConstructor
@Slf4j
public class EnvironmentRunner implements ApplicationRunner {

    private final AppProperties appProperties;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        log.info("/------------------------------------------------------------------------------\\");
        log.info("| Web container environment is ready.");
        log.info("| Development mode: {}", appProperties.isDevelopment());
        if (appProperties.isDevelopment()) {
            log.info("| JwtSecret: {}", appProperties.getJwtSecret());
        }
        log.info("| JwtExpireSeconds: {}", appProperties.getJwtExpireSeconds());
        log.info("| JwtMaxRefreshWindowSeconds: {}", appProperties.getJwtMaxRefreshWindowSeconds());
        log.info("\\------------------------------------------------------------------------------/");

    }
}
