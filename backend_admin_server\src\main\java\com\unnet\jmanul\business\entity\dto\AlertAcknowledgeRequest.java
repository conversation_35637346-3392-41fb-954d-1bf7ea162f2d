package com.unnet.jmanul.business.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 告警确认请求DTO
 */
@Data
@ApiModel(description = "告警确认请求")
public class AlertAcknowledgeRequest {

    @ApiModelProperty(value = "确认人", required = true, example = "admin")
    @NotBlank(message = "确认人不能为空")
    private String acknowledgedBy;

    @ApiModelProperty(value = "确认备注", example = "已确认此告警")
    private String comment;
}
