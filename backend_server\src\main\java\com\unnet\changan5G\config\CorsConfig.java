package com.unnet.changan5G.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * CORS跨域配置
 * 
 * <AUTHOR>
 * @date 2024-07-14
 */
@Configuration
public class CorsConfig implements WebMvcConfigurer {

    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**")
                .allowedOriginPatterns("*") // 允许所有域名，包括HTTPS
                .allowedMethods("GET", "POST", "PUT", "DELETE", "OPTIONS", "HEAD", "PATCH")
                .allowedHeaders("*") // 允许所有请求头，包括X-API-Key
                .exposedHeaders("*") // 暴露所有响应头
                .allowCredentials(true) // 允许发送Cookie和认证信息
                .maxAge(86400); // 预检请求缓存24小时
    }

    @Bean
    public CorsConfigurationSource corsConfigurationSource() {
        CorsConfiguration configuration = new CorsConfiguration();

        // 允许所有域名，包括HTTP和HTTPS
        configuration.addAllowedOriginPattern("*");

        // 允许所有HTTP方法
        configuration.addAllowedMethod("*");

        // 允许所有请求头，特别是API认证相关的头
        configuration.addAllowedHeader("*");

        // 暴露响应头，让前端能够读取
        configuration.addExposedHeader("*");

        // 修复：设置为false以避免CORS错误
        configuration.setAllowCredentials(false);

        // 预检请求的缓存时间（24小时）
        configuration.setMaxAge(86400L);

        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", configuration);

        return source;
    }
}
