//package com.unnet.jmanul.business.controller;
//
//import com.unnet.jmanul.business.service.ITerminalBasicInfoService;
//import com.unnet.jmanul.business.service.ITerminalMetricElasticsearchService;
//import com.unnet.jmanul.common.annotation.AnonymousAccess;
//import io.swagger.annotations.Api;
//import io.swagger.annotations.ApiOperation;
//import lombok.RequiredArgsConstructor;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.http.ResponseEntity;
//import org.springframework.web.bind.annotation.GetMapping;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RestController;
//
//import java.time.LocalDateTime;
//import java.util.HashMap;
//import java.util.Map;
//
///**
// * 长安5G健康检查Controller
// *
// * <AUTHOR>
// * @date 2024-07-16
// */
//@RestController
//@RequestMapping("/api/v1/admin/changan5g")
//@Slf4j
//@RequiredArgsConstructor
//@Api(tags = {"长安5G - 健康检查"})
//public class Changan5GHealthController {
//
//    private final ITerminalBasicInfoService terminalBasicInfoService;
//    private final ITerminalMetricElasticsearchService terminalMetricElasticsearchService;
//
//    /**
//     * 健康检查接口
//     */
//    @GetMapping("/health")
//    @ApiOperation(value = "长安5G模块健康检查", notes = "检查长安5G模块各组件状态")
//    @AnonymousAccess
//    public ResponseEntity<Map<String, Object>> healthCheck() {
//        Map<String, Object> result = new HashMap<>();
//
//        try {
//            // 检查数据库连接
//            long onlineCount = terminalBasicInfoService.getOnlineDeviceCount();
//            long offlineCount = terminalBasicInfoService.getOfflineDeviceCount();
//
//            // 检查Elasticsearch连接
//            boolean esIndexExists = terminalMetricElasticsearchService.indexExists();
//
//            result.put("status", "UP");
//            result.put("timestamp", LocalDateTime.now());
//            result.put("module", "长安5G业务模块");
//            result.put("database", Map.of(
//                "status", "UP",
//                "onlineDevices", onlineCount,
//                "offlineDevices", offlineCount
//            ));
//            result.put("elasticsearch", Map.of(
//                "status", esIndexExists ? "UP" : "DOWN",
//                "indexExists", esIndexExists
//            ));
//
//            log.info("长安5G健康检查完成 - 在线设备: {}, 离线设备: {}, ES索引: {}",
//                    onlineCount, offlineCount, esIndexExists);
//
//            return ResponseEntity.ok(result);
//
//        } catch (Exception e) {
//            log.error("长安5G健康检查失败: {}", e.getMessage(), e);
//
//            result.put("status", "DOWN");
//            result.put("timestamp", LocalDateTime.now());
//            result.put("error", e.getMessage());
//
//            return ResponseEntity.status(500).body(result);
//        }
//    }
//
//    /**
//     * 模块信息接口
//     */
//    @GetMapping("/info")
//    @ApiOperation(value = "长安5G模块信息", notes = "获取长安5G模块基本信息")
//    @AnonymousAccess
//    public ResponseEntity<Map<String, Object>> moduleInfo() {
//        Map<String, Object> result = new HashMap<>();
//
//        result.put("module", "长安5G业务模块");
//        result.put("version", "1.0.0");
//        result.put("description", "长安5G终端管理和指标查询功能");
//        result.put("features", new String[]{
//            "终端管理", "指标查询", "实时监控", "权限控制"
//        });
//        result.put("endpoints", Map.of(
//            "terminals", "/api/v1/admin/terminals",
//            "metrics", "/api/v1/admin/terminal/metrics"
//        ));
//        result.put("timestamp", LocalDateTime.now());
//
//        return ResponseEntity.ok(result);
//    }
//}
