package com.unnet.jmanul.system.controller;

import com.unnet.jmanul.common.properties.AppProperties;
import com.unnet.jmanul.common.service.impl.RedisService;
import com.unnet.jmanul.common.utils.CaptchaUtil;
import com.unnet.jmanul.common.utils.ClientUtils;
import com.unnet.jmanul.common.utils.DateTimeUtils;
import com.unnet.jmanul.common.utils.IdGenerator;
import com.unnet.jmanul.common.utils.jwt.IssueRequest;
import com.unnet.jmanul.common.utils.jwt.JwtComponent;
import com.unnet.jmanul.system.service.dto.auth.*;
import com.unnet.jmanul.system.service.dto.user.UserPostReq;
import com.unnet.jmanul.system.entity.LoginHistory;
import com.unnet.jmanul.system.entity.User;
import com.unnet.jmanul.system.service.ILoginHistoryService;
import com.unnet.jmanul.system.service.IUserService;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.awt.*;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Optional;

import static com.unnet.jmanul.common.rest.ApiDef.V1_AUTH;

@Slf4j
@RestController
@RequestMapping(V1_AUTH)
@RequiredArgsConstructor
@Api(tags = {"Z. 认证接口 AuthController"})
public class AuthController {

    private final AuthenticationManager authenticationManager;
    private final RedisService redisService;
    private final PasswordEncoder passwordEncoder;

    private final IUserService userService;
    private final ILoginHistoryService loginHistoryService;
    private final ClientUtils clientUtils;
    private final JwtComponent jwtComponent;
    private final AppProperties appProperties;

    private static final String CAPTCHA_REDIS_PREFIX = "CAPTCHA_";
    private static final long CAPTCHA_REDIS_TTL_SECONDS = 60;
    private static final String PASSWORD_MASK = "********";

    private String captchaKey(String seq) {
        return CAPTCHA_REDIS_PREFIX + seq;
    }

    /**
     * 编码中文消息以便在HTTP响应头中使用
     */
    private String encodeMessage(String message) {
        try {
            return URLEncoder.encode(message, StandardCharsets.UTF_8.toString());
        } catch (Exception e) {
            log.error("Failed to encode message: {}", message, e);
            return "Authentication failed";
        }
    }

    private void setCaptcha(String seq, String code) {
        redisService.set(captchaKey(seq), code, CAPTCHA_REDIS_TTL_SECONDS);
    }

    private boolean captchaInvalid(String seq, String code) {
        String store = (String) redisService.get(captchaKey(seq));
        log.info("check if captcha invalid, store={}, code={}", store, code);
        return store == null || !store.equalsIgnoreCase(code);
    }

    @GetMapping("/captcha")
    public ResponseEntity<CaptchaGetResp> registrationCaptcha(HttpServletResponse response) {
        try {
            String seq = IdGenerator.simpleUuid();

            var code = CaptchaUtil.newBuilder()
                    .setWidth(120)   //设置图片的宽度
                    .setHeight(35)   //设置图片的高度
                    .setSize(4)      //设置字符的个数
                    .setLines(8)     //设置干扰线的条数
                    .setFontSize(25) //设置字体的大小
                    .setTilt(true)   //设置是否需要倾斜
                    .setBackgroundColor(Color.WHITE) //设置验证码的背景颜色
                    .build()         //构建 CaptchaUtil 实例
                    .createImage();  //生成图片

            setCaptcha(seq, code.getLeft());

            CaptchaGetResp resp = new CaptchaGetResp();
            resp.setId(seq);
            resp.setImg(CaptchaUtil.convertBufferedImageToBase64(code.getRight(), "png"));

            log.info("captcha: [id->{}]", resp.getId());

            return ResponseEntity.ok(resp);
        } catch (Exception e) {
            log.error("", e);
        }

        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
    }

    @GetMapping("/publicKey")
    public ResponseEntity<String> getPublicKey() {
        Optional<String> publicKey = userService.getAuthPublicKey();
        return publicKey.map(ResponseEntity::ok).orElseGet(() -> ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build());
    }

    @PostMapping("/login")
    public ResponseEntity<LoginResp> login(
            @RequestParam("method") String loginMethod,
            @NotNull @RequestBody LoginReq req,
            HttpServletRequest request
    ) {
        LoginHistory history = new LoginHistory();
        history.setLoginMethod(loginMethod);
        history.setUsername(req.getUsername());
        history.setIpAddr(clientUtils.getIp(request));
        history.setLocation(clientUtils.getCityInfo(history.getIpAddr()));
        history.setBrowser(clientUtils.getBrowser(request));
        history.setSuccess(false);

        try {

            // 验证码校验 - 现在在所有环境中都启用
            if (captchaInvalid(req.getCaptchaId(), req.getCaptchaCode())) {
                log.error("captcha invalid: {}, {}", req.getCaptchaId(), req.getCaptchaCode());
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                        .header("X-Auth-Error", "CAPTCHA_INVALID")
                        .header("X-Auth-Message", encodeMessage("验证码错误，请重新输入"))
                        .build();
            }

            ResponseEntity<LoginResp> resp;
            switch (loginMethod) {
                case "password":
                    resp = loginByPassword(req);
                    break;
                default:
                    return ResponseEntity.status(HttpStatus.BAD_REQUEST).build();
            }
            history.setSuccess(resp.getStatusCodeValue() == HttpStatus.OK.value());
            return resp;
        } finally {
            loginHistoryService.save(history);
        }
    }

    private ResponseEntity<LoginResp> loginByPassword(LoginReq req) {

        // 首先解密密码
        Optional<String> rawCredentialOpt = userService.decodeAuthCredential(req.getCredential());
        if (rawCredentialOpt.isEmpty()) {
            log.error("Failed to decrypt password for user: {}", req.getUsername());
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).build();
        }

        String decryptedPassword = rawCredentialOpt.get();
        log.info("Decrypted password for user {}: {}", req.getUsername(), decryptedPassword);

        // 获取用户信息以验证密码
        Optional<User> userOpt = userService.getUserByUsername(req.getUsername());
        if (userOpt.isPresent()) {
            User user = userOpt.get();
            log.info("Database stored password for user {}: {}", req.getUsername(), user.getPassword());
            boolean passwordMatches = passwordEncoder.matches(decryptedPassword, user.getPassword());
            log.info("Password matches for user {}: {}", req.getUsername(), passwordMatches);

            // 生成正确的BCrypt哈希值用于调试
            String correctHash = passwordEncoder.encode(decryptedPassword);
            log.info("Correct BCrypt hash for password '{}': {}", decryptedPassword, correctHash);
        }

        req.setCredential(decryptedPassword);

        log.info("Attempting authentication for user: {} with password: {}", req.getUsername(), decryptedPassword);

        try {
            Authentication authentication = authenticationManager.authenticate(new UsernamePasswordAuthenticationToken(req.getUsername(), req.getCredential()));
            log.info("Authentication successful for user: {}", req.getUsername());

            // Get the authenticated user information
            UserDetails details = (UserDetails) authentication.getPrincipal();

            Optional<User> modelUser = userService.getUserByUsername(details.getUsername());
            if (modelUser.isEmpty()) {
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).build();
            }

            return ResponseEntity.ok(jwtComponent.generate(IssueRequest.of(modelUser.get())).buildLoginResp());
        } catch (UsernameNotFoundException ex) {
            // 用户不存在
            log.warn("User not found: {}", req.getUsername());
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .header("X-Auth-Error", "USER_NOT_FOUND")
                    .header("X-Auth-Message", encodeMessage("当前账户不存在"))
                    .build();
        } catch (BadCredentialsException ex) {
            log.error("Authentication failed for user: {} with decrypted password: {}", req.getUsername(), decryptedPassword);

            // 检查具体的失败原因
            Optional<User> userOpt_fail = userService.getUserByUsername(req.getUsername());
            if (userOpt_fail.isEmpty()) {
                // 用户不存在
                log.warn("User not found: {}", req.getUsername());
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                        .header("X-Auth-Error", "USER_NOT_FOUND")
                        .header("X-Auth-Message", encodeMessage("当前账户不存在"))
                        .build();
            }

            User user = userOpt_fail.get();

            // 检查账号是否过期
            if (!DateTimeUtils.isNonExpired(user.getAccountExpireDate())) {
                log.warn("Account expired for user: {}", req.getUsername());
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                        .header("X-Auth-Error", "ACCOUNT_EXPIRED")
                        .header("X-Auth-Message", encodeMessage("账号已过期，请联系管理员进行处理"))
                        .build();
            }

            // 检查密码是否过期
            if (!DateTimeUtils.isNonExpired(user.getCredentialExpireDate())) {
                log.warn("Credentials expired for user: {}", req.getUsername());
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                        .header("X-Auth-Error", "CREDENTIALS_EXPIRED")
                        .header("X-Auth-Message", encodeMessage("密码已过期，请联系管理员进行处理"))
                        .build();
            }

            // 检查账号是否被锁定
            if (user.getLocked()) {
                log.warn("Account locked for user: {}", req.getUsername());
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                        .header("X-Auth-Error", "ACCOUNT_LOCKED")
                        .header("X-Auth-Message", encodeMessage("账号已被锁定，请联系管理员进行处理"))
                        .build();
            }

            // 检查账号是否被禁用
            if (!user.getEnable()) {
                log.warn("Account disabled for user: {}", req.getUsername());
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                        .header("X-Auth-Error", "ACCOUNT_DISABLED")
                        .header("X-Auth-Message", encodeMessage("账号已被禁用，请联系管理员进行处理"))
                        .build();
            }

            // 如果用户存在且账号状态正常，则是密码错误
            log.warn("Password incorrect for user: {}", req.getUsername());
            String encodedMessage = encodeMessage("用户密码错误，请重新输入");
            log.info("Returning password error response with encoded message: {}", encodedMessage);
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .header("X-Auth-Error", "PASSWORD_INCORRECT")
                    .header("X-Auth-Message", encodedMessage)
                    .build();
        } catch (AuthenticationException ex) {
            // 捕获所有其他认证异常
            log.error("Authentication exception for user: {} - {}", req.getUsername(), ex.getMessage());

            // 检查用户是否存在
            Optional<User> userOpt_fail = userService.getUserByUsername(req.getUsername());
            if (userOpt_fail.isEmpty()) {
                // 用户不存在
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                        .header("X-Auth-Error", "USER_NOT_FOUND")
                        .header("X-Auth-Message", encodeMessage("当前账户不存在"))
                        .build();
            } else {
                // 用户存在但认证失败，通常是密码错误
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                        .header("X-Auth-Error", "PASSWORD_INCORRECT")
                        .header("X-Auth-Message", encodeMessage("用户名或密码错误，请重新输入"))
                        .build();
            }
        }
    }

    @GetMapping("/refreshToken")
    public ResponseEntity<LoginResp> tokenRefresh(HttpServletRequest request) {

        Optional<String> username = jwtComponent.getUsernameFromSecurityContext();
        if (username.isPresent() && jwtComponent.isRefreshable(request)) {
            Optional<User> modelUser = userService.getUserByUsername(username.get());
            if (modelUser.isEmpty()) {
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).build();
            }

            LoginResp resp = jwtComponent.generate(IssueRequest.of(modelUser.get())).buildLoginResp();
            return ResponseEntity.ok(resp);
        }

        return ResponseEntity.status(HttpStatus.FORBIDDEN).build();
    }

    @GetMapping("/logout")
    public ResponseEntity<?> logout() {
        // TODO block access token
        return ResponseEntity.noContent().build();
    }

    @PostMapping(value = "/code")
    public ResponseEntity<?> code(@RequestBody @Valid UserConfirmCodePostReq req) {
        // TODO impl
        return ResponseEntity.ok().build();
    }


    @PostMapping("/register")
    private ResponseEntity<?> registration(RegisterReq req) {
        log.debug("Registering user account with information: {}", req);

        if (captchaInvalid(req.getSeq(), req.getCode())) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).build();
        }

        if (!req.getPassword().equals(req.getMatchingPassword())) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).build();
        }

        UserPostReq dto = UserPostReq.builder()
                .username(req.getUsername())
                .password(req.getPassword())
                .enable(false)
                .build();

        User user = new User();

        boolean ok = userService.save(user);
        log.info("register user result: {}", ok);

        user.setPassword(PASSWORD_MASK);

        // TODO send confirm link to mailbox etc
        return ResponseEntity.ok(user);
    }

}
