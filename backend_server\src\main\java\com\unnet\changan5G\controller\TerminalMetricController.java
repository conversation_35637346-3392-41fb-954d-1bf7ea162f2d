//package com.unnet.changan5G.controller;
//
//import com.unnet.changan5G.document.TerminalMetricDocument;
//import com.unnet.changan5G.dto.ApiResp;
//import com.unnet.changan5G.service.TerminalMetricElasticsearchService;
//import io.swagger.v3.oas.annotations.Operation;
//import io.swagger.v3.oas.annotations.Parameter;
//import io.swagger.v3.oas.annotations.responses.ApiResponse;
//import io.swagger.v3.oas.annotations.responses.ApiResponses;
//import io.swagger.v3.oas.annotations.tags.Tag;
//import lombok.RequiredArgsConstructor;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.data.domain.Page;
//import org.springframework.data.domain.PageRequest;
//import org.springframework.data.domain.Pageable;
//import org.springframework.format.annotation.DateTimeFormat;
//import org.springframework.web.bind.annotation.*;
//
//import java.time.LocalDateTime;
//import java.util.Optional;
//
///**
// * 终端指标查询接口
// *
// * <AUTHOR>
// * @date 2024-07-14
// */
//@RestController
//@RequestMapping("/terminal/metrics")
//@Slf4j
//@RequiredArgsConstructor
//@Tag(name = "终端指标查询接口", description = "基于Elasticsearch的终端指标数据查询")
//public class TerminalMetricController {
//
//    private final TerminalMetricElasticsearchService terminalMetricElasticsearchService;
//
//    /**
//     * 根据设备ID分页查询指标数据
//     */
//    @GetMapping("/device/{deviceId}")
//    @Operation(summary = "根据设备ID分页查询指标数据", description = "按采集时间倒序返回指定设备的指标数据，每页10条")
//    @ApiResponses({
//        @ApiResponse(responseCode = "200", description = "查询成功"),
//        @ApiResponse(responseCode = "400", description = "请求参数错误"),
//        @ApiResponse(responseCode = "500", description = "服务器内部错误")
//    })
//    public ApiResp<Page<TerminalMetricDocument>> getMetricsByDeviceId(
//            @Parameter(name = "deviceId", description = "设备ID", required = true)
//            @PathVariable String deviceId,
//            @Parameter(name = "page", description = "页码，从0开始", required = false)
//            @RequestParam(defaultValue = "0") int page,
//            @Parameter(name = "size", description = "每页大小，默认10条", required = false)
//            @RequestParam(defaultValue = "10") int size) {
//
//        try {
//            log.info("根据设备ID分页查询指标数据 - 设备: {}, 页码: {}, 每页大小: {}", deviceId, page, size);
//
//            // 限制每页最大数量
//            if (size > 100) {
//                size = 100;
//            }
//
//            Pageable pageable = PageRequest.of(page, size);
//            Page<TerminalMetricDocument> result = terminalMetricElasticsearchService.getMetricsByDeviceId(deviceId, pageable);
//
//            log.info("查询完成 - 设备: {}, 总数: {}, 当前页数据量: {}",
//                    deviceId, result.getTotalElements(), result.getContent().size());
//
//            return ApiResp.success(result);
//
//        } catch (Exception e) {
//            log.error("根据设备ID分页查询指标数据失败 - 设备: {}, 错误: {}", deviceId, e.getMessage(), e);
//            return ApiResp.error(500, "查询指标数据失败: " + e.getMessage());
//        }
//    }
//
//    /**
//     * 根据设备ID获取最新的指标数据
//     */
//    @GetMapping("/device/{deviceId}/latest")
//    @Operation(summary = "获取设备最新指标数据", description = "根据采集时间获取指定设备的最新一条指标数据")
//    @ApiResponses({
//        @ApiResponse(responseCode = "200", description = "查询成功"),
//        @ApiResponse(responseCode = "404", description = "未找到数据"),
//        @ApiResponse(responseCode = "500", description = "服务器内部错误")
//    })
//    public ApiResp<TerminalMetricDocument> getLatestMetricByDeviceId(
//            @Parameter(name = "deviceId", description = "设备ID", required = true)
//            @PathVariable String deviceId) {
//
//        try {
//            log.info("获取设备最新指标数据 - 设备: {}", deviceId);
//
//            Optional<TerminalMetricDocument> result = terminalMetricElasticsearchService.getLatestMetricByDeviceId(deviceId);
//
//            if (result.isPresent()) {
//                log.info("获取最新指标数据成功 - 设备: {}, 采集时间: {}",
//                        deviceId, result.get().getMetricTime());
//                return ApiResp.success(result.get());
//            } else {
//                log.warn("未找到设备的指标数据 - 设备: {}", deviceId);
//                return ApiResp.error(404, "未找到设备的指标数据");
//            }
//
//        } catch (Exception e) {
//            log.error("获取设备最新指标数据失败 - 设备: {}, 错误: {}", deviceId, e.getMessage(), e);
//            return ApiResp.error(500, "获取最新指标数据失败: " + e.getMessage());
//        }
//    }
//
//    /**
//     * 根据设备ID和时间范围分页查询指标数据
//     */
//    @GetMapping("/device/{deviceId}/timerange")
//    @Operation(summary = "根据时间范围分页查询指标数据", description = "查询指定设备在指定时间范围内的指标数据，按采集时间倒序")
//    @ApiResponses({
//        @ApiResponse(responseCode = "200", description = "查询成功"),
//        @ApiResponse(responseCode = "400", description = "请求参数错误"),
//        @ApiResponse(responseCode = "500", description = "服务器内部错误")
//    })
//    public ApiResp<Page<TerminalMetricDocument>> getMetricsByDeviceIdAndTimeRange(
//            @Parameter(name = "deviceId", description = "设备ID", required = true)
//            @PathVariable String deviceId,
//            @Parameter(name = "startTime", description = "开始时间", required = true, example = "2024-07-14 00:00:00")
//            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
//            @Parameter(name = "endTime", description = "结束时间", required = true, example = "2024-07-14 23:59:59")
//            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime,
//            @Parameter(name = "page", description = "页码，从0开始", required = false)
//            @RequestParam(defaultValue = "0") int page,
//            @Parameter(name = "size", description = "每页大小，默认10条", required = false)
//            @RequestParam(defaultValue = "10") int size) {
//
//        try {
//            log.info("根据设备ID和时间范围分页查询指标数据 - 设备: {}, 时间范围: {} - {}, 页码: {}, 每页大小: {}",
//                    deviceId, startTime, endTime, page, size);
//
//            // 参数验证
//            if (startTime.isAfter(endTime)) {
//                return ApiResp.error(400, "开始时间不能晚于结束时间");
//            }
//
//            // 限制每页最大数量
//            if (size > 100) {
//                size = 100;
//            }
//
//            Pageable pageable = PageRequest.of(page, size);
//            Page<TerminalMetricDocument> result = terminalMetricElasticsearchService
//                    .getMetricsByDeviceIdAndTimeRange(deviceId, startTime, endTime, pageable);
//
//            log.info("时间范围查询完成 - 设备: {}, 总数: {}, 当前页数据量: {}",
//                    deviceId, result.getTotalElements(), result.getContent().size());
//
//            return ApiResp.success(result);
//
//        } catch (Exception e) {
//            log.error("根据设备ID和时间范围分页查询指标数据失败 - 设备: {}, 时间范围: {} - {}, 错误: {}",
//                    deviceId, startTime, endTime, e.getMessage(), e);
//            return ApiResp.error(500, "查询指标数据失败: " + e.getMessage());
//        }
//    }
//
//    /**
//     * 获取设备指标数据统计信息
//     */
//    @GetMapping("/device/{deviceId}/stats")
//    @Operation(summary = "获取设备指标统计信息", description = "获取指定设备的指标数据统计信息")
//    @ApiResponses({
//        @ApiResponse(responseCode = "200", description = "查询成功"),
//        @ApiResponse(responseCode = "500", description = "服务器内部错误")
//    })
//    public ApiResp<Object> getMetricStats(
//            @Parameter(name = "deviceId", description = "设备ID", required = true)
//            @PathVariable String deviceId,
//            @Parameter(name = "startTime", description = "开始时间（可选）", required = false)
//            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
//            @Parameter(name = "endTime", description = "结束时间（可选）", required = false)
//            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime) {
//
//        try {
//            log.info("获取设备指标统计信息 - 设备: {}", deviceId);
//
//            long totalCount;
//            if (startTime != null && endTime != null) {
//                totalCount = terminalMetricElasticsearchService.countByDeviceIdAndTimeRange(deviceId, startTime, endTime);
//            } else {
//                totalCount = terminalMetricElasticsearchService.countByDeviceId(deviceId);
//            }
//
//            // 获取最新数据
//            Optional<TerminalMetricDocument> latestMetric = terminalMetricElasticsearchService.getLatestMetricByDeviceId(deviceId);
//
//            // 构建统计信息
//            final String currentDeviceId = deviceId; // 避免自引用
//            Object stats = new Object() {
//                public final long totalRecords = totalCount;
//                public final String deviceId = currentDeviceId;
//                public final LocalDateTime latestMetricTime = latestMetric.map(TerminalMetricDocument::getMetricTime).orElse(null);
//                public final LocalDateTime queryTime = LocalDateTime.now();
//            };
//
//            return ApiResp.success(stats);
//
//        } catch (Exception e) {
//            log.error("获取设备指标统计信息失败 - 设备: {}, 错误: {}", deviceId, e.getMessage(), e);
//            return ApiResp.error(500, "获取统计信息失败: " + e.getMessage());
//        }
//    }
//}
