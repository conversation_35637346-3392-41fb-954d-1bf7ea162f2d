package com.unnet.jmanul.common.utils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.MapperFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.io.IOException;

public final class JacksonUtils {

    /**
     * Disable Jackson specific features like annotations. Support only
     * basic POJO serialization to limit our coupling to Jackson.
     */
    private static final ObjectMapper MAPPER = new ObjectMapper()
        .disable(MapperFeature.USE_ANNOTATIONS)
        .disable(MapperFeature.CAN_OVERRIDE_ACCESS_MODIFIERS)
        .disable(MapperFeature.AUTO_DETECT_FIELDS);

    private JacksonUtils() {
    }

    /**
     * Converts a {@link JsonNode} into a string.
     *
     * @param node Node to convert.
     * @return String of JSON document.
     */
    public static String jsonToString(JsonNode node) {
        try {
            return node == null ? null : MAPPER.writeValueAsString(node);
        } catch (JsonProcessingException e) {
            throw new IllegalArgumentException("Could not serialize J<PERSON><PERSON>", e);
        }
    }

    /**
     * Converts a String containing a JSON document into a {@link JsonNode}.
     *
     * @param paramName Param name to use in exception message.
     * @param val String JSON document.
     * @return JsonNode representing document.
     */
    public static JsonNode stringToJsonNode(String paramName, String val) {
        if (val == null) {
            return null;
        }
        try {
            return MAPPER.readTree(val);
        } catch (IOException e) {
            throw new IllegalArgumentException(paramName + " must be a valid JSON document", e);
        }
    }

    /**
     * Converts a POJO object into a JSON node. Limits Jackson features to reduce coupling.
     *
     * @param val Value to convert to {@link JsonNode}.
     * @return JsonNode representing POJO.
     */
    public static JsonNode objectToJsonNode(Object val) {
        return MAPPER.valueToTree(val);
    }

    public static String objectToString(Object val) {
        return jsonToString(objectToJsonNode(val));
    }
}