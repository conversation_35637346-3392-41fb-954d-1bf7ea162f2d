# MinIO集群配置说明

## 概述

本项目支持MinIO的单节点和集群模式部署，采用环境分离的配置策略：
- **开发环境**: 使用单节点模式，配置简单，适合本地开发
- **生产环境**: 使用集群模式，支持高可用性和负载均衡

## 版本信息

- **MinIO客户端版本**: 8.5.2
- **Java版本**: 17+
- **Spring Boot版本**: 2.7.x

## 配置架构

项目采用了类似Kafka配置的分离策略，创建了两个独立的配置类：
- `MinioDevConfig`: 开发环境配置类，使用 `@Profile("dev")`
- `MinioProdConfig`: 生产环境配置类，使用 `@Profile("prod")`

## 配置说明

### 开发环境配置 (MinioDevConfig)

开发环境使用单节点模式，配置在 `application-dev.yml` 中：

```yaml
spring:
  minio:
    host: http://*************:9000
    url: ${spring.minio.host}/${spring.minio.bucket}/
    access-key: minioadmin
    secret-key: minioadmin
    bucket: log-files
```

**特点**:
- 单节点模式，无集群功能
- 简化配置，适合本地开发
- 使用默认的MinIO认证信息
- 自动激活条件：`@Profile("dev")`

### 生产环境配置 (MinioProdConfig)

生产环境使用MinIO集群，配置在 `application-prod.yml` 中：

```yaml
spring:
  minio:
    # MinIO集群节点配置（负载均衡）
    hosts:
      - http://********:9000
      - http://********:9000
      - http://********:9000
    # 主节点配置（用于兼容单节点配置的代码）
    host: http://********:9000
    url: ${MINIO_URL:${spring.minio.host}/${spring.minio.bucket}/}
    # 认证配置
    access-key: admin
    secret-key: Changan5g.minio
    # 存储桶配置
    bucket: log-files
    # 集群配置
    cluster:
      enabled: true
      retry-attempts: 3
      retry-delay-ms: 1000
      health-check-interval-seconds: 30
```

**特点**:
- 集群模式，支持3个节点
- 负载均衡和故障转移
- 健康检查和自动恢复
- 自动激活条件：`@Profile("prod")`

## 配置类架构

### MinioDevConfig (开发环境)

```java
@Configuration
@Profile("dev")
@ConfigurationProperties(prefix = "spring.minio")
public class MinioDevConfig {
    // 单节点配置
    private String host;
    private String accessKey;
    private String secretKey;
    private String bucket;

    @Bean
    public MinioClient minioClient() {
        // 创建单节点MinIO客户端
    }
}
```

### MinioProdConfig (生产环境)

```java
@Configuration
@Profile("prod")
@ConfigurationProperties(prefix = "spring.minio")
public class MinioProdConfig {
    // 集群配置
    private List<String> hosts;
    private String host; // 兼容性配置
    private Cluster cluster;

    @Bean
    public MinioClient minioClient() {
        // 创建集群MinIO客户端，支持负载均衡
    }
}
```

### MinioClusterService (集群服务)

```java
@Service
public class MinioClusterService {
    @Autowired(required = false)
    private MinioDevConfig minioDevConfig;

    @Autowired(required = false)
    private MinioProdConfig minioProdConfig;

    // 自动适配不同环境的配置
    // 提供集群健康检查、故障转移等功能
}
```

## 功能特性

### 1. 集群支持
- **负载均衡**: 使用轮询算法在健康节点间分配请求
- **故障转移**: 自动检测节点故障并切换到健康节点
- **健康检查**: 定期检查所有节点的健康状态
- **重试机制**: 操作失败时自动重试

### 2. 高可用性
- **多节点冗余**: 支持多个MinIO节点，提供数据冗余
- **自动恢复**: 节点恢复后自动重新加入集群
- **连接池**: 优化连接管理，提高性能

### 3. 监控和管理
- **集群状态API**: 提供集群健康状态查询接口
- **健康检查端点**: 支持外部监控系统集成
- **详细日志**: 记录集群操作和状态变化

## API接口

### 1. 文件操作

#### 上传文件
```http
POST /api/minio/upload
Content-Type: multipart/form-data

file: [文件]
path: [可选，文件路径]
```

#### 检查文件存在
```http
GET /api/minio/exists/{objectName}
```

#### 删除文件
```http
DELETE /api/minio/delete/{objectName}
```

### 2. 集群管理

#### 获取集群状态
```http
GET /api/minio/cluster/status
```

响应示例：
```json
{
  "success": true,
  "data": {
    "clusterEnabled": true,
    "totalNodes": 3,
    "healthyNodes": 3,
    "nodeStatus": {
      "http://********:9000": true,
      "http://********:9000": true,
      "http://********:9000": false
    }
  }
}
```

#### 健康检查
```http
GET /api/minio/health
```

## 部署说明

### Docker集群部署

生产环境使用Docker Compose部署MinIO集群：

```yaml
version: '3.8'
services:
  minio1:
    image: minio/minio:latest
    hostname: minio1
    volumes:
      - /data/minio1:/data
    ports:
      - "9000:9000"
      - "9001:9001"
    environment:
      MINIO_ROOT_USER: admin
      MINIO_ROOT_PASSWORD: Changan5g.minio
    command: server --console-address ":9001" http://minio{1...3}/data
    networks:
      - minio-network

  minio2:
    image: minio/minio:latest
    hostname: minio2
    volumes:
      - /data/minio2:/data
    ports:
      - "9002:9000"
      - "9003:9001"
    environment:
      MINIO_ROOT_USER: admin
      MINIO_ROOT_PASSWORD: Changan5g.minio
    command: server --console-address ":9001" http://minio{1...3}/data
    networks:
      - minio-network

  minio3:
    image: minio/minio:latest
    hostname: minio3
    volumes:
      - /data/minio3:/data
    ports:
      - "9004:9000"
      - "9005:9001"
    environment:
      MINIO_ROOT_USER: admin
      MINIO_ROOT_PASSWORD: Changan5g.minio
    command: server --console-address ":9001" http://minio{1...3}/data
    networks:
      - minio-network

networks:
  minio-network:
    driver: bridge
```

### 环境变量配置

可以通过环境变量覆盖配置：

```bash
# MinIO连接配置
export MINIO_HOST=http://********:9000
export MINIO_ACCESS_KEY=admin
export MINIO_SECRET_KEY=Changan5g.minio
export MINIO_BUCKET=log-files

# 集群配置
export MINIO_CLUSTER_ENABLED=true
export MINIO_CLUSTER_RETRY_ATTEMPTS=3
export MINIO_CLUSTER_RETRY_DELAY=1000
export MINIO_CLUSTER_HEALTH_CHECK_INTERVAL=30

# 连接超时配置
export MINIO_CONNECTION_TIMEOUT=10000
export MINIO_READ_TIMEOUT=30000
export MINIO_WRITE_TIMEOUT=30000
```

## 版本说明

### MinIO客户端版本限制

当前使用的MinIO Java客户端版本为8.5.2，该版本有以下特点：

1. **超时配置**: 不支持直接在Builder中设置超时时间
   - 如需自定义超时，需要通过OkHttp客户端配置
   - 默认超时时间通常足够大多数使用场景

2. **API兼容性**:
   - 支持基本的文件操作（上传、下载、删除）
   - 支持存储桶操作
   - 支持预签名URL生成

3. **集群支持**:
   - 客户端级别的负载均衡通过应用层实现
   - 不是MinIO服务端的原生集群功能

## 故障排除

### 1. 集群节点不健康
- 检查网络连接
- 验证MinIO服务状态
- 查看应用日志中的健康检查信息

### 2. 文件上传失败
- 检查存储桶是否存在
- 验证认证信息
- 查看集群状态

### 3. 编译错误
- 确保使用正确的MinIO客户端版本 (8.5.2)
- 检查Java版本兼容性
- 验证依赖配置

## 监控建议

1. **集群状态监控**: 定期调用 `/api/minio/cluster/status` 接口
2. **健康检查**: 配置外部监控系统调用 `/api/minio/health` 接口
3. **日志监控**: 关注应用日志中的MinIO相关错误和警告
4. **性能监控**: 监控文件上传下载的响应时间和成功率

## 安全建议

1. **网络安全**: 确保MinIO集群在内网环境中运行
2. **认证安全**: 使用强密码，定期更换访问密钥
3. **访问控制**: 限制对MinIO管理接口的访问
4. **数据加密**: 考虑启用MinIO的数据加密功能
