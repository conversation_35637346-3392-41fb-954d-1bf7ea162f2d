# 长安5G运维管理平台

## 项目概述

长安5G运维管理平台是一个完整的终端设备监控和管理系统，用于实时监控5G终端设备的运行状态、性能指标和告警信息。系统采用前后端分离架构，包含数据收集服务、管理平台服务和前端展示三个核心模块。

## 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   front_server  │    │backend_admin_   │    │  backend_server │
│   (前端服务)     │◄──►│    server       │◄──►│  (数据收集服务)  │
│   Vue3 + Vite   │    │  (管理平台服务)  │    │  Spring Boot    │
│   Port: 5173    │    │  Spring Boot    │    │  Port: 8081     │
└─────────────────┘    │  Port: 8080     │    └─────────────────┘
                       └─────────────────┘
                              │
                              ▼
                       ┌─────────────────┐
                       │   数据存储层     │
                       │ MySQL/PostgreSQL│
                       │ Redis + ES      │
                       │ Kafka           │
                       └─────────────────┘
```

## 服务介绍

### 1. backend_server (数据收集服务)

**功能描述**: 负责接收终端设备上报的指标数据，进行数据处理、存储和分发。

**技术栈**:
- Spring Boot 2.7.16
- Java 8
- MyBatis Plus 3.5.5
- Elasticsearch
- Redis
- Kafka
- MySQL/PostgreSQL

**主要功能**:
- 终端指标数据接收和处理
- 终端设备注册和状态管理
- 实时数据存储到Elasticsearch
- 告警检测和生成
- 数据缓存管理
- Kafka消息队列处理

**核心API接口**:
- `POST /api/agent/report` - 接收终端指标数据上报
- `GET /api/terminals` - 查询终端列表
- `GET /api/terminals/{deviceId}` - 查询终端详情
- `GET /api/demo/hello` - 健康检查接口

### 2. backend_admin_server (管理平台服务)

**功能描述**: 提供Web管理界面的后端API服务，包含用户管理、权限控制、数据查询等功能。

**技术栈**:
- Spring Boot 2.7.18
- Java 11
- MyBatis Plus 3.5.5
- Spring Security
- Casbin (权限管理)
- JWT认证
- Swagger API文档

**主要功能**:
- 用户认证和授权
- 基于RBAC的权限管理
- 终端设备管理
- 告警管理
- 系统配置管理
- 数据统计和报表
- API文档生成

**核心API接口**:
- `POST /api/v1/auth/login` - 用户登录
- `GET /api/v1/admin/terminals` - 终端管理
- `GET /api/v1/admin/alerts` - 告警管理
- `GET /api/v1/admin/users` - 用户管理
- `GET /api/v1/admin/overview/init` - 概览数据

### 3. front_server (前端服务)

**功能描述**: 基于Vue3的现代化Web前端应用，提供直观的用户界面和良好的用户体验。

**技术栈**:
- Vue 3.5.17
- Vite 7.0.0
- Vue Router 4.5.1
- Pinia 3.0.3 (状态管理)
- Element Plus 2.10.4 (UI组件库)
- Axios 1.10.0 (HTTP客户端)

**主要功能**:
- 系统概览和统计
- 终端设备管理
- 实时指标监控
- 历史数据查询
- 告警管理
- 用户权限管理
- 响应式设计

**核心页面**:
- `/overview` - 系统概览
- `/terminals` - 终端管理
- `/alerts` - 告警管理
- `/real-time-metrics/:deviceId` - 实时指标
- `/history-metrics/:deviceId` - 历史指标
- `/system/user-management` - 用户管理

## 快速开始

### 环境要求

- **Java**: JDK 8+ (backend_server), JDK 11+ (backend_admin_server)
- **Node.js**: 16+ (front_server)
- **数据库**: MySQL 8.0+ 或 PostgreSQL 12+
- **缓存**: Redis 6.0+
- **搜索引擎**: Elasticsearch 7.x+
- **消息队列**: Kafka 2.8+

### 数据库初始化

1. 创建数据库:
```sql
CREATE DATABASE changan5g_admin CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

2. 执行初始化脚本:
```bash
# 管理平台数据库初始化
mysql -u root -p changan5g_admin < backend_admin_server/sql/init_admin_tables.sql
mysql -u root -p changan5g_admin < backend_admin_server/sql/metric_threshold_config.sql
```

### 服务启动

#### 1. 启动数据收集服务 (backend_server)

```bash
cd backend_server

# 开发环境启动
mvn spring-boot:run -Dspring-boot.run.profiles=dev

# 或者打包后启动
mvn clean package -DskipTests
java -jar target/backend_server-0.0.1.jar --spring.profiles.active=dev
```

**默认端口**: 8081
**API文档**: http://localhost:8081/api/swagger-ui.html
**健康检查**: http://localhost:8081/api/demo/hello

#### 2. 启动管理平台服务 (backend_admin_server)

```bash
cd backend_admin_server

# 开发环境启动
mvn spring-boot:run -Dspring-boot.run.profiles=dev

# 或者打包后启动
mvn clean package -DskipTests
java -jar target/changan5G-1.0.0.jar --spring.profiles.active=dev
```

**默认端口**: 8080
**API文档**: http://localhost:8080/swagger-ui.html
**默认管理员账号**: admin/test

#### 3. 启动前端服务 (front_server)

```bash
cd front_server

# 安装依赖
npm install

# 开发环境启动
npm run dev

# 生产环境构建
npm run build
```

**默认端口**: 5173
**访问地址**: http://localhost:5173

## 配置说明

### 环境变量配置

各服务支持通过环境变量进行配置：

**backend_server**:
```bash
SPRING_PROFILES_ACTIVE=dev
SPRING_DATASOURCE_URL=*************************************
SPRING_DATASOURCE_USERNAME=root
SPRING_DATASOURCE_PASSWORD=password
SPRING_REDIS_HOST=localhost
SPRING_REDIS_PORT=6379
SPRING_ELASTICSEARCH_URIS=localhost:9200
```

**backend_admin_server**:
```bash
SPRING_PROFILES_ACTIVE=dev
SPRING_DATASOURCE_URL=*******************************************
SPRING_DATASOURCE_USERNAME=root
SPRING_DATASOURCE_PASSWORD=password
SPRING_REDIS_HOST=localhost
SPRING_REDIS_PORT=6379
```

**front_server**:
```bash
VITE_ADMIN_API_BASE_URL=http://localhost:8080
VITE_DATA_API_BASE_URL=http://localhost:8081
```

### Docker部署

每个服务都提供了Dockerfile，支持容器化部署：

```bash
# 构建数据收集服务镜像
cd backend_server
docker build -t changan5g/backend-server:latest .

# 构建管理平台服务镜像
cd backend_admin_server
docker build -t changan5g/admin-server:latest .

# 构建前端服务镜像
cd front_server
docker build -t changan5g/front-server:latest .
```

## 开发指南

### 代码结构

```
changan-5G-admin/
├── backend_server/          # 数据收集服务
│   ├── src/main/java/
│   │   └── com/unnet/changan5G/
│   │       ├── controller/  # REST控制器
│   │       ├── service/     # 业务逻辑层
│   │       ├── mapper/      # 数据访问层
│   │       └── entity/      # 实体类
│   ├── dockerfile
│   └── pom.xml
├── backend_admin_server/    # 管理平台服务
│   ├── src/main/java/
│   │   └── com/unnet/jmanul/
│   │       ├── system/      # 系统管理模块
│   │       ├── business/    # 业务模块
│   │       └── common/      # 公共模块
│   ├── sql/                 # 数据库脚本
│   ├── dockerfile
│   └── pom.xml
├── front_server/            # 前端服务
│   ├── src/
│   │   ├── views/          # 页面组件
│   │   ├── components/     # 公共组件
│   │   ├── stores/         # 状态管理
│   │   ├── api/            # API接口
│   │   └── router/         # 路由配置
│   ├── package.json
│   └── vite.config.js
└── README.md
```

### API接口规范

系统采用RESTful API设计规范：

- **数据收集服务** (backend_server): `/api/*`
- **管理平台服务** (backend_admin_server): `/api/v1/*`

### 权限管理

系统使用Casbin进行权限管理，支持RBAC模型：

- **admin**: 管理员角色，拥有所有权限
- **user**: 普通用户角色，只有查看权限
- **auditor**: 审计员角色，拥有查看和部分管理权限

## 监控和运维

### 健康检查

- **backend_server**: `GET /api/demo/hello`
- **backend_admin_server**: `GET /api/v1/auth/publicKey`
- **front_server**: 访问首页检查

### 日志管理

所有服务都配置了详细的日志输出：

- 日志级别: INFO (生产环境), DEBUG (开发环境)
- 日志格式: 时间戳 + 级别 + 类名 + 消息
- 日志文件: `logs/` 目录下

### 性能监控

- JVM监控: 通过Spring Boot Actuator
- 数据库监控: Druid连接池监控
- Redis监控: 通过Redis客户端
- Elasticsearch监控: 通过ES集群状态API

## 故障排查

### 常见问题

1. **服务启动失败**
   - 检查端口是否被占用
   - 检查数据库连接配置
   - 检查依赖服务是否启动

2. **数据库连接失败**
   - 检查数据库服务状态
   - 验证连接参数
   - 检查防火墙设置

3. **前端页面空白**
   - 检查后端API服务状态
   - 检查浏览器控制台错误
   - 验证API接口地址配置

### 日志查看

```bash
# 查看服务日志
tail -f backend_server/logs/changan5g-data.log
tail -f backend_admin_server/logs/changan5g-admin.log

# 查看Docker容器日志
docker logs -f container_name
```

## 贡献指南

### Git提交规范

- `feat`: 添加新特性
- `fix`: 修复问题
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

### 开发流程

1. Fork项目到个人仓库
2. 创建功能分支: `git checkout -b feature/new-feature`
3. 提交代码: `git commit -m 'feat: add new feature'`
4. 推送分支: `git push origin feature/new-feature`
5. 创建Pull Request
