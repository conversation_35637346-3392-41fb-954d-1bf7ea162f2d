package com.unnet.jmanul.business.service;

import com.unnet.jmanul.business.document.TerminalMetricDocument;
import com.unnet.jmanul.business.entity.dto.TerminalMetricInfo;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.time.LocalDateTime;
import java.util.Optional;

/**
 * 终端指标信息Elasticsearch服务接口
 * 
 * <AUTHOR>
 * @date 2024-07-16
 */
public interface ITerminalMetricElasticsearchService {

    /**
     * 保存指标数据到Elasticsearch
     */
    boolean saveMetricData(TerminalMetricInfo metricInfo);



    /**
     * 根据MAC地址分页查询指标数据
     */
    Page<TerminalMetricDocument> getMetricsByIdentityMac(String identityMac, Pageable pageable);

    /**
     * 根据MAC地址获取最新的指标数据
     */
    Optional<TerminalMetricDocument> getLatestMetricByIdentityMac(String identityMac);

    /**
     * 根据MAC地址和时间范围分页查询指标数据
     */
    Page<TerminalMetricDocument> getMetricsByIdentityMacAndTimeRange(String identityMac,
                                                                    LocalDateTime startTime,
                                                                    LocalDateTime endTime,
                                                                    Pageable pageable);

    /**
     * 根据MAC地址查询数据总数
     */
    long countByIdentityMac(String identityMac);

    /**
     * 根据指标ID获取指标详情
     */
    Optional<TerminalMetricDocument> getMetricById(String metricId);

    /**
     * 根据设备MAC地址和时间范围查询数据总数
     */
    long countByIdentityMacAndTimeRange(String identityMac, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 删除历史数据
     */
    boolean deleteHistoryData(LocalDateTime beforeTime);

    /**
     * 检查索引是否存在
     */
    boolean indexExists();

    /**
     * 创建索引
     */
    boolean createIndex();
}
