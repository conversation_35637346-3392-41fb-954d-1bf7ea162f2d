# 终端运维管理平台

## 项目概述

这是一个用于终端运维的管理平台，方便运维人员对终端的控制和状态信息查看。项目包含了完整的HTML原型页面，可以作为Vue3开发的参考。

## 项目结构

```
├── index.html              # 主页面（包含所有功能的集成版本）
├── overview.html           # 概览页面组件
├── terminals.html          # 终端管理页面组件
├── real-time-metrics.html  # 实时指标页面组件
├── history-metrics.html    # 历史指标页面组件
├── alerts.html             # 站内告警页面组件
├── 项目说明                # 项目需求文档和数据库设计
└── README.md              # 项目说明文档
```

## 功能模块

### 1. 概览页面 (overview.html)
- 终端统计信息（终端数量、在线数量、离线数量、告警数量）
- 终端上下线实时动态滚动显示
- 自动更新日志记录

### 2. 终端管理页面 (terminals.html)
- 终端列表展示，包含基本字段信息
- 过滤查询功能（设备ID、主机名、状态、标签）
- 操作功能：
  - 实时指标查看
  - 历史指标查看
  - 终端详情查看
  - 终端编辑（支持自定义字段和标签管理）
  - 离线设备删除
- 分页显示

### 3. 实时指标页面 (real-time-metrics.html)
- 实时指标数据展示
- 刷新按钮，获取最新数据
- 详细指标信息（CPU、内存、磁盘使用情况）
- 进度条可视化显示
- 面包屑导航

### 4. 历史指标页面 (history-metrics.html)
- 历史指标数据列表
- 时间范围筛选功能
- 历史数据查询
- 查看详情功能（复用实时指标页面）

### 5. 站内告警页面 (alerts.html)
- 告警列表展示
- 告警筛选功能（设备ID、告警类型、告警状态）
- 告警详情查看
- 实时指标跳转
- 钉钉提醒功能
- 告警确认和解决功能

## 技术特点

1. **响应式设计**: 采用现代化的CSS布局，支持不同屏幕尺寸
2. **模块化设计**: 每个页面都是独立的组件，便于Vue3开发时复用
3. **交互友好**: 包含hover效果、状态颜色区分、模态框等用户体验优化
4. **数据可视化**: 使用统计卡片、进度条、表格等方式展示数据
5. **页面间通信**: 通过URL参数传递数据，支持页面间跳转

## 数据库设计

项目包含三个核心数据表：

1. **terminal_basic_info**: 终端基本信息表
2. **terminal_metric_info**: 终端指标信息表
3. **terminal_alert_info**: 终端告警信息表

详细的数据库设计和示例数据请参考 `项目说明` 文件。

## 使用说明

### 独立页面访问
- 概览页面: 直接打开 `overview.html`
- 终端管理: 直接打开 `terminals.html`
- 实时指标: 打开 `real-time-metrics.html?deviceId=设备ID`
- 历史指标: 打开 `history-metrics.html?deviceId=设备ID`
- 站内告警: 直接打开 `alerts.html`

### 集成版本访问
- 打开 `index.html` 可以访问所有功能的集成版本

## Vue3开发建议

1. **组件拆分**: 每个HTML文件可以作为一个Vue组件
2. **路由配置**: 使用Vue Router配置页面路由
3. **状态管理**: 使用Pinia管理全局状态
4. **API接口**: 将模拟数据替换为真实的API调用
5. **样式优化**: 可以使用Tailwind CSS或Element Plus等UI框架

## 功能扩展

- [ ] 添加图表展示（CPU、内存使用率趋势图）
- [ ] 实现WebSocket实时数据推送
- [ ] 添加用户权限管理
- [ ] 支持批量操作
- [ ] 添加导出功能
- [ ] 集成更多第三方通知方式

## 技术栈建议

- **前端框架**: Vue3 + TypeScript
- **路由管理**: Vue Router 4
- **状态管理**: Pinia
- **UI组件**: Element Plus / Ant Design Vue
- **图表库**: ECharts / Chart.js
- **HTTP客户端**: Axios
- **构建工具**: Vite

## 部署说明

1. 将HTML文件部署到Web服务器
2. 确保所有文件在同一目录下
3. 配置服务器支持HTML5 History模式（如果使用Vue Router）
4. 设置适当的缓存策略

## 联系方式

如有问题或建议，请联系开发团队。 