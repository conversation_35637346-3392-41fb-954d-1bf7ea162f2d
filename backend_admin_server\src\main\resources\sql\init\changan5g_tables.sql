-- 长安5G业务模块数据库表结构
-- 创建时间：2024-07-16
-- 说明：用于长安5G终端管理和指标查询功能

-- 1. 终端基本信息表
CREATE TABLE IF NOT EXISTS `terminal_basic_info` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `device_id` varchar(100) NOT NULL COMMENT '设备唯一标识符',
    `hostname` varchar(100) DEFAULT NULL COMMENT '主机名',
    `identity_mac` varchar(50) DEFAULT NULL COMMENT '对接长安车辆所在网口的MAC地址',
    `app_version` json DEFAULT NULL COMMENT '当前运行的组件版本号',
    `expired_date` varchar(30) DEFAULT NULL COMMENT '软件授权过期时间',
    `receive_time` datetime DEFAULT NULL COMMENT '数据接收时间',
    `data_source` varchar(20) DEFAULT 'kafka' COMMENT '数据来源',
    `status` tinyint(1) DEFAULT '1' COMMENT '设备状态：0-离线，1-在线',
    `first_register_time` datetime DEFAULT NULL COMMENT '首次注册时间',
    `last_update_time` datetime DEFAULT NULL COMMENT '最后更新时间',
    `tags` json DEFAULT NULL COMMENT '终端标签信息（JSON格式）',
    `custom_fields` json DEFAULT NULL COMMENT '自定义字段信息（JSON格式）',
    `created_by` varchar(50) DEFAULT NULL COMMENT '创建人',
    `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_by` varchar(50) DEFAULT NULL COMMENT '更新人',
    `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at` bigint(20) DEFAULT NULL COMMENT '删除时间戳',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_device_id` (`device_id`),
    KEY `idx_hostname` (`hostname`),
    KEY `idx_status` (`status`),
    KEY `idx_last_update_time` (`last_update_time`),
    KEY `idx_device_id` (`device_id`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='终端基本信息表';

-- 2. 终端指标信息表（MySQL存储，用于备份和历史查询）
CREATE TABLE IF NOT EXISTS `terminal_metric_info` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `device_id` varchar(100) NOT NULL COMMENT '设备唯一标识符',
    `uptime` decimal(15,2) DEFAULT NULL COMMENT '系统运行时长（单位：秒）',
    `cpu_temp` decimal(5,2) DEFAULT NULL COMMENT 'CPU温度（单位：摄氏度）',
    `temperatures` json DEFAULT NULL COMMENT '温度信息',
    `cpu_usage` json DEFAULT NULL COMMENT 'CPU使用率信息',
    `cpu_percent` decimal(5,2) DEFAULT NULL COMMENT 'CPU总使用率（%）',
    `memory_percent` decimal(5,2) DEFAULT NULL COMMENT '内存使用率（%）',
    `memory_usage` json DEFAULT NULL COMMENT '内存详细信息',
    `disk_usage` json DEFAULT NULL COMMENT '磁盘使用情况数组',
    `disk_data_percent` decimal(5,2) DEFAULT NULL COMMENT '采集数据磁盘分区使用率',
    `disk_system_percent` decimal(5,2) DEFAULT NULL COMMENT '系统盘使用率',
    `cdata` json DEFAULT NULL COMMENT '长安程序落盘文件信息',
    `zdata` json DEFAULT NULL COMMENT '压缩文件信息',
    `group_usage` json DEFAULT NULL COMMENT 'CPE集合信息',
    `group_bps` bigint(20) DEFAULT NULL COMMENT '所有CPE的带宽占用（单位：Byte/s）',
    `metric_time` datetime NOT NULL COMMENT '指标采集时间',
    `receive_time` datetime DEFAULT NULL COMMENT '数据接收时间',
    `created_by` varchar(50) DEFAULT NULL COMMENT '创建人',
    `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_by` varchar(50) DEFAULT NULL COMMENT '更新人',
    `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_device_id` (`device_id`),
    KEY `idx_metric_time` (`metric_time`),
    KEY `idx_device_metric_time` (`device_id`, `metric_time`),
    KEY `idx_cpu_temp` (`cpu_temp`),
    KEY `idx_memory_percent` (`memory_percent`),
    KEY `idx_disk_data_percent` (`disk_data_percent`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='终端指标信息表';

-- 3. 终端告警信息表
CREATE TABLE IF NOT EXISTS `terminal_alert_info` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `device_id` varchar(100) NOT NULL COMMENT '设备唯一标识符',
    `alert_type` varchar(50) NOT NULL COMMENT '告警类型',
    `alert_message` text NOT NULL COMMENT '告警消息',
    `alert_value` decimal(10,2) DEFAULT NULL COMMENT '告警值',
    `threshold_value` decimal(10,2) DEFAULT NULL COMMENT '阈值',
    `alert_time` datetime NOT NULL COMMENT '告警时间',
    `status` tinyint(1) DEFAULT '1' COMMENT '告警状态：0-已处理，1-未处理',
    `created_by` varchar(50) DEFAULT NULL COMMENT '创建人',
    `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_by` varchar(50) DEFAULT NULL COMMENT '更新人',
    `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at` bigint(20) DEFAULT NULL COMMENT '删除时间戳',
    PRIMARY KEY (`id`),
    KEY `idx_device_id` (`device_id`),
    KEY `idx_alert_type` (`alert_type`),
    KEY `idx_alert_time` (`alert_time`),
    KEY `idx_status` (`status`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='终端告警信息表';

-- 插入测试数据（可选）
-- INSERT INTO `terminal_basic_info` (`device_id`, `hostname`, `identity_mac`, `status`, `data_source`) VALUES
-- ('_test_device_001', 'ec_3568_test001', 'de0765523e60', 1, 'kafka'),
-- ('_test_device_002', 'ec_3568_test002', 'de0765523e61', 0, 'kafka');

COMMIT;
