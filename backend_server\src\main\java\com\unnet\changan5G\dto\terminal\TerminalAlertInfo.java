package com.unnet.changan5G.dto.terminal;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 终端告警信息
 * 
 * <AUTHOR>
 * @date 2024-07-14
 */
@Data
@Schema(description = "终端告警信息")
public class TerminalAlertInfo {

    @Schema(description = "告警ID（自动生成）")
    private String alertId;

    @Schema(description = "告警终端设备MAC地址")
    private String identityMac;

    @Schema(description = "告警类型")
    private String alertType;

    @Schema(description = "告警级别")
    private String alertLevel;

    @Schema(description = "告警详情")
    private String alertDetails;

    @Schema(description = "告警指标名称")
    private String metricName;

    @Schema(description = "告警阈值")
    private String threshold;

    @Schema(description = "当前值")
    private String currentValue;

    @Schema(description = "关联的指标记录ID（Elasticsearch文档ID）")
    private String metricId;

    @Schema(description = "告警时间")
    private LocalDateTime alertTime;

    @Schema(description = "告警状态")
    private AlertStatus alertStatus = AlertStatus.ACTIVE;

    // 告警类型现在直接使用数据库中的中文描述字符串，不再使用枚举



    /**
     * 告警状态枚举
     */
    public enum AlertStatus {
        ACTIVE("活跃"),
        RESOLVED("已解决"),
        ACKNOWLEDGED("已确认");

        private final String description;

        AlertStatus(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }
}
