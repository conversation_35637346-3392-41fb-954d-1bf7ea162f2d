<template>
  <div class="layout-container">
    <!-- 头部导航栏 -->
    <div class="header">
      <h1>数据上云设备管理平台</h1>
      <div class="header-right">
        <el-dropdown @command="handleCommand">
          <span class="user-info">
            <el-icon><User /></el-icon>
            {{ userInfo?.name || userInfo?.username || '用户' }}
            <el-icon class="el-icon--right"><arrow-down /></el-icon>
          </span>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="profile">
                <el-icon><User /></el-icon>
                个人信息
              </el-dropdown-item>
              <el-dropdown-item command="logout" divided>
                <el-icon><SwitchButton /></el-icon>
                退出登录
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>

    <!-- 主容器 -->
    <div class="container">
      <!-- 侧边栏 -->
      <div class="sidebar">
        <ul class="nav-menu">
          <li>
            <router-link
              to="/overview"
              class="nav-item"
              :class="{ active: $route.path === '/overview' }"
            >
              <i>📊</i>概览
            </router-link>
          </li>
          <li>
            <router-link
              to="/terminals"
              class="nav-item"
              :class="{ active: $route.path === '/terminals' }"
            >
              <i>💻</i>终端管理
            </router-link>
          </li>
          <li>
            <router-link
              to="/alerts"
              class="nav-item"
              :class="{ active: $route.path === '/alerts' }"
            >
              <i>🚨</i>站内告警
            </router-link>
          </li>
          <!-- 管理员专用菜单 -->
          <li v-if="isAdmin" class="nav-group">
            <div class="nav-group-header">
              <i>⚙️</i>系统管理
            </div>
            <ul class="nav-submenu">
              <li>
                <router-link
                  to="/system/user-management"
                  class="nav-item nav-subitem"
                  :class="{ active: $route.path === '/system/user-management' }"
                >
                  <i>👥</i>用户管理
                </router-link>
              </li>
              <li>
                <router-link
                  to="/system/metric-config"
                  class="nav-item nav-subitem"
                  :class="{ active: $route.path === '/system/metric-config' }"
                >
                  <i>📊</i>指标配置管理
                </router-link>
              </li>
            </ul>
          </li>
        </ul>

        <!-- 用户角色标识 -->
        <div class="user-role-badge">
          <el-tag :type="isAdmin ? 'danger' : 'info'" size="small">
            {{ isAdmin ? '管理员' : '普通用户' }}
          </el-tag>
        </div>
      </div>

      <!-- 主内容区 -->
      <div class="main-content">
        <router-view />
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { User, ArrowDown, SwitchButton } from '@element-plus/icons-vue'
import { useAuthStore } from '@/stores/auth'

const router = useRouter()
const authStore = useAuthStore()

// 用户信息
const userInfo = computed(() => authStore.userInfo)
const isAdmin = computed(() => authStore.isAdmin)

// 处理下拉菜单命令
const handleCommand = async (command) => {
  switch (command) {
    case 'profile':
      // 个人信息页面（暂时用消息提示）
      ElMessage.info('个人信息功能开发中...')
      break
    case 'logout':
      try {
        await ElMessageBox.confirm(
          '确定要退出登录吗？',
          '提示',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          }
        )

        await authStore.logout()
        ElMessage.success('已退出登录')
        router.push('/login')
      } catch (error) {
        if (error !== 'cancel') {
          console.error('退出登录失败:', error)
          ElMessage.error('退出登录失败')
        }
      }
      break
  }
}
</script>

<style scoped>
.layout-container {
  height: 100vh;
  width: 100vw;
  overflow: hidden;
}

/* 头部样式 - 完全按照原型 */
.header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 0 20px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.header h1 {
  font-size: 20px;
  font-weight: 600;
  margin: 0;
}

.header-right {
  display: flex;
  align-items: center;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  color: white;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 6px;
  transition: background-color 0.3s;
}

.user-info:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* 主容器 */
.container {
  display: flex;
  height: calc(100vh - 60px);
}

/* 侧边栏样式 - 完全按照原型 */
.sidebar {
  width: 250px;
  background: white;
  box-shadow: 2px 0 10px rgba(0,0,0,0.1);
  padding: 20px 0;
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
}

.user-role-badge {
  margin-top: auto;
  padding: 20px;
  text-align: center;
}

.nav-menu {
  list-style: none;
  margin: 0;
  padding: 0;
}

.nav-menu li {
  margin-bottom: 5px;
}

.nav-item {
  display: block;
  padding: 15px 20px;
  color: #333;
  text-decoration: none;
  transition: all 0.3s;
  border-left: 3px solid transparent;
}

.nav-item:hover,
.nav-item.active {
  background: #f0f7ff;
  border-left-color: #667eea;
  color: #667eea;
}

.nav-item i {
  margin-right: 10px;
  width: 20px;
  text-align: center;
}

/* 导航组样式 */
.nav-group {
  margin-bottom: 5px;
}

.nav-group-header {
  padding: 15px 20px;
  color: #666;
  font-weight: 600;
  font-size: 14px;
  border-left: 3px solid transparent;
  cursor: default;
}

.nav-group-header i {
  margin-right: 10px;
  width: 20px;
  text-align: center;
}

.nav-submenu {
  list-style: none;
  margin: 0;
  padding: 0;
  background: #f8f9fa;
}

.nav-submenu li {
  margin-bottom: 0;
}

.nav-subitem {
  padding: 12px 20px 12px 45px;
  font-size: 13px;
  border-left: 3px solid transparent;
}

.nav-subitem:hover,
.nav-subitem.active {
  background: #e3f2fd;
  border-left-color: #667eea;
  color: #667eea;
}

.nav-subitem i {
  margin-right: 8px;
  width: 16px;
  font-size: 12px;
}

/* 导航组样式 */
.nav-group {
  margin-bottom: 5px;
}

.nav-group-header {
  padding: 15px 20px;
  color: #666;
  font-weight: 600;
  font-size: 14px;
  border-left: 3px solid transparent;
  cursor: default;
}

.nav-group-header i {
  margin-right: 10px;
  width: 20px;
  text-align: center;
}

.nav-submenu {
  list-style: none;
  margin: 0;
  padding: 0;
  background: #f8f9fa;
}

.nav-submenu li {
  margin-bottom: 0;
}

.nav-subitem {
  padding: 12px 20px 12px 45px;
  font-size: 13px;
  border-left: 3px solid transparent;
}

.nav-subitem:hover,
.nav-subitem.active {
  background: #e3f2fd;
  border-left-color: #667eea;
  color: #667eea;
}

.nav-subitem i {
  margin-right: 8px;
  width: 16px;
  font-size: 12px;
}

/* 主内容区域 */
.main-content {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  background-color: #f5f5f5;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .sidebar {
    width: 200px;
  }
  
  .header h1 {
    font-size: 18px;
  }
  
  .main-content {
    padding: 15px;
  }
}

@media (max-width: 480px) {
  .sidebar {
    width: 180px;
  }
  
  .header h1 {
    font-size: 16px;
  }
  
  .header {
    padding: 0 15px;
  }
  
  .main-content {
    padding: 10px;
  }
  
  .nav-item {
    padding: 12px 15px;
    font-size: 14px;
  }
  
  .nav-item i {
    margin-right: 8px;
  }
}
</style> 