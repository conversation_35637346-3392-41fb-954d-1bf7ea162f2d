package com.unnet.changan5G.dto.metric;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 文件数据信息（cdata和zdata）
 * 
 * <AUTHOR>
 * @date 2024-07-14
 */
@Data
@Schema(description = "文件数据信息")
public class FileDataInfo {

    @Schema(description = "文件数量", example = "1")
    private Integer count;

    @Schema(description = "文件总大小（字节）", example = "3627078580")
    @JsonProperty("size")
    private Long size;
}
