package com.unnet.jmanul.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.unnet.jmanul.business.entity.UserRole;

import java.util.List;

/**
 * 用户角色关系服务接口
 * 
 * <AUTHOR>
 * @date 2024-07-16
 */
public interface IUserRoleService extends IService<UserRole> {

    /**
     * 根据用户ID查询角色列表
     */
    List<UserRole> getRolesByUserId(Long userId);

    /**
     * 根据角色名称查询用户列表
     */
    List<UserRole> getUsersByRoleName(String roleName);

    /**
     * 检查用户是否拥有指定角色
     */
    boolean hasRole(Long userId, String roleName);

    /**
     * 为用户分配角色
     */
    boolean assignRole(Long userId, String roleName);

    /**
     * 移除用户角色
     */
    boolean removeRole(Long userId, String roleName);

    /**
     * 获取用户的主要角色（第一个角色）
     */
    String getPrimaryRole(Long userId);
}
