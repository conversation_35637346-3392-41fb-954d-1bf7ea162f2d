<template>
  <div class="group-usage-test">
    <div class="card">
      <div class="card-header">
        <h3>GroupUsage数据处理测试</h3>
        <button class="btn btn-primary" @click="testDataProcessing">测试数据处理</button>
      </div>
      <div class="card-body">
        <!-- 原始数据 -->
        <div class="test-section">
          <h4>原始API数据</h4>
          <pre>{{ JSON.stringify(rawData, null, 2) }}</pre>
        </div>

        <!-- 处理后数据 -->
        <div class="test-section">
          <h4>处理后数据</h4>
          <pre>{{ JSON.stringify(cleanedData, null, 2) }}</pre>
        </div>

        <!-- 拓扑图预览 -->
        <div class="test-section" v-if="cleanedData.buckets && cleanedData.buckets.length > 0">
          <h4>拓扑图预览</h4>
          <div class="topology-container">
            <div class="topology-title">终端系统架构图</div>
            <div class="topology-graph">
              <!-- 主终端 -->
              <div class="main-terminal">
                <div class="main-terminal-title">主终端服务</div>
                <div class="main-terminal-id">测试终端</div>
              </div>
              
              <!-- 连接线 -->
              <div class="connection-line"></div>
              
              <!-- CPE集合 -->
              <div class="cpe-container">
                <div
                  v-for="(bucket, index) in cleanedData.buckets"
                  :key="index"
                  class="cpe-item"
                  :class="bucket.conn > 0 || bucket.pps > 0 || bucket.bps > 0 ? 'active' : 'inactive'"
                >
                  <div class="cpe-status" :class="bucket.conn > 0 || bucket.pps > 0 || bucket.bps > 0 ? 'online' : 'offline'"></div>
                  <div class="cpe-title">CPE</div>
                  <div class="cpe-bucket">Bucket {{ bucket.bucket }}</div>
                  <div class="cpe-stats">
                    连接: {{ bucket.conn || 0 }}<br>
                    PPS: {{ (bucket.pps || 0).toFixed(1) }}<br>
                    BPS: {{ formatBytes(bucket.bps || 0) }}
                  </div>
                </div>
              </div>
            </div>
            
            <!-- 拓扑统计信息 -->
            <div class="topology-summary">
              <div class="summary-grid">
                <div class="summary-item">
                  <div class="summary-value">{{ cleanedData.group_id }}</div>
                  <div class="summary-label">组ID</div>
                </div>
                <div class="summary-item">
                  <div class="summary-value">{{ cleanedData.buckets ? cleanedData.buckets.length : 0 }}</div>
                  <div class="summary-label">CPE数量</div>
                </div>
                <div class="summary-item">
                  <div class="summary-value">{{ cleanedData.pps_total ? cleanedData.pps_total.toFixed(1) : '0.0' }}</div>
                  <div class="summary-label">总PPS</div>
                </div>
                <div class="summary-item">
                  <div class="summary-value">{{ formatBytes(cleanedData.bps_total || 0) }}/s</div>
                  <div class="summary-label">总带宽</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive } from 'vue'

export default {
  name: 'GroupUsageTest',
  setup() {
    const rawData = ref({})
    const cleanedData = reactive({})

    // 清理groupUsage数据中的字段名空格
    const cleanGroupUsageData = (groupUsage) => {
      if (!groupUsage) {
        console.log('groupUsage为空，返回默认值')
        return {
          group_id: 'N/A',
          buckets: [],
          pps_total: 0,
          bps_total: 0
        }
      }
      
      console.log('原始groupUsage数据:', groupUsage)
      
      const cleaned = {}
      
      // 清理顶级字段名的空格
      Object.keys(groupUsage).forEach(key => {
        const cleanKey = key.trim()
        cleaned[cleanKey] = groupUsage[key]
        if (key !== cleanKey) {
          console.log(`字段名清理: "${key}" -> "${cleanKey}"`)
        }
      })
      
      // 特别处理buckets数组
      if (cleaned.buckets && Array.isArray(cleaned.buckets)) {
        console.log(`处理buckets数组，共${cleaned.buckets.length}个bucket`)
        cleaned.buckets = cleaned.buckets.map((bucket, index) => {
          const cleanedBucket = {}
          Object.keys(bucket).forEach(key => {
            const cleanKey = key.trim()
            cleanedBucket[cleanKey] = bucket[key]
          })
          console.log(`Bucket ${index}:`, cleanedBucket)
          return cleanedBucket
        })
      } else {
        console.log('buckets字段不存在或不是数组')
        cleaned.buckets = []
      }
      
      // 确保数值字段存在
      cleaned.pps_total = cleaned.pps_total || 0
      cleaned.bps_total = cleaned.bps_total || 0
      cleaned.group_id = cleaned.group_id || 'N/A'
      
      console.log('清理后的groupUsage数据:', cleaned)
      return cleaned
    }

    // 格式化字节数
    const formatBytes = (bytes, decimals = 2) => {
      if (bytes === 0) return '0 Bytes'
      const k = 1024
      const dm = decimals < 0 ? 0 : decimals
      const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i]
    }

    // 测试数据处理
    const testDataProcessing = () => {
      // 模拟API返回的数据（包含空格的字段名）
      const testData = {
        "group_id ": 105,
        " bps_total ": 0.0,
        " buckets ": [
          {
            " bucket ": 0,
            " conn ": 0,
            " pps ": 0.0,
            " bps ": 0.0
          },
          {
            " bucket ": 1,
            " conn ": 5,
            " pps ": 100.5,
            " bps ": 50000.0
          },
          {
            " bucket ": 2,
            " conn ": 3,
            " pps ": 75.2,
            " bps ": 30000.0
          },
          {
            " bucket ": 3,
            " conn ": 8,
            " pps ": 200.8,
            " bps ": 80000.0
          },
          {
            " bucket ": 4,
            " conn ": 2,
            " pps ": 50.1,
            " bps ": 20000.0
          }
        ],
        " pps_total ": 426.6
      }

      rawData.value = testData
      const cleaned = cleanGroupUsageData(testData)
      Object.assign(cleanedData, cleaned)
    }

    return {
      rawData,
      cleanedData,
      testDataProcessing,
      formatBytes
    }
  }
}
</script>

<style scoped>
.group-usage-test {
  padding: 20px;
}

.card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #eee;
}

.card-header h3 {
  margin: 0;
  color: #333;
}

.card-body {
  padding: 20px;
}

.test-section {
  margin-bottom: 30px;
}

.test-section h4 {
  margin-bottom: 10px;
  color: #333;
}

pre {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 15px;
  overflow-x: auto;
  font-size: 12px;
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.btn-primary {
  background: #007bff;
  color: white;
}

.btn-primary:hover {
  background: #0056b3;
}

/* 拓扑图样式 */
.topology-container {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 20px;
  margin: 20px 0;
}

.topology-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 20px;
  color: #495057;
  text-align: center;
}

.topology-graph {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 30px;
}

.main-terminal {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px;
  border-radius: 12px;
  text-align: center;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  min-width: 200px;
}

.main-terminal-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 8px;
}

.main-terminal-id {
  font-size: 14px;
  opacity: 0.9;
}

.connection-line {
  width: 2px;
  height: 40px;
  background: #6c757d;
  position: relative;
}

.cpe-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 15px;
  width: 100%;
  max-width: 800px;
}

.cpe-item {
  background: white;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  padding: 12px;
  text-align: center;
  position: relative;
  transition: all 0.3s ease;
}

.cpe-item.active {
  border-color: #28a745;
  box-shadow: 0 2px 8px rgba(40, 167, 69, 0.2);
}

.cpe-item.inactive {
  border-color: #dc3545;
  opacity: 0.7;
}

.cpe-status {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.cpe-status.online {
  background: #28a745;
}

.cpe-status.offline {
  background: #dc3545;
}

.cpe-title {
  font-weight: 600;
  margin-bottom: 4px;
  color: #495057;
}

.cpe-bucket {
  font-size: 12px;
  color: #6c757d;
  margin-bottom: 8px;
}

.cpe-stats {
  font-size: 11px;
  color: #495057;
  line-height: 1.4;
}

.topology-summary {
  background: #e3f2fd;
  border: 1px solid #bbdefb;
  border-radius: 6px;
  padding: 15px;
  margin-top: 20px;
}

.summary-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 15px;
}

.summary-item {
  text-align: center;
}

.summary-value {
  font-size: 18px;
  font-weight: 600;
  color: #1976d2;
  margin-bottom: 4px;
}

.summary-label {
  font-size: 12px;
  color: #666;
}
</style>
