package com.unnet.jmanul.common.config.mbg;

import com.unnet.jmanul.common.utils.jwt.JwtComponent;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.Optional;

@Slf4j
@Component
@RequiredArgsConstructor
public class MetaObjectHandler implements com.baomidou.mybatisplus.core.handlers.MetaObjectHandler {

    private final JwtComponent jwtComponent;

    private String getContextUser() {
        String ANONYMOUS_USER = "anonymousUser";
        try {
             Optional<String> user = jwtComponent.getUsernameFromSecurityContext();
             return user.orElse(ANONYMOUS_USER);
         } catch (Exception e) {
             return ANONYMOUS_USER;
         }
    }

    @Override
    public void insertFill(MetaObject metaObject) {
        log.info("mbg insertFill ...");
        // 支持 Date 类型字段（BaseEntity）
        this.strictInsertFill(metaObject, "createdAt", Date.class, new Date());
        this.strictInsertFill(metaObject, "createdBy", String.class, getContextUser());
        this.strictInsertFill(metaObject, "updatedAt", Date.class, new Date());
        this.strictInsertFill(metaObject, "updatedBy", String.class, getContextUser());

        // 支持 LocalDateTime 类型字段（LogFile等实体）
        this.strictInsertFill(metaObject, "createTime", LocalDateTime.class, LocalDateTime.now());
        this.strictInsertFill(metaObject, "updateTime", LocalDateTime.class, LocalDateTime.now());
    }

    @Override
    public void updateFill(MetaObject metaObject) {
        log.info("mbg updateFill ...");
        // 支持 Date 类型字段（BaseEntity）
        this.strictUpdateFill(metaObject, "updatedAt", Date.class, new Date());
        this.strictInsertFill(metaObject, "updatedBy", String.class, getContextUser());

        // 支持 LocalDateTime 类型字段（LogFile等实体）
        this.strictUpdateFill(metaObject, "updateTime", LocalDateTime.class, LocalDateTime.now());
    }

}