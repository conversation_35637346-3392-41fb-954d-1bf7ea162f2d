<script setup>
import HelloWorld from './components/HelloWorld.vue'
import TheWelcome from './components/TheWelcome.vue'
</script>

<template>
  <div id="app">
    <router-view />
  </div>
</template>

<script>
export default {
  name: 'App'
}
</script>

<style>
/* 全局重置样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
  background-color: #f5f5f5;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#app {
  width: 100%;
  height: 100vh;
  color: #2c3e50;
  overflow: hidden;
}

/* Element Plus 组件样式微调 */
.el-container {
  height: 100%;
}

.el-main {
  padding: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .el-aside {
    width: 200px !important;
  }
}

@media (max-width: 480px) {
  .el-aside {
    width: 180px !important;
  }
}
</style>
