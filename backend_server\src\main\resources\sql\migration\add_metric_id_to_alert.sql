-- 为告警表添加指标ID字段的迁移脚本
-- 执行时间：2024-07-17
-- 目的：建立告警与指标记录的关联关系

-- 1. 添加metric_id字段
ALTER TABLE `terminal_alert_info` 
ADD COLUMN `metric_id` varchar(100) DEFAULT NULL COMMENT '关联的指标记录ID（Elasticsearch文档ID）' 
AFTER `current_value`;

-- 2. 添加metric_id字段的索引
ALTER TABLE `terminal_alert_info` 
ADD INDEX `idx_metric_id` (`metric_id`);

-- 3. 验证字段添加成功
SELECT COLUMN_NAME, COLUMN_TYPE, COLUMN_COMMENT 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'terminal_alert_info' 
  AND COLUMN_NAME = 'metric_id';

-- 4. 验证索引添加成功
SHOW INDEX FROM `terminal_alert_info` WHERE Key_name = 'idx_metric_id';
