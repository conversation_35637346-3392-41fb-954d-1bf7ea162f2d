<template>
  <div class="metric-config-page">
    <!-- 主卡片 -->
    <div class="card">
      <div class="card-header">
        <h3>指标配置管理</h3>
        <button class="btn btn-primary" @click="showCreateDialog">
          <i>➕</i> 新增配置
        </button>
      </div>
      <div class="card-body">
        <!-- 搜索过滤区域 -->
        <div class="search-filters">
          <div class="form-group">
            <label>指标类型</label>
            <select class="form-control" v-model="searchParams.metricType">
              <option value="">全部</option>
              <option v-for="type in metricTypeOptions" :key="type.code" :value="type.code">
                {{ type.description }}
              </option>
            </select>
          </div>
          <div class="form-group">
            <label>指标名称</label>
            <input
              type="text"
              class="form-control"
              v-model="searchParams.metricName"
              placeholder="输入指标名称"
              @keyup.enter="searchConfigs"
            />
          </div>
          <div class="form-group">
            <label>状态</label>
            <select class="form-control" v-model="searchParams.isEnabled">
              <option value="">全部</option>
              <option :value="true">启用</option>
              <option :value="false">禁用</option>
            </select>
          </div>
          <div class="form-group">
            <label>告警级别</label>
            <select class="form-control" v-model="searchParams.alertLevel">
              <option value="">全部</option>
              <option v-for="level in alertLevelOptions" :key="level.code" :value="level.code">
                {{ level.description }}
              </option>
            </select>
          </div>
          <div class="form-group button-group">
            <label>&nbsp;</label>
            <div class="button-container">
              <button class="btn btn-primary" @click="searchConfigs">搜索</button>
              <button class="btn btn-secondary" @click="resetSearch">重置</button>
            </div>
          </div>
        </div>

        <!-- 批量操作区域 -->
        <div class="table-actions" v-if="selectedConfigs.length > 0">
          <button
            @click="batchDelete"
            class="btn btn-warning btn-sm"
          >
            <i>🗑️</i>批量删除 ({{ selectedConfigs.length }})
          </button>
        </div>

        <!-- 配置列表表格 -->
        <table class="table">
          <thead>
            <tr>
              <th width="50">
                <input
                  type="checkbox"
                  :checked="isAllSelected"
                  @change="toggleSelectAll"
                />
              </th>
              <th>指标类型</th>
              <th>指标名称</th>
              <th>阈值</th>
              <th>单位</th>
              <th>操作符</th>
              <th>告警级别</th>
              <th>状态</th>
              <th>更新时间</th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="config in configList" :key="config.id" v-show="!loading">
              <td>
                <input
                  type="checkbox"
                  :value="config.id"
                  v-model="selectedConfigs"
                />
              </td>
              <td>{{ config.metricTypeDescription }}</td>
              <td>
                <span class="metric-name">{{ config.metricName }}</span>
              </td>
              <td>
                <span class="threshold-value">{{ config.thresholdValue }}</span>
              </td>
              <td>{{ config.thresholdUnit }}</td>
              <td>
                <span class="comparison-operator">{{ config.comparisonOperator }}</span>
              </td>
              <td>
                <span
                  :class="getAlertLevelBadgeClass(config.alertLevel)"
                  class="badge"
                >
                  {{ config.alertLevelDescription }}
                </span>
              </td>
              <td>
                <span :class="config.isEnabled ? 'status-online' : 'status-offline'">
                  {{ config.isEnabled ? '启用' : '禁用' }}
                </span>
              </td>
              <td>{{ formatDateTime(config.updateTime) }}</td>
              <td>
                <div class="action-buttons">
                  <button
                    @click="showDetailDialogHandler(config)"
                    class="btn btn-primary btn-sm"
                    title="查看详情"
                  >
                    详情
                  </button>
                  <button
                    @click="showEditDialog(config)"
                    class="btn btn-info btn-sm"
                    title="编辑"
                  >
                    编辑
                  </button>
                  <button
                    @click="toggleConfigStatus(config)"
                    :class="config.isEnabled ? 'btn btn-warning btn-sm' : 'btn btn-success btn-sm'"
                    :title="config.isEnabled ? '禁用' : '启用'"
                  >
                    {{ config.isEnabled ? '禁用' : '启用' }}
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>

        <!-- 分页 -->
        <div class="pagination">
          <button
            @click="changePage(pagination.current - 1)"
            :disabled="pagination.current <= 1"
          >
            上一页
          </button>
          <span class="page-info">
            第 {{ pagination.current }} 页，共 {{ Math.ceil(pagination.total / pagination.size) }} 页，共 {{ pagination.total }} 条记录
          </span>
          <button
            @click="changePage(pagination.current + 1)"
            :disabled="pagination.current >= Math.ceil(pagination.total / pagination.size)"
          >
            下一页
          </button>
        </div>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-state">
      <div class="loading-spinner"></div>
      <p>加载中...</p>
    </div>

    <!-- 空状态 -->
    <div v-if="!loading && configList.length === 0" class="card">
      <div class="card-body">
        <div class="empty-state">
          <div class="empty-icon">📊</div>
          <h3>暂无配置数据</h3>
          <p>点击"新增配置"按钮创建第一个指标配置</p>
          <button @click="showCreateDialog" class="btn btn-primary">
            <i>➕</i>新增配置
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- 创建/编辑对话框 -->
  <div v-if="showDialog" class="dialog-overlay" @click="closeDialog">
    <div class="dialog-container" @click.stop>
      <div class="dialog-header">
        <h3 class="dialog-title">
          <span class="icon">{{ isEditing ? '✏️' : '➕' }}</span>
          {{ isEditing ? '编辑' : '新增' }}指标配置
        </h3>
        <button @click="closeDialog" class="dialog-close">✕</button>
      </div>
      <div class="dialog-body">
        <form @submit.prevent="saveConfig">
          <div class="form-row">
            <div class="form-field">
              <label class="field-label required">指标类型</label>
              <div class="metric-type-input-group">
                <input
                  type="text"
                  class="field-input"
                  v-model="formData.metricType"
                  placeholder="请输入或选择指标类型"
                  required
                  :disabled="isEditing"
                  list="metric-type-list"
                />
                <datalist id="metric-type-list">
                  <option v-for="type in metricTypeOptions" :key="type.code" :value="type.code">
                    {{ type.description }}
                  </option>
                </datalist>
                <button
                  type="button"
                  class="btn btn-secondary btn-sm dropdown-btn"
                  @click="showMetricTypeDropdown = !showMetricTypeDropdown"
                  :disabled="isEditing"
                >
                  ▼
                </button>
                <div v-if="showMetricTypeDropdown && !isEditing" class="metric-type-dropdown">
                  <div
                    v-for="type in metricTypeOptions"
                    :key="type.code"
                    class="dropdown-item"
                    @click="selectMetricType(type.code)"
                  >
                    <span class="type-code">{{ type.code }}</span>
                    <span class="type-description">{{ type.description }}</span>
                  </div>
                </div>
              </div>
            </div>
            <div class="form-field">
              <label class="field-label required">指标名称</label>
              <input
                type="text"
                class="field-input"
                v-model="formData.metricName"
                placeholder="请输入指标名称"
                required
                maxlength="100"
              />
            </div>
          </div>
          <div class="form-row">
            <div class="form-field">
              <label class="field-label required">JSON字段路径</label>
              <div class="json-field-input-group">
                <select class="field-select" v-model="formData.jsonFieldPath" required>
                  <option value="">请选择字段路径</option>
                  <option v-for="path in availableFieldPaths" :key="path" :value="path">
                    {{ path }}
                  </option>
                </select>
                <button type="button" class="btn btn-secondary btn-sm" @click="showJsonInputDialog">
                  解析JSON
                </button>
              </div>
            </div>
            <div class="form-field">
              <label class="field-label">指标描述</label>
              <input
                type="text"
                class="field-input"
                v-model="formData.metricDescription"
                placeholder="请输入指标描述"
                maxlength="200"
              />
            </div>
          </div>
          <div class="form-row">
            <div class="form-field">
              <label class="field-label required">阈值</label>
              <input
                type="number"
                class="field-input"
                v-model="formData.thresholdValue"
                placeholder="请输入阈值"
                required
                min="0"
                max="999999.99"
                step="0.01"
              />
            </div>
            <div class="form-field">
              <label class="field-label required">阈值单位</label>
              <input
                type="text"
                class="field-input"
                v-model="formData.thresholdUnit"
                placeholder="如：%、°C、days等"
                required
                maxlength="20"
              />
            </div>
          </div>
          <div class="form-row">
            <div class="form-field">
              <label class="field-label required">比较操作符</label>
              <select class="field-select" v-model="formData.comparisonOperator" required>
                <option value="">请选择操作符</option>
                <option value=">=">&gt;=</option>
                <option value=">">&gt;</option>
                <option value="<=">&lt;=</option>
                <option value="<">&lt;</option>
                <option value="=">=</option>
              </select>
            </div>
            <div class="form-field">
              <label class="field-label required">告警级别</label>
              <select class="field-select" v-model="formData.alertLevel" required>
                <option value="">请选择告警级别</option>
                <option v-for="level in alertLevelOptions" :key="level.code" :value="level.code">
                  {{ level.description }}
                </option>
              </select>
            </div>
          </div>

          <div class="form-field">
            <label class="field-label">告警消息模板</label>
            <textarea
              class="field-input"
              v-model="formData.alertMessage"
              placeholder="请输入告警消息模板，可使用 {current_value} 和 {threshold_value} 占位符"
              maxlength="500"
              rows="3"
            ></textarea>
          </div>
          <div class="form-row">
            <div class="form-field">
              <label class="field-label">排序</label>
              <input
                type="number"
                class="field-input"
                v-model="formData.sortOrder"
                placeholder="排序值，数字越小越靠前"
                min="0"
              />
            </div>
            <div class="form-field">
              <label class="field-label">状态</label>
              <div class="checkbox-group">
                <label class="checkbox-label">
                  <input type="checkbox" v-model="formData.isEnabled" />
                  启用配置
                </label>
              </div>
            </div>
          </div>
          <div class="form-field">
            <label class="field-label">备注</label>
            <textarea
              class="field-input"
              v-model="formData.remark"
              placeholder="请输入备注信息"
              maxlength="500"
              rows="2"
            ></textarea>
          </div>
        </form>
      </div>
      <div class="dialog-footer">
        <button @click="closeDialog" class="btn btn-secondary">取消</button>
        <button @click="saveConfig" class="btn btn-primary" :disabled="saving">
          {{ saving ? '保存中...' : '保存' }}
        </button>
      </div>
    </div>
  </div>

  <!-- 详情查看对话框 -->
  <div v-if="showDetailDialog" class="dialog-overlay" @click="closeDetailDialog">
    <div class="dialog-container detail-dialog" @click.stop>
      <div class="dialog-header">
        <h3 class="dialog-title">
          <span class="icon">📊</span>
          指标配置详情
        </h3>
        <button @click="closeDetailDialog" class="dialog-close">✕</button>
      </div>
      <div class="dialog-body">
        <div class="detail-content" v-if="currentDetailConfig">
          <!-- 基本信息 -->
          <div class="detail-section">
            <h4 class="section-title">基本信息</h4>
            <div class="detail-grid">
              <div class="detail-item">
                <label class="detail-label">指标类型：</label>
                <span class="detail-value">{{ currentDetailConfig.metricTypeDescription }}</span>
              </div>
              <div class="detail-item">
                <label class="detail-label">指标名称：</label>
                <span class="detail-value">{{ currentDetailConfig.metricName }}</span>
              </div>
              <div class="detail-item">
                <label class="detail-label">指标描述：</label>
                <span class="detail-value">{{ currentDetailConfig.metricDescription || '无' }}</span>
              </div>
              <div class="detail-item">
                <label class="detail-label">JSON字段路径：</label>
                <span class="detail-value json-path">{{ currentDetailConfig.jsonFieldPath || '无' }}</span>
              </div>
            </div>
          </div>

          <!-- 阈值配置 -->
          <div class="detail-section">
            <h4 class="section-title">阈值配置</h4>
            <div class="detail-grid">
              <div class="detail-item">
                <label class="detail-label">阈值：</label>
                <span class="detail-value threshold-value">{{ currentDetailConfig.thresholdValue }}</span>
              </div>
              <div class="detail-item">
                <label class="detail-label">单位：</label>
                <span class="detail-value">{{ currentDetailConfig.thresholdUnit }}</span>
              </div>
              <div class="detail-item">
                <label class="detail-label">比较操作符：</label>
                <span class="detail-value operator">{{ currentDetailConfig.comparisonOperator }}</span>
              </div>
              <div class="detail-item">
                <label class="detail-label">告警级别：</label>
                <span :class="getAlertLevelBadgeClass(currentDetailConfig.alertLevel)" class="badge">
                  {{ currentDetailConfig.alertLevelDescription }}
                </span>
              </div>
            </div>
          </div>

          <!-- 告警配置 -->
          <div class="detail-section">
            <h4 class="section-title">告警配置</h4>
            <div class="detail-grid">
              <div class="detail-item full-width">
                <label class="detail-label">告警消息：</label>
                <span class="detail-value">{{ currentDetailConfig.alertMessage || '无' }}</span>
              </div>
              <div class="detail-item">
                <label class="detail-label">状态：</label>
                <span :class="currentDetailConfig.isEnabled ? 'status-online' : 'status-offline'">
                  {{ currentDetailConfig.isEnabled ? '启用' : '禁用' }}
                </span>
              </div>
              <div class="detail-item">
                <label class="detail-label">排序顺序：</label>
                <span class="detail-value">{{ currentDetailConfig.sortOrder || 0 }}</span>
              </div>
            </div>
          </div>

          <!-- 系统信息 -->
          <div class="detail-section">
            <h4 class="section-title">系统信息</h4>
            <div class="detail-grid">
              <div class="detail-item">
                <label class="detail-label">创建时间：</label>
                <span class="detail-value">{{ formatDateTime(currentDetailConfig.createTime) }}</span>
              </div>
              <div class="detail-item">
                <label class="detail-label">更新时间：</label>
                <span class="detail-value">{{ formatDateTime(currentDetailConfig.updateTime) }}</span>
              </div>
              <div class="detail-item full-width" v-if="currentDetailConfig.remark">
                <label class="detail-label">备注：</label>
                <span class="detail-value">{{ currentDetailConfig.remark }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="dialog-footer">
        <button @click="closeDetailDialog" class="btn btn-secondary">关闭</button>
        <button @click="editFromDetail" class="btn btn-primary">编辑配置</button>
      </div>
    </div>
  </div>

  <!-- JSON字段解析对话框 -->
  <div v-if="showJsonDialog" class="dialog-overlay" @click="closeJsonDialog">
    <div class="dialog-container json-dialog" @click.stop>
      <div class="dialog-header">
        <h3 class="dialog-title">
          <span class="icon">🔍</span>
          解析JSON字段路径
        </h3>
        <button @click="closeJsonDialog" class="dialog-close">✕</button>
      </div>
      <div class="dialog-body">
        <div class="json-parse-container">
          <!-- 左侧：JSON数据输入 -->
          <div class="json-input-section">
            <div class="form-field">
              <label class="field-label">JSON数据</label>
              <textarea
                class="field-input json-textarea"
                v-model="jsonInputData"
                placeholder="请粘贴完整的JSON数据..."
                rows="20"
              ></textarea>
            </div>
          </div>

          <!-- 右侧：字段路径列表 -->
          <div class="json-paths-section">
            <div class="form-field">
              <label class="field-label">
                可用字段路径
                <span class="path-count" v-if="extractedPaths.length > 0">
                  ({{ extractedPaths.length }}个)
                </span>
              </label>
              <div class="path-list" v-if="extractedPaths.length > 0">
                <div
                  v-for="path in extractedPaths"
                  :key="path"
                  class="path-item"
                  @click="selectPath(path)"
                  :class="{
                    'selected': selectedPath === path,
                    'used': isPathUsed(path)
                  }"
                >
                  <span class="path-text">{{ path }}</span>
                  <span v-if="isPathUsed(path)" class="used-indicator" title="此字段已被其他指标配置使用">
                    已使用
                  </span>
                </div>
              </div>
              <div v-else class="empty-paths">
                <p>暂无可用字段路径</p>
                <p class="hint">请先输入JSON数据并点击"解析字段"按钮</p>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="dialog-footer">
        <button @click="closeJsonDialog" class="btn btn-secondary">取消</button>
        <button @click="parseJsonFields" class="btn btn-info" :disabled="!jsonInputData.trim()">
          解析字段
        </button>
        <button @click="confirmSelectedPath" class="btn btn-primary" :disabled="!selectedPath">
          确认选择
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { metricConfigApi, cacheApi } from '@/api/services'

export default {
  name: 'MetricConfig',
  setup() {
    // 响应式数据
    const loading = ref(false)
    const saving = ref(false)
    const showDialog = ref(false)
    const isEditing = ref(false)
    const configList = ref([])
    const selectedConfigs = ref([])
    const metricTypeOptions = ref([])
    const alertLevelOptions = ref([])

    // JSON字段解析相关
    const showJsonDialog = ref(false)
    const jsonInputData = ref('')
    const extractedPaths = ref([])
    const selectedPath = ref('')
    const availableFieldPaths = ref([])

    // 详情查看相关
    const showDetailDialog = ref(false)
    const currentDetailConfig = ref(null)

    // 指标类型下拉相关
    const showMetricTypeDropdown = ref(false)

    // 已使用的字段路径
    const usedFieldPaths = ref([])

    // 搜索参数
    const searchParams = reactive({
      metricType: '',
      metricName: '',
      isEnabled: '',
      alertLevel: ''
    })

    // 分页参数
    const pagination = reactive({
      current: 1,
      size: 10,
      total: 0
    })

    // 表单数据
    const formData = reactive({
      id: null,
      metricType: '',
      metricName: '',
      metricDescription: '',
      jsonFieldPath: '',
      thresholdValue: '',
      thresholdUnit: '',
      comparisonOperator: '',
      alertLevel: '',
      alertMessage: '',
      isEnabled: true,
      sortOrder: 0,
      remark: ''
    })

    // 计算属性
    const isAllSelected = computed(() => {
      return configList.value.length > 0 && selectedConfigs.value.length === configList.value.length
    })

    // 方法
    const loadConfigs = async () => {
      loading.value = true
      try {
        const params = {
          current: pagination.current,
          size: pagination.size,
          ...searchParams
        }

        const response = await metricConfigApi.getConfigList(params)
        // axios在成功时直接返回data，不需要检查status
        configList.value = response.records || []
        pagination.total = response.total || 0

        // 加载已使用的字段路径
        await loadUsedFieldPaths()
      } catch (error) {
        console.error('加载配置列表失败:', error)
        ElMessage.error('加载配置列表失败: ' + error.message)
      } finally {
        loading.value = false
      }
    }

    const loadOptions = async () => {
      try {
        const [typeResponse, levelResponse] = await Promise.all([
          metricConfigApi.getMetricTypeOptions(),
          metricConfigApi.getAlertLevelOptions()
        ])

        // 指标类型现在直接是中文描述的字符串数组
        metricTypeOptions.value = (typeResponse || []).map(item => ({
          code: item.code || item,
          description: item.description || item
        }))
        alertLevelOptions.value = levelResponse || []

        // 预设常用的JSON字段路径
        if (availableFieldPaths.value.length === 0) {
          availableFieldPaths.value = [
            'cpu_temp',
            'memory_percent',
            'disk_usage[*].percent',
            'disk_data_percent',
            'disk_system_percent',
            'expired_date',
            'cpu_percent',
            'temperatures.soc-thermal',
            'temperatures.gpu-thermal',
            'memory_usage.percent',
            'disk_usage[0].percent'
          ]
        }
      } catch (error) {
        console.error('加载选项失败:', error)
        ElMessage.error('加载选项失败: ' + error.message)
      }
    }

    const searchConfigs = () => {
      pagination.current = 1
      loadConfigs()
    }

    const resetSearch = () => {
      Object.assign(searchParams, {
        metricType: '',
        metricName: '',
        isEnabled: '',
        alertLevel: ''
      })
      pagination.current = 1
      loadConfigs()
    }

    const changePage = (page) => {
      if (page >= 1 && page <= Math.ceil(pagination.total / pagination.size)) {
        pagination.current = page
        loadConfigs()
      }
    }

    const toggleSelectAll = () => {
      if (isAllSelected.value) {
        selectedConfigs.value = []
      } else {
        selectedConfigs.value = configList.value.map(config => config.id)
      }
    }

    const showCreateDialog = () => {
      isEditing.value = false
      resetFormData()
      showDialog.value = true
    }

    const showEditDialog = (config) => {
      isEditing.value = true
      Object.assign(formData, {
        id: config.id,
        metricType: config.metricType,
        metricName: config.metricName,
        metricDescription: config.metricDescription || '',
        jsonFieldPath: config.jsonFieldPath || '',
        thresholdValue: config.thresholdValue,
        thresholdUnit: config.thresholdUnit,
        comparisonOperator: config.comparisonOperator,
        alertLevel: config.alertLevel,
        alertMessage: config.alertMessage || '',
        isEnabled: config.isEnabled,
        sortOrder: config.sortOrder || 0,
        remark: config.remark || ''
      })

      // 确保当前的JSON字段路径在可选项中
      if (config.jsonFieldPath && !availableFieldPaths.value.includes(config.jsonFieldPath)) {
        availableFieldPaths.value = [config.jsonFieldPath, ...availableFieldPaths.value]
      }

      showDialog.value = true
    }

    const closeDialog = () => {
      showDialog.value = false
      showMetricTypeDropdown.value = false
      resetFormData()
    }

    const resetFormData = () => {
      Object.assign(formData, {
        id: null,
        metricType: '',
        metricName: '',
        metricDescription: '',
        jsonFieldPath: '',
        thresholdValue: '',
        thresholdUnit: '',
        comparisonOperator: '',
        alertLevel: '',
        alertMessage: '',
        isEnabled: true,
        sortOrder: 0,
        remark: ''
      })
    }

    const saveConfig = async () => {
      saving.value = true
      try {
        const data = { ...formData }
        delete data.id

        let response
        if (isEditing.value) {
          response = await metricConfigApi.updateConfig(formData.id, data)
        } else {
          response = await metricConfigApi.createConfig(data)
        }

        // axios在成功时直接返回data
        ElMessage.success(response.message || (isEditing.value ? '更新成功' : '创建成功'))
        closeDialog()
        loadConfigs()

        // 刷新backend_server的缓存
        await refreshCache()
      } catch (error) {
        console.error('保存配置失败:', error)
        ElMessage.error('保存配置失败: ' + error.message)
      } finally {
        saving.value = false
      }
    }

    const toggleConfigStatus = async (config) => {
      try {
        const newStatus = !config.isEnabled
        const response = await metricConfigApi.toggleConfig(config.id, newStatus)

        // axios在成功时直接返回data
        ElMessage.success(response.message || (newStatus ? '启用成功' : '禁用成功'))
        loadConfigs()

        // 刷新backend_server的缓存
        await refreshCache()
      } catch (error) {
        console.error('切换状态失败:', error)
        ElMessage.error('切换状态失败: ' + error.message)
      }
    }

    const deleteConfig = async (config) => {
      try {
        await ElMessageBox.confirm(
          `确定要删除"${config.metricName}"配置吗？`,
          '确认删除',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          }
        )

        const response = await metricConfigApi.deleteConfig(config.id)

        // axios在成功时直接返回data
        ElMessage.success(response.message || '删除成功')
        loadConfigs()

        // 刷新backend_server的缓存
        await refreshCache()
      } catch (error) {
        if (error === 'cancel') {
          return
        }
        console.error('删除配置失败:', error)
        ElMessage.error('删除配置失败: ' + error.message)
      }
    }

    const batchDelete = async () => {
      if (selectedConfigs.value.length === 0) {
        ElMessage.warning('请先选择要删除的配置')
        return
      }

      try {
        await ElMessageBox.confirm(
          `确定要删除选中的${selectedConfigs.value.length}项配置吗？`,
          '确认批量删除',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          }
        )

        const response = await metricConfigApi.batchDeleteConfig(selectedConfigs.value)

        // axios在成功时直接返回data
        ElMessage.success(response.message || '批量删除成功')
        selectedConfigs.value = []
        loadConfigs()

        // 刷新backend_server的缓存
        await refreshCache()
      } catch (error) {
        if (error === 'cancel') {
          return
        }
        console.error('批量删除失败:', error)
        ElMessage.error('批量删除失败: ' + error.message)
      }
    }

    const getAlertLevelBadgeClass = (level) => {
      switch (level) {
        case 'LOW':
          return 'badge-success'
        case 'MEDIUM':
          return 'badge-info'
        case 'HIGH':
          return 'badge-warning'
        case 'CRITICAL':
          return 'badge-danger'
        default:
          return 'badge-info'
      }
    }

    const formatDateTime = (dateTimeStr) => {
      if (!dateTimeStr) return ''

      try {
        const date = new Date(dateTimeStr)
        return date.toLocaleString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit',
          hour12: false
        })
      } catch (error) {
        return dateTimeStr
      }
    }

    // 刷新缓存方法
    const refreshCache = async () => {
      try {
        console.log('开始刷新指标阈值配置缓存...')
        const response = await cacheApi.refreshThresholdConfigCache()
        console.log('缓存刷新成功:', response)
        // 不显示成功消息，避免过多提示
      } catch (error) {
        console.error('刷新缓存失败:', error)
        // 缓存刷新失败不影响主流程，只记录日志
        console.warn('缓存刷新失败，但不影响配置操作:', error.message)
      }
    }

    // JSON字段解析相关方法
    const showJsonInputDialog = () => {
      // 设置默认JSON数据
      if (!jsonInputData.value) {
        jsonInputData.value = JSON.stringify({
          'hostname': 'ec_3568_25030031',
          'device_id': '_0fd94938951f4a64bb11c6817a81f7e7',
          'identity_mac': 'de0765523e60',
          'uptime': 5916.29,
          'cpu_temp': 58.888,
          'temperatures': {
            'soc-thermal': 58.888,
            'gpu-thermal': 57.777
          },
          'cpu_usage': {
            'num': '4',
            'core': '4',
            'thread': '4',
            'user': '46.3',
            'sys': '9.7',
            'idle': '43.7'
          },
          'cpu_percent': 43.2,
          'memory_percent': 50.9,
          'memory_usage': {
            'total': 4085002240,
            'available': 2006622208,
            'percent': 50.9,
            'used': 2024525824,
            'free': 1373265920,
            'active': 229298176,
            'inactive': 2192863232,
            'buffers': 56995840,
            'cached': 630214656,
            'shared': 1032192,
            'slab': 136617984
          },
          'disk_usage': [{
            'device': '/dev/mmcblk0p3',
            'mountpoint': '/',
            'fstype': 'ext4',
            'total': 30602608640,
            'used': 15916904448,
            'free': 13409980416,
            'percent': 54.3
          }],
          'disk_data_percent': 0,
          'disk_system_percent': 54.3,
          'expired_date': '2035-03-09 22:47:20'
        }, null, 2)
      }
      showJsonDialog.value = true
    }

    const closeJsonDialog = () => {
      showJsonDialog.value = false
      extractedPaths.value = []
      selectedPath.value = ''
    }

    const parseJsonFields = async () => {
      if (!jsonInputData.value.trim()) {
        ElMessage.warning('请输入JSON数据')
        return
      }

      try {
        // 验证JSON格式
        JSON.parse(jsonInputData.value)

        // 调用后端API解析字段路径
        const response = await metricConfigApi.extractFieldPaths(jsonInputData.value)
        extractedPaths.value = response || []

        if (extractedPaths.value.length === 0) {
          ElMessage.warning('未找到可用的字段路径')
        } else {
          ElMessage.success(`成功解析出 ${extractedPaths.value.length} 个字段路径`)
        }
      } catch (error) {
        if (error.name === 'SyntaxError') {
          ElMessage.error('JSON格式错误，请检查输入')
        } else {
          ElMessage.error('解析字段路径失败: ' + error.message)
        }
      }
    }

    const selectPath = (path) => {
      selectedPath.value = path
    }

    const confirmSelectedPath = () => {
      if (!selectedPath.value) {
        ElMessage.warning('请选择一个字段路径')
        return
      }

      formData.jsonFieldPath = selectedPath.value
      availableFieldPaths.value = [...extractedPaths.value]
      closeJsonDialog()
      ElMessage.success('字段路径已选择')
    }

    // 详情查看相关方法
    const showDetailDialogHandler = (config) => {
      currentDetailConfig.value = { ...config }
      showDetailDialog.value = true
    }

    const closeDetailDialog = () => {
      showDetailDialog.value = false
      currentDetailConfig.value = null
    }

    const editFromDetail = () => {
      if (currentDetailConfig.value) {
        closeDetailDialog()
        showEditDialog(currentDetailConfig.value)
      }
    }

    // 指标类型相关方法
    const selectMetricType = (typeCode) => {
      formData.metricType = typeCode
      showMetricTypeDropdown.value = false
    }

    // 检查字段路径是否已被使用
    const isPathUsed = (path) => {
      return usedFieldPaths.value.includes(path)
    }

    // 获取已使用的字段路径
    const loadUsedFieldPaths = async () => {
      try {
        // 从当前配置列表中提取已使用的字段路径
        const paths = configList.value
          .filter(config => config.jsonFieldPath && config.jsonFieldPath.trim())
          .map(config => config.jsonFieldPath)

        usedFieldPaths.value = [...new Set(paths)] // 去重
      } catch (error) {
        console.error('获取已使用字段路径失败:', error)
      }
    }

    // 生命周期钩子
    onMounted(() => {
      loadOptions()
      loadConfigs()
    })

    return {
      loading,
      saving,
      showDialog,
      isEditing,
      configList,
      selectedConfigs,
      metricTypeOptions,
      alertLevelOptions,
      searchParams,
      pagination,
      formData,
      isAllSelected,
      // JSON字段解析相关
      showJsonDialog,
      jsonInputData,
      extractedPaths,
      selectedPath,
      availableFieldPaths,
      // 详情查看相关
      showDetailDialog,
      currentDetailConfig,
      // 指标类型下拉相关
      showMetricTypeDropdown,
      // 已使用字段路径
      usedFieldPaths,
      // 方法
      loadConfigs,
      searchConfigs,
      resetSearch,
      changePage,
      toggleSelectAll,
      showCreateDialog,
      showEditDialog,
      closeDialog,
      saveConfig,
      toggleConfigStatus,
      deleteConfig,
      batchDelete,
      getAlertLevelBadgeClass,
      formatDateTime,
      // JSON解析方法
      showJsonInputDialog,
      closeJsonDialog,
      parseJsonFields,
      selectPath,
      confirmSelectedPath,
      // 详情查看方法
      showDetailDialogHandler,
      closeDetailDialog,
      editFromDetail,
      // 指标类型相关方法
      selectMetricType,
      // 字段路径相关方法
      isPathUsed,
      loadUsedFieldPaths
    }
  }
}
</script>

<style scoped>
.metric-config-page {
  padding: 20px;
  background: #f5f7fa;
  min-height: 100vh;
}

/* 卡片样式 */
.card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.card-header {
  padding: 20px;
  border-bottom: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  color: #333;
  font-size: 18px;
  font-weight: 600;
}

.card-body {
  padding: 20px;
}

/* 搜索过滤样式 */
.search-filters {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.form-group {
  display: flex;
  flex-direction: column;
  min-width: 150px;
}

.form-group label {
  margin-bottom: 5px;
  font-weight: 500;
  color: #333;
  font-size: 14px;
}

.form-control {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  height: 38px;
  box-sizing: border-box;
}

.form-control:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
}

.button-group {
  justify-content: flex-end;
  align-items: flex-end;
}

.button-container {
  display: flex;
  gap: 10px;
  align-items: center;
  height: 38px; /* 与form-control高度一致 */
}

/* 按钮样式 */
.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
  transition: all 0.2s;
  height: 38px;
  box-sizing: border-box;
}

.btn-primary {
  background: #667eea;
  color: white;
}

.btn-primary:hover {
  background: #5a6fd8;
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background: #5a6268;
}

.btn-success {
  background: #28a745;
  color: white;
}

.btn-success:hover {
  background: #218838;
}

.btn-warning {
  background: #ffc107;
  color: #212529;
}

.btn-warning:hover {
  background: #e0a800;
}

.btn-info {
  background: #17a2b8;
  color: white;
}

.btn-info:hover {
  background: #138496;
}

.btn-sm {
  padding: 5px 10px;
  font-size: 12px;
}

/* 表格样式 */
.table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 15px;
}

.table th,
.table td {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid #eee;
}

.table th {
  background: #f8f9fa;
  font-weight: 600;
  color: #333;
}

.table tr:hover {
  background: #f8f9fa;
}

/* 状态样式 */
.status-online {
  color: #28a745;
  font-weight: 500;
}

.status-offline {
  color: #dc3545;
  font-weight: 500;
}

.badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.badge-info {
  background: #d1ecf1;
  color: #0c5460;
}

.badge-success {
  background: #d4edda;
  color: #155724;
}

.badge-warning {
  background: #fff3cd;
  color: #856404;
}

.badge-danger {
  background: #f8d7da;
  color: #721c24;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  gap: 5px;
  flex-wrap: wrap;
}

/* 表格操作区域 */
.table-actions {
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  gap: 10px;
}

/* 分页样式 */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
  margin-top: 20px;
}

.pagination button {
  padding: 8px 12px;
  border: 1px solid #ddd;
  background: white;
  cursor: pointer;
  border-radius: 4px;
}

.pagination button:hover:not(:disabled) {
  background: #f8f9fa;
}

.pagination button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  background: white !important;
  color: #999 !important;
  border-color: #ddd !important;
}

.page-info {
  color: #666;
  font-size: 14px;
}

/* 特殊样式 */
.metric-name {
  font-weight: 500;
  color: #667eea;
}

.threshold-value {
  font-weight: 600;
  color: #333;
}

.comparison-operator {
  font-family: monospace;
  background: #f0f0f0;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 12px;
}

/* 加载状态 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 50px 0;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 15px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 0;
  text-align: center;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 20px;
  color: #ccc;
}

.empty-state h3 {
  margin: 0 0 10px 0;
  color: #333;
  font-size: 20px;
}

.empty-state p {
  margin: 0 0 20px 0;
  color: #666;
  font-size: 16px;
}

/* 对话框样式 */
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(2px);
}

.dialog-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  width: 90%;
  max-width: 800px;
  max-height: 90vh;
  overflow: hidden;
  animation: dialogSlideIn 0.3s ease-out;
}

@keyframes dialogSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.dialog-header {
  padding: 24px 24px 16px 24px;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.dialog-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
  display: flex;
  align-items: center;
  gap: 8px;
}

.dialog-title .icon {
  font-size: 18px;
}

.dialog-close {
  background: none;
  border: none;
  width: 32px;
  height: 32px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #6b7280;
  transition: all 0.2s;
}

.dialog-close:hover {
  background: #f3f4f6;
  color: #374151;
}

.dialog-body {
  padding: 24px;
  max-height: 60vh;
  overflow-y: auto;
}

.dialog-footer {
  padding: 15px 20px 20px 20px;
  text-align: center;
  border-top: 1px solid #eee;
  margin-top: 20px;
}

.dialog-footer .btn {
  margin: 0 5px;
}

/* 表单样式 */
.form-row {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.form-field {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.field-label {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 6px;
}

.field-label.required::after {
  content: ' *';
  color: #ef4444;
}

.field-input,
.field-select {
  padding: 10px 12px;
  border: 1.5px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  transition: all 0.2s;
  background: white;
}

.field-input:focus,
.field-select:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.checkbox-group {
  margin-top: 10px;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .search-filters {
    flex-direction: column;
    gap: 15px;
  }

  .dialog-container {
    width: 95%;
    margin: 20px;
  }

  .form-row {
    flex-direction: column;
    gap: 16px;
  }

  .table {
    font-size: 12px;
  }

  .table th,
  .table td {
    padding: 8px;
  }

  .action-buttons {
    flex-direction: column;
    gap: 3px;
  }
}

/* 额外的样式 - 这些在之前的编辑中被意外删除了 */
.checkbox-group {
  margin-top: 10px;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
}

/* JSON字段解析相关样式 */
.json-field-input-group {
  display: flex;
  gap: 10px;
  align-items: center;
}

.json-field-input-group .field-select {
  flex: 1;
}

.json-dialog {
  max-width: 900px;
  width: 95%;
}

.json-textarea {
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.4;
  resize: vertical;
}

.path-list {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 5px;
}

.path-item {
  padding: 8px 12px;
  cursor: pointer;
  border-radius: 4px;
  margin-bottom: 2px;
  font-family: 'Courier New', monospace;
  font-size: 13px;
  transition: all 0.2s;
}

.path-item:hover {
  background: #f0f0f0;
}

.path-item.selected {
  background: #667eea;
  color: white;
}

.path-item.used {
  background: #fff2e8;
  border-color: #ffb366;
  opacity: 0.8;
}

.path-item.used .path-text {
  color: #d46b08;
}

.used-indicator {
  background: #fa8c16;
  color: white;
  font-size: 11px;
  padding: 2px 6px;
  border-radius: 10px;
  margin-left: 8px;
}

/* 详情对话框样式 */
.detail-dialog {
  max-width: 800px;
  width: 90%;
  max-height: 90vh;
}

.detail-dialog .dialog-body {
  max-height: calc(90vh - 120px);
  overflow-y: auto;
}

.detail-content {
  /* 移除内层滚动条 */
}

.detail-section {
  margin-bottom: 24px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.section-title {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
  border-bottom: 2px solid #667eea;
  padding-bottom: 8px;
}

.detail-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.detail-item {
  display: flex;
  align-items: flex-start;
  padding: 8px 0;
}

.detail-item.full-width {
  grid-column: 1 / -1;
}

.detail-label {
  font-weight: 600;
  color: #666;
  min-width: 120px;
  margin-right: 12px;
  flex-shrink: 0;
}

.detail-value {
  color: #333;
  flex: 1;
  word-break: break-all;
}

.detail-value.json-path {
  font-family: 'Courier New', monospace;
  background: #f0f0f0;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 13px;
}

.detail-value.threshold-value {
  font-weight: 600;
  color: #e74c3c;
}

.detail-value.operator {
  font-family: 'Courier New', monospace;
  background: #e8f4fd;
  padding: 2px 6px;
  border-radius: 3px;
  color: #1890ff;
}

/* 指标类型输入组样式 */
.metric-type-input-group {
  position: relative;
  display: flex;
  align-items: center;
}

.metric-type-input-group .field-input {
  flex: 1;
  margin-right: 8px;
}

.dropdown-btn {
  padding: 8px 12px;
  border: 1px solid #ddd;
  background: #f8f9fa;
  cursor: pointer;
}

.dropdown-btn:hover {
  background: #e9ecef;
}

.metric-type-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 40px;
  background: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  max-height: 200px;
  overflow-y: auto;
}

.dropdown-item {
  padding: 8px 12px;
  cursor: pointer;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.dropdown-item:hover {
  background: #f8f9fa;
}

.dropdown-item:last-child {
  border-bottom: none;
}

.type-code {
  font-family: monospace;
  font-weight: 600;
  color: #333;
}

.type-description {
  color: #666;
  font-size: 13px;
}

/* JSON解析容器样式 */
.json-parse-container {
  display: flex;
  gap: 20px;
  height: 500px;
}

.json-input-section {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.json-paths-section {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.json-input-section .form-field,
.json-paths-section .form-field {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.json-textarea {
  flex: 1;
  resize: none;
  font-family: 'Courier New', monospace;
  font-size: 13px;
}

.path-list {
  flex: 1;
  overflow-y: auto;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 8px;
}

.path-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  margin-bottom: 4px;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
}

.path-item:hover {
  border-color: #667eea;
  background: #f0f4ff;
}

.path-text {
  font-family: 'Courier New', monospace;
  font-size: 13px;
  flex: 1;
}

.path-count {
  color: #666;
  font-size: 12px;
  margin-left: 8px;
}

.empty-paths {
  text-align: center;
  color: #999;
  padding: 40px 20px;
}

.empty-paths .hint {
  font-size: 13px;
  margin-top: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .detail-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .detail-dialog {
    width: 95%;
    margin: 10px;
  }

  .detail-label {
    min-width: 100px;
  }

  .json-parse-container {
    flex-direction: column;
    height: auto;
  }

  .json-input-section,
  .json-paths-section {
    height: 250px;
  }

  .metric-type-dropdown {
    right: 0;
  }
}
</style>
