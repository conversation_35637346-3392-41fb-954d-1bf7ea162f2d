package com.unnet.jmanul.business.service;

import com.unnet.jmanul.business.service.dto.overview.OverviewStatsResp;
import com.unnet.jmanul.business.service.dto.overview.OfflineTerminalResp;
import com.unnet.jmanul.business.service.dto.overview.AlertTerminalResp;

import java.util.List;

/**
 * 概览服务接口
 * 提供概览页面所需的统计信息、离线终端列表、告警终端列表等数据
 */
public interface IOverviewService {

    /**
     * 获取概览统计信息
     * 包括终端总数、在线数、离线数、告警数等
     * 
     * @return 概览统计信息
     */
    OverviewStatsResp getOverviewStats();

    /**
     * 获取离线终端列表
     * 用于概览页面显示当前离线的终端
     * 
     * @param limit 限制返回的数量
     * @return 离线终端列表
     */
    List<OfflineTerminalResp> getOfflineTerminals(int limit);

    /**
     * 获取告警终端列表
     * 用于概览页面显示当前有告警的终端
     * 
     * @param limit 限制返回的数量
     * @return 告警终端列表
     */
    List<AlertTerminalResp> getAlertTerminals(int limit);
}
