package com.unnet.jmanul.common.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 统一API响应格式
 * 
 * <AUTHOR>
 * @since 2025-07-28
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ApiResult<T> {
    
    /**
     * 业务状态码
     */
    private Integer code;
    
    /**
     * 响应消息
     */
    private String message;
    
    /**
     * 响应数据
     */
    private T data;
    
    /**
     * 成功响应
     */
    public static <T> ApiResult<T> success(T data) {
        return new ApiResult<>(200, "操作成功", data);
    }
    
    /**
     * 成功响应（带消息）
     */
    public static <T> ApiResult<T> success(String message, T data) {
        return new ApiResult<>(200, message, data);
    }
    
    /**
     * 成功响应（仅消息）
     */
    public static <T> ApiResult<T> success(String message) {
        return new ApiResult<>(200, message, null);
    }
    
    /**
     * 失败响应
     */
    public static <T> ApiResult<T> error(Integer code, String message) {
        return new ApiResult<>(code, message, null);
    }
    
    /**
     * 失败响应（默认500错误码）
     */
    public static <T> ApiResult<T> error(String message) {
        return new ApiResult<>(500, message, null);
    }
}
