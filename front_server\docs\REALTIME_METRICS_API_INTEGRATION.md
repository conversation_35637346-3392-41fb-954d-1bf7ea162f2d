# 实时指标页面API集成说明

## 概述

实时指标页面现在完全使用真实的后端API，不再依赖Mock数据。页面会：
1. 获取当天从凌晨到现在的历史数据
2. 每10秒轮询获取最新指标数据
3. 将新数据添加到图表中显示

## API调用流程

### 1. 页面初始化
```
页面加载 → 获取今日历史数据 → 获取最新数据 → 启动10秒轮询
```

### 2. 使用的API接口

#### 获取今日历史数据
```http
GET /api/v1/admin/terminal/metrics/device/{identityMac}/range
?startTime=2024-07-28 00:00:00
&endTime=2024-07-28 23:59:59
&page=0
&size=1000
```

#### 获取最新指标数据（每10秒调用）
```http
GET /api/v1/admin/terminal/metrics/device/{identityMac}/latest
```

## 数据处理流程

### 1. 历史数据处理
```javascript
// 1. 调用API获取今日历史数据
const response = await terminalApi.getTodayHistoryMetrics(identityMac)

// 2. 按时间正序排列（API返回倒序）
const sortedData = response.content.sort((a, b) => 
  new Date(a.metricTime) - new Date(b.metricTime)
)

// 3. 转换为前端图表格式
const historyData = sortedData.map(item => ({
  time: new Date(item.metricTime),
  cpuUsage: item.cpuUsage || 0,
  memory: item.memoryUsage || 0,
  // ... 其他指标
}))

// 4. 存储到metricsHistory用于图表显示
metricsHistory.value = historyData
```

### 2. 实时数据处理
```javascript
// 1. 每10秒调用最新数据API
const response = await terminalApi.getRealTimeMetrics(identityMac)

// 2. 转换ES数据格式为前端格式
const processedData = convertBackendMetricsToFrontend(response)

// 3. 更新当前指标显示
metrics.value = processedData

// 4. 添加到历史记录用于图表
addToHistory(processedData)
```

## 数据格式转换

### ES数据格式（后端返回）
```json
{
  "identityMac": "de:07:65:52:3e:60",
  "hostname": "ec_3568_25030031",
  "metricTime": "2024-07-28T14:30:25.000Z",
  "cpuUsage": {
    "user": 15.5,
    "system": 8.2,
    "idle": 76.3
  },
  "cpuPercent": 23.7,
  "cpuTemp": 58.3,
  "memoryUsage": {
    "total": 8589934592,
    "used": 5774458880,
    "available": 2815475712,
    "percent": 67.2
  },
  "diskUsage": [
    {
      "device": "/dev/mmcblk0p1",
      "mountpoint": "/",
      "total": 536870912000,
      "used": 279172096000,
      "free": 257698816000,
      "percent": 52.1
    }
  ],
  "temperatures": {
    "soc-thermal": 55.0,
    "gpu-thermal": 61.5
  }
}
```

### 前端格式（转换后）
```json
{
  "identityMac": "de:07:65:52:3e:60",
  "hostname": "ec_3568_25030031",
  "metricTime": "2024-07-28T14:30:25.000Z",
  "cpu": {
    "usage": 23.7,
    "temperature": 58.3,
    "cores": 4
  },
  "memory": {
    "usage": 67.2,
    "total": 8589934592,
    "used": 5774458880,
    "available": 2815475712
  },
  "disk": {
    "usage": 52.1,
    "total": 536870912000,
    "used": 279172096000,
    "available": 257698816000
  },
  "network": {
    "uploadSpeed": 0,
    "downloadSpeed": 0,
    "totalUpload": 0,
    "totalDownload": 0
  }
}
```

## 图表数据格式

### 历史数据点格式
```javascript
{
  time: new Date('2024-07-28T14:30:25.000Z'),
  cpuUsage: 23.7,
  cpuTemp: 58.3,
  memory: 67.2,
  disk: 52.1,
  upload: 0, // MB/s
  // ... 其他指标
}
```

## 错误处理

### 1. API调用失败
- 显示错误信息给用户
- 提供重新加载按钮
- 提供测试连接功能

### 2. 数据格式异常
- 使用默认值填充缺失字段
- 记录错误日志
- 继续显示可用的数据

### 3. 网络连接问题
- 自动重试机制
- 用户友好的错误提示
- 保持现有数据显示

## 验证步骤

### 1. 检查后端服务
```bash
# 确保backend_admin_server运行
curl http://localhost:8080/api/v1/admin/terminal/metrics/device/de:07:65:52:3e:60/latest

# 检查历史数据
curl "http://localhost:8080/api/v1/admin/terminal/metrics/device/de:07:65:52:3e:60/range?startTime=2024-07-28%2000:00:00&endTime=2024-07-28%2023:59:59&page=0&size=10"
```

### 2. 检查前端调用
```javascript
// 在浏览器控制台查看
console.log('Environment:', import.meta.env.VITE_USE_MOCK) // 应该是 'false'
console.log('API Base URL:', import.meta.env.VITE_ADMIN_API_BASE_URL)
```

### 3. 监控网络请求
- 打开浏览器开发者工具
- 查看Network标签页
- 确认API请求正常发送和接收

### 4. 验证数据显示
- 页面加载时应显示今日历史数据
- 图表应显示完整的时间轴
- 每10秒应有新的数据点添加

## 性能优化

### 1. 数据量控制
- 历史数据最多1000条记录
- 图表显示最多288个数据点（24小时×12个5分钟间隔）
- 自动清理超出限制的历史数据

### 2. 请求优化
- 使用分页查询避免一次性加载过多数据
- 10秒轮询间隔平衡实时性和性能
- 错误时使用指数退避重试

### 3. 内存管理
- 限制内存中保存的历史数据量
- 及时清理不需要的数据
- 避免内存泄漏

## 故障排除

### 常见问题
1. **页面显示"无法获取指标数据"**
   - 检查backend_admin_server是否运行
   - 检查Elasticsearch是否有数据
   - 查看浏览器控制台错误信息

2. **图表没有数据**
   - 检查API返回的数据格式
   - 确认数据转换逻辑正确
   - 查看metricsHistory数组内容

3. **轮询不工作**
   - 检查autoRefreshTimer是否正确设置
   - 确认loadLatestMetrics函数正常执行
   - 查看控制台是否有错误信息

### 调试技巧
```javascript
// 在浏览器控制台执行
// 查看当前指标数据
console.log('Current metrics:', metrics.value)

// 查看历史数据
console.log('History data:', metricsHistory.value)

// 手动触发数据更新
loadLatestMetrics()

// 查看定时器状态
console.log('Auto refresh timer:', autoRefreshTimer.value)
```
