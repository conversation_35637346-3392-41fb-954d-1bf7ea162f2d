-- ========================================
--  终端监控相关表初始化脚本
-- 创建时间: 2024-07-14
-- 说明: 包含终端基本信息、指标信息、告警信息三张核心表
-- ========================================

-- 设置字符集
SET NAMES utf8mb4;

-- ========================================
-- 1. 终端基本信息表
-- ========================================
DROP TABLE IF EXISTS `terminal_basic_info`;
CREATE TABLE `terminal_basic_info` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `device_id` varchar(100) NOT NULL COMMENT '设备唯一标识符',
  `hostname` varchar(100) DEFAULT NULL COMMENT '主机名',
  `identity_mac` varchar(20) DEFAULT NULL COMMENT '对接长安车辆所在网口的MAC地址',
  `app_version` json DEFAULT NULL COMMENT '当前运行的组件版本号(JSON格式)',
  `expired_date` varchar(30) DEFAULT NULL COMMENT '软件授权过期时间',
  `receive_time` datetime DEFAULT NULL COMMENT '数据接收时间',
  `data_source` varchar(20) DEFAULT 'kafka' COMMENT '数据来源',
  `status` tinyint(1) DEFAULT 1 COMMENT '设备状态：0-离线，1-在线',
  `first_register_time` datetime DEFAULT NULL COMMENT '首次注册时间',
  `last_update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  `tags` json DEFAULT NULL COMMENT '终端标签信息(JSON格式)，用于标签过滤',
  `custom_fields` json DEFAULT NULL COMMENT '自定义字段信息(JSON格式)，用于动态扩展字段',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_deleted` tinyint(1) DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_device_id` (`device_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='终端基本信息表';

-- ========================================
-- 2. 终端指标信息表
-- ========================================
DROP TABLE IF EXISTS `terminal_metric_info`;
CREATE TABLE `terminal_metric_info` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `device_id` varchar(100) NOT NULL COMMENT '设备唯一标识符',
  `uptime` decimal(10,2) DEFAULT NULL COMMENT '系统运行时长（单位：秒）',
  `cpu_temp` decimal(5,2) DEFAULT NULL COMMENT 'CPU温度（单位：摄氏度）',
  `temperatures` json DEFAULT NULL COMMENT '温度信息(JSON格式)',
  `cpu_usage` json DEFAULT NULL COMMENT 'CPU使用率信息(JSON格式)',
  `cpu_percent` decimal(5,2) DEFAULT NULL COMMENT 'CPU总使用率（%）',
  `memory_percent` decimal(5,2) DEFAULT NULL COMMENT '内存使用率（%）',
  `memory_usage` json DEFAULT NULL COMMENT '内存详细信息(JSON格式)',
  `disk_usage` json DEFAULT NULL COMMENT '磁盘使用情况数组(JSON格式)',
  `disk_data_percent` decimal(5,2) DEFAULT NULL COMMENT '采集数据磁盘分区使用率',
  `disk_system_percent` decimal(5,2) DEFAULT NULL COMMENT '系统盘使用率',
  `cdata` json DEFAULT NULL COMMENT '长安程序落盘文件信息(JSON格式)',
  `zdata` json DEFAULT NULL COMMENT '压缩文件信息(JSON格式)',
  `group_usage` json DEFAULT NULL COMMENT 'CPE集合信息(JSON格式)',
  `group_bps` bigint(20) DEFAULT NULL COMMENT '所有CPE的带宽占用（单位：Byte/s）',
  `metric_time` datetime DEFAULT NULL COMMENT '指标采集时间',
  `receive_time` datetime DEFAULT NULL COMMENT '数据接收时间',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_device_id` (`device_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='终端指标信息表';

-- ========================================
-- 3. 终端告警信息表
-- ========================================
DROP TABLE IF EXISTS `terminal_alert_info`;
CREATE TABLE `terminal_alert_info` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `alert_id` varchar(50) NOT NULL COMMENT '告警唯一ID（UUID）',
  `device_id` varchar(100) NOT NULL COMMENT '告警终端设备ID',
  `alert_type` varchar(30) NOT NULL COMMENT '告警类型：CPU_TEMPERATURE,MEMORY_USAGE,DISK_USAGE,DISK_DATA_USAGE,DISK_SYSTEM_USAGE,LICENSE_EXPIRY',
  `alert_details` varchar(500) DEFAULT NULL COMMENT '告警详情',
  `metric_name` varchar(50) DEFAULT NULL COMMENT '告警指标名称',
  `threshold` varchar(20) DEFAULT NULL COMMENT '告警阈值',
  `current_value` varchar(20) DEFAULT NULL COMMENT '当前值',
  `alert_time` datetime DEFAULT NULL COMMENT '告警时间',
  `alert_status` varchar(20) DEFAULT 'ACTIVE' COMMENT '告警状态：ACTIVE,RESOLVED,ACKNOWLEDGED',
  `resolved_time` datetime DEFAULT NULL COMMENT '告警解决时间',
  `acknowledged_time` datetime DEFAULT NULL COMMENT '告警确认时间',
  `acknowledged_by` varchar(50) DEFAULT NULL COMMENT '告警确认人',
  `resolve_comment` varchar(500) DEFAULT NULL COMMENT '解决备注',
  `notification_sent` tinyint(1) DEFAULT 0 COMMENT '是否已发送通知：0-未发送，1-已发送',
  `notification_time` datetime DEFAULT NULL COMMENT '通知发送时间',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_alert_id` (`alert_id`),
  KEY `idx_device_id` (`device_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='终端告警信息表';

-- ========================================
-- 4. 插入测试数据（可选）
-- ========================================

-- 插入终端基本信息测试数据
INSERT INTO `terminal_basic_info` (`device_id`, `hostname`, `identity_mac`, `app_version`, `expired_date`, `receive_time`, `first_register_time`, `tags`, `custom_fields`) VALUES
('_0fd94938951f4a64bb11c6817a81f7e7', 'ec_3568_25030031', 'de0765523e60',
 '{"controller": "1.0.1", "detector": "1.0.48", "web": "1.0.55", "agent": "1.0.0", "front": "1.0.40"}',
 '2035-03-09 22:47:20', NOW(), NOW(),
 '{"location": "北京", "department": "研发部", "environment": "生产", "priority": "高"}',
 '{"vehicle_model": "长安CS75", "installation_date": "2024-01-15", "maintenance_contact": "张工", "notes": "重要测试设备"}'),
('_test_device_001', 'test_host_001', 'aa0765523e61',
 '{"controller": "1.0.2", "detector": "1.0.49", "web": "1.0.56", "agent": "1.0.1", "front": "1.0.41"}',
 '2025-12-31 23:59:59', NOW(), NOW(),
 '{"location": "上海", "department": "测试部", "environment": "测试", "priority": "中"}',
 '{"vehicle_model": "长安UNI-T", "installation_date": "2024-02-20", "maintenance_contact": "李工", "notes": "测试环境设备"}');



-- ========================================
-- 脚本执行完成
-- ========================================
SELECT 'Terminal tables created successfully!' AS message;
