package com.unnet.jmanul.business.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.unnet.jmanul.business.entity.LogFile;
import com.unnet.jmanul.business.mapper.LogFileMapper;
import com.unnet.jmanul.business.service.LogFileService;
import com.unnet.jmanul.service.MinioService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 日志文件记录 服务实现类
 * 
 * <AUTHOR>
 * @since 2025-07-28
 */
@Slf4j
@Service
public class LogFileServiceImpl extends ServiceImpl<LogFileMapper, LogFile> implements LogFileService {

    @Autowired
    private LogFileMapper logFileMapper;

    @Autowired
    private MinioService minioService;

    @Override
    public IPage<LogFile> getLogFilesByIdentityMac(Page<LogFile> page, String identityMac) {
        return logFileMapper.selectByIdentityMacPage(page, identityMac);
    }

    @Override
    public List<LogFile> getLogFilesByIdentityMac(String identityMac) {
        return logFileMapper.selectByIdentityMac(identityMac);
    }

    @Override
    public String getDownloadUrl(Long fileId) {
        LogFile logFile = this.getById(fileId);
        if (logFile == null) {
            throw new RuntimeException("文件记录不存在");
        }

        if (logFile.getStatus() != 0) {
            throw new RuntimeException("文件已被删除");
        }

        // 生成7天有效期的下载URL
        return minioService.getDownloadUrl(logFile.getStoragePath(), 7 * 24 * 3600);
    }

    @Override
    public boolean deleteLogFile(Long fileId) {
        LogFile logFile = this.getById(fileId);
        if (logFile == null) {
            throw new RuntimeException("文件记录不存在");
        }

        try {
            // 从MinIO删除文件
            boolean deleteSuccess = minioService.deleteFile(logFile.getStoragePath());
            if (!deleteSuccess) {
                log.warn("从MinIO删除文件失败，但继续删除数据库记录: {}", logFile.getStoragePath());
            }

            // 逻辑删除数据库记录
            return this.removeById(fileId);

        } catch (Exception e) {
            log.error("删除日志文件失败: fileId={}", fileId, e);
            throw new RuntimeException("删除日志文件失败: " + e.getMessage(), e);
        }
    }

    @Override
    public boolean isFileExists(String identityMac, String fileMd5) {
        LogFile existingFile = logFileMapper.selectByIdentityMacAndMd5(identityMac, fileMd5);
        return existingFile != null;
    }
}
