-- 长安5G管理平台用户管理相关表结构
-- 创建时间：2024-07-16
-- 说明：用于backend_admin_server管理平台的用户认证和权限管理

-- 1. 用户表
CREATE TABLE IF NOT EXISTS `admin_users` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '用户ID',
    `username` varchar(50) NOT NULL COMMENT '用户名',
    `password` varchar(255) NOT NULL COMMENT '密码（明文存储）',
    `real_name` varchar(100) DEFAULT NULL COMMENT '真实姓名',
    `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
    `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
    `role_id` bigint(20) NOT NULL COMMENT '角色ID',
    `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
    `last_login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
    `last_login_ip` varchar(50) DEFAULT NULL COMMENT '最后登录IP',
    `login_count` int(11) DEFAULT '0' COMMENT '登录次数',
    `avatar` varchar(255) DEFAULT NULL COMMENT '头像URL',
    `remark` varchar(500) DEFAULT NULL COMMENT '备注',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除：0-否，1-是',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_username` (`username`),
    KEY `idx_role_id` (`role_id`),
    KEY `idx_status` (`status`),
    KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='管理员用户表';

-- 2. 角色表
CREATE TABLE IF NOT EXISTS `admin_roles` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '角色ID',
    `role_name` varchar(50) NOT NULL COMMENT '角色名称',
    `role_code` varchar(50) NOT NULL COMMENT '角色编码',
    `description` varchar(200) DEFAULT NULL COMMENT '角色描述',
    `permissions` json DEFAULT NULL COMMENT '权限列表（JSON格式）',
    `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
    `sort_order` int(11) DEFAULT '0' COMMENT '排序',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除：0-否，1-是',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_role_code` (`role_code`),
    KEY `idx_status` (`status`),
    KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='管理员角色表';

-- 3. 权限表
CREATE TABLE IF NOT EXISTS `admin_permissions` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '权限ID',
    `permission_name` varchar(100) NOT NULL COMMENT '权限名称',
    `permission_code` varchar(100) NOT NULL COMMENT '权限编码',
    `permission_type` varchar(20) NOT NULL DEFAULT 'menu' COMMENT '权限类型：menu-菜单，button-按钮，api-接口',
    `parent_id` bigint(20) DEFAULT '0' COMMENT '父权限ID',
    `path` varchar(200) DEFAULT NULL COMMENT '路径',
    `component` varchar(200) DEFAULT NULL COMMENT '组件',
    `icon` varchar(100) DEFAULT NULL COMMENT '图标',
    `sort_order` int(11) DEFAULT '0' COMMENT '排序',
    `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
    `description` varchar(200) DEFAULT NULL COMMENT '权限描述',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除：0-否，1-是',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_permission_code` (`permission_code`),
    KEY `idx_parent_id` (`parent_id`),
    KEY `idx_permission_type` (`permission_type`),
    KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='管理员权限表';

-- 4. 用户登录日志表
CREATE TABLE IF NOT EXISTS `admin_login_logs` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '日志ID',
    `user_id` bigint(20) DEFAULT NULL COMMENT '用户ID',
    `username` varchar(50) NOT NULL COMMENT '用户名',
    `login_ip` varchar(50) DEFAULT NULL COMMENT '登录IP',
    `login_location` varchar(100) DEFAULT NULL COMMENT '登录地点',
    `browser` varchar(100) DEFAULT NULL COMMENT '浏览器',
    `os` varchar(100) DEFAULT NULL COMMENT '操作系统',
    `login_status` tinyint(1) NOT NULL COMMENT '登录状态：0-失败，1-成功',
    `login_message` varchar(255) DEFAULT NULL COMMENT '登录消息',
    `login_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '登录时间',
    PRIMARY KEY (`id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_username` (`username`),
    KEY `idx_login_time` (`login_time`),
    KEY `idx_login_status` (`login_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='管理员登录日志表';

-- 5. 操作日志表
CREATE TABLE IF NOT EXISTS `admin_operation_logs` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '日志ID',
    `user_id` bigint(20) DEFAULT NULL COMMENT '用户ID',
    `username` varchar(50) DEFAULT NULL COMMENT '用户名',
    `operation_type` varchar(50) NOT NULL COMMENT '操作类型',
    `operation_name` varchar(100) NOT NULL COMMENT '操作名称',
    `method` varchar(10) NOT NULL COMMENT '请求方法',
    `request_url` varchar(500) NOT NULL COMMENT '请求URL',
    `request_ip` varchar(50) DEFAULT NULL COMMENT '请求IP',
    `request_params` text COMMENT '请求参数',
    `response_result` text COMMENT '响应结果',
    `error_message` text COMMENT '错误信息',
    `operation_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
    `cost_time` bigint(20) DEFAULT NULL COMMENT '耗时（毫秒）',
    `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '操作状态：0-失败，1-成功',
    PRIMARY KEY (`id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_username` (`username`),
    KEY `idx_operation_type` (`operation_type`),
    KEY `idx_operation_time` (`operation_time`),
    KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='管理员操作日志表';

-- 插入初始数据

-- 插入默认角色
INSERT INTO `admin_roles` (`id`, `role_name`, `role_code`, `description`, `permissions`, `status`, `sort_order`) VALUES
(1, '超级管理员', 'SUPER_ADMIN', '拥有所有权限的超级管理员', '["*"]', 1, 1),
(2, '普通管理员', 'ADMIN', '只读权限的普通管理员', '["terminal:view", "metric:view", "dashboard:view"]', 1, 2);

-- 插入默认用户（密码为明文）
INSERT INTO `admin_users` (`id`, `username`, `password`, `real_name`, `role_id`, `status`) VALUES
(1, 'admin', 'admin123', '系统管理员', 1, 1),
(2, 'user', 'user123', '普通用户', 2, 1);

-- 插入基础权限
INSERT INTO `admin_permissions` (`id`, `permission_name`, `permission_code`, `permission_type`, `parent_id`, `path`, `icon`, `sort_order`, `description`) VALUES
(1, '系统管理', 'system', 'menu', 0, '/system', 'system', 1, '系统管理菜单'),
(2, '用户管理', 'system:user', 'menu', 1, '/system/user', 'user', 1, '用户管理'),
(3, '角色管理', 'system:role', 'menu', 1, '/system/role', 'role', 2, '角色管理'),
(4, '权限管理', 'system:permission', 'menu', 1, '/system/permission', 'permission', 3, '权限管理'),
(5, '终端管理', 'terminal', 'menu', 0, '/terminal', 'terminal', 2, '终端管理菜单'),
(6, '终端列表', 'terminal:list', 'menu', 5, '/terminal/list', 'list', 1, '终端列表'),
(7, '终端详情', 'terminal:detail', 'menu', 5, '/terminal/detail', 'detail', 2, '终端详情'),
(8, '指标管理', 'metric', 'menu', 0, '/metric', 'metric', 3, '指标管理菜单'),
(9, '指标查询', 'metric:query', 'menu', 8, '/metric/query', 'search', 1, '指标查询'),
(10, '数据统计', 'dashboard', 'menu', 0, '/dashboard', 'dashboard', 4, '数据统计菜单'),
(11, '概览页面', 'dashboard:overview', 'menu', 10, '/dashboard/overview', 'overview', 1, '概览页面');

COMMIT;
