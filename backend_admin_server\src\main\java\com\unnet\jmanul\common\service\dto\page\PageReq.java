package com.unnet.jmanul.common.service.dto.page;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class PageReq {
    @ApiModelProperty(value = "页码", example = "1", required = true)
    private int page;
    @ApiModelProperty(value = "每页大小", example = "10", required = true)
    private int size;
    @ApiModelProperty(value = "排序字段", example = "id")
    private String sortBy;
    @ApiModelProperty(value = "是否升序", example = "true")
    private boolean isAsc;
}
