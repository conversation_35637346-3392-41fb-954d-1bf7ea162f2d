# 长安5G管理平台 - 环境配置说明

## 🎯 环境切换方式

### 1. 通过配置文件切换
修改 `application.yml` 中的 `spring.profiles.active` 值：
```yaml
spring:
  profiles:
    active: dev  # 开发环境
    # active: prod  # 生产环境
```

### 2. 通过环境变量切换
```bash
# 启动时指定环境
export SPRING_PROFILES_ACTIVE=prod
java -jar backend_admin_server.jar

# 或者直接在启动命令中指定
java -jar backend_admin_server.jar --spring.profiles.active=prod
```

### 3. 通过Docker环境变量切换
```bash
# Docker运行时指定
docker run -e SPRING_PROFILES_ACTIVE=prod backend_admin_server:latest
```

## 🔧 生产环境配置详情

### Kafka集群配置
- **集群地址**: ********:9092, ********:9092, ********:9092
- **认证方式**: SASL_PLAINTEXT
- **用户名/密码**: admin/kafka123
- **安全配置**: ACL授权

### Elasticsearch集群配置
- **集群地址**: ********:9200, ********:9200, ********:9200
- **用户名/密码**: elastic/es123
- **集群名称**: changan5g-cluster

### Redis集群配置
- **主从模式**: 每个服务器包含主从两个Redis实例
- **地址**: 
  - ********:7000, ********:7001
  - ********:7000, ********:7001
  - ********:7000, ********:7001
- **密码**: redis123

## 📊 环境变量覆盖

生产环境支持以下环境变量覆盖：

### 数据库相关
- `DB_HOST`: 数据库主机地址
- `DB_PORT`: 数据库端口
- `DB_NAME`: 数据库名称
- `DB_USERNAME`: 数据库用户名
- `DB_PASSWORD`: 数据库密码

### Redis相关
- `REDIS_HOST`: Redis主机地址
- `REDIS_PORT`: Redis端口
- `REDIS_PASSWORD`: Redis密码

### Elasticsearch相关
- `ES_USERNAME`: ES用户名
- `ES_PASSWORD`: ES密码
- `ES_CLUSTER_NAME`: ES集群名称

### Kafka相关
- `KAFKA_USERNAME`: Kafka用户名
- `KAFKA_PASSWORD`: Kafka密码

### 应用相关
- `APP_DEVELOPMENT`: 是否开发模式
- `APP_JWT_SECRET`: JWT密钥
- `LOG_LEVEL_ROOT`: 根日志级别
- `LOG_LEVEL_APP`: 应用日志级别

## 🚀 部署示例

### Docker部署（生产环境）
```bash
docker run -d \
  --name changan5g_backend_admin_server \
  -p 8080:8080 \
  -e SPRING_PROFILES_ACTIVE=prod \
  -e DB_HOST=your-db-host \
  -e DB_PASSWORD=your-db-password \
  -e REDIS_PASSWORD=redis123 \
  -e ES_PASSWORD=es123 \
  -e KAFKA_PASSWORD=kafka123 \
  backend_admin_server:latest
```

### JAR包部署（生产环境）
```bash
java -jar \
  -Dspring.profiles.active=prod \
  -DDB_HOST=your-db-host \
  -DDB_PASSWORD=your-db-password \
  -DREDIS_PASSWORD=redis123 \
  -DES_PASSWORD=es123 \
  -DKAFKA_PASSWORD=kafka123 \
  backend_admin_server.jar
```

## 🔍 配置验证

### 检查当前环境
访问健康检查端点：
```bash
curl http://localhost:8080/actuator/health
```

### 检查配置信息
```bash
curl http://localhost:8080/actuator/info
```

### 查看环境配置
```bash
curl http://localhost:8080/actuator/env
```

## ⚠️ 注意事项

1. **生产环境安全**：
   - 禁用了Swagger文档
   - 使用强密码
   - 启用了访问控制

2. **集群配置**：
   - Kafka使用SASL认证
   - Elasticsearch使用用户名密码认证
   - Redis配置了密码认证

3. **日志配置**：
   - 生产环境日志级别为INFO
   - 开发环境日志级别为DEBUG
   - 支持日志文件轮转

4. **监控配置**：
   - 生产环境只暴露必要的监控端点
   - 开发环境暴露所有监控端点
