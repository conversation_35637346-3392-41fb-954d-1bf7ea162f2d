# MinIO配置说明 - 管理后台

## 概述

backend_admin_server采用与backend_server相同的MinIO配置策略，支持开发环境和生产环境的分离配置：
- **开发环境**: 使用单节点模式，配置简单，适合本地开发
- **生产环境**: 使用集群模式，支持高可用性和负载均衡

## 版本信息

- **MinIO客户端版本**: 8.5.2
- **Java版本**: 17+
- **Spring Boot版本**: 2.7.x

## 配置架构

项目采用了环境分离的配置策略，创建了两个独立的配置类：
- `MinioDevConfig`: 开发环境配置类，使用 `@Profile("dev")`
- `MinioProdConfig`: 生产环境配置类，使用 `@Profile("prod")`

## 配置说明

### 开发环境配置 (MinioDevConfig)

开发环境使用单节点模式，配置在 `application-dev.yml` 中：

```yaml
spring:
  minio:
    host: http://*************:9000
    url: ${spring.minio.host}/${spring.minio.bucket}/
    access-key: minioadmin
    secret-key: minioadmin
    bucket: log-files
```

**特点**:
- 单节点模式，无集群功能
- 简化配置，适合本地开发
- 使用默认的MinIO认证信息
- 自动激活条件：`@Profile("dev")`

### 生产环境配置 (MinioProdConfig)

生产环境使用MinIO集群，配置在 `application-prod.yml` 中：

```yaml
spring:
  minio:
    # MinIO集群节点配置（负载均衡）
    hosts:
      - http://********:9000
      - http://********:9000
      - http://********:9000
    # 主节点配置（用于兼容单节点配置的代码）
    host: http://********:9000
    url: ${MINIO_URL:${spring.minio.host}/${spring.minio.bucket}/}
    # 认证配置
    access-key: admin
    secret-key: Changan5g.minio
    # 存储桶配置
    bucket: log-files
    # 集群配置
    cluster:
      enabled: true
      retry-attempts: 3
      retry-delay-ms: 1000
      health-check-interval-seconds: 30
```

**特点**:
- 集群模式，支持3个节点
- 负载均衡和故障转移
- 健康检查和自动恢复
- 自动激活条件：`@Profile("prod")`

## 功能特性

### 1. 集群支持（生产环境）
- **负载均衡**: 使用轮询算法在健康节点间分配请求
- **故障转移**: 自动检测节点故障并切换到健康节点
- **健康检查**: 定期检查所有节点的健康状态
- **重试机制**: 操作失败时自动重试

### 2. 高可用性
- **多节点冗余**: 支持多个MinIO节点，提供数据冗余
- **自动恢复**: 节点恢复后自动重新加入集群
- **连接池**: 优化连接管理，提高性能

### 3. 监控和管理
- **集群状态API**: 提供集群健康状态查询接口
- **健康检查端点**: 支持外部监控系统集成
- **详细日志**: 记录集群操作和状态变化

## API接口

### 1. 文件操作

#### 检查文件存在
```http
GET /api/minio/exists/{objectName}
```

#### 删除文件
```http
DELETE /api/minio/delete/{objectName}
```

#### 获取下载URL
```http
GET /api/minio/download-url/{objectName}?expiry=3600
```

### 2. 集群管理

#### 获取集群状态
```http
GET /api/minio/cluster/status
```

响应示例：
```json
{
  "success": true,
  "data": {
    "clusterEnabled": true,
    "totalNodes": 3,
    "healthyNodes": 3,
    "environment": "production",
    "nodeStatus": {
      "http://********:9000": true,
      "http://********:9000": true,
      "http://********:9000": false
    }
  }
}
```

#### 健康检查
```http
GET /api/minio/health
```

## 环境变量配置

生产环境支持通过环境变量覆盖配置：

```bash
# MinIO连接配置
export MINIO_HOST=http://********:9000
export MINIO_ACCESS_KEY=admin
export MINIO_SECRET_KEY=Changan5g.minio
export MINIO_BUCKET=log-files

# 集群配置
export MINIO_CLUSTER_ENABLED=true
export MINIO_CLUSTER_RETRY_ATTEMPTS=3
export MINIO_CLUSTER_RETRY_DELAY=1000
export MINIO_CLUSTER_HEALTH_CHECK_INTERVAL=30
```

## 与backend_server的差异

| 特性 | backend_admin_server | backend_server |
|------|---------------------|----------------|
| 包名 | com.unnet.jmanul | com.unnet.changan5G |
| 主要用途 | 管理后台文件管理 | 业务数据文件存储 |
| 文件上传 | 通过管理界面 | 支持API上传 |
| 集群配置 | 相同的集群策略 | 相同的集群策略 |
| 监控接口 | 管理后台专用 | 业务系统专用 |

## 部署验证

### 开发环境启动
```bash
java -jar backend_admin_server.jar --spring.profiles.active=dev
```

### 生产环境启动
```bash
java -jar backend_admin_server.jar --spring.profiles.active=prod
```

### 验证集群状态
```bash
curl http://localhost:8080/api/minio/cluster/status
```

### 健康检查
```bash
curl http://localhost:8080/api/minio/health
```

## 故障排除

### 1. 集群节点不健康
- 检查网络连接
- 验证MinIO服务状态
- 查看应用日志中的健康检查信息

### 2. 文件操作失败
- 检查存储桶是否存在
- 验证认证信息
- 查看集群状态

### 3. 配置加载问题
- 确认Profile设置正确
- 检查配置文件语法
- 验证环境变量设置

## 监控建议

1. **集群状态监控**: 定期调用 `/api/minio/cluster/status` 接口
2. **健康检查**: 配置外部监控系统调用 `/api/minio/health` 接口
3. **日志监控**: 关注应用日志中的MinIO相关错误和警告
4. **性能监控**: 监控文件操作的响应时间和成功率
