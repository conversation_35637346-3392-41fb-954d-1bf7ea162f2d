<template>
  <div class="login-container">
    <div class="login-box">
      <div class="login-header">
        <h2>数据上云设备管理平台</h2>
        <p>用户登录</p>
      </div>
      
      <el-form 
        ref="loginFormRef" 
        :model="loginForm" 
        :rules="loginRules" 
        class="login-form"
        @submit.prevent="handleLogin"
      >
        <el-form-item prop="username">
          <el-input
            v-model="loginForm.username"
            placeholder="请输入用户名"
            prefix-icon="User"
            size="large"
            clearable
          />
        </el-form-item>
        
        <el-form-item prop="password">
          <el-input
            v-model="loginForm.password"
            type="password"
            placeholder="请输入密码"
            prefix-icon="Lock"
            size="large"
            show-password
            clearable
            @keyup.enter="handleLogin"
          />
        </el-form-item>
        
        <el-form-item prop="captchaCode" v-if="!isDevelopment">
          <div class="captcha-container">
            <el-input
              v-model="loginForm.captchaCode"
              placeholder="请输入验证码"
              prefix-icon="Picture"
              size="large"
              clearable
              style="flex: 1; margin-right: 10px;"
            />
            <div class="captcha-image" @click="refreshCaptcha">
              <img 
                v-if="captchaImage" 
                :src="captchaImage" 
                alt="验证码"
                style="width: 100%; height: 100%; cursor: pointer;"
              />
              <div v-else class="captcha-loading">
                <el-icon><Loading /></el-icon>
              </div>
            </div>
          </div>
        </el-form-item>
        
        <el-form-item>
          <el-button 
            type="primary" 
            size="large" 
            style="width: 100%;"
            :loading="loading"
            @click="handleLogin"
          >
            {{ loading ? '登录中...' : '登录' }}
          </el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { User, Lock, Picture, Loading } from '@element-plus/icons-vue'
import { useAuthStore } from '@/stores/auth'

const router = useRouter()
const authStore = useAuthStore()

// 表单引用
const loginFormRef = ref()

// 是否为开发环境（开发环境也显示验证码进行测试）
const isDevelopment = ref(false)

// 登录表单数据
const loginForm = reactive({
  username: '',
  password: '',
  captchaCode: '',
  captchaId: ''
})

// 表单验证规则
const loginRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 2, max: 20, message: '用户名长度在 2 到 20 个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
  ],
  captchaCode: [
    { 
      required: !isDevelopment.value, 
      message: '请输入验证码', 
      trigger: 'blur' 
    }
  ]
}

// 状态
const loading = ref(false)
const captchaImage = ref('')

// 获取验证码
const getCaptcha = async () => {
  try {
    const response = await authStore.getCaptcha()
    if (response && response.id && response.img) {
      loginForm.captchaId = response.id
      captchaImage.value = `data:image/png;base64,${response.img}`
    }
  } catch (error) {
    console.error('获取验证码失败:', error)
    ElMessage.error('获取验证码失败')
  }
}

// 刷新验证码
const refreshCaptcha = () => {
  loginForm.captchaCode = ''
  getCaptcha()
}

// 处理登录
const handleLogin = async () => {
  if (!loginFormRef.value) return

  try {
    // 表单验证
    await loginFormRef.value.validate()

    loading.value = true

    // 执行登录
    const success = await authStore.login({
      username: loginForm.username,
      password: loginForm.password,
      captchaCode: loginForm.captchaCode,
      captchaId: loginForm.captchaId
    })

    if (success) {
      // 检查账号和密码是否过期
      const userInfo = authStore.userInfo
      if (userInfo) {
        const now = new Date()
        const accountExpireDate = userInfo.accountExpireDate ? new Date(userInfo.accountExpireDate) : null
        const credentialExpireDate = userInfo.credentialExpireDate ? new Date(userInfo.credentialExpireDate) : null

        // 检查账号是否过期
        if (accountExpireDate && accountExpireDate <= now) {
          ElMessage.error('账号已过期，请联系管理员进行处理')
          await authStore.logout()
          return
        }

        // 检查密码是否过期
        if (credentialExpireDate && credentialExpireDate <= now) {
          ElMessage.error('密码已过期，请联系管理员进行处理')
          await authStore.logout()
          return
        }

        // 检查账号即将过期（7天内）
        if (accountExpireDate) {
          const daysUntilExpire = Math.ceil((accountExpireDate - now) / (1000 * 60 * 60 * 24))
          if (daysUntilExpire <= 7 && daysUntilExpire > 0) {
            ElMessage.warning(`账号将在 ${daysUntilExpire} 天后过期，请及时联系管理员续期`)
          }
        }

        // 检查密码即将过期（7天内）
        if (credentialExpireDate) {
          const daysUntilExpire = Math.ceil((credentialExpireDate - now) / (1000 * 60 * 60 * 24))
          if (daysUntilExpire <= 7 && daysUntilExpire > 0) {
            ElMessage.warning(`密码将在 ${daysUntilExpire} 天后过期，请及时联系管理员处理`)
          }
        }
      }

      ElMessage.success('登录成功')
      // 跳转到首页
      router.push('/')
    } else {
      ElMessage.error('登录失败，请检查用户名和密码')
      // 刷新验证码
      if (!isDevelopment.value) {
        refreshCaptcha()
      }
    }
  } catch (error) {
    console.error('登录错误:', error)

    // 处理具体的认证错误信息
    if (error.message) {
      // 根据错误信息类型显示不同的提示
      if (error.message.includes('验证码错误')) {
        ElMessage.error(error.message)
        // 验证码错误时刷新验证码
        if (!isDevelopment.value) {
          refreshCaptcha()
        }
      } else if (error.message.includes('当前账户不存在')) {
        ElMessage.error(error.message)
      } else if (error.message.includes('用户密码错误')) {
        ElMessage.error(error.message)
        // 密码错误时刷新验证码
        if (!isDevelopment.value) {
          refreshCaptcha()
        }
      } else if (error.message.includes('过期') || error.message.includes('锁定') || error.message.includes('禁用')) {
        ElMessage.error(error.message)
      } else {
        ElMessage.error('登录失败：' + error.message)
        // 其他错误也刷新验证码
        if (!isDevelopment.value) {
          refreshCaptcha()
        }
      }
    } else {
      ElMessage.error('登录失败：网络错误或服务器异常')
      // 网络错误时刷新验证码
      if (!isDevelopment.value) {
        refreshCaptcha()
      }
    }
  } finally {
    loading.value = false
  }
}

// 组件挂载时获取验证码
onMounted(() => {
  if (!isDevelopment.value) {
    getCaptcha()
  }
})
</script>

<style scoped>
.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(245, 245, 245, 1);
  padding: 20px;
}

.login-box {
  width: 100%;
  max-width: 400px;
  background: white;
  border-radius: 10px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  padding: 40px;
}

.login-header {
  text-align: center;
  margin-bottom: 30px;
}

.login-header h2 {
  color: #333;
  margin-bottom: 10px;
  font-size: 24px;
  font-weight: 600;
}

.login-header p {
  color: #666;
  font-size: 14px;
  margin: 0;
}

.login-form {
  margin-bottom: 20px;
}

.captcha-container {
  display: flex;
  align-items: center;
  width: 100%;
}

.captcha-image {
  width: 120px;
  height: 40px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  background-color: #f5f7fa;
}

.captcha-loading {
  color: #909399;
}

.login-footer {
  text-align: center;
  color: #999;
  font-size: 12px;
  line-height: 1.5;
}

.login-footer p {
  margin: 5px 0;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .login-box {
    padding: 30px 20px;
  }
  
  .login-header h2 {
    font-size: 20px;
  }
  
  .captcha-container {
    flex-direction: column;
    gap: 10px;
  }
  
  .captcha-image {
    width: 100%;
    height: 50px;
  }
}
</style>
