# jmanul

> jmanul is manul java impl

## 项目依赖

+ redis -- 基于 spring-boot-starter-data-redis
+ swagger -- 基于 springfox-boot-starter->3.0.0
+ casbin -- 基于 casbin-spring-boot-starter->1.5.0
+ jwt -- 基于 com.auth0->java-jwt->4.3.0
+ mybatis-plus -- 基于 com.baomidou->mybatis-plus-boot-starter->3.5.7
+ lombok -- 基于 org.projectlombok->lombok->1.18.30
+ geoip2 -- 基于 com.maxmind->geoip2->4.0.1
+ ua 解析 -- 基于 nl.basjes.parse.useragent->yauaa->7.16.0

## 上手

+ 数据库 mysql / postgresql
+ 缓存 redis
+ 配置文件 application-xxx.yml
+ 设置 application.yml
+ 启动

## TODO

+ 支持 Redis 集群
+ 支持 minio 对象存储
+ 数据字典功能
+ 前端菜单权限和 casbin 对应
+ Docker 部署 - ricl

## 特性

+ 认证接口基于 RSA 加密密码
+ 用户管理
+ 访问权限控制
+ 登录日志（支持地理位置 geoip）
+ 基于数据库的运行时配置(RuntimeConfig)
+ CRUD 代码示例
+ WebSocket Server - dev 默认开启 | prod 默认关闭
+ 模板化生成代码 - com.unnet.jmanul.common.codegen.BusinessCodeGenerator
+ 自动初始化用户 - com.unnet.jmanul.system.init.DefaultUserInitializer

## 其他配置

+ 密码 `test` 对应的 bcrypt = `$2a$10$ejcbB/aQQpSIUlytviOHiuDLONPFsJo87.DySFVcLT5.pkh4jqASu`

## Git 提交格式

+ `feat` 添加了新特性
+ `fix` 修复问题
+ `style` 无逻辑改动的代码风格调整
+ `perf` 性能/优化
+ `refactor` 重构
+ `revert` 回滚提交
+ `test` 测试
+ `docs` 文档
+ `chore` 依赖或者脚手架调整
+ `workflow` 工作流优化
+ `ci` 持续集成
+ `types` 类型定义
+ `wip` 开发中
