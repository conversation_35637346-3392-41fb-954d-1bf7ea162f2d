package com.unnet.changan5G.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.unnet.changan5G.entity.TerminalBasicInfoEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 终端基本信息Mapper
 * 
 * <AUTHOR>
 * @date 2024-07-14
 */
@Mapper
public interface TerminalBasicInfoMapper extends BaseMapper<TerminalBasicInfoEntity> {

    /**
     * 根据设备ID查询终端基本信息
     */
    @Select("SELECT * FROM terminal_basic_info WHERE device_id = #{deviceId} AND is_deleted = 0")
    TerminalBasicInfoEntity selectByDeviceId(@Param("deviceId") String deviceId);

    /**
     * 根据MAC地址查询终端基本信息
     */
    @Select("SELECT * FROM terminal_basic_info WHERE identity_mac = #{identityMac} AND is_deleted = 0")
    TerminalBasicInfoEntity selectByIdentityMac(@Param("identityMac") String identityMac);

    /**
     * 更新设备状态
     */
    @Update("UPDATE terminal_basic_info SET status = #{status}, last_update_time = #{updateTime} " +
            "WHERE device_id = #{deviceId} AND is_deleted = 0")
    int updateStatusByDeviceId(@Param("deviceId") String deviceId,
                              @Param("status") Integer status,
                              @Param("updateTime") LocalDateTime updateTime);

    /**
     * 根据MAC地址更新设备状态
     */
    @Update("UPDATE terminal_basic_info SET status = #{status}, last_update_time = #{updateTime} " +
            "WHERE identity_mac = #{identityMac} AND is_deleted = 0")
    int updateStatusByIdentityMac(@Param("identityMac") String identityMac,
                                 @Param("status") Integer status,
                                 @Param("updateTime") LocalDateTime updateTime);

    /**
     * 批量更新设备状态
     */
    @Update("<script>" +
            "UPDATE terminal_basic_info SET status = #{status}, last_update_time = #{updateTime} " +
            "WHERE device_id IN " +
            "<foreach collection='deviceIds' item='deviceId' open='(' separator=',' close=')'>" +
            "#{deviceId}" +
            "</foreach>" +
            " AND is_deleted = 0" +
            "</script>")
    int batchUpdateStatusByDeviceIds(@Param("deviceIds") List<String> deviceIds, 
                                   @Param("status") Integer status, 
                                   @Param("updateTime") LocalDateTime updateTime);

    /**
     * 查询在线设备数量
     */
    @Select("SELECT COUNT(*) FROM terminal_basic_info WHERE status = 1 AND is_deleted = 0")
    long countOnlineDevices();

    /**
     * 查询离线设备数量
     */
    @Select("SELECT COUNT(*) FROM terminal_basic_info WHERE status = 0 AND is_deleted = 0")
    long countOfflineDevices();

    /**
     * 根据状态统计设备数量
     */
    @Select("SELECT COUNT(*) FROM terminal_basic_info WHERE status = #{status} AND is_deleted = 0")
    int countByStatus(@Param("status") Integer status);

    /**
     * 查询所有设备的最后更新时间
     */
    @Select("SELECT device_id, last_update_time FROM terminal_basic_info WHERE is_deleted = 0")
    List<TerminalBasicInfoEntity> selectDeviceLastUpdateTimes();

    /**
     * 根据标签查询设备（精确匹配）
     */
    @Select("SELECT * FROM terminal_basic_info WHERE JSON_EXTRACT(tags, '$.${tagKey}') = #{tagValue} AND is_deleted = 0")
    List<TerminalBasicInfoEntity> selectByTag(@Param("tagKey") String tagKey, @Param("tagValue") String tagValue);

    /**
     * 根据标签内容模糊搜索设备（搜索键名和值）
     * 使用更简单的方法：将JSON转换为字符串进行搜索
     */
    @Select("SELECT * FROM terminal_basic_info WHERE " +
            "JSON_UNQUOTE(tags) LIKE #{tagValue} " +
            "AND is_deleted = 0")
    List<TerminalBasicInfoEntity> selectByTagSearch(@Param("tagValue") String tagValue);

    /**
     * 查询即将过期的设备
     */
    @Select("SELECT * FROM terminal_basic_info " +
            "WHERE STR_TO_DATE(expired_date, '%Y-%m-%d %H:%i:%s') BETWEEN NOW() AND DATE_ADD(NOW(), INTERVAL #{days} DAY) " +
            "AND is_deleted = 0")
    List<TerminalBasicInfoEntity> selectExpiringDevices(@Param("days") int days);
}
