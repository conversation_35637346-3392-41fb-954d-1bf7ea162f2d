package com.unnet.changan5G.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.unnet.changan5G.entity.LogFile;
import org.springframework.web.multipart.MultipartFile;

/**
 * 日志文件记录 服务类 - 只负责上传功能
 *
 * <AUTHOR>
 * @since 2025-07-28
 */
public interface LogFileService extends IService<LogFile> {

    /**
     * 上传日志文件
     *
     * @param identityMac 终端MAC地址
     * @param file 上传的文件
     * @param description 文件描述
     * @return 上传结果
     */
    LogFile uploadLogFile(String identityMac, MultipartFile file, String description);

    /**
     * 检查文件是否已存在（基于MD5）
     *
     * @param identityMac 终端MAC地址
     * @param fileMd5 文件MD5值
     * @return 是否存在
     */
    boolean isFileExists(String identityMac, String fileMd5);
}
