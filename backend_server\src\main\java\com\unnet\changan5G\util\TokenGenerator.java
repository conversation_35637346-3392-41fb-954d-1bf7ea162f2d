package com.unnet.changan5G.util;

import java.security.SecureRandom;
import java.util.Base64;
import java.util.UUID;

/**
 * API Token生成工具类
 */
public class TokenGenerator {
    
    private static final String TOKEN_PREFIX = "api-";
    private static final SecureRandom RANDOM = new SecureRandom();
    private static final char[] CHARS = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".toCharArray();
    
    /**
     * 生成API Token
     * 格式：ragflow-{随机字母数字字符串}
     * @return API Token字符串
     */
    public static String generateApiToken() {
        return generateApiTokenWithLength(32);
    }
    
    /**
     * 使用UUID生成API Token
     * @return API Token字符串
     */
    public static String generateApiTokenWithUUID() {
        String uuid = UUID.randomUUID().toString().replace("-", "");
        StringBuilder sb = new StringBuilder();
        
        for (int i = 0; i < uuid.length(); i++) {
            char c = uuid.charAt(i);
            // 确保只有字母和数字
            if ((c >= '0' && c <= '9') || (c >= 'a' && c <= 'z') || (c >= 'A' && c <= 'Z')) {
                sb.append(c);
            } else {
                sb.append(CHARS[RANDOM.nextInt(CHARS.length)]);
            }
        }
        
        return TOKEN_PREFIX + sb.toString();
    }
    
    /**
     * 生成固定长度的API Token，只包含字母和数字
     * @param length Token长度（不包括前缀）
     * @return API Token字符串
     */
    public static String generateApiTokenWithLength(int length) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < length; i++) {
            sb.append(CHARS[RANDOM.nextInt(CHARS.length)]);
        }
        return TOKEN_PREFIX + sb.toString();
    }
} 