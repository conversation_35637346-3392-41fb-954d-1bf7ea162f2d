//package com.unnet.jmanul.system.websocket;
//
//import com.unnet.jmanul.common.utils.JacksonUtils;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.stereotype.Component;
//
//import javax.websocket.*;
//import javax.websocket.server.PathParam;
//import javax.websocket.server.ServerEndpoint;
//import java.io.IOException;
//import java.util.Objects;
//import java.util.concurrent.CopyOnWriteArraySet;
//
///**
// * WebSocket服务端，实际用的时候应该增加认证
// */
//@ServerEndpoint("/websocket/{sid}")
//@Slf4j
//@Component
//public class WebSocketServer {
//
//    /**
//     * concurrent包的线程安全Set，用来存放每个客户端对应的MyWebSocket对象。
//     */
//    private static final CopyOnWriteArraySet<WebSocketServer> currentConnectionSet = new CopyOnWriteArraySet<WebSocketServer>();
//
//    /**
//     * 与某个客户端的连接会话，需要通过它来给客户端发送数据
//     */
//    private Session session;
//
//    /**
//     * 接收sid
//     */
//    private String sid = "";
//
//    /**
//     * 连接建立成功调用的方法，连接方需要传入 sid
//     */
//    @OnOpen
//    public void onOpen(Session session, @PathParam("sid") String sid) {
//        this.session = session;
//        // 如果存在就先删除一个，防止重复推送消息
//        currentConnectionSet.removeIf(webSocket -> webSocket.sid.equals(sid));
//        currentConnectionSet.add(this);
//        this.sid = sid;
//        log.info("onOpen: new connection: {}, current connection count: {}", sid, currentConnectionSet.size());
//    }
//
//    /**
//     * 连接关闭调用的方法
//     */
//    @OnClose
//    public void onClose() {
//        currentConnectionSet.remove(this);
//        log.info("onClose: client {} closed, current connection count: {}", sid, currentConnectionSet.size());
//    }
//
//    /**
//     * 收到客户端消息后调用的方法
//     *
//     * @param message 客户端发送过来的消息
//     */
//    @OnMessage
//    public void onMessage(String message, Session session) {
//        log.info("onMessage: [{}, {}]", sid, message);
//
//        // 群发消息
//        for (WebSocketServer item : currentConnectionSet) {
//            try {
//                item.sendMessage(message);
//            } catch (IOException e) {
//                log.error(e.getMessage(), e);
//            }
//        }
//    }
//
//    @OnError
//    public void onError(Session session, Throwable error) throws IOException {
//        log.error("onError: [{}, {}]", sid, error.getMessage());
//        session.close(new CloseReason(CloseReason.CloseCodes.UNEXPECTED_CONDITION, error.getMessage()));
//    }
//
//    /**
//     * 实现服务器主动推送
//     */
//    private void sendMessage(String message) throws IOException {
//        this.session.getBasicRemote().sendText(message);
//    }
//
//    /**
//     * 群发自定义消息
//     */
//    public static void sendInfo(ServerMessage msg, @PathParam("sid") String sid) throws IOException {
//        String message = JacksonUtils.objectToString(msg);
//        log.info("推送消息到:{}，推送内容:{}", sid, message);
//        for (WebSocketServer item : currentConnectionSet) {
//            try {
//                // 这里可以设定只推送给这个sid的，为null则全部推送
//                if (sid == null) {
//                    item.sendMessage(message);
//                } else if (item.sid.equals(sid)) {
//                    item.sendMessage(message);
//                }
//            } catch (IOException ignored) {
//            }
//        }
//    }
//
//    @Override
//    public boolean equals(Object o) {
//        if (this == o) {
//            return true;
//        }
//        if (o == null || getClass() != o.getClass()) {
//            return false;
//        }
//        WebSocketServer that = (WebSocketServer) o;
//        return Objects.equals(session, that.session) &&
//                Objects.equals(sid, that.sid);
//    }
//
//    @Override
//    public int hashCode() {
//        return Objects.hash(session, sid);
//    }
//}
