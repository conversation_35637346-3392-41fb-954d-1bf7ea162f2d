package com.unnet.changan5G.controller;

import com.unnet.changan5G.service.MinioService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * MinIO文件管理控制器
 * 
 * <AUTHOR>
 * @since 2025-07-28
 */
@Slf4j
@RestController
@RequestMapping("/api/minio")
@Tag(name = "MinIO文件管理", description = "MinIO文件上传、下载、删除等操作")
public class MinioController {

    @Autowired
    private MinioService minioService;

    /**
     * 上传文件
     */
    @PostMapping("/upload")
    @Operation(summary = "上传文件", description = "上传文件到MinIO存储")
    public ResponseEntity<Map<String, Object>> uploadFile(
            @RequestParam("file") MultipartFile file,
            @RequestParam(value = "path", required = false, defaultValue = "") String path) {
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            if (file.isEmpty()) {
                result.put("success", false);
                result.put("message", "文件不能为空");
                return ResponseEntity.badRequest().body(result);
            }

            // 生成唯一文件名
            String originalFilename = file.getOriginalFilename();
            String extension = "";
            if (originalFilename != null && originalFilename.contains(".")) {
                extension = originalFilename.substring(originalFilename.lastIndexOf("."));
            }
            
            String objectName = path.isEmpty() ? 
                UUID.randomUUID().toString() + extension :
                path + "/" + UUID.randomUUID().toString() + extension;

            boolean uploadResult = minioService.uploadFile(file, objectName);
            
            if (uploadResult) {
                result.put("success", true);
                result.put("message", "文件上传成功");
                result.put("objectName", objectName);
                result.put("originalFilename", originalFilename);
                result.put("size", file.getSize());
                return ResponseEntity.ok(result);
            } else {
                result.put("success", false);
                result.put("message", "文件上传失败");
                return ResponseEntity.internalServerError().body(result);
            }
        } catch (Exception e) {
            log.error("文件上传异常", e);
            result.put("success", false);
            result.put("message", "文件上传异常: " + e.getMessage());
            return ResponseEntity.internalServerError().body(result);
        }
    }

    /**
     * 检查文件是否存在
     */
    @GetMapping("/exists/{objectName}")
    @Operation(summary = "检查文件是否存在", description = "检查指定文件是否存在于MinIO存储中")
    public ResponseEntity<Map<String, Object>> checkFileExists(@PathVariable String objectName) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            boolean exists = minioService.isFileExists(objectName);
            result.put("success", true);
            result.put("exists", exists);
            result.put("objectName", objectName);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("检查文件存在性异常", e);
            result.put("success", false);
            result.put("message", "检查文件存在性异常: " + e.getMessage());
            return ResponseEntity.internalServerError().body(result);
        }
    }

    /**
     * 删除文件
     */
    @DeleteMapping("/delete/{objectName}")
    @Operation(summary = "删除文件", description = "从MinIO存储中删除指定文件")
    public ResponseEntity<Map<String, Object>> deleteFile(@PathVariable String objectName) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            boolean deleteResult = minioService.deleteFile(objectName);
            
            if (deleteResult) {
                result.put("success", true);
                result.put("message", "文件删除成功");
                result.put("objectName", objectName);
                return ResponseEntity.ok(result);
            } else {
                result.put("success", false);
                result.put("message", "文件删除失败");
                return ResponseEntity.internalServerError().body(result);
            }
        } catch (Exception e) {
            log.error("文件删除异常", e);
            result.put("success", false);
            result.put("message", "文件删除异常: " + e.getMessage());
            return ResponseEntity.internalServerError().body(result);
        }
    }

    /**
     * 获取MinIO集群状态
     */
    @GetMapping("/cluster/status")
    @Operation(summary = "获取集群状态", description = "获取MinIO集群的健康状态和节点信息")
    public ResponseEntity<Map<String, Object>> getClusterStatus() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            Object clusterStatus = minioService.getClusterStatus();
            result.put("success", true);
            result.put("data", clusterStatus);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取集群状态异常", e);
            result.put("success", false);
            result.put("message", "获取集群状态异常: " + e.getMessage());
            return ResponseEntity.internalServerError().body(result);
        }
    }

    /**
     * 健康检查端点
     */
    @GetMapping("/health")
    @Operation(summary = "健康检查", description = "检查MinIO服务是否正常")
    public ResponseEntity<Map<String, Object>> healthCheck() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 尝试检查存储桶是否存在来验证MinIO连接
            minioService.ensureBucketExists();
            
            result.put("success", true);
            result.put("status", "healthy");
            result.put("message", "MinIO服务正常");
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("MinIO健康检查失败", e);
            result.put("success", false);
            result.put("status", "unhealthy");
            result.put("message", "MinIO服务异常: " + e.getMessage());
            return ResponseEntity.internalServerError().body(result);
        }
    }
}
