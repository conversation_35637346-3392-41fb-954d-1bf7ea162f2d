package com.unnet.changan5G.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.unnet.changan5G.dto.terminal.TerminalMetricInfo;
import com.unnet.changan5G.entity.TerminalMetricInfoEntity;
import com.unnet.changan5G.mapper.TerminalMetricInfoMapper;
import com.unnet.changan5G.service.TerminalMetricInfoService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 终端指标信息服务实现
 * 
 * <AUTHOR>
 * @date 2024-07-14
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class TerminalMetricInfoServiceImpl extends ServiceImpl<TerminalMetricInfoMapper, TerminalMetricInfoEntity> 
        implements TerminalMetricInfoService {

    private final TerminalMetricInfoMapper terminalMetricInfoMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveMetricInfo(TerminalMetricInfo terminalMetricInfo) {
        try {
            TerminalMetricInfoEntity entity = new TerminalMetricInfoEntity();
            BeanUtils.copyProperties(terminalMetricInfo, entity);
            
            boolean result = save(entity);
            if (result) {
                log.debug("保存终端指标信息成功 - 设备: {}, 采集时间: {}", 
                        terminalMetricInfo.getIdentityMac(), terminalMetricInfo.getMetricTime());
            }
            return result;
        } catch (Exception e) {
            log.error("保存终端指标信息失败 - 设备: {}, 错误: {}", 
                    terminalMetricInfo.getIdentityMac(), e.getMessage(), e);
            return false;
        }
    }

    @Override
    public TerminalMetricInfoEntity getLatestByDeviceId(String deviceId) {
        return terminalMetricInfoMapper.selectLatestByDeviceId(deviceId);
    }

    @Override
    public List<TerminalMetricInfoEntity> getByDeviceIdAndTimeRange(String deviceId, 
                                                                   LocalDateTime startTime, 
                                                                   LocalDateTime endTime) {
        return terminalMetricInfoMapper.selectByDeviceIdAndTimeRange(deviceId, startTime, endTime);
    }

    @Override
    public List<TerminalMetricInfoEntity> getByCpuTempThreshold(Double threshold, LocalDateTime startTime) {
        return terminalMetricInfoMapper.selectByCpuTempThreshold(threshold, startTime);
    }

    @Override
    public List<TerminalMetricInfoEntity> getByMemoryPercentThreshold(Double threshold, LocalDateTime startTime) {
        return terminalMetricInfoMapper.selectByMemoryPercentThreshold(threshold, startTime);
    }

    @Override
    public List<TerminalMetricInfoEntity> getByDiskPercentThreshold(Double threshold, LocalDateTime startTime) {
        return terminalMetricInfoMapper.selectByDiskPercentThreshold(threshold, startTime);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteHistoryData(LocalDateTime beforeTime) {
        try {
            int deletedCount = terminalMetricInfoMapper.deleteHistoryData(beforeTime);
            log.info("删除历史指标数据完成 - 删除时间点: {}, 删除数量: {}", beforeTime, deletedCount);
            return deletedCount >= 0;
        } catch (Exception e) {
            log.error("删除历史指标数据失败 - 删除时间点: {}, 错误: {}", beforeTime, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public TerminalMetricInfoEntity getStatsByDeviceIdAndTimeRange(String deviceId, 
                                                                  LocalDateTime startTime, 
                                                                  LocalDateTime endTime) {
        return terminalMetricInfoMapper.selectStatsByDeviceIdAndTimeRange(deviceId, startTime, endTime);
    }
}
