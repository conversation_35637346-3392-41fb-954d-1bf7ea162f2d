package com.unnet.jmanul.business.document;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 终端指标信息Elasticsearch文档
 * 
 * <AUTHOR>
 * @date 2024-07-16
 */
@Data
@Document(indexName = "terminal_metrics")
public class TerminalMetricDocument {

    @Id
    private String id;

    @Field(type = FieldType.Keyword)
    private String identityMac;

    @Schema(description = "vin")
    private String vin;

    @Field(type = FieldType.Double)
    private BigDecimal uptime;

    @Field(type = FieldType.Double)
    private BigDecimal cpuTemp;

    @Field(type = FieldType.Object)
    private Object temperatures;

    @Field(type = FieldType.Object)
    private Object cpuUsage;

    @Field(type = FieldType.Double)
    private BigDecimal cpuPercent;

    @Field(type = FieldType.Double)
    private BigDecimal memoryPercent;

    @Field(type = FieldType.Object)
    private Object memoryUsage;

    @Field(type = FieldType.Object)
    private Object diskUsage;

    @Field(type = FieldType.Double)
    private BigDecimal diskDataPercent;

    @Field(type = FieldType.Double)
    private BigDecimal diskSystemPercent;

    @Field(type = FieldType.Object)
    private Object cdata;

    @Field(type = FieldType.Object)
    private Object zdata;

    @Field(type = FieldType.Object)
    private Object groupUsage;

    @Field(type = FieldType.Long)
    private Long groupBps;

    @Field(type = FieldType.Date, format = DateFormat.date_hour_minute_second)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime metricTime;

    @Field(type = FieldType.Date, format = DateFormat.date_hour_minute_second)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime receiveTime;

    @Field(type = FieldType.Date, format = DateFormat.date_hour_minute_second)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @Field(type = FieldType.Date, format = DateFormat.date_hour_minute_second)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    /**
     * 生成文档ID（MAC地址 + 时间戳）
     */
    public void generateId() {
        if (identityMac != null && metricTime != null) {
            this.id = identityMac + "_" + metricTime.toString().replace(":", "").replace("-", "").replace(" ", "_");
        }
    }

    /**
     * 格式化MAC地址（每两个字符添加冒号）
     */
    public static String formatMacAddress(String rawMac) {
        if (rawMac == null || rawMac.length() != 12) {
            return rawMac;
        }
        StringBuilder formatted = new StringBuilder();
        for (int i = 0; i < rawMac.length(); i += 2) {
            if (i > 0) {
                formatted.append(":");
            }
            formatted.append(rawMac.substring(i, i + 2));
        }
        return formatted.toString();
    }
}
