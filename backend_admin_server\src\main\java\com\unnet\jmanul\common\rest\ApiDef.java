package com.unnet.jmanul.common.rest;

import org.apache.commons.lang3.tuple.Pair;
import org.springframework.http.HttpMethod;

import java.util.ArrayList;
import java.util.List;

public class ApiDef {

    public static final String V1 = "/api/v1";

    public static final String V1_AUTH = V1 + "/auth";
    public static final String V1_ACCOUNT = V1 + "/account";

    public static final String V1_ADMIN = V1 + "/admin";
    public static final String V1_ADMIN_USER = V1_ADMIN + "/users";
    public static final String V1_ADMIN_SAMPLE = V1_ADMIN + "/samples";
    public static final String V1_ADMIN_RBAC = V1_ADMIN + "/rbac";

    // 长安5G业务模块
    public static final String V1_ADMIN_TERMINAL = V1_ADMIN + "/terminals";
    public static final String V1_ADMIN_TERMINAL_METRICS = V1_ADMIN + "/terminal/metrics";

    // 对于白名单中的URL，不检查JWT和鉴权
    public static final List<Pair<HttpMethod, String>> ANT_WHITE_LIST = new ArrayList<>();
    public static final List<String> ANT_WHITE_LIST_ANY = new ArrayList<>();

    static {
        // base
        ANT_WHITE_LIST.add(Pair.of(HttpMethod.GET, "/favicon.ico"));

        // auth
        ANT_WHITE_LIST.add(Pair.of(HttpMethod.GET, V1_AUTH + "/captcha"));
        ANT_WHITE_LIST.add(Pair.of(HttpMethod.POST, V1_AUTH + "/register"));
        ANT_WHITE_LIST.add(Pair.of(HttpMethod.GET, V1_AUTH + "/register/confirm"));

        ANT_WHITE_LIST.add(Pair.of(HttpMethod.POST, V1_AUTH + "/login"));
        ANT_WHITE_LIST.add(Pair.of(HttpMethod.GET, V1_AUTH + "/logout"));
        ANT_WHITE_LIST.add(Pair.of(HttpMethod.GET, V1_AUTH + "/publicKey"));

        // CORS
        ANT_WHITE_LIST.add(Pair.of(HttpMethod.OPTIONS, "/**"));

        // websocket
        ANT_WHITE_LIST_ANY.add("/websocket/**");

        // swagger 文档
        ANT_WHITE_LIST_ANY.add("/swagger-ui/**");
        ANT_WHITE_LIST_ANY.add("/swagger-resources/**");
        ANT_WHITE_LIST_ANY.add("/v3/api-docs/swagger-config");
        ANT_WHITE_LIST_ANY.add("/v3/api-docs");

        // Error
        ANT_WHITE_LIST_ANY.add("/error");

        // 长安5G业务模块 - 需要认证的接口，这里不添加到白名单
        // 终端管理和指标查询接口需要通过JWT认证和权限验证

    }

}


