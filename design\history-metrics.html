<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>历史指标页面</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            color: #333;
            padding: 20px;
        }

        .breadcrumb {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            font-size: 14px;
        }

        .breadcrumb a {
            color: #667eea;
            text-decoration: none;
            cursor: pointer;
        }

        .breadcrumb a:hover {
            text-decoration: underline;
        }

        .breadcrumb span {
            margin: 0 8px;
            color: #999;
        }

        .card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            overflow: hidden;
        }

        .card-header {
            padding: 20px;
            border-bottom: 1px solid #eee;
            background: #fafafa;
        }

        .card-header h3 {
            font-size: 18px;
            font-weight: 600;
            color: #333;
        }

        .card-body {
            padding: 20px;
        }

        .history-filters {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
            align-items: end;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            margin-bottom: 5px;
            font-weight: 500;
            color: #555;
        }

        .datetime-input {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s;
            text-decoration: none;
            display: inline-block;
        }

        .btn-primary {
            background: #667eea;
            color: white;
        }

        .btn-primary:hover {
            background: #5a6fd8;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-info {
            background: #17a2b8;
            color: white;
        }

        .btn-sm {
            padding: 5px 10px;
            font-size: 12px;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }

        .table th,
        .table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }

        .table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #495057;
        }

        .table tr:hover {
            background: #f8f9fa;
        }

        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 10px;
            margin-top: 20px;
        }

        .pagination button {
            padding: 8px 12px;
            border: 1px solid #ddd;
            background: white;
            cursor: pointer;
            border-radius: 4px;
        }

        .pagination button:hover {
            background: #f8f9fa;
        }

        .pagination button.active {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }
    </style>
</head>
<body>
    <div class="breadcrumb">
        <a onclick="goBack()">终端管理</a>
        <span>></span>
        <span>历史指标</span>
    </div>
    
    <div class="card">
        <div class="card-header">
            <h3>终端历史指标 - <span id="historyDeviceId"></span></h3>
        </div>
        <div class="card-body">
            <div class="history-filters">
                <div class="form-group">
                    <label>开始时间</label>
                    <input type="datetime-local" class="datetime-input" id="startTime" value="2024-07-14T10:00">
                </div>
                <div class="form-group">
                    <label>结束时间</label>
                    <input type="datetime-local" class="datetime-input" id="endTime" value="2024-07-14T20:00">
                </div>
                <button class="btn btn-primary" onclick="searchHistoryMetrics()">查询</button>
                <button class="btn btn-secondary" onclick="resetHistoryFilters()">重置</button>
            </div>

            <table class="table">
                <thead>
                    <tr>
                        <th>采集时间</th>
                        <th>CPU温度</th>
                        <th>CPU使用率</th>
                        <th>内存使用率</th>
                        <th>磁盘使用率</th>
                        <th>运行时长</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody id="historyTableBody">
                    <tr>
                        <td>2024-07-14 18:30:45</td>
                        <td>85.9°C</td>
                        <td>43.2%</td>
                        <td>50.9%</td>
                        <td>54.3%</td>
                        <td>98分钟36秒</td>
                        <td>
                            <button class="btn btn-info btn-sm" onclick="viewHistoryDetail('2024-07-14 18:30:45')">查看详情</button>
                        </td>
                    </tr>
                    <tr>
                        <td>2024-07-14 18:25:45</td>
                        <td>84.2°C</td>
                        <td>41.8%</td>
                        <td>48.5%</td>
                        <td>54.1%</td>
                        <td>93分钟36秒</td>
                        <td>
                            <button class="btn btn-info btn-sm" onclick="viewHistoryDetail('2024-07-14 18:25:45')">查看详情</button>
                        </td>
                    </tr>
                    <tr>
                        <td>2024-07-14 18:20:45</td>
                        <td>83.5°C</td>
                        <td>39.6%</td>
                        <td>46.2%</td>
                        <td>53.8%</td>
                        <td>88分钟36秒</td>
                        <td>
                            <button class="btn btn-info btn-sm" onclick="viewHistoryDetail('2024-07-14 18:20:45')">查看详情</button>
                        </td>
                    </tr>
                    <tr>
                        <td>2024-07-14 18:15:45</td>
                        <td>82.8°C</td>
                        <td>37.4%</td>
                        <td>44.1%</td>
                        <td>53.5%</td>
                        <td>83分钟36秒</td>
                        <td>
                            <button class="btn btn-info btn-sm" onclick="viewHistoryDetail('2024-07-14 18:15:45')">查看详情</button>
                        </td>
                    </tr>
                    <tr>
                        <td>2024-07-14 18:10:45</td>
                        <td>81.9°C</td>
                        <td>35.2%</td>
                        <td>42.8%</td>
                        <td>53.2%</td>
                        <td>78分钟36秒</td>
                        <td>
                            <button class="btn btn-info btn-sm" onclick="viewHistoryDetail('2024-07-14 18:10:45')">查看详情</button>
                        </td>
                    </tr>
                </tbody>
            </table>

            <div class="pagination">
                <button>上一页</button>
                <button class="active">1</button>
                <button>2</button>
                <button>3</button>
                <button>下一页</button>
            </div>
        </div>
    </div>

    <script>
        // 从URL参数获取设备ID
        function getDeviceIdFromUrl() {
            const urlParams = new URLSearchParams(window.location.search);
            return urlParams.get('deviceId') || '_0fd94938951f4a64bb11c6817a81f7e7';
        }

        // 初始化页面
        function initPage() {
            const deviceId = getDeviceIdFromUrl();
            document.getElementById('historyDeviceId').textContent = deviceId;
        }

        // 返回上一页
        function goBack() {
            // 在实际Vue应用中，这里会使用路由返回
            window.history.back();
        }

        // 搜索历史指标
        function searchHistoryMetrics() {
            const startTime = document.getElementById('startTime').value;
            const endTime = document.getElementById('endTime').value;
            
            if (!startTime || !endTime) {
                alert('请选择开始时间和结束时间');
                return;
            }
            
            if (new Date(startTime) > new Date(endTime)) {
                alert('开始时间不能大于结束时间');
                return;
            }
            
            // 模拟查询结果
            const tbody = document.getElementById('historyTableBody');
            tbody.innerHTML = '';
            
            // 生成模拟数据
            const start = new Date(startTime);
            const end = new Date(endTime);
            const interval = 5 * 60 * 1000; // 5分钟间隔
            
            for (let time = start; time <= end; time = new Date(time.getTime() + interval)) {
                const timeString = time.getFullYear() + '-' + 
                                 String(time.getMonth() + 1).padStart(2, '0') + '-' + 
                                 String(time.getDate()).padStart(2, '0') + ' ' + 
                                 String(time.getHours()).padStart(2, '0') + ':' + 
                                 String(time.getMinutes()).padStart(2, '0') + ':' + 
                                 String(time.getSeconds()).padStart(2, '0');
                
                const cpuTemp = (80 + Math.random() * 10).toFixed(1);
                const cpuUsage = (35 + Math.random() * 15).toFixed(1);
                const memoryUsage = (40 + Math.random() * 20).toFixed(1);
                const diskUsage = (53 + Math.random() * 3).toFixed(1);
                const uptime = Math.floor(Math.random() * 120) + 60;
                
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${timeString}</td>
                    <td>${cpuTemp}°C</td>
                    <td>${cpuUsage}%</td>
                    <td>${memoryUsage}%</td>
                    <td>${diskUsage}%</td>
                    <td>${uptime}分钟</td>
                    <td>
                        <button class="btn btn-info btn-sm" onclick="viewHistoryDetail('${timeString}')">查看详情</button>
                    </td>
                `;
                tbody.appendChild(row);
            }
            
            alert(`查询完成！时间范围: ${startTime} 至 ${endTime}`);
        }

        // 重置历史筛选器
        function resetHistoryFilters() {
            document.getElementById('startTime').value = '2024-07-14T10:00';
            document.getElementById('endTime').value = '2024-07-14T20:00';
        }

        // 查看历史详情
        function viewHistoryDetail(time) {
            const deviceId = getDeviceIdFromUrl();
            // 在实际Vue应用中，这里会使用路由跳转
            window.open(`real-time-metrics.html?deviceId=${deviceId}&historyTime=${encodeURIComponent(time)}`, '_blank');
        }

        // 页面加载时初始化
        window.onload = function() {
            initPage();
        };
    </script>
</body>
</html> 