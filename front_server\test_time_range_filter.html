<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>时间范围过滤测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button.active {
            background-color: #28a745;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 10px;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .time-info {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>时间范围过滤测试</h1>
        <p>测试实时指标页面的时间范围过滤功能</p>

        <div class="test-section">
            <h3>时间范围选择</h3>
            <button onclick="testTimeRange('最近10分钟')" id="btn-10min">最近10分钟</button>
            <button onclick="testTimeRange('最近1小时')" id="btn-1hour" class="active">最近1小时</button>
            <button onclick="testTimeRange('最近6小时')" id="btn-6hour">最近6小时</button>
            <button onclick="testTimeRange('今日全天')" id="btn-today">今日全天</button>
            <button onclick="testTimeRange('最近24小时')" id="btn-24hour">最近24小时</button>
            
            <div class="time-info" id="time-info">
                当前时间范围: 最近1小时
            </div>
        </div>

        <div class="test-section">
            <h3>API数据获取</h3>
            <button onclick="fetchTestData()">获取测试数据</button>
            <button onclick="testTimeFilter()">测试时间过滤</button>
            <div id="api-status"></div>
        </div>

        <div class="test-section">
            <h3>过滤结果</h3>
            <div id="filter-status"></div>
            <pre id="filter-result"></pre>
        </div>

        <div class="test-section">
            <h3>时间点格式测试</h3>
            <button onclick="testTimeFormat()">测试时间格式</button>
            <div id="format-status"></div>
            <pre id="format-result"></pre>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:8080';
        const IDENTITY_MAC = 'cc:dd:ee:52:3e:60';
        
        let currentTimeRange = '最近1小时';
        let testData = null;

        function showStatus(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        function showData(elementId, data) {
            const element = document.getElementById(elementId);
            element.textContent = JSON.stringify(data, null, 2);
        }

        function testTimeRange(range) {
            currentTimeRange = range;
            
            // 更新按钮状态
            document.querySelectorAll('button').forEach(btn => btn.classList.remove('active'));
            document.getElementById(`btn-${range.replace('最近', '').replace('小时', 'hour').replace('分钟', 'min').replace('今日全天', 'today').replace('最近24小时', '24hour')}`).classList.add('active');
            
            // 更新时间信息
            document.getElementById('time-info').textContent = `当前时间范围: ${range}`;
            
            // 如果有数据，重新过滤
            if (testData) {
                testTimeFilter();
            }
        }

        async function fetchTestData() {
            try {
                showStatus('api-status', '正在获取测试数据...', 'info');
                
                const today = new Date();
                const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 0, 0, 0);
                const endOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 23, 59, 59);
                
                const formatDateTime = (date) => {
                    const year = date.getFullYear();
                    const month = String(date.getMonth() + 1).padStart(2, '0');
                    const day = String(date.getDate()).padStart(2, '0');
                    const hours = String(date.getHours()).padStart(2, '0');
                    const minutes = String(date.getMinutes()).padStart(2, '0');
                    const seconds = String(date.getSeconds()).padStart(2, '0');
                    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
                };

                const startTime = formatDateTime(startOfDay);
                const endTime = formatDateTime(endOfDay);
                
                const url = `${API_BASE_URL}/api/v1/admin/terminal/metrics/device/${IDENTITY_MAC}/range?startTime=${encodeURIComponent(startTime)}&endTime=${encodeURIComponent(endTime)}&page=0&size=1000`;
                
                const response = await fetch(url);
                const data = await response.json();
                
                testData = data;
                showStatus('api-status', `获取成功，共 ${data.content?.length || 0} 条记录`, 'success');
                
            } catch (error) {
                console.error('获取测试数据失败:', error);
                showStatus('api-status', `获取失败: ${error.message}`, 'error');
            }
        }

        function filterDataByTimeRange(data, timeRange) {
            if (!data || data.length === 0) return [];
            
            const now = new Date();
            let startTime;
            
            switch (timeRange) {
                case '最近10分钟':
                    startTime = new Date(now.getTime() - 10 * 60 * 1000);
                    break;
                case '最近1小时':
                    startTime = new Date(now.getTime() - 60 * 60 * 1000);
                    break;
                case '最近6小时':
                    startTime = new Date(now.getTime() - 6 * 60 * 60 * 1000);
                    break;
                case '今日全天':
                    startTime = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 0, 0, 0);
                    break;
                case '最近24小时':
                    startTime = new Date(now.getTime() - 24 * 60 * 60 * 1000);
                    break;
                default:
                    startTime = new Date(now.getTime() - 60 * 60 * 1000);
            }
            
            const filteredData = data.filter(item => {
                const itemTime = new Date(item.receiveTime || item.metricTime);
                return itemTime >= startTime && itemTime <= now;
            });
            
            return filteredData;
        }

        function testTimeFilter() {
            if (!testData || !testData.content) {
                showStatus('filter-status', '请先获取测试数据', 'error');
                return;
            }
            
            try {
                const originalData = testData.content;
                const filteredData = filterDataByTimeRange(originalData, currentTimeRange);
                
                const result = {
                    timeRange: currentTimeRange,
                    originalCount: originalData.length,
                    filteredCount: filteredData.length,
                    timeSpan: {
                        start: filteredData.length > 0 ? filteredData[0].receiveTime : null,
                        end: filteredData.length > 0 ? filteredData[filteredData.length - 1].receiveTime : null
                    },
                    sampleData: filteredData.slice(0, 3).map(item => ({
                        receiveTime: item.receiveTime,
                        metricTime: item.metricTime,
                        cpuPercent: item.cpuPercent,
                        memoryPercent: item.memoryPercent
                    }))
                };
                
                showData('filter-result', result);
                showStatus('filter-status', `过滤完成: ${originalData.length} → ${filteredData.length} 条记录`, 'success');
                
            } catch (error) {
                console.error('时间过滤测试失败:', error);
                showStatus('filter-status', `过滤失败: ${error.message}`, 'error');
            }
        }

        function testTimeFormat() {
            if (!testData || !testData.content) {
                showStatus('format-status', '请先获取测试数据', 'error');
                return;
            }
            
            const sampleData = testData.content.slice(0, 5);
            const formatResults = sampleData.map(item => {
                const receiveTime = new Date(item.receiveTime);
                const metricTime = new Date(item.metricTime);
                
                return {
                    original: {
                        receiveTime: item.receiveTime,
                        metricTime: item.metricTime
                    },
                    formatted: {
                        receiveTimeHMS: receiveTime.toLocaleTimeString('zh-CN', {
                            hour: '2-digit',
                            minute: '2-digit',
                            second: '2-digit'
                        }),
                        metricTimeHMS: metricTime.toLocaleTimeString('zh-CN', {
                            hour: '2-digit',
                            minute: '2-digit',
                            second: '2-digit'
                        }),
                        receiveTimeFull: receiveTime.toLocaleString('zh-CN'),
                        metricTimeFull: metricTime.toLocaleString('zh-CN')
                    }
                };
            });
            
            showData('format-result', formatResults);
            showStatus('format-status', `时间格式测试完成，处理了 ${formatResults.length} 个样本`, 'success');
        }

        // 页面加载完成后自动获取数据
        window.addEventListener('load', () => {
            console.log('时间范围过滤测试页面加载完成');
            fetchTestData();
        });
    </script>
</body>
</html>
