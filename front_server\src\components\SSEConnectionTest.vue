<template>
  <div class="sse-test-container">
    <el-card class="test-card">
      <template #header>
        <div class="card-header">
          <span>SSE连接测试</span>
          <el-button 
            :type="isConnected ? 'success' : 'danger'" 
            size="small"
            @click="toggleConnection"
          >
            {{ isConnected ? '断开连接' : '连接SSE' }}
          </el-button>
        </div>
      </template>

      <!-- 连接状态 -->
      <div class="connection-status">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-statistic title="连接状态" :value="connectionStatusText" />
          </el-col>
          <el-col :span="8">
            <el-statistic title="重连次数" :value="connectionStatus.reconnectAttempts" />
          </el-col>
          <el-col :span="8">
            <el-statistic title="连接URL" :value="sseEndpoint" />
          </el-col>
        </el-row>
      </div>

      <!-- 环境信息 -->
      <div class="env-info">
        <h4>环境配置信息</h4>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="当前环境">{{ currentEnv }}</el-descriptions-item>
          <el-descriptions-item label="API基础URL">{{ apiBaseUrl }}</el-descriptions-item>
          <el-descriptions-item label="SSE基础URL">{{ sseBaseUrl }}</el-descriptions-item>
          <el-descriptions-item label="完整SSE地址">{{ fullSseUrl }}</el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 测试按钮 -->
      <div class="test-actions">
        <el-button type="primary" @click="testConnection">测试连接</el-button>
        <el-button type="warning" @click="clearLogs">清空日志</el-button>
        <el-button type="info" @click="sendTestData">发送测试数据</el-button>
      </div>

      <!-- 连接日志 -->
      <div class="connection-logs">
        <h4>连接日志</h4>
        <div class="log-container">
          <div 
            v-for="(log, index) in logs" 
            :key="index"
            class="log-item"
            :class="log.type"
          >
            <span class="log-time">{{ log.time }}</span>
            <span class="log-type">[{{ log.type.toUpperCase() }}]</span>
            <span class="log-message">{{ log.message }}</span>
          </div>
          <div v-if="logs.length === 0" class="no-logs">
            暂无日志
          </div>
        </div>
      </div>

      <!-- 接收到的消息 -->
      <div class="received-messages">
        <h4>接收到的消息</h4>
        <div class="message-container">
          <div 
            v-for="(message, index) in messages" 
            :key="index"
            class="message-item"
          >
            <div class="message-header">
              <span class="message-type">{{ message.type }}</span>
              <span class="message-time">{{ message.time }}</span>
            </div>
            <div class="message-content">
              <pre>{{ JSON.stringify(message.data, null, 2) }}</pre>
            </div>
          </div>
          <div v-if="messages.length === 0" class="no-messages">
            暂无消息
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
import sseService from '@/services/sseService'
import { dataApi } from '@/api'

export default {
  name: 'SSEConnectionTest',
  data() {
    return {
      isConnected: false,
      logs: [],
      messages: [],
      connectionStatus: {
        isConnected: false,
        readyState: EventSource.CLOSED,
        reconnectAttempts: 0
      }
    }
  },
  computed: {
    connectionStatusText() {
      const stateMap = {
        [EventSource.CONNECTING]: '连接中',
        [EventSource.OPEN]: '已连接',
        [EventSource.CLOSED]: '已断开'
      }
      return stateMap[this.connectionStatus.readyState] || '未知'
    },
    currentEnv() {
      return import.meta.env.MODE
    },
    apiBaseUrl() {
      return import.meta.env.VITE_ADMIN_API_BASE_URL || 'http://localhost:8080'
    },
    sseBaseUrl() {
      return import.meta.env.VITE_SSE_BASE_URL || 'http://localhost:8081'
    },
    sseEndpoint() {
      if (this.sseBaseUrl.startsWith('http')) {
        // 开发环境：使用完整URL
        return `${this.sseBaseUrl}/api/device/notifications/sse`
      } else if (this.sseBaseUrl) {
        // 生产环境：使用相对路径，通过nginx代理
        return `${this.sseBaseUrl}/api/device/notifications/sse`
      } else {
        // 默认相对路径
        return '/api/device/notifications/sse'
      }
    },
    fullSseUrl() {
      if (this.sseBaseUrl.startsWith('http')) {
        return this.sseEndpoint
      } else {
        return `${window.location.origin}${this.sseEndpoint}`
      }
    }
  },
  mounted() {
    this.setupSSEListeners()
    this.updateConnectionStatus()
    
    // 定期更新连接状态
    this.statusInterval = setInterval(() => {
      this.updateConnectionStatus()
    }, 1000)
  },
  beforeUnmount() {
    if (this.statusInterval) {
      clearInterval(this.statusInterval)
    }
    this.cleanupSSEListeners()
  },
  methods: {
    setupSSEListeners() {
      sseService.on('connected', this.handleConnected)
      sseService.on('device-registered', this.handleMessage)
      sseService.on('device-online', this.handleMessage)
      sseService.on('device-offline', this.handleMessage)
      sseService.on('statistics-update', this.handleMessage)
      sseService.on('max-reconnect-reached', this.handleMaxReconnectReached)
    },
    
    cleanupSSEListeners() {
      sseService.off('connected', this.handleConnected)
      sseService.off('device-registered', this.handleMessage)
      sseService.off('device-online', this.handleMessage)
      sseService.off('device-offline', this.handleMessage)
      sseService.off('statistics-update', this.handleMessage)
      sseService.off('max-reconnect-reached', this.handleMaxReconnectReached)
    },
    
    handleConnected(data) {
      this.addLog('info', `连接成功: ${data}`)
    },
    
    handleMessage(data) {
      this.addMessage(data.type || 'unknown', data)
      this.addLog('success', `收到消息: ${data.type || 'unknown'}`)
    },
    
    handleMaxReconnectReached() {
      this.addLog('error', '达到最大重连次数，停止重连')
    },
    
    toggleConnection() {
      if (this.isConnected) {
        sseService.disconnect()
        this.addLog('warning', '手动断开连接')
      } else {
        sseService.connect()
        this.addLog('info', '手动连接SSE服务')
      }
    },
    
    testConnection() {
      this.addLog('info', '开始测试连接...')
      this.addLog('info', `连接地址: ${this.fullSseUrl}`)
      
      // 测试基本的HTTP连接
      fetch(this.fullSseUrl.replace('/sse', '/test'))
        .then(response => {
          this.addLog('success', `HTTP测试成功: ${response.status}`)
        })
        .catch(error => {
          this.addLog('error', `HTTP测试失败: ${error.message}`)
        })
      
      // 重新连接SSE
      sseService.disconnect()
      setTimeout(() => {
        sseService.connect()
      }, 1000)
    },
    
    async sendTestData() {
      try {
        this.addLog('info', '发送测试数据到后端...')
        
        const testData = {
          device_id: 'test_sse_device',
          hostname: 'test_sse_terminal',
          uptime: 3600,
          cpu_temp: 65.5,
          temperatures: {
            'soc-thermal': 65.5,
            'gpu-thermal': 63.2
          },
          cpu_usage: {
            num: '4',
            core: '4',
            thread: '4',
            user: '25.5',
            sys: '8.2',
            idle: '66.3'
          },
          cpu_percent: 33.7,
          memory_percent: 45.8,
          memory_usage: {
            total: 4085002240,
            available: 2200000000,
            percent: 45.8,
            used: 1885002240,
            free: 2200000000,
            active: 1600000000,
            inactive: 285002240,
            buffers: 56995840,
            cached: 630214656,
            shared: 1032192,
            slab: 136617984
          },
          disk_usage: [{
            device: '/dev/mmcblk0p3',
            mountpoint: '/',
            fstype: 'ext4',
            total: 30602608640,
            used: 15301304320,
            free: 15301304320,
            percent: 50.0
          }],
          disk_data_percent: 45.2,
          disk_system_percent: 50.0,
          cdata: {
            count: 25,
            size: 1250000000
          },
          zdata: {
            count: 10,
            size: 500000000
          },
          group_usage: {},
          group_bps: 512000,
          app_version: {
            controller: '1.0.7',
            detector: '1.0.54',
            web: '1.0.61',
            agent: '1.0.5',
            front: '1.0.46'
          },
          expired_date: '2025-10-31 23:59:59'
        }
        
        const response = await dataApi.post('/api/agent/report', testData)
        this.addLog('success', '测试数据发送成功')
        console.log('测试数据响应:', response)
        
      } catch (error) {
        this.addLog('error', `发送测试数据失败: ${error.message}`)
        console.error('发送测试数据错误:', error)
      }
    },
    
    updateConnectionStatus() {
      this.connectionStatus = sseService.getConnectionStatus()
      this.isConnected = this.connectionStatus.isConnected
    },
    
    addLog(type, message) {
      const now = new Date()
      const time = now.toLocaleTimeString()
      this.logs.unshift({ type, message, time })
      
      // 限制日志数量
      if (this.logs.length > 50) {
        this.logs = this.logs.slice(0, 50)
      }
    },
    
    addMessage(type, data) {
      const now = new Date()
      const time = now.toLocaleTimeString()
      this.messages.unshift({ type, data, time })
      
      // 限制消息数量
      if (this.messages.length > 20) {
        this.messages = this.messages.slice(0, 20)
      }
    },
    
    clearLogs() {
      this.logs = []
      this.messages = []
    }
  }
}
</script>

<style scoped>
.sse-test-container {
  padding: 20px;
}

.test-card {
  max-width: 1200px;
  margin: 0 auto;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.connection-status {
  margin-bottom: 20px;
}

.env-info {
  margin-bottom: 20px;
}

.test-actions {
  margin-bottom: 20px;
}

.test-actions .el-button {
  margin-right: 10px;
}

.connection-logs,
.received-messages {
  margin-bottom: 20px;
}

.log-container,
.message-container {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 10px;
  background-color: #f8f9fa;
}

.log-item {
  margin-bottom: 5px;
  font-family: monospace;
  font-size: 12px;
}

.log-item.info { color: #409eff; }
.log-item.success { color: #67c23a; }
.log-item.warning { color: #e6a23c; }
.log-item.error { color: #f56c6c; }

.log-time {
  color: #909399;
  margin-right: 10px;
}

.log-type {
  font-weight: bold;
  margin-right: 10px;
}

.message-item {
  margin-bottom: 15px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 10px;
  background-color: white;
}

.message-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
  font-weight: bold;
}

.message-type {
  color: #409eff;
}

.message-time {
  color: #909399;
  font-size: 12px;
}

.message-content pre {
  margin: 0;
  font-size: 12px;
  background-color: #f5f7fa;
  padding: 10px;
  border-radius: 4px;
  overflow-x: auto;
}

.no-logs,
.no-messages {
  text-align: center;
  color: #909399;
  padding: 20px;
}
</style>
