package com.unnet.jmanul.common.utils;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.Calendar;
import java.util.Date;

public class DateTimeUtils {

    public static boolean isExpired(LocalDateTime expiry) {
        return System.currentTimeMillis() > expiry.atZone(ZoneOffset.UTC).toInstant().toEpochMilli();
    }

    public static boolean isNonExpired(LocalDateTime expiry) {
        return System.currentTimeMillis() < expiry.atZone(ZoneOffset.UTC).toInstant().toEpochMilli();
    }

    public static Date getMaxDate() {
        var cal = Calendar.getInstance();
        cal.set(2099, 12, 30, 0, 0, 0);
        return cal.getTime();
    }

    public static LocalDateTime getMaxLocalDate() {
        LocalDateTime now = LocalDateTime.now();
        return now.withYear(2099).withMonth(12).withDayOfMonth(30);
    }

}
