// 上传src文件到/data/backend/backend_admin_server中

// 构建管理平台镜像
docker build -f ./dockerfile -t backend_admin_server:1.0 .

// 启动容器
docker run -d \
--name backend_admin_server \
-p 8080:8080 \
-e SPRING_PROFILES_ACTIVE=prod \
-e SPRING_ELASTICSEARCH_URIS=10.0.0.3:9200,10.0.0.6:9200,10.0.0.7:9200 \
-e SPRING_ELASTICSEARCH_USERNAME=elastic \
-e SPRING_ELASTICSEARCH_PASSWORD=Changan5g.es \
-e JAVA_TOOL_OPTIONS="-Duser.timezone=Asia/Shanghai" \
backend_admin_server:1.0

3293737847

// 查看容器日志
docker logs front_server