package com.unnet.changan5G.component;

import com.unnet.changan5G.service.TerminalMetricElasticsearchService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

/**
 * Elasticsearch初始化组件
 *
 * <AUTHOR>
 * @date 2024-07-14
 */
@Component
@Slf4j
public class ElasticsearchInitializer implements ApplicationRunner {

    @Autowired(required = false)
    private TerminalMetricElasticsearchService terminalMetricElasticsearchService;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        if (terminalMetricElasticsearchService == null) {
            log.warn("TerminalMetricElasticsearchService未初始化，跳过Elasticsearch初始化");
            return;
        }

        log.info("开始初始化Elasticsearch...");

        try {
            // 延迟一下，等待Elasticsearch连接建立
            Thread.sleep(2000);

            // 检查索引是否存在，不存在则创建
            if (!terminalMetricElasticsearchService.indexExists()) {
                log.info("Elasticsearch索引不存在，开始创建索引...");
                boolean created = terminalMetricElasticsearchService.createIndex();
                if (created) {
                    log.info("Elasticsearch索引创建成功");
                } else {
                    log.error("Elasticsearch索引创建失败");
                }
            } else {
                log.info("Elasticsearch索引已存在，跳过创建");
            }

            log.info("Elasticsearch初始化完成");

        } catch (Exception e) {
            log.error("Elasticsearch初始化失败: {}", e.getMessage(), e);
            // 不抛出异常，避免影响应用启动
        }
    }
}
